﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Features.Admin.InstallmentProcessJob
{
    [DisallowConcurrentExecution]
    public class InstallmentDefaultedJob : IJob
    {
        private readonly BackgroundJobOptions _jobOptions;
        private readonly INotifyService _notifyService;
        private readonly IUserService _userService;
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<InstallmentDefaultedJob> _logger;
        public InstallmentDefaultedJob(INotifyService notifyService, IUserService userService, IOptions<BackgroundJobOptions> jobOptions, ApplicationDbContext dbContext, ILogger<InstallmentDefaultedJob> logger)
        {
            _jobOptions = jobOptions.Value;
            _notifyService = notifyService;
            _userService = userService;
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            if (context.RefireCount > 10)
            {
                _logger.LogInformation($"At InstallmentDefaultedJob Refire Count exceeded 10 times, short-circuiting job");
                return;
            }

            try
            {
                // notify defaulted installment to consumer
                _logger.LogInformation($"At InstallmentDefaultedJob started at {DateTimeOffset.UtcNow}");

                // since installments duedates had safe-scaped irantimezone
                // no duedate is set between 23:30 and 3:29 irantime
                // comparing dates makes no problem
                DateTimeOffset margin = DateTimeOffset.UtcNow.AddDays(-ApplicationConstants.InstallmentDefaultDays);

                var contextResult = new List<long>();

                var stats = OrderStatusProvider.GetInstallmentDefaultedJobAcceptable();

                var defaulteds = await _dbContext.Installments
                    .Where(i =>
                        i.Status != InstallmentStatus.PaidOff && i.Status != InstallmentStatus.SalaryDeductionPaidOff && i.Status != InstallmentStatus.SalaryDeductionWaiting &&
                        stats.Contains(i.Order.Status) &&
                        i.PaymentStatus != PaymentStatus.PaymentSucceeded &&
                        i.DueDate.Date < margin.Date)
                    .Select(i => new DefaultedInstallmentDto()
                    {
                        InstallmentId = i.Id,
                        OrderId = i.OrderId,
                        DueDate = i.DueDate,
                        InstallmentCode = i.Code,
                        InstallmentStatus = i.Status,
                        ConsumerUserId = i.Order.ConsumerUserId,
                        MerchantUserId = i.Order.MerchantUserId,
                        ConsumerUserName = i.Order.ConsumerUserName,
                    })
                    .ToListAsync();

                if (defaulteds != null && defaulteds.Count > 0)
                {
                    foreach (var item in defaulteds)
                    {
                        await ProcessDefaultedInstallmentAsync(context, item);

                        contextResult.Add(item.InstallmentId);
                    }
                }
                else
                {
                    _logger.LogInformation($"At InstallmentDefaultedJob no defaulted installments found");
                }

                context.Result = contextResult;
            }
            catch (Exception ex)
            {
                throw new JobExecutionException(msg: "At InstallmentDefaultedJob exception happened", refireImmediately: false, cause: ex);
            }

        }

        private async Task ProcessDefaultedInstallmentAsync(IJobExecutionContext context, DefaultedInstallmentDto item)
        {
            try
            {
                var gapDays = (DateTimeOffset.UtcNow - item.DueDate).Days;

                if (gapDays == (ApplicationConstants.InstallmentDefaultDays + 1) && item.ConsumerUserId.HasValue && !string.IsNullOrWhiteSpace(item.ConsumerUserName))
                {
                    // notify consumer
                    // do not send for salary deduction plan consumer
                    await _notifyService.SendInstallmentDefaultedMessage(
                        item.ConsumerUserName,
                        item.MerchantUserId,
                        gapDays.ToString(),
                        item.InstallmentCode,
                        context.CancellationToken);

                    _logger.LogInformation($"At InstallmentDefaultedJob, InstallmentDefaultedMessage sent for consumer: {item.ConsumerUserId.Value}, merchant: {item.MerchantUserId}, installment code: {item.InstallmentCode}");
                }

                if (item.InstallmentStatus == InstallmentStatus.Waiting || item.InstallmentStatus == InstallmentStatus.Delayed)
                {
                    // set installment to Defaulted
                    // set order to InstallmentsPaymentDefaulted
                    var installment = await GetInstallmentWithOrderAsync(item.InstallmentId, context.CancellationToken);

                    if (installment != null)
                    {
                        installment.Status = InstallmentStatus.Defaulted;
                        installment.Order.Status = OrderStatus.InstallmentsPaymentDefaulted;

                        await _dbContext.SaveChangesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "At InstallmentDefaultedJob exception happened processing installment {InstallmentId} of order {OrderId}", item.InstallmentId, item.OrderId);
            }
        }

        private async Task<Installment> GetInstallmentWithOrderAsync(long instId, CancellationToken cancellationToken)
        {
            return await _dbContext.Installments.Where(i => i.Id == instId).Include(i => i.Order).SingleOrDefaultAsync(cancellationToken);
        }
    }

    public class DefaultedInstallmentDto
    {
        public long InstallmentId { get; set; }
        public long OrderId { get; set; }
        public InstallmentStatus InstallmentStatus { get; set; }
        public DateTimeOffset DueDate { get; set; }
        public Guid InstallmentCode { get; set; }
        public int? ConsumerUserId { get; set; }
        public string ConsumerUserName { get; set; }

        public int MerchantUserId { get; set; }

    }
}
