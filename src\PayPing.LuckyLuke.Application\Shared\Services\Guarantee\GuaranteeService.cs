﻿using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Factory;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Shared.Abstractions;

namespace PayPing.LuckyLuke.Application.Shared.Services.Guarantee
{
    public class GuaranteeService : IGuaranteeService
    {
        private readonly IEnumerable<IGuaranteeFactory> _guaranteeFactories;
        private readonly IUserService _userService;
        private readonly ApplicationDbContext _dbContext;

        public GuaranteeService(IEnumerable<IGuaranteeFactory> guaranteeFactories, IUserService userService, ApplicationDbContext dbContext)
        {
            _guaranteeFactories = guaranteeFactories;
            _userService = userService;
            _dbContext = dbContext;
        }

        public async ValueTask<(GuaranteeType guaranteeType, GuaranteeProvider providerType)> GetGuaranteeTypesByGuarantorId(int guarantorId)
        {
            var guarantor = await _dbContext.Guarantors.Where(x => x.Id == guarantorId).FirstOrDefaultAsync();

            if (guarantor == null)
            {
                throw new Exception($"guarantor not found in GuaranteeService GetGuaranteeTypeByGuarantorId with id: {guarantorId}");
            }

            GuaranteeProvider providerType = guarantor.GuaranteeProvider;
            GuaranteeType guaranteeType = guarantor.GuaranteeType;

            return new(guaranteeType, providerType);
        }

        public async Task<IGuaranteeProvider> GetGuaranteeProviderByGuarantorId(int guarantorId)
        {
            var guarantor = await _dbContext.Guarantors.Where(x => x.Id == guarantorId).FirstOrDefaultAsync();

            if (guarantor == null)
            {
                throw new Exception($"guarantor not found in GuaranteeService GetGuaranteeProvider with id: {guarantorId}");
            }

            GuaranteeProvider providerType = guarantor.GuaranteeProvider;
            GuaranteeType guaranteeType = guarantor.GuaranteeType;

            return GetGuaranteeProviderByTypes(guaranteeType, providerType);
        }

        public IGuaranteeProvider GetGuaranteeProviderByTypes(GuaranteeType guaranteeType, GuaranteeProvider providerType)
        {
            IGuaranteeProvider provider;
            switch (guaranteeType)
            {
                case GuaranteeType.Promissory:
                    provider = _guaranteeFactories.First(f => f.GuaranteeProvider == providerType).CreatePromissory();
                    break;
                case GuaranteeType.Cheque:
                    provider = _guaranteeFactories.First(f => f.GuaranteeProvider == providerType).CreateCheque();
                    break;
                case GuaranteeType.None:
                    provider = _guaranteeFactories.First(f => f.GuaranteeProvider == providerType).CreateNoneGuarantor();
                    break;
                case GuaranteeType.Salary:
                    provider = _guaranteeFactories.First(f => f.GuaranteeProvider == providerType).CreateSalaryGuarantor();
                    break;
                default:
                    throw new Exception("invalid guarantee type");
            }
            return provider;
        }

        public async Task<IPromissoryGuaranteeProvider> GetPromissoryGuaranteeProviderByGuarantorId(int guarantorId)
        {
            var guarantor = await _dbContext.Guarantors.Where(x => x.Id == guarantorId).FirstOrDefaultAsync();

            if (guarantor == null)
            {
                throw new Exception($"guarantor not found in GuaranteeService GetGuaranteeProvider with id: {guarantorId}");
            }

            return GetPromissoryProviderByProvider(guarantor.GuaranteeProvider);
        }

        public IPromissoryGuaranteeProvider GetPromissoryProviderByProvider(GuaranteeProvider providerType)
        {
            return _guaranteeFactories.First(f => f.GuaranteeProvider == providerType).CreatePromissory();
        }

        public async ValueTask<Guarantor> FindGuarantorForType(GuaranteeType guaranteeType, int merchantUserId, CancellationToken cancellationToken)
        {
            //TODO: implement competition algorithm
            Guarantor guarantor = null;
            switch (guaranteeType)
            {

                case GuaranteeType.None:
                    {
                        guarantor = await _dbContext.Guarantors.AsNoTracking()
                            .Where(g => g.GuaranteeType == guaranteeType && g.GuaranteeProvider == GuaranteeProvider.None)
                            .FirstOrDefaultAsync();
                        break;
                    }
                case GuaranteeType.Salary:
                    {
                        guarantor = await _dbContext.Guarantors.AsNoTracking()
                            .Where(g => g.GuaranteeType == guaranteeType && g.GuaranteeProvider == GuaranteeProvider.Salary)
                            .FirstOrDefaultAsync();
                        break;
                    }
                case GuaranteeType.Promissory:
                case GuaranteeType.Cheque:
                default:
                    {
                        var merchPredefinedCode = await _dbContext.MerchantInfos.AsNoTracking()
                            .Where(x => x.MerchantUserId == merchantUserId)
                            .Select(x => x.PredefinedCode)
                            .FirstOrDefaultAsync();

                        if (!string.IsNullOrEmpty(merchPredefinedCode) && !merchPredefinedCode.StartsWith("RECIPIENT-"))
                        {
                            // can have koknus
                            guarantor = await _dbContext.Guarantors.AsNoTracking()
                                .Where(g => g.GuaranteeType == guaranteeType && g.GuaranteeProvider == GuaranteeProvider.Kuknos)
                                .FirstOrDefaultAsync();
                        }
                        else
                        {
                            guarantor = await _dbContext.Guarantors.AsNoTracking()
                                .Where(g => g.GuaranteeType == guaranteeType && g.GuaranteeProvider == GuaranteeProvider.Kiahooshan)
                                .FirstOrDefaultAsync();
                        }
                        break;
                    }
            }

            if (guarantor == null)
            {
                throw new Exception($"guarantor not found for guarantee type: {guaranteeType}");
            }

            return guarantor;
        }

        public async ValueTask<DigitalSignType> GetGuarantorCompliantSignatureTypeByGuarantorId(int guarantorId)
        {
            var guarantor = await _dbContext.Guarantors.Where(x => x.Id == guarantorId).FirstOrDefaultAsync();

            if (guarantor == null)
            {
                throw new Exception($"guarantor not found in GuaranteeService GetGuarantorCompliantSignatureTypeByGuarantorId with id: {guarantorId}");
            }

            switch (guarantor.GuaranteeProvider)
            {
                case GuaranteeProvider.Kuknos:
                    return DigitalSignType.KuknosVersion1;
                case GuaranteeProvider.None:
                case GuaranteeProvider.Salary:
                case GuaranteeProvider.Kiahooshan:
                    return DigitalSignType.KiahooshanVersion1;
                default:
                    throw new Exception($"GuaranteeProvider not found in GuaranteeService GetGuarantorCompliantSignatureTypeByGuarantorId with id: {guarantorId}");
            }
        }

        public async ValueTask<DigitalSignType> GetGuarantorCompliantSignatureTypeByGuaranteeType(GuaranteeType guaranteeType, int merchantUserId, CancellationToken cancellationToken)
        {
            switch (guaranteeType)
            {
                case GuaranteeType.None:
                case GuaranteeType.Salary:
                    return DigitalSignType.KiahooshanVersion1;
                default:
                    {
                        var gt = await FindGuarantorForType(guaranteeType, merchantUserId, cancellationToken);
                        switch (gt.GuaranteeProvider)
                        {
                            case GuaranteeProvider.Kuknos:
                                return DigitalSignType.KuknosVersion1;
                            case GuaranteeProvider.None:
                            case GuaranteeProvider.Salary:
                            case GuaranteeProvider.Kiahooshan:
                                return DigitalSignType.KiahooshanVersion1;
                            default:
                                throw new Exception($"GuaranteeProvider not found in GuaranteeService GetGuarantorCompliantSignatureTypeByGuarantorId with id: {gt.Id}");
                        }
                    }
            }
        }
    }
}
