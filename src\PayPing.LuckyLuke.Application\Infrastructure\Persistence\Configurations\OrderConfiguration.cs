﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Application.Infrastructure.Persistence.NpgSql;
using PayPing.BNPL.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class OrderConfiguration : IEntityTypeConfiguration<Order>
{
    public void Configure(EntityTypeBuilder<Order> builder)
    {
        builder.ComplexProperty(e => e.OrderPlan);

        builder.HasIndex(x => x.MerchantUserId).HasDatabaseName("IX_Order_MerchantUserId");
        builder.HasIndex(x => x.ConsumerUserId).HasDatabaseName("IX_Order_ConsumerUserId");

        builder.Property(nameof(Order.ConsumerIdentifier)).HasMaxLength(256);
        builder.Property(nameof(Order.ClientRefId)).HasMaxLength(512);
        builder.Property(nameof(Order.ClientCallbackUrl)).HasMaxLength(512);
        builder.Property(nameof(Order.ClientCancelUrl)).HasMaxLength(512);
        builder.Property(nameof(Order.CreditLockId)).HasMaxLength(256);
        builder.Property(nameof(Order.MerchantName)).HasMaxLength(2046);

        builder.HasIndex(x => x.TrackingCode).IsUnique().HasDatabaseName("IX_Order_TrackingCode");
        builder.HasIndex(x => x.ClientRefId).HasDatabaseName("IX_Order_ClientRefId");
        
        builder.Property(nameof(Order.ConsumerNationalHashId)).HasMaxLength(64);
        builder.HasIndex(x => x.ConsumerNationalHashId).HasDatabaseName("IX_Order_ConsumerNationalHashId");

        builder.Property(nameof(Order.ConsumerUserName)).HasMaxLength(64);
        builder.HasIndex(x => x.ConsumerUserName).HasDatabaseName("IX_Order_ConsumerUserName");

        builder.Property(nameof(Order.ConsumerName)).HasMaxLength(2046);
        builder.HasIndex(x => x.ConsumerName).HasDatabaseName("IX_Order_ConsumerName");
        
        builder.Property(nameof(Order.SignedContractFileId)).HasMaxLength(128);

        builder.Property(nameof(Order.ExpireDate)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);
        builder.Property(nameof(Order.LatestPrePaymentTryDate)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);
        builder.Property(nameof(Order.DueDate)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);

        builder.ComplexProperty(e => e.OrderCancellation).Property(a => a.RefundRefId).HasMaxLength(64);
        builder.ComplexProperty(e => e.OrderCancellation).Property(a => a.Reason).HasMaxLength(2046);
        builder.ComplexProperty(e => e.OrderCancellation).Property(a => a.Description).HasMaxLength(4092);


        builder
            .HasMany(e => e.Installments)
            .WithOne(e => e.Order)
            .HasForeignKey(e => e.OrderId)
            .IsRequired();
        builder
            .HasMany(e => e.OrderCreditValidations)
            .WithOne(e => e.Order)
            .HasForeignKey(e => e.OrderId)
            .IsRequired();
        builder
            .HasMany(e => e.OrderGuarantees)
            .WithOne(e => e.Order)
            .HasForeignKey(e => e.OrderId)
            .IsRequired();

        builder
            .HasMany(e => e.OrderPayments)
            .WithOne(e => e.Order)
            .HasForeignKey(e => e.OrderId)
            .IsRequired();

        builder
            .HasMany(e => e.OrderExternalEventOutboxes)
            .WithOne(e => e.Order)
            .HasForeignKey(e => e.OrderId)
            .IsRequired();

        builder
            .HasMany(e => e.OrderTargetPlans)
            .WithOne(e => e.Order)
            .HasForeignKey(e => e.OrderId)
            .IsRequired();
    }
}
