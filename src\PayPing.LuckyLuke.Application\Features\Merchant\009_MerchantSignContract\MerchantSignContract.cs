﻿using FluentValidation;
using iText.Bouncycastle.Crypto;
using iText.Bouncycastle.X509;
using iText.Commons.Bouncycastle.Cert;
using iText.Commons.Bouncycastle.Crypto;
using iText.Forms.Form.Element;
using iText.IO.Image;
using iText.IO.Source;
using iText.Kernel.Crypto;
using iText.Kernel.Geom;
using iText.Kernel.Pdf;
using iText.Signatures;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Org.BouncyCastle.Asn1.Pkcs;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.X509;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._009_MerchantSignContract
{
    public record MerchantSignContractRequest(int contractId);

    public record MerchantSignContractResponse(string fileName);

    public class MerchantSignContractEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.MerchantSignContract,
                async (
                    MerchantSignContractRequest request,
                    IMerchantSignContractRequestHandler handler,
                    IValidator<MerchantSignContractRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("MerchantSignContract")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<MerchantSignContractResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Merchant Sign Contract")
            .WithDescription("Merchant Sign Contract")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class MerchantSignContractRequestHandler : IMerchantSignContractRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IEnumerable<IDigitalSignProvider> _digitalSignProviders;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly IS3ServiceApiClient _s3ServiceApiClient;
        private readonly IUserService _userService;
        private readonly IUserContext _userContext;

        public MerchantSignContractRequestHandler(ApplicationDbContext dbContext, IGuaranteeService guaranteeService,
            IEnumerable<IDigitalSignProvider> digitalSignProviders, IUploadGrpcClient uploadGrpcClient,
            IS3ServiceApiClient s3ServiceApiClient, IUserService userService, IUserContext userContext)
        {
            _dbContext = dbContext;
            _guaranteeService = guaranteeService;
            _digitalSignProviders = digitalSignProviders;
            _uploadGrpcClient = uploadGrpcClient;
            _s3ServiceApiClient = s3ServiceApiClient;
            _userService = userService;
            _userContext = userContext;
        }

        public async ValueTask<MerchantSignContractResponse> HandleAsync(MerchantSignContractRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var contract = await _dbContext.Contracts
                .Where(c => c.Id == request.contractId)
                .FirstOrDefaultAsync();

            if (contract == null)
            {
                throw new ArgumentException("قرارداد فروش اقساطی فعال یافت نشد");
            }

            if (contract.ActivationStatus != ActivationStatus.AdminContractWageSet)
            {
                throw new ArgumentException("قرارداد در مرحله تایید و امضا نمی باشد");
            }

            if (string.IsNullOrEmpty(contract.FilledContractId))
            {
                throw new ArgumentException("فایل قرارداد یافت نشد");
            }

            var dlResult = await _uploadGrpcClient.GetPresignedUrlAsync(contract.FilledContractId, userId, FileManager.Grpc.UploadTypeEnum.Misc, cancellationToken);

            if (!dlResult.Succeeded)
            {
                throw new ArgumentException("خطا در دانلود فایل قرارداد");
            }

            var merchantInfo = await _dbContext.MerchantInfos.AsNoTracking().Where(x => x.MerchantUserId == userId).FirstOrDefaultAsync();

            var dsp = ChooseProvider();

            var signData = await dsp.GetPdfSignatureDataAsync(userId);

            if (string.IsNullOrWhiteSpace(merchantInfo.SignatureImageFileId) || string.IsNullOrWhiteSpace(signData.certificate))
            {
                throw new ArgumentException("فایل امضا یافت نشد");
            }

            var imageDlResult = await _uploadGrpcClient.GetPresignedUrlAsync(merchantInfo.SignatureImageFileId, userId, FileManager.Grpc.UploadTypeEnum.Misc, cancellationToken);

            if (!imageDlResult.Succeeded)
            {
                throw new ArgumentException("خطا در دانلود امضا");
            }

            using ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

            using Stream filledontract = await _s3ServiceApiClient.DownloadAsync(dlResult.SuccessResult, cancellationToken);

            using Stream signImageStream = await _s3ServiceApiClient.DownloadAsync(imageDlResult.SuccessResult, cancellationToken);

            var signImageArray = await signImageStream.ToByteArrayAsync();

            var imgData = ImageDataFactory.Create(signImageArray);

            SignDocumentSignature(filledontract, byteArrayOutputStream, imgData, signData.privateKey, signData.certificate);

            var finalPdf = await byteArrayOutputStream.ToByteArrayAsync();

            var uploadResult = await _uploadGrpcClient.UploadUserDocumentAsync(finalPdf, "pdf", userId, CancellationToken.None);

            if (!uploadResult.Succeeded)
            {
                throw new ArgumentException("خطا در ذخیره فایل قرارداد");
            }

            contract.SignedContractId = uploadResult.SuccessResult;
            contract.ActivationStatus = ActivationStatus.Final;
            await _dbContext.SaveChangesAsync();

            return new(uploadResult.SuccessResult);
        }

        private IDigitalSignProvider ChooseProvider()
        {
            var provider = _digitalSignProviders.FirstOrDefault(f => f.DigitalSignType == DigitalSignType.KuknosVersion1);

            return provider ?? _digitalSignProviders.OfType<KuknosDigitalSignProvider>().FirstOrDefault()
                ?? throw new ArgumentException("No valid digital sign provider found.");
        }

        private void SignDocumentSignature(Stream filledContract, ByteArrayOutputStream byteArrayOutputStream, ImageData imageData, string pkey, string certStr)
        {
            PdfSigner pdfSigner = new PdfSigner(new PdfReader(filledContract), new PdfWriter(byteArrayOutputStream), new StampingProperties());
            SignerProperties signerProperties = new SignerProperties();
            signerProperties.SetCertificationLevel(AccessPermissions.NO_CHANGES_PERMITTED);

            signerProperties.SetFieldName("signature");

            pdfSigner.SetSignerProperties(signerProperties);

            SignatureFieldAppearance appearance = new SignatureFieldAppearance(SignerProperties.IGNORED_ID).SetContent(imageData);

            var documnet = pdfSigner.GetDocument();


            signerProperties.SetPageNumber(documnet.GetNumberOfPages()).SetPageRect(new Rectangle(120, 150, 100, 100)).SetSignatureAppearance(appearance);

            IExternalSignature pks = GetPrivateKeySignature(pkey);
            IX509Certificate[] chain = LoadCertificateChainFromBase64(certStr);

            pdfSigner.SignDetached(
                new BouncyCastleDigest(),
                pks,
                chain,
                null, null, null, 0, PdfSigner.CryptoStandard.CMS);
        }


        private PrivateKeySignature GetPrivateKeySignature(string pkey)
        {
            IPrivateKey pk = new PrivateKeyBC(ReadPrivateKey(pkey));
            return new PrivateKeySignature(pk, DigestAlgorithms.SHA256);
        }

        public static IX509Certificate[] LoadCertificateChainFromBase64(string base64Certificate)
        {
            byte[] certBytes = Convert.FromBase64String(base64Certificate);

            X509CertificateParser certParser = new X509CertificateParser();
            var certificate = certParser.ReadCertificate(certBytes);

            List<IX509Certificate> certificateChain = new List<IX509Certificate> { new X509CertificateBC(certificate) };

            return certificateChain.ToArray();
        }

        private AsymmetricKeyParameter ReadPrivateKey(string base64Key)
        {
            string pkPem = PemFormatUtil.GetPkcs8PrivateKeyFormat(base64Key);

            AsymmetricKeyParameter privateKeyParam;
            using (StringReader sr = new StringReader(pkPem))
            {
                PemReader pemReader = new PemReader(sr);
                var pemObject = pemReader.ReadPemObject();
                RsaPrivateKeyStructure rsa = RsaPrivateKeyStructure.GetInstance(pemObject.Content);

                return new RsaPrivateCrtKeyParameters(rsa.Modulus,
                            rsa.PublicExponent, rsa.PrivateExponent,
                            rsa.Prime1, rsa.Prime2, rsa.Exponent1,
                            rsa.Exponent2, rsa.Coefficient);
            }
        }
    }

    public interface IMerchantSignContractRequestHandler
    {
        ValueTask<MerchantSignContractResponse> HandleAsync(MerchantSignContractRequest request, CancellationToken cancellationToken);
    }

    public class MerchantSignContractValidator : AbstractValidator<MerchantSignContractRequest>
    {
        public MerchantSignContractValidator()
        {
            RuleFor(c => c.contractId).NotEmpty().WithResourceError(() => ValidatorDictionary.contract_id_is_required);
        }
    }
}
