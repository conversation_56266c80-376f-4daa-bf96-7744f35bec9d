﻿using Asp.Versioning;
using Currency.Coin;
using FluentValidation;
using IdentityModel.AspNetCore.AccessTokenValidation;
using iText.Commons.Actions;
using MassTransit;
using MicroElements.Swashbuckle.FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using PaymentGrpc;
using PayPing.BNPL.Application.Infrastructure.Persistence.Extensions;
using PayPing.FileManager.Grpc;
using PayPing.Integrations.AdminSDK;
using PayPing.LuckyLuke.Application.Features.Admin._001_MerchantContractList;
using PayPing.LuckyLuke.Application.Features.Admin._002_GetMerchantContract;
using PayPing.LuckyLuke.Application.Features.Admin._003_AdminContractUpdate;
using PayPing.LuckyLuke.Application.Features.Admin.AdminMerchantInfoGet;
using PayPing.LuckyLuke.Application.Features.Admin.ContractList;
using PayPing.LuckyLuke.Application.Features.Admin.CreditSettingGet;
using PayPing.LuckyLuke.Application.Features.Admin.CreditSettingUpdate;
using PayPing.LuckyLuke.Application.Features.Admin.ExpiredOrderProcessJob;
using PayPing.LuckyLuke.Application.Features.Admin.GuarantorCreate;
using PayPing.LuckyLuke.Application.Features.Admin.GuarantorDelete;
using PayPing.LuckyLuke.Application.Features.Admin.GuarantorUpdate;
using PayPing.LuckyLuke.Application.Features.Admin.InstallmentDueJob;
using PayPing.LuckyLuke.Application.Features.Admin.InstallmentProcessJob;
using PayPing.LuckyLuke.Application.Features.Admin.OrderCancel;
using PayPing.LuckyLuke.Application.Features.Admin.OrderExternalEventJob;
using PayPing.LuckyLuke.Application.Features.Admin.OrderTimeLine;
using PayPing.LuckyLuke.Application.Features.Admin.SuspeciousCreditTransactionJob;
using PayPing.LuckyLuke.Application.Features.Consumer._01_SetPlan;
using PayPing.LuckyLuke.Application.Features.Consumer._03_GetSteps;
using PayPing.LuckyLuke.Application.Features.Consumer._039_Dummy;
using PayPing.LuckyLuke.Application.Features.Consumer._039_GetProfile;
using PayPing.LuckyLuke.Application.Features.Consumer._04_FillProfile;
using PayPing.LuckyLuke.Application.Features.Consumer._085_UploadSignature;
using PayPing.LuckyLuke.Application.Features.Consumer._086_CheckSignature;
using PayPing.LuckyLuke.Application.Features.Consumer._086_UploadVideoSignature;
using PayPing.LuckyLuke.Application.Features.Consumer._09_GetPromissoryPreIssueInfo;
using PayPing.LuckyLuke.Application.Features.Consumer._095_PromissoryIssue;
using PayPing.LuckyLuke.Application.Features.Consumer._10_PromissorySigning;
using PayPing.LuckyLuke.Application.Features.Consumer._12_PromissoryPayment;
using PayPing.LuckyLuke.Application.Features.Consumer._121_PromissoryPaymentVerifyVerify;
using PayPing.LuckyLuke.Application.Features.Consumer._126_GetContractInfo;
using PayPing.LuckyLuke.Application.Features.Consumer._127_SignContract;
using PayPing.LuckyLuke.Application.Features.Consumer._135_Quote;
using PayPing.LuckyLuke.Application.Features.Consumer._14_PrePaymentRequest;
using PayPing.LuckyLuke.Application.Features.Consumer._15_PaymentVerify;
using PayPing.LuckyLuke.Application.Features.Consumer._20_InstallmentsList;
using PayPing.LuckyLuke.Application.Features.Consumer._21_InstallmentPayment;
using PayPing.LuckyLuke.Application.Features.Consumer._22_InstallmentVerify;
using PayPing.LuckyLuke.Application.Features.Consumer._40_OrderCancel;
using PayPing.LuckyLuke.Application.Features.Consumer.InProgressOrdersChoose;
using PayPing.LuckyLuke.Application.Features.Consumer.InProgressOrdersGet;
using PayPing.LuckyLuke.Application.Features.Consumer.InstallmentDetail;
using PayPing.LuckyLuke.Application.Features.Consumer.InstallmentGroupPayment;
using PayPing.LuckyLuke.Application.Features.Consumer.InstallmentGroupVerify;
using PayPing.LuckyLuke.Application.Features.Consumer.SendContinueOnPhoneMessage;
using PayPing.LuckyLuke.Application.Features.Merchant._000_GetContract;
using PayPing.LuckyLuke.Application.Features.Merchant._001_CreateContract;
using PayPing.LuckyLuke.Application.Features.Merchant._002_SetCredit;
using PayPing.LuckyLuke.Application.Features.Merchant._003_SetWalletCreditAmount;
using PayPing.LuckyLuke.Application.Features.Merchant._004_SetMinPrePaymentRate;
using PayPing.LuckyLuke.Application.Features.Merchant._005_SetMonthlyInterestRate;
using PayPing.LuckyLuke.Application.Features.Merchant._006_CompleteMerchantProfileInfo;
using PayPing.LuckyLuke.Application.Features.Merchant._007_AcceptRegulations;
using PayPing.LuckyLuke.Application.Features.Merchant._009_MerchantSignContract;
using PayPing.LuckyLuke.Application.Features.Merchant._01_OrderCreate;
using PayPing.LuckyLuke.Application.Features.Merchant._061_GetMerchantProfileInfo;
using PayPing.LuckyLuke.Application.Features.Merchant._071_UploadSignatureImage;
using PayPing.LuckyLuke.Application.Features.Merchant._12_PlanCreate;
using PayPing.LuckyLuke.Application.Features.Merchant._15_OwnedCreditIncrease;
using PayPing.LuckyLuke.Application.Features.Merchant._40_OrderCancel;
using PayPing.LuckyLuke.Application.Features.Merchant.CreditTransactionList;
using PayPing.LuckyLuke.Application.Features.Merchant.GetOptions;
using PayPing.LuckyLuke.Application.Features.Merchant.InstallmentsCalendar;
using PayPing.LuckyLuke.Application.Features.Merchant.OrderListExcel;
using PayPing.LuckyLuke.Application.Features.Merchant.RefundFailureMessageConsumer;
using PayPing.LuckyLuke.Application.Features.Merchant.RefundSuccessMessageConsumer;
using PayPing.LuckyLuke.Application.Infrastructure.Builder;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Factory;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Quartz;
using PayPing.LuckyLuke.Application.Infrastructure.Security;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.OpenApiFilters;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Resources;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kuknos;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kuknos;
using PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kuknos;
using PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
using PayPing.LuckyLuke.Application.Shared.Clients.Http;
using PayPing.LuckyLuke.Application.Shared.Services.Credit;
using PayPing.LuckyLuke.Application.Shared.Services.DigitalSign.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Services.DigitalSign.Kuknos;
using PayPing.LuckyLuke.Application.Shared.Services.EventLog;
using PayPing.LuckyLuke.Application.Shared.Services.Excel;
using PayPing.LuckyLuke.Application.Shared.Services.Guarantee;
using PayPing.LuckyLuke.Application.Shared.Services.PdfSigner;
using PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kuknos;
using PayPing.LuckyLuke.Application.Shared.Services.PromissorySettlement.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Services.PromissorySettlement.Kuknos;
using PayPing.LuckyLuke.Application.Shared.Services.User;
using PayPing.Refund.WebApi.gRPC;
using Quartz;
using RedLockNet;
using RedLockNet.SERedis;
using RedLockNet.SERedis.Configuration;
using RobinHood.Services.ServiceManager.Protos;
using Scrutor;
using StackExchange.Redis;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;
using Unchase.Swashbuckle.AspNetCore.Extensions.Extensions;
using Wallet;
using Wallet.Type;
using Admin = PayPing.LuckyLuke.Application.Features.Admin;
using Consumer = PayPing.LuckyLuke.Application.Features.Consumer;
using Merchant = PayPing.LuckyLuke.Application.Features.Merchant;

namespace PayPing.BNPL.Application.Infrastructure.Configurations
{
    public static partial class ServiceCollectionExtensions
    {
        public static IServiceCollection AddConfigurations(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<IdentityServerOptions>(configuration);
            services.Configure<ServiceDiscoveryOptions>(configuration);
            services.Configure<RedisOptions>(configuration);
            services.Configure<ElasticOptions>(configuration);
            services.Configure<BNPLOptions>(configuration.GetSection(BNPLOptions.SectionName));
            services.Configure<PostgresOptions>(configuration.GetSection(PostgresOptions.SectionName));
            services.Configure<KuknosOptions>(configuration.GetSection(KuknosOptions.SectionName));
            services.Configure<ProxyOptions>(configuration.GetSection(ProxyOptions.SectionName));
            services.Configure<BackgroundJobOptions>(configuration.GetSection(BackgroundJobOptions.SectionName));
            services.Configure<RabbitMqOptions>(configuration);
            services.Configure<KiahooshanOptions>(configuration.GetSection(KiahooshanOptions.SectionName));

            return services;
        }


        #region Consumer Application

        public static IServiceCollection AddConsumerApplication(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            services.AddConfigurations(configuration);

            services.AddHttpContextAccessor();

            services.AddConsumerMinimalEndpoints();

            services.AddConsumerIdentity(configuration);

            services.AddConsumerHttpClients(configuration, environment);
            services.AddConsumerGrpcClients(configuration, environment);
            services.AddCors(corsOption =>
            {
                corsOption.AddPolicy(ApplicationConstants.CorsPolicy,
                    builder => builder.AllowAnyOrigin()
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .WithExposedHeaders("X-PayPingRequest-ID")
                        .SetPreflightMaxAge(TimeSpan.FromMinutes(10)));
            });

            services.AddConsumerServices(configuration);
            services.AddCommonServices(configuration);

            services.AddConsumerQueueHandlers(configuration);
            return services;
        }

        public static IServiceCollection AddConsumerMinimalEndpoints(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped, params Assembly[] assemblies)
        {

            var scanAssemblies = assemblies.Any()
                ? assemblies
                : TypeProvider.GetReferencedAssemblies(Assembly.GetCallingAssembly())
                    .Concat(TypeProvider.GetApplicationPartAssemblies(Assembly.GetCallingAssembly()))
                    .Distinct()
                    .ToArray();

            services.Scan(scan => scan
                .FromAssemblies(scanAssemblies)
                .AddClasses(classes => classes.AssignableTo(typeof(IMinimalEndpoint)))
                .UsingRegistrationStrategy(RegistrationStrategy.Append)
                .As<IMinimalEndpoint>()
                .WithLifetime(lifetime));

            return services;
        }

        public static IServiceCollection AddConsumerIdentity(this IServiceCollection services, IConfiguration configuration)
        {
            var bnplOptions = configuration.GetSection(BNPLOptions.SectionName).Get<BNPLOptions>();
            var isrvOptions = configuration.Get<IdentityServerOptions>();

            services.AddAutomaticAccessTokenManagementForHttpClients(isrvOptions, bnplOptions);
            services.AddAuthentication(authenticationOptions =>
            {
                authenticationOptions.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                authenticationOptions.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.Authority = isrvOptions.Identity_Address;
                options.Audience = bnplOptions.ApiName;
                options.MapInboundClaims = false;
                options.ForwardDefaultSelector = Selector.ForwardReferenceToken();
            })
            .AddOAuth2Introspection("Introspection", options =>
            {
                options.EnableCaching = true;
                options.CacheDuration = TimeSpan.FromHours(1);
                options.Authority = isrvOptions.Identity_Address;
                options.ClientId = bnplOptions.ApiName;
                options.ClientSecret = bnplOptions.ApiSecret;
            });

            services.AddAuthorization(options =>
            {
                options.AddPolicy("read", policy => policy.RequireClaim("scope", "bnpl:consumerread"));
                options.AddPolicy("write", policy => policy.RequireClaim("scope", "bnpl:consumerwrite"));
                options.AddPolicy("merchantWrite", policy => policy.RequireClaim("scope", "bnpl:merchantwrite"));

            });

            services.AddSingleton<IAuthorizationMiddlewareResultHandler, CustomAuthorizationMiddlewareResultHandler>();

            return services;
        }

        public static IServiceCollection AddConsumerHttpClients(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            var discoveryOptions = configuration.Get<ServiceDiscoveryOptions>();
            var kuknosOptions = configuration.GetSection(KuknosOptions.SectionName).Get<KuknosOptions>();
            var proxyOptions = configuration.GetSection(ProxyOptions.SectionName).Get<ProxyOptions>();
            var kihooOptions = configuration.GetSection(KiahooshanOptions.SectionName).Get<KiahooshanOptions>();

            services.AddInquiryHttpClient(discoveryOptions);
            services.AddUserHttpClient(discoveryOptions);
            services.AddS3ServiceHttpClient(discoveryOptions);
            services.AddPaymentIpgHttpClient(discoveryOptions);

            services.AddKuknosHttpClient(kuknosOptions, proxyOptions, environment);
            services.AddKiahooshanHttpClient(kihooOptions, proxyOptions, environment);

            return services;
        }

        public static IServiceCollection AddConsumerGrpcClients(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            var discoveryOptions = configuration.Get<ServiceDiscoveryOptions>();

            services.AddGrpcClient<PaymentService.PaymentServiceClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.PaymentGrpcServiceAddress);
            });
            services.AddScoped<IPaymentGrpcClient, PaymentGrpcClient>();

            services.AddGrpcClient<Uploads.UploadsClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.FileManagerGrpcServiceAddress);
            });
            services.AddScoped<IUploadGrpcClient, UploadGrpcClient>();

            services.AddGrpcClient<InquiryService.InquiryServiceClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.InquiryGrpcServiceAddress);
            });
            services.AddScoped<IInquiryGrpcClient, InquiryGrpcClient>();

            services.AddGrpcClient<WalletGrpc.WalletGrpcClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.WalletGrpcAddress);
            });

            services.AddGrpcClient<WalletTypeGrpc.WalletTypeGrpcClient>(o =>
                o.Address = new Uri(discoveryOptions.WalletGrpcAddress));

            services.AddGrpcClient<CoinGrpc.CoinGrpcClient>(o =>
                o.Address = new Uri(discoveryOptions.WalletGrpcAddress));

            services.AddScoped<IWalletGrpcClient, WalletGrpcClient>();

            return services;
        }

        public static IServiceCollection AddConsumerServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IUserContext, UserContext>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<ICreditService, CreditService>();
            services.AddScoped<IWalletService, WalletService>();
            services.AddScoped<IGuaranteeService, GuaranteeService>();

            services.AddScoped<IDummyRequestHandler, DummyRequestHandler>();

            services.AddScoped<Consumer._00_PlanList.IPlanListRequestHandler, Consumer._00_PlanList.PlanListRequestHandler>();
            services.AddScoped<Consumer.OrderList.IOrderListRequestHandler, Consumer.OrderList.OrderListRequestHandler>();
            services.AddScoped<Consumer.OrderDetail.IOrderDetailRequestHandler, Consumer.OrderDetail.OrderDetailRequestHandler>();
            services.AddScoped<Consumer.PaymentList.IPaymentListRequestHandler, Consumer.PaymentList.PaymentListRequestHandler>();
            services.AddScoped<Consumer._07_CreditValidationOTP.ICreditScoreValidationOTPRequestHandler, Consumer._07_CreditValidationOTP.CreditScoreValidationOTPRequestHandler>();
            services.AddScoped<Consumer.CreditScoreValidationVerify.ICreditScoreValidationVerifyRequestHandler, Consumer.CreditScoreValidationVerify.CreditScoreValidationVerifyRequestHandler>();
            services.AddScoped<Consumer._08_CreditValidationInquiry.ICreditScoreInquiryRequestHandler, Consumer._08_CreditValidationInquiry.CreditScoreInquiryRequestHandler>();
            services.AddScoped<ISetPlanRequestHandler, SetPlanRequestHandler>();
            services.AddScoped<IGetOrderRequestHandler, GetOrderRequestHandler>();
            services.AddScoped<IGetProfileRequestHandler, GetProfileRequestHandler>();
            services.AddScoped<IFillProfileRequestHandler, FillProfileRequestHandler>();
            services.AddScoped<IUploadSignatureRequestHandler, UploadSignatureRequestHandler>();
            services.AddScoped<IGetPromissoryPreIssueInfoRequestHandler, GetPromissoryPreIssueInfoRequestHandler>();
            services.AddScoped<IPromissoryIssueRequestHandler, PromissoryIssueRequestHandler>();
            services.AddScoped<IPromissorySigningRequestHandler, PromissorySigningRequestHandler>();
            services.AddScoped<IPromissoryPaymentRequestHandler, PromissoryPaymentRequestHandler>();
            services.AddScoped<IPromissoryPaymentVerifyRequestHandler, PromissoryPaymentVerifyRequestHandler>();
            services.AddScoped<IGetQuoteRequestHandler, GetQuoteRequestHandler>();
            services.AddScoped<IPrePaymentRequestHandler, PrePaymentRequestHandler>();
            services.AddScoped<IPaymentVerifyHandler, PaymentVerifyHandler>();
            services.AddScoped<IOrderCancelConsumerRequestHandler, OrderCancelConsumerRequestHandler>();
            services.AddScoped<IInstallmentPaymentRequestHandler, InstallmentPaymentRequestHandler>();
            services.AddScoped<IInstallmentVerifyHandler, InstallmentVerifyHandler>();
            services.AddScoped<IInstallmentDetailRequestHandler, InstallmentDetailRequestHandler>();
            services.AddScoped<Consumer.GetDownloadPath.IGetDownloadPathRequestHandler, Consumer.GetDownloadPath.GetDownloadPathRequestHandler>();
            services.AddScoped<IInstallmentsListRequestHandler, InstallmentsListRequestHandler>();
            services.AddScoped<IInstallmentGroupPaymentRequestHandler, InstallmentGroupPaymentRequestHandler>();
            services.AddScoped<IInstallmentGroupVerifyHandler, InstallmentGroupVerifyHandler>();
            services.AddScoped<ISignContractRequestHandler, SignContractRequestHandler>();
            services.AddScoped<IGetContractInfoRequestHandler, GetContractInfoRequestHandler>();
            services.AddScoped<IRawContractPdfGenerator, RawContractPdfGenerator>();
            services.AddScoped<IUploadVideoSignatureRequestHandler, UploadVideoSignatureRequestHandler>();
            services.AddScoped<ICheckSignatureRequestHandler, CheckSignatureRequestHandler>();

            services.AddScoped<IOrderStepsFactory, PromissoryOrderStepsFactory>();
            services.AddScoped<IOrderStepsFactory, NoGuaranteeOrderStepsFactory>();
            services.AddScoped<IOrderStepsFactory, SalaryGuaranteeOrderStepsFactory>();
            services.AddScoped<IContractMandatoryStepsProvider, PromissoryContractMandatoryStepsProvider>();
            services.AddScoped<IContractLessStepsProvider, PromissoryContractLessStepsProvider>();
            services.AddScoped<IContractMandatoryStepsProvider, NoGuaranteeContractMandatoryStepsProvider>();
            services.AddScoped<IContractLessStepsProvider, NoGuaranteeContractLessStepsProvider>();
            services.AddScoped<IContractMandatoryStepsProvider, SalaryGuaranteeContractMandatoryStepsProvider>();
            services.AddScoped<IContractLessStepsProvider, SalaryGuaranteeContractLessStepsProvider>();

            services.AddScoped<ISendContinueOnPhoneMessageRequestHandler, SendContinueOnPhoneMessageRequestHandler>();
            services.AddScoped<IGetInProgressOrdersRequestHandler, GetInProgressOrdersRequestHandler>();
            services.AddScoped<IChooseInProgressOrdersRequestHandler, ChooseInProgressOrdersRequestHandler>();

            return services;
        }

        private static IServiceCollection AddConsumerQueueHandlers(this IServiceCollection services, IConfiguration configuration)
        {
            var rabbitOptions = configuration.Get<RabbitMqOptions>();

            services.AddMassTransit(x =>
            {
                x.UsingRabbitMq((context, cfg) =>
                {
                    cfg.Host(new Uri(rabbitOptions.RabbitUri), "/", c =>
                    {
                        c.Username(rabbitOptions.RabbitUsername);
                        c.Password(rabbitOptions.RabbitPassword);
                    });

                    cfg.ConfigureEndpoints(context);
                });
            });

            return services;
        }

        #endregion

        #region Merchant Application

        public static IServiceCollection AddMerchantApplication(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            services.AddConfigurations(configuration);

            services.AddHttpContextAccessor();

            services.AddMerchantMinimalEndpoints();

            services.AddMerchantIdentity(configuration);

            services.AddMerchantHttpClients(configuration, environment);
            services.AddMerchantGrpcClients(configuration, environment);

            services.AddCors(corsOption =>
            {
                corsOption.AddPolicy(ApplicationConstants.MerchantCorsPolicy,
                    builder => builder.AllowAnyOrigin()
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .WithExposedHeaders("X-PayPingRequest-ID")
                        .SetPreflightMaxAge(TimeSpan.FromMinutes(10)));
            });

            services.AddMerchantServices(configuration);
            services.AddCommonServices(configuration);

            services.AddMerchantQueueHandlers(configuration);
            return services;
        }

        public static IServiceCollection AddMerchantMinimalEndpoints(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped, params Assembly[] assemblies)
        {

            var scanAssemblies = assemblies.Any()
                ? assemblies
                : TypeProvider.GetReferencedAssemblies(Assembly.GetCallingAssembly())
                    .Concat(TypeProvider.GetApplicationPartAssemblies(Assembly.GetCallingAssembly()))
                    .Distinct()
                    .ToArray();

            services.Scan(scan => scan
                .FromAssemblies(scanAssemblies)
                .AddClasses(classes => classes.AssignableTo(typeof(IMerchantMinimalEndpoint)))
                .UsingRegistrationStrategy(RegistrationStrategy.Append)
                .As<IMerchantMinimalEndpoint>()
                .WithLifetime(lifetime));

            return services;
        }

        public static IServiceCollection AddMerchantIdentity(this IServiceCollection services, IConfiguration configuration)
        {
            var bnplOptions = configuration.GetSection(BNPLOptions.SectionName).Get<BNPLOptions>();
            var isrvOptions = configuration.Get<IdentityServerOptions>();

            services.AddAutomaticAccessTokenManagementForHttpClients(isrvOptions, bnplOptions);
            services.AddAuthentication(authenticationOptions =>
            {
                authenticationOptions.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                authenticationOptions.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.Authority = isrvOptions.Identity_Address;
                options.Audience = bnplOptions.ApiName;
                options.MapInboundClaims = false;
                options.ForwardDefaultSelector = Selector.ForwardReferenceToken("Introspection");
            })
            .AddOAuth2Introspection("Introspection", options =>
            {
                options.Authority = isrvOptions.Identity_Address;
                options.ClientId = bnplOptions.ApiName;
                options.ClientSecret = bnplOptions.ApiSecret;
                options.EnableCaching = true;
                options.CacheDuration = TimeSpan.FromHours(1);
                //options.Events = new IdentityModel.AspNetCore.OAuth2Introspection.OAuth2IntrospectionEvents()
                //{
                //    OnSendingRequest = async (ctx) =>
                //    {
                //        Console.WriteLine(ctx.ToString());
                //    },
                //    OnAuthenticationFailed = async (ctx) =>
                //    {
                //        Console.WriteLine(ctx.Error);
                //    },
                //    OnTokenValidated = async (ctx) =>
                //    {

                //    },
                //    OnUpdateClientAssertion = async (ctx) =>
                //    {

                //    }

                //};
            });

            services.AddAuthorization(options =>
            {
                options.AddPolicy("read", policy => policy.RequireClaim("scope", "bnpl:merchantread"));
                options.AddPolicy("write", policy => policy.RequireClaim("scope", "bnpl:merchantwrite"));

            });

            services.AddSingleton<IAuthorizationMiddlewareResultHandler, CustomAuthorizationMiddlewareResultHandler>();

            return services;
        }

        public static IServiceCollection AddMerchantHttpClients(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            var discoveryOptions = configuration.Get<ServiceDiscoveryOptions>();
            var kuknosOptions = configuration.GetSection(KuknosOptions.SectionName).Get<KuknosOptions>();
            var proxyOptions = configuration.GetSection(ProxyOptions.SectionName).Get<ProxyOptions>();
            var kihooOptions = configuration.GetSection(KiahooshanOptions.SectionName).Get<KiahooshanOptions>();

            services.AddInquiryHttpClient(discoveryOptions);
            services.AddUserHttpClient(discoveryOptions);
            services.AddS3ServiceHttpClient(discoveryOptions);


            services.AddKuknosHttpClient(kuknosOptions, proxyOptions, environment);
            services.AddKiahooshanHttpClient(kihooOptions, proxyOptions, environment);

            return services;
        }

        public static IServiceCollection AddMerchantGrpcClients(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            var discoveryOptions = configuration.Get<ServiceDiscoveryOptions>();

            services.AddGrpcClient<Uploads.UploadsClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.FileManagerGrpcServiceAddress);
            });

            services.AddScoped<IUploadGrpcClient, UploadGrpcClient>();

            services.AddGrpcClient<InquiryService.InquiryServiceClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.InquiryGrpcServiceAddress);
            });
            services.AddScoped<IInquiryGrpcClient, InquiryGrpcClient>();

            services.AddGrpcClient<WalletGrpc.WalletGrpcClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.WalletGrpcAddress);
            });

            services.AddGrpcClient<WalletTypeGrpc.WalletTypeGrpcClient>(o =>
                o.Address = new Uri(discoveryOptions.WalletGrpcAddress));

            services.AddGrpcClient<CoinGrpc.CoinGrpcClient>(o =>
                o.Address = new Uri(discoveryOptions.WalletGrpcAddress));

            services.AddScoped<IWalletGrpcClient, WalletGrpcClient>();

            services.AddGrpcClient<RefundOperationService.RefundOperationServiceClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.RefundGrpcServiceAddress);
            });
            services.AddGrpcClient<Refund.WebApi.gRPC.UserConfigurationService.UserConfigurationServiceClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.RefundGrpcServiceAddress);
            });
            services.AddScoped<IRefundGrpcClient, RefundGrpcClient>();

            return services;
        }

        public static IServiceCollection AddMerchantServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IUserContext, UserContext>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<ICreditService, CreditService>();
            services.AddScoped<IWalletService, WalletService>();
            services.AddScoped<IGuaranteeService, GuaranteeService>();
            services.AddScoped<IExcelService, ExcelService>();


            services.AddScoped<ICreateOrderRequestHandler, CreateOrderRequestHandler>();
            services.AddScoped<Merchant.PlanList.IPlanListRequestHandler, Merchant.PlanList.PlanListRequestHandler>();
            services.AddScoped<Merchant.PlanGet.IPlanGetRequestHandler, Merchant.PlanGet.PlanGetRequestHandler>();
            services.AddScoped<Merchant.OrderList.IOrderListRequestHandler, Merchant.OrderList.OrderListRequestHandler>();
            services.AddScoped<Merchant.OrderDetail.IOrderDetailRequestHandler, Merchant.OrderDetail.OrderDetailRequestHandler>();
            services.AddScoped<ICreatePlanRequestHandler, CreatePlanRequestHandler>();
            services.AddScoped<Merchant.OrderList.IOrderListRequestHandler, Merchant.OrderList.OrderListRequestHandler>();
            services.AddScoped<IOrderListExcelRequestHandler, OrderListExcelRequestHandler>();
            services.AddScoped<Merchant.OrderDetail.IOrderDetailRequestHandler, Merchant.OrderDetail.OrderDetailRequestHandler>();

            services.AddScoped<ICreateContractRequestHandler, CreateContractRequestHandler>();
            services.AddScoped<ISetCreditRequestHandler, SetCreditRequestHandler>();
            services.AddScoped<ISetWalletCreditAmountRequestHandler, SetWalletCreditAmountRequestHandler>();
            services.AddScoped<ISetMinPrePaymentRateRequestHandler, SetMinPrePaymentRateRequestHandler>();
            services.AddScoped<ISetMonthlyInterestRateRequestHandler, SetMonthlyInterestRateRequestHandler>();
            services.AddScoped<IGetMerchantProfileInfoRequestHandler, GetMerchantProfileInfoRequestHandler>();
            services.AddScoped<ICompleteMerchantProfileInfoRequestHandler, CompleteMerchantProfileInfoRequestHandler>();
            services.AddScoped<IUploadSignatureImageRequestHandler, UploadSignatureImageRequestHandler>();
            services.AddScoped<IAcceptRegulationsRequestHandler, AcceptRegulationsRequestHandler>();
            services.AddScoped<IMerchantSignContractRequestHandler, MerchantSignContractRequestHandler>();
            services.AddScoped<IFileManagerApiClient, FileManagerApiClient>();

            services.AddScoped<IGetContractRequestHandler, GetContractRequestHandler>();
            services.AddScoped<IOwnedCreditIncreaseRequestHandler, OwnedCreditIncreaseRequestHandler>();
            services.AddScoped<ICreditTransactionListRequestHandler, CreditTransactionListRequestHandler>();
            services.AddScoped<IGetOptionsRequestHandler, GetOptionsRequestHandler>();
            services.AddScoped<IUpdatePlanRequestHandler, UpdatePlanRequestHandler>();
            services.AddScoped<ITogglePlanActiveRequestHandler, TogglePlanActiveRequestHandler>();

            services.AddScoped<IConfigurationsUpdateRequestHandler, ConfigurationsUpdateRequestHandler>();
            services.AddScoped<IOrderCancelMerchantRequestHandler, OrderCancelMerchantRequestHandler>();
            services.AddScoped<Merchant.GetDownloadPath.IGetDownloadPathRequestHandler, Merchant.GetDownloadPath.GetDownloadPathRequestHandler>();
            services.AddScoped<IInstallmentsCalendarRequestHandler, InstallmentsCalendarRequestHandler>();
            services.AddScoped<ISetSalaryDeductionPaymentRequestHandler, SetSalaryDeductionPaymentRequestHandler>();
            services.AddScoped<Merchant.OrderRefundInfo.IGetOrderRefundInfoRequestHandler, Merchant.OrderRefundInfo.GetOrderRefundInfoRequestHandler>();


            services.AddScoped<IMerchantCancelFactory, MerchantCancelFactory>();
            services.AddScoped<IMerchantCanceler, MerchantBeforePaymentCanceler>();
            services.AddScoped<IMerchantCanceler, MerchantAfterPaymentCanceler>();


            return services;
        }

        private static IServiceCollection AddMerchantQueueHandlers(this IServiceCollection services, IConfiguration configuration)
        {
            var rabbitOptions = configuration.Get<RabbitMqOptions>();


            services.AddMassTransit(x =>
            {
                x.AddConsumer<RefundSucceededMessageConsumer>();
                x.AddConsumer<RefundFailedMessageConsumer>();

                x.UsingRabbitMq((context, cfg) =>
                {
                    cfg.Host(new Uri(rabbitOptions.RabbitUri), "/", c =>
                    {
                        c.Username(rabbitOptions.RabbitUsername);
                        c.Password(rabbitOptions.RabbitPassword);
                    });

                    cfg.ReceiveEndpoint(ApplicationConstants.RefundFanoutQueueNameForBNPLMerchant, e =>
                    {
                        // since this is not a high throuput consumer, we limit parallel(not acked) message to 5 at a time, for better performance
                        e.PrefetchCount = 10;
                        e.UseMessageRetry(r =>
                        {
                            //r.Handle<SagaRequestFailedException>(ex => ex.StopRetrying == false); or r.Ignore<InvalidOperationException>();
                            r.Incremental(5, TimeSpan.FromSeconds(15), TimeSpan.FromMinutes(2));
                        });
                        e.ConfigureConsumer<RefundSucceededMessageConsumer>(context);
                        e.ConfigureConsumer<RefundFailedMessageConsumer>(context);


                    });



                    cfg.ConfigureEndpoints(context);
                });
            });

            return services;
        }

        #endregion

        #region Admin

        public static IServiceCollection AddAdminApplication(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            services.AddConfigurations(configuration);

            services.AddHttpContextAccessor();

            services.AddAdminMinimalEndpoints();

            services.AddAdminIdentity(configuration);

            services.AddAdminHttpClients(configuration, environment);
            services.AddAdminGrpcClients(configuration, environment);

            services.AddCors(corsOption =>
            {
                corsOption.AddPolicy(ApplicationConstants.AdminCorsPolicy,
                    builder => builder.AllowAnyOrigin()
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .WithExposedHeaders("X-PayPingRequest-ID")
                        .SetPreflightMaxAge(TimeSpan.FromMinutes(10)));
            });

            services.AddAdminJobs(configuration);
            services.AddAdminServices(configuration);
            services.AddCommonServices(configuration);

            services.AddAdminQueueHandlers(configuration);
            return services;
        }
        public static IServiceCollection AddAdminMinimalEndpoints(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped, params Assembly[] assemblies)
        {

            var scanAssemblies = assemblies.Any()
                ? assemblies
                : TypeProvider.GetReferencedAssemblies(Assembly.GetCallingAssembly())
                    .Concat(TypeProvider.GetApplicationPartAssemblies(Assembly.GetCallingAssembly()))
                    .Distinct()
                    .ToArray();

            services.Scan(scan => scan
                .FromAssemblies(scanAssemblies)
                .AddClasses(classes => classes.AssignableTo(typeof(IAdminMinimalEndpoint)))
                .UsingRegistrationStrategy(RegistrationStrategy.Append)
                .As<IAdminMinimalEndpoint>()
                .WithLifetime(lifetime));

            return services;
        }

        public static IServiceCollection AddAdminIdentity(this IServiceCollection services, IConfiguration configuration)
        {
            var bnplOptions = configuration.GetSection(BNPLOptions.SectionName).Get<BNPLOptions>();
            var isrvOptions = configuration.Get<IdentityServerOptions>();

            services.AddAutomaticAccessTokenManagementForHttpClients(isrvOptions, bnplOptions);
            services.AddAuthentication(authenticationOptions =>
            {
                authenticationOptions.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                authenticationOptions.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.Authority = isrvOptions.Identity_Address;
                options.Audience = bnplOptions.ApiName;
                options.MapInboundClaims = false;
                options.ForwardDefaultSelector = Selector.ForwardReferenceToken("Introspection");
            })
            .AddOAuth2Introspection("Introspection", options =>
            {
                options.Authority = isrvOptions.Identity_Address;
                options.ClientId = bnplOptions.ApiName;
                options.ClientSecret = bnplOptions.ApiSecret;
                options.EnableCaching = true;
                options.CacheDuration = TimeSpan.FromMinutes(30);
                //options.Events = new IdentityModel.AspNetCore.OAuth2Introspection.OAuth2IntrospectionEvents()
                //{
                //    OnSendingRequest = async (ctx) =>
                //    {
                //        Console.WriteLine(ctx.ToString());
                //    },
                //    OnAuthenticationFailed = async (ctx) =>
                //    {
                //        Console.WriteLine(ctx.Error);
                //    },
                //    OnTokenValidated = async (ctx) =>
                //    {

                //    },
                //    OnUpdateClientAssertion = async (ctx) =>
                //    {

                //    }

                //};
            });

            services.AddAuthorization(options =>
            {
                options.AddPolicy("admin", policy => policy.RequireClaim("scope", "bnpl:admin"));
            });

            services.AddSingleton<IAuthorizationMiddlewareResultHandler, CustomAuthorizationMiddlewareResultHandler>();

            return services;
        }

        public static IServiceCollection AddAdminHttpClients(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            var discoveryOptions = configuration.Get<ServiceDiscoveryOptions>();
            var kuknosOptions = configuration.GetSection(KuknosOptions.SectionName).Get<KuknosOptions>();
            var proxyOptions = configuration.GetSection(ProxyOptions.SectionName).Get<ProxyOptions>();
            var kihooOptions = configuration.GetSection(KiahooshanOptions.SectionName).Get<KiahooshanOptions>();

            services.AddInquiryHttpClient(discoveryOptions);
            services.AddUserHttpClient(discoveryOptions);
            services.AddS3ServiceHttpClient(discoveryOptions);

            services.AddKuknosHttpClient(kuknosOptions, proxyOptions, environment);
            services.AddKiahooshanHttpClient(kihooOptions, proxyOptions, environment);

            return services;
        }

        public static IServiceCollection AddAdminGrpcClients(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            var discoveryOptions = configuration.Get<ServiceDiscoveryOptions>();

            services.AddGrpcClient<PaymentService.PaymentServiceClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.PaymentGrpcServiceAddress);
            });

            services.AddGrpcClient<Uploads.UploadsClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.FileManagerGrpcServiceAddress);
            });
            services.AddGrpcClient<WalletGrpc.WalletGrpcClient>(o =>
            {
                o.Address = new Uri(discoveryOptions.WalletGrpcAddress);
            });

            services.AddGrpcClient<WalletTypeGrpc.WalletTypeGrpcClient>(o =>
                o.Address = new Uri(discoveryOptions.WalletGrpcAddress));

            services.AddGrpcClient<CoinGrpc.CoinGrpcClient>(o =>
                o.Address = new Uri(discoveryOptions.WalletGrpcAddress));

            services.AddScoped<IWalletGrpcClient, WalletGrpcClient>();

            services.AddScoped<IPaymentGrpcClient, PaymentGrpcClient>();
            services.AddScoped<IUploadGrpcClient, UploadGrpcClient>();

            return services;
        }

        public static IServiceCollection AddAdminJobs(this IServiceCollection services, IConfiguration configuration)
        {
            var bjOptions = configuration.GetSection(BackgroundJobOptions.SectionName).Get<BackgroundJobOptions>();

            services.AddQuartz(q =>
            {
                q.AddJobAndUtcTrigger<ExpiredOrderProcessJob>(configuration, bjOptions.ExpiredOrderJobGroup);
                q.AddJob<SuspeciousCreditTransactionJob>(configuration, bjOptions.ExpiredOrderJobGroup);
                q.AddJobAndUtcTrigger<OrderExternalEventJob>(configuration, bjOptions.ExpiredOrderJobGroup);

                q.AddJobAndUtcTrigger<InstallmentDueJob>(configuration, bjOptions.InstallmentJobGroup);
                q.AddJobAndUtcTrigger<InstallmentDelayedJob>(configuration, bjOptions.InstallmentJobGroup);
                q.AddJobAndUtcTrigger<InstallmentDefaultedJob>(configuration, bjOptions.InstallmentJobGroup);

            });

            services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

            return services;
        }

        public static IServiceCollection AddAdminServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IGeneratePdfFile, GeneratePdfFile>();
            services.AddScoped<IUserContext, UserContext>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<ICreditService, CreditService>();
            services.AddScoped<IWalletService, WalletService>();
            services.AddScoped<IGuaranteeService, GuaranteeService>();

            services.AddScoped<ICreditSettingGetRequestHandler, CreditSettingGetRequestHandler>();
            services.AddScoped<ICreditSettingUpdateRequestHandler, CreditSettingUpdateRequestHandler>();
            services.AddScoped<Admin.OrdersList.IOrderListRequestHandler, Admin.OrdersList.OrderListRequestHandler>();
            services.AddScoped<Admin.OrderDetail.IOrderDetailRequestHandler, Admin.OrderDetail.OrderDetailRequestHandler>();
            services.AddScoped<Admin.CreditTransactionList.ICreditTransactionListRequestHandler, Admin.CreditTransactionList.CreditTransactionListRequestHandler>();
            services.AddScoped<IContractListRequestHandler, ContractListRequestHandler>();
            services.AddScoped<IOrderCancelAdminRequestHandler, OrderCancelAdminRequestHandler>();
            services.AddScoped<Admin.GetDownloadPath.IGetDownloadPathRequestHandler, Admin.GetDownloadPath.GetDownloadPathRequestHandler>();

            services.AddScoped<IGuarantorCreateRequestHandler, GuarantorCreateRequestHandler>();
            services.AddScoped<IGuarantorUpdateRequestHandler, GuarantorUpdateRequestHandler>();
            services.AddScoped<IGuarantorDeleteRequestHandler, GuarantorDeleteRequestHandler>();

            services.AddScoped<IAdminContractUpdateRequestHandler, AdminContractUpdateRequestHandler>();
            services.AddScoped<IMerchantContractListRequestHandler, MerchantContractListRequestHandler>();
            services.AddScoped<IGetMerchantContractRequestHandler, GetMerchantContractRequestHandler>();
            services.AddScoped<IOrderTimeLineRequestHandler, OrderTimeLineRequestHandler>();
            services.AddScoped<IAdminMerchantInfoGetRequestHandler, AdminMerchantInfoGetRequestHandler>();


            return services;
        }
        private static IServiceCollection AddAdminQueueHandlers(this IServiceCollection services, IConfiguration configuration)
        {
            var rabbitOptions = configuration.Get<RabbitMqOptions>();

            services.AddMassTransit(x =>
            {
                x.UsingRabbitMq((context, cfg) =>
                {
                    cfg.Host(new Uri(rabbitOptions.RabbitUri), "/", c =>
                    {
                        c.Username(rabbitOptions.RabbitUsername);
                        c.Password(rabbitOptions.RabbitPassword);
                    });

                    cfg.ConfigureEndpoints(context);
                });
            });

            return services;
        }


        #endregion

        #region Infrustructure

        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
        {
            EventManager.AcknowledgeAgplUsageDisableWarningMessage();

            services.AddMemoryCache();
            services.AddRedis(configuration);
            services.AddCustomProblemDetails();
            services.AddCustomVersioning();
            services.AddCustomSwagger();
            services.AddCustomOpentTelemetry(configuration, webHostEnvironment);
            services.AddCustomValidators(Assembly.GetExecutingAssembly());
            services.AddCustomHealthCheck(configuration);
            services.AddPostgresDbContext();

            services.AddInternalServices(configuration);
            services.AddScoped<INotifyService, NotifyService>();

            return services;
        }

        public static IServiceCollection AddCustomOpentTelemetry(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            var otel = services.AddOpenTelemetry();

            otel.ConfigureResource(resource => resource.AddService(serviceName: environment.ApplicationName));

            otel.WithMetrics(config =>
            {
                config
                    .AddRuntimeInstrumentation()
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddMeter("Microsoft.AspNetCore.Hosting", "Microsoft.AspNetCore.Server.Kestrel", "System.Net.Http", "System.Net.NameResolution")
                    .AddPrometheusExporter(pc => pc.ScrapeEndpointPath = "/metrics");

                if (environment.IsDevelopment())
                {
                    config.AddConsoleExporter();
                }

            });

            return services;
        }

        public static IServiceCollection AddCustomHealthCheck(this IServiceCollection services, IConfiguration configuration)
        {
            var bnplOptions = configuration.GetSection(BNPLOptions.SectionName).Get<BNPLOptions>();
            var redisOptions = configuration.Get<RedisOptions>();
            var rabbitOptions = configuration.Get<RabbitMqOptions>();
            var postgresOptions = configuration.GetSection(PostgresOptions.SectionName).Get<PostgresOptions>();
            var elasticOptions = configuration.Get<ElasticOptions>();

            services.AddHealthChecks()
                .AddCheck(elasticOptions.ELASTIC_APM_SERVICE_NAME, () => HealthCheckResult.Healthy("The service is up and running."), tags: ["liveness"])
                //.AddRabbitMQ(rabbitConnectionString: rabbitOptions.RabbitUri, tags: ["readiness"])
                .AddRedis(redisOptions.ConnectionString, tags: ["readiness"])
                .AddNpgSql(postgresOptions.ConnectionString, tags: ["readiness"]);
            //.AddElasticsearch(elasticOptions.ELASTIC_HOST, tags: ["readiness"]);

            //services.AddHealthChecksUI(setup =>
            //{
            //    setup.SetEvaluationTimeInSeconds(10); //time in seconds between check
            //    setup.MaximumHistoryEntriesPerEndpoint(60); //maximum history of checks
            //    setup.SetApiMaxActiveRequests(1); //api requests concurrency
            //    setup.AddHealthCheckEndpoint("Liveness", "/health/liveness");
            //    setup.AddHealthCheckEndpoint("Readiness", "/health/readiness");
            //}).AddInMemoryStorage();

            return services;
        }

        public static IServiceCollection AddRedis(this IServiceCollection services, IConfiguration configuration)
        {
            var redisOptions = configuration.Get<RedisOptions>();

            var mux = ConnectionMultiplexer.Connect(redisOptions.ConnectionString);

            services.AddSingleton<IConnectionMultiplexer>(mux);
            services.AddSingleton<ConnectionMultiplexer>(p => p.GetService<IConnectionMultiplexer>() as ConnectionMultiplexer);

            services.AddStackExchangeRedisCache(options =>
            {
                options.ConnectionMultiplexerFactory = () => Task.FromResult(mux as IConnectionMultiplexer);
                if (!string.IsNullOrEmpty(redisOptions.RedisInstanceName))
                    options.InstanceName = redisOptions.RedisInstanceName;
            });

            var multiplexers = new List<RedLockMultiplexer> { mux };

            services.AddSingleton<IDistributedLockFactory, RedLockFactory>(x => RedLockFactory.Create(multiplexers));

            return services;
        }

        private static IServiceCollection AddCustomProblemDetails(this IServiceCollection services)
        {
            services.AddExceptionHandler<ApplicationExceptionHandler>();
            services.AddExceptionHandler<NotHandledExceptionHandler>();
            return services;
        }

        private static IServiceCollection AddCustomVersioning(this IServiceCollection services, Action<ApiVersioningOptions> configurator = null)
        {
            services.AddEndpointsApiExplorer();

            // Support versioning in minimal apis with (Asp.Versioning.Http) dll
            services
                .AddApiVersioning(options =>
                {
                    // Add the headers "api-supported-versions" and "api-deprecated-versions"
                    // This is better for discoverability
                    options.ReportApiVersions = true;

                    // AssumeDefaultVersionWhenUnspecified should only be enabled when supporting legacy services that did not previously
                    // support API versioning. Forcing existing clients to specify an explicit API version for an
                    // existing service introduces a breaking change. Conceptually, clients in this situation are
                    // bound to some API version of a service, but they don't know what it is and never explicit request it.
                    options.AssumeDefaultVersionWhenUnspecified = true;
                    options.DefaultApiVersion = new ApiVersion(1, 0);

                    // Defines how an API version is read from the current HTTP request
                    options.ApiVersionReader = ApiVersionReader.Combine(
                        new HeaderApiVersionReader("api-version"),
                        new QueryStringApiVersionReader(),
                        new UrlSegmentApiVersionReader()
                    );

                    configurator?.Invoke(options);
                })
                .AddApiExplorer(options =>
                {
                    // add the versioned api explorer, which also adds IApiVersionDescriptionProvider service
                    // note: the specified format code will format the version as "'v'major[.minor][-status]"
                    options.GroupNameFormat = "'v'VVV";

                    // note: this option is only necessary when versioning by url segment. the SubstitutionFormat
                    // can also be used to control the format of the API version in route templates
                    options.SubstituteApiVersionInUrl = true;
                });


            return services;
        }

        private static IServiceCollection AddCustomSwagger(this IServiceCollection services)
        {
            services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();
            services.AddSwaggerGen(options =>
            {
                // https://github.com/unchase/Unchase.Swashbuckle.AspNetCore.Extensions#fix-enums
                options.AddEnumsWithValuesFixFilters(option =>
                {
                    option.IncludeXEnumRemarks = true;
                    option.IncludeDescriptions = true;
                    option.DescriptionSource = DescriptionSources.DescriptionAttributes;

                });

                options.SchemaFilter<OpenApiIgnoreEnumSchemaFilter>();
                options.DocumentFilter<OpenApiIgnoreEnumSchemaFilter>();
                options.SchemaFilter<OpenApiIgnoreSchemaFilter>();
                options.DocumentFilter<OpenApiIgnoreSchemaFilter>();
                options.OperationFilter<OpenApiDefaultValues>();

                var bearerScheme = new OpenApiSecurityScheme()
                {
                    Type = SecuritySchemeType.Http,
                    Name = JwtBearerDefaults.AuthenticationScheme,
                    Scheme = JwtBearerDefaults.AuthenticationScheme,
                    Reference = new() { Type = ReferenceType.SecurityScheme, Id = JwtBearerDefaults.AuthenticationScheme }
                };
                options.AddSecurityDefinition(JwtBearerDefaults.AuthenticationScheme, bearerScheme);
                options.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    { bearerScheme, Array.Empty<string>() }
                 });

                options.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());
                options.EnableAnnotations();


            });

            return services;
        }

        private static IServiceCollection AddCustomValidators(this IServiceCollection services, Assembly assembly, ServiceLifetime serviceLifetime = ServiceLifetime.Scoped)
        {
            // https://docs.fluentvalidation.net/en/latest/di.html
            // I have some problem with registering IQuery validators with this
            // services.AddValidatorsFromAssembly(assembly);
            services.Scan(
                scan =>
                    scan.FromAssemblies(assembly)
                        .AddClasses(classes => classes.AssignableTo(typeof(IValidator<>)))
                        .UsingRegistrationStrategy(RegistrationStrategy.Skip)
                        .AsImplementedInterfaces()
                        .WithLifetime(serviceLifetime)
            );

            ValidatorOptions.Global.DisplayNameResolver = (type, member, expression) =>
            {
                if (member != null)
                {
                    return PropertyNameDictionary.ResourceManager.GetString(member.Name);
                }
                return null;
            };
            ValidatorOptions.Global.DefaultClassLevelCascadeMode = CascadeMode.Stop;
            ValidatorOptions.Global.DefaultRuleLevelCascadeMode = CascadeMode.Stop;
            ValidatorOptions.Global.LanguageManager.Enabled = true;

            services.AddFluentValidationRulesToSwagger();

            return services;
        }





        private static IServiceCollection AddInternalServices(this IServiceCollection services, IConfiguration configuration)
        {
            var discoveryOptions = configuration.Get<ServiceDiscoveryOptions>();

            services.AddIntegrations(
                new Uri(discoveryOptions.IntegrationServiceAddress),
                httpClientBuilder => httpClientBuilder.AddClientAccessTokenHandler(ApplicationConstants.AutomaticAccessTokenManagementName));

            return services;
        }

        private static IServiceCollection AddCommonServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IGuaranteeFactory, NoneGuaranteeFactory>();
            services.AddScoped<IGuaranteeFactory, SalaryGuaranteeFactory>();
            services.AddScoped<IGuaranteeFactory, KuknosGuaranteeFactory>();
            services.AddScoped<IGuaranteeFactory, KiahooshanGuaranteeFactory>();

            services.AddScoped<IEventLogService, EventLogService>();


            services.AddScoped<INoneGuaranteeProvider, NoneGuaranteeProvider>();
            services.AddScoped<ISalaryGuaranteeProvider, SalaryGuaranteeProvider>();
            services.AddScoped<IPromissoryGuaranteeProvider, KuknosPromissoryProvider>();
            services.AddScoped<IPromissoryGuaranteeProvider, KiahooshanPromissoryProvider>();

            services.AddScopedChain<IKuknosPromissoryService>()
                .Use<IssuePromissoryHandler>()
                .Use<PostIssuePromissoryHandler>()
                .Use<SignDocumentWithSignerPdfHandler>()
                .Use<SignedHandler>()
                .Use<PayHandler>()
                .Use<FinalizeHandler>()
                .Use<DoneHandler>()
                .Build();

            services.AddScopedChain<IKuknosPromissorySettlementService>()
                .Use<IssuePromissorySettlementHandler>()
                .Use<SignSettlementHandler>()
                .Use<FinalizeSettlementHandler>()
                .Build();

            services.AddScopedChain<IKiahooshanPromissoryService>()
                .Use<KiaIssuePromissoryHandler>()
                .Use<KiaSignRawDocumentHandler>()
                .Use<KiaFinalizeHandler>()
                .Build();

            services.AddScopedChain<IKiahooshanPromissorySettlementService>()
                .Use<KiaIssueSettlementHandler>()
                .Use<KiaFinalizeSettlementHandler>()
                .Build();


            services.AddScoped<IDigitalSignProvider, KuknosDigitalSignProvider>();
            services.AddScoped<IDigitalSignProvider, KiahooshanDigitalSignProvider>();

            services.AddScoped<IPdfSignerService, KuknosPdfSignerService>();
            services.AddScoped<IPdfSignerService, KiahooshanPdfSignerService>();

            services.AddScopedChain<IKuknosDigitalSignService>()
                .Use<AccountHandler>()
                .Use<CreateCertificateHandler>()
                .Use<GetCertificateHandler>()
                .Build();

            services.AddScopedChain<IKiahooshanDigitalSignService>()
                .Use<KiaVideoVerifyHandler>()
                .Use<KiaVideoResultHandler>()
                .Use<KiaSignatureHandler>()
                .Build();

            return services;
        }
        #endregion

    }
}
