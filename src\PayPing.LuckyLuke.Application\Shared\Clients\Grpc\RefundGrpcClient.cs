﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using Microsoft.Extensions.Logging;
using Grpc.Core;
using PayPing.Refund.WebApi.gRPC;

namespace PayPing.LuckyLuke.Application.Shared.Clients.Grpc
{
    public class RefundGrpcClient : IRefundGrpcClient
    {
        private readonly RefundOperationService.RefundOperationServiceClient _client;
        private readonly UserConfigurationService.UserConfigurationServiceClient _userconfigurationClient;
        private readonly ILogger<RefundGrpcClient> _logger;

        public RefundGrpcClient(RefundOperationService.RefundOperationServiceClient client, UserConfigurationService.UserConfigurationServiceClient userconfigurationClient, ILogger<RefundGrpcClient> logger)
        {
            _client = client;
            _userconfigurationClient = userconfigurationClient;
            _logger = logger;
        }

        public async ValueTask<RefundGrpcResponseBase> CreateRefundAsync(CreateRefundRequest request, CancellationToken cancellationToken)
        {
            try
            {
                CreateRefundResponse response = await _client.CreateRefundAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } }, null, cancellationToken);
                switch (response.ResultCase)
                {
                    case CreateRefundResponse.ResultOneofCase.Success:
                        return new RefundGrpcCreateResponseSuccess(
                            true,
                            response.Success.Code,
                            response.Success.IsPendingRecharge);
                    case CreateRefundResponse.ResultOneofCase.Failure:
                        _logger.LogWarning("grpc CreateRefundAsync ResultOneofCase Failure. {Title}, {Detail}", response.Failure.Title, response.Failure.Detail);
                        return new RefundGrpcResponseError(
                            false,
                            response.Failure.Title,
                            response.Failure.Detail,
                            response.Failure.ErrorCode,
                            response.Failure.Instance,
                            response.Failure.PaypingTraceId,
                            response.Failure.StatusCode,
                            response.Failure.Errors?.ToArray());
                    case CreateRefundResponse.ResultOneofCase.None:
                    default:
                        _logger.LogWarning("grpc CreateRefundAsync ResultOneofCase None.");
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc CreateRefundAsync exception occured");
                return null;
            }
        }

        public async ValueTask<RefundGrpcResponseBase> GetRefundDetailsAsync(GetRefundDetailsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                GetRefundDetailsResponse response = await _client.GetRefundDetailsAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } }, null, cancellationToken);
                switch (response.ResultCase)
                {
                    case GetRefundDetailsResponse.ResultOneofCase.Success:
                        var s = response.Success;
                        return new RefundGrpcGetDetailsResponseSuccess(
                            true,
                           s.BankTrackingCode,
                           s.CancellationDateTime.ToDateTimeOffset(),
                           s.Code,
                           s.CreationDateTime.ToDateTimeOffset(),
                           s.FailureDateTime.ToDateTimeOffset(),
                           s.LocalizedState,
                           s.LocalizedWagePayer,
                           s.MaskedPAN,
                           s.PaymentCode,
                           s.PreparationDateTime.ToDateTimeOffset(),
                           s.ProcessStartDateTime.ToDateTimeOffset(),
                           s.RefundAmount,
                           s.RefundWage,
                           s.RequestedAmount,
                           s.State,
                           s.SuccessDateTime.ToDateTimeOffset(),
                           s.TransferReference,
                           s.WagePayer);
                    case GetRefundDetailsResponse.ResultOneofCase.Failure:
                        return new RefundGrpcResponseError(
                            false,
                            response.Failure.Title,
                            response.Failure.Detail,
                            response.Failure.ErrorCode,
                            response.Failure.Instance,
                            response.Failure.PaypingTraceId,
                            response.Failure.StatusCode,
                            response.Failure.Errors?.ToArray());
                    case GetRefundDetailsResponse.ResultOneofCase.None:
                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc GetRefundDetailsAsync failed");
                return null;
            }
        }

        public async ValueTask<RefundGrpcResponseBase> GetUserConfigurationAsync(GetUserConfigurationRequest request, CancellationToken cancellationToken)
        {
            try
            {
                GetUserConfigurationResponse response = await _userconfigurationClient.GetUserConfigurationAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } }, null, cancellationToken);
                switch (response.ResultCase)
                {
                    case GetUserConfigurationResponse.ResultOneofCase.Success:
                        var s = response.Success;
                        return new RefundGrpcUserConfigurationResponseSuccess(
                            true,
                           s.AutoInitializePendingRefunds,
                           s.CreationDateTime.ToDateTimeOffset(),
                           s.MaxPercentageWage,
                           s.MinPercentageWage,
                           s.WageValue,
                           s.WageType,
                           s.UserInitializationType);
                    case GetUserConfigurationResponse.ResultOneofCase.Failure:
                        _logger.LogWarning("grpc GetUserConfigurationAsync ResultOneofCase Failure. {Title}, {Detail}", response.Failure.Title, response.Failure.Detail);
                        return new RefundGrpcResponseError(
                            false,
                            response.Failure.Title,
                            response.Failure.Detail,
                            response.Failure.ErrorCode,
                            response.Failure.Instance,
                            response.Failure.PaypingTraceId,
                            response.Failure.StatusCode,
                            response.Failure.Errors?.ToArray());
                    case GetUserConfigurationResponse.ResultOneofCase.None:
                    default:
                        _logger.LogWarning("grpc GetUserConfigurationAsync ResultOneofCase None.");

                        return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc GetUserConfigurationAsync exception occured");
                return null;
            }
        }
    }                           
}
