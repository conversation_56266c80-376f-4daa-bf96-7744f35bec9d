﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._004_SetMinPrePaymentRate
{
    public record SetMinPrePaymentRateRequest(int contractId, decimal minPrePaymentRate);

    public record SetMinPrePaymentRateResponse(int contractId);

    public class SetMinPrePaymentRateEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.SetMinPrePaymentRate,
                async (SetMinPrePaymentRateRequest request, ISetMinPrePaymentRateRequestHandler handler, IValidator<SetMinPrePaymentRateRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("SetMinPrePaymentRate")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<SetMinPrePaymentRateResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Set Min PrePayment Rate")
            .WithDescription("Set Min PrePayment Rate")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class SetMinPrePaymentRateRequestHandler : ISetMinPrePaymentRateRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public SetMinPrePaymentRateRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<SetMinPrePaymentRateResponse> HandleAsync(SetMinPrePaymentRateRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var contract = await _dbContext.Contracts
               .Where(c => c.Id == request.contractId)
               .Include(p => p.Credits)
               .FirstOrDefaultAsync();

            if (contract == null)
            {
                throw new ArgumentException("قرارداد فروش اقساطی فعال یافت نشد");
            }

            var merchantInfo = await _dbContext.MerchantInfos
                .Where(p => p.MerchantUserId == userId)
                .FirstOrDefaultAsync();

            if (merchantInfo == null)
            {
                throw new ArgumentException("اطلاعات مرچنت یافت نشد");
            }

            merchantInfo.MinPrePaymentRate = request.minPrePaymentRate;

            contract.ActivationStatus = ActivationStatus.SetMinPrePaymentRate;

            await _dbContext.SaveChangesAsync();

            return new SetMinPrePaymentRateResponse(contract.Id);
        }

    }
    public interface ISetMinPrePaymentRateRequestHandler
    {
        ValueTask<SetMinPrePaymentRateResponse> HandleAsync(SetMinPrePaymentRateRequest request, CancellationToken cancellationToken);
    }

    public class SetMinPrePaymentRateValidator : AbstractValidator<SetMinPrePaymentRateRequest>
    {
        public SetMinPrePaymentRateValidator()
        {
            RuleFor(c => c.contractId)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.contract_id_is_required);
            RuleFor(x => x.minPrePaymentRate).GreaterThanOrEqualTo(0).WithResourceError(() => ValidatorDictionary.min_prePayment_rate_greater);
        }
    }

}
