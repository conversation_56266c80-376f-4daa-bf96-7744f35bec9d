﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.Tools.SdkBase.Types;

namespace PayPing.LuckyLuke.Application.Shared.Services.PdfSigner;

public class KiahooshanPdfSignerService(
    IEnumerable<IDigitalSignProvider> digitalSignProviders,
    IKiahooshanApiHttpClient kihooApi,
    IUploadGrpcClient uploadGrpcClient)
    : IPdfSignerService
{
    public DigitalSignType DigitalSignType => DigitalSignType.KiahooshanVersion1;

    public async Task<ServiceResult<string>> SignPdfDocumentAsync(int userId,
        Stream rawContract,
        Guid orderTrackingCode,
        CancellationToken cancellationToken)
    {
        var dsp = ChooseProvider(DigitalSignType);
        var sigData = await dsp.GetPdfSignatureDataAsync(userId);

        if (string.IsNullOrWhiteSpace(sigData.certificate))
            throw new CustomValidationException(orderTrackingCode.ToString(), "سفارش متعلق به کاربر نیست",
                string.Empty);

        var signedDocument = await kihooApi.SignDoc(sigData.certificate,
            rawContract,
            "contract.pdf",
            orderTrackingCode,
            cancellationToken);

        if (signedDocument == null)
            throw new CustomValidationException(orderTrackingCode.ToString(), "خطا در امضا قرارداد", string.Empty);

        var validName = Path.ChangeExtension(Path.GetRandomFileName(), ".pdf");

        var uploadResult = await uploadGrpcClient.UploadUserDocumentAsync(await signedDocument.ToByteArrayAsync(),
            Path.GetExtension(validName).TrimStart('.'),
            userId,
            cancellationToken);

        if (!uploadResult.Succeeded)
            throw new CustomValidationException(orderTrackingCode.ToString(), "خطا در ذخیره فایل قرارداد",
                string.Empty);

        return uploadResult;
    }

    private IDigitalSignProvider ChooseProvider(DigitalSignType digitalSignType)
    {
        return digitalSignProviders.First(f => f.DigitalSignType == digitalSignType);
    }
}