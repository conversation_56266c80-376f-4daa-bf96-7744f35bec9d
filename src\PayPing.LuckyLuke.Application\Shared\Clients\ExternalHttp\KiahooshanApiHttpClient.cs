﻿using Ardalis.GuardClauses;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using System.Net.Http.Json;
using Microsoft.Extensions.Options;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using System.Text.Json;
using Microsoft.Extensions.Caching.Memory;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using Microsoft.Extensions.Logging;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using Elastic.Apm.Api;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.Tools.SdkBase.Types;

namespace PayPing.LuckyLuke.Application.Shared.Clients.ExternalHttp
{
    public class KiahooshanApiHttpClient : IKiahooshanApiHttpClient
    {
        private readonly HttpClient _httpClient;
        private readonly KiahooshanOptions _options;
        private readonly IMemoryCache _memoryCache;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<KiahooshanApiHttpClient> _logger;

        public KiahooshanApiHttpClient(HttpClient httpClient, IOptions<KiahooshanOptions> options, IMemoryCache memoryCache, IWebHostEnvironment environment,
            ILogger<KiahooshanApiHttpClient> logger)
        {
            _httpClient = Guard.Against.Null(httpClient, nameof(httpClient));
            _options = options.Value;
            _memoryCache = memoryCache;
            _environment = environment;
            _logger = logger;
        }

        public async ValueTask<string> GetTokenAsync(Guid? orderTrackingCode, CancellationToken cancellationToken = default)
        {
            if (!_memoryCache.TryGetValue(ApplicationConstants.KiahooshanTokenCacheKey, out string token))
            {
                var cacheEntryOptions = new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24) };

                KiahooshanApiLoginResponse tokenResult = await LoginAsync(orderTrackingCode, cancellationToken);

                if (tokenResult == null || string.IsNullOrEmpty(tokenResult.accessToken))
                {
                    throw new PromissoryProviderException(orderTrackingCode?.ToString(), $"kiahooshan login result is null", false);
                }

                _memoryCache.Set(ApplicationConstants.KiahooshanTokenCacheKey, tokenResult.accessToken, cacheEntryOptions);

                return tokenResult.accessToken;
            }

            return token;
        }

        private async ValueTask<KiahooshanApiLoginResponse> LoginAsync(Guid? orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}api/ums/token");

            string orgName = _environment.IsDevelopment() ? "kiahooshan" : "payping";
            string orgNationalCode = _environment.IsDevelopment() ? "14009966114" : "14005304514";
            var contentjson = JsonSerializer.Serialize(new KiahooshanApiLoginRequest(_options.Username, _options.Password, orgName, orgNationalCode));

            request.Content = new StringContent(contentjson, null, "application/json");

            var response = await _httpClient.SendAsync(request, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                throw new PromissoryProviderException(orderTrackingCode?.ToString(), message, false);
            }

            return await response.Content.ReadFromJsonAsync<KiahooshanApiLoginResponse>(cancellationToken);
        }

        public async ValueTask<KiahooshanApiVideoVerifyResponse> VerifyVideoWithNationalId(
            string birthDate,
            string nationalCode,
            string randText,
            byte[] selfieVideo,
            string selfieVideoName,
            Guid? orderTrackingCode,
            CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}api/vvs/video/verify-idcard-img");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            using MemoryStream stream = new MemoryStream(selfieVideo);

            var vc = new StreamContent(stream);
            vc.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("video/mp4");

            var content = new MultipartFormDataContent
            {
                { new StringContent(nationalCode), "nationalCode" },
                { new StringContent(birthDate.Replace("/", "")), "birthDate" }, //"yyyymmdd"
                { new StringContent(randText), "randAction" },
                { new StringContent("85"), "matchingTHR" },
                { new StringContent("70"), "livenessTHR" },
                { vc, "selfieVideo", selfieVideoName },
            };
            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KiahooshanApiDigitalSignatureErrorResponse err)) 
                {
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), err.errors, true);
                }
                else
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KiahooshanApiVideoVerifyResponse>(cancellationToken);

            if (result == null || string.IsNullOrWhiteSpace(result.uniqueId))
            {
                throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), $"kiahooshan VerifyVideoWithNationalId not succeeded with message: {result?.message}", false);
            }

            return result;
        }

        public async ValueTask<KiahooshanApiGetVideoVerifyResultResponse> GetVideoVerifyResult(string selfieVideoUniqueId, Guid? orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_httpClient.BaseAddress}api/vvs/video/verify/result?uniqueId={selfieVideoUniqueId}");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();
                if (JsonHelpers.TryDeserializeJson(message, out KiahooshanApiDigitalSignatureErrorResponse err))
                {
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), err.errors, true);
                }
                else
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KiahooshanApiGetVideoVerifyResultResponse>(cancellationToken);

            if (result == null || string.IsNullOrWhiteSpace(result.uniqueId))
            {
                throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), $"kiahooshan GetVideoVerifyResult not succeeded with message: {result?.message}", false);
            }

            return result;

        }

        public async ValueTask<KiahooshanApiIssueDigiSigResponse> IssueDigiSig(
            string birthDate,
            string mobileNumber,
            string nationalCode,
            string randText,
            byte[] selfieVideo,
            string selfieVideoName,
            string vvsId,
            Guid? orderTrackingCode,
            CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}api/dsig/issue");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            using MemoryStream stream = new MemoryStream(selfieVideo);

            var vc = new StreamContent(stream);
            vc.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("video/mp4");

            var content = new MultipartFormDataContent
            {
                { new StringContent(birthDate.Replace("/", "")), "birthDate" }, //"yyyymmdd"
                { new StringContent(nationalCode), "nationalCode" },
                { new StringContent(mobileNumber), "mobileNumber" },
                { new StringContent(randText), "randText" },
                { new StringContent(vvsId), "vvs-uniqueId" },
                { vc, "selfieVideo", selfieVideoName }
            };
            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();
                if (JsonHelpers.TryDeserializeJson(message, out KiahooshanApiDigitalSignatureErrorResponse err))
                {
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), err.errors, true);
                }
                else
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KiahooshanApiIssueDigiSigResponse>(cancellationToken);

            if (result == null || string.IsNullOrWhiteSpace(result.uniqueId))
            {
                throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), $"kiahooshan IssueDigiSig not succeeded with message: {result?.message}", false);
            }

            return result;
        }

        public async ValueTask<Stream> SignDoc(
            string sigUniqueId,
            Stream docFile,
            string docFileName,
            Guid? orderTrackingCode,
            CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}api/dsig/sign-doc");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new MultipartFormDataContent
            {
                { new StringContent(sigUniqueId), "uniqueId" },
                { new StreamContent(docFile), "docFile", docFileName }

            };
            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();
                if (JsonHelpers.TryDeserializeJson(message, out KiahooshanApiDigitalSignatureErrorResponse err))
                {
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), err.errors, true);
                }
                else
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), message, false);
            }

            var result = await response.Content.ReadAsStreamAsync(cancellationToken);

            return result;
        }

        public async ValueTask<Stream> GetSignedDoc(string signedDocUniqueId, Guid? orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_httpClient.BaseAddress}api/dsig/getSignedDoc?uniqueId={signedDocUniqueId}");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                _logger.LogError(message);

                return null;
            }

            return await response.Content.ReadAsStreamAsync(cancellationToken);
        }

        public async ValueTask<KiahooshanApiResponse<KiahooshanApiIssueDigiProNoteResponse>> IssueDigiProNote(KiahooshanApiIssuePromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}api/dpronote/initial-issue");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");
            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {                
                var message = await response.Content.ReadAsStringAsync();
                if (JsonHelpers.TryDeserializeJson(message, out KiahooshanApiPromissoryErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.error?.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KiahooshanApiResponse<KiahooshanApiIssueDigiProNoteResponse>>(cancellationToken);

            if (result == null || result.status != ApplicationConstants.KiahooshanPromissorySuccessStatus || result.data == null || string.IsNullOrWhiteSpace(result.data.unSignedPdf))
            {
                throw new PromissoryProviderException(orderTrackingCode.ToString(), $"kiahooshan IssueDigiProNote not succeeded", false);
            }

            return result;
        }

        public async ValueTask<ServiceResult<KiahooshanApiResponse<KiahooshanApiFinalizeResponse>>> FinalizeAsync(KiahooshanApiFinalizePromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}api/dpronote/finalize");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");

            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KiahooshanApiPromissoryErrorResponse err))
                {
                    return new ServiceResult<KiahooshanApiResponse<KiahooshanApiFinalizeResponse>> 
                    {
                        Succeeded = false,
                        Message = err.error?.message,
                        RawResponseContent = message
                    };
                }
                else
                {
                    return new ServiceResult<KiahooshanApiResponse<KiahooshanApiFinalizeResponse>>
                    {
                        Succeeded = false,
                        Message = message,
                        RawResponseContent = message
                    };
                }
            }

            var result = await response.Content.ReadFromJsonAsync<KiahooshanApiResponse<KiahooshanApiFinalizeResponse>>(cancellationToken);

            if (result == null || result.status != ApplicationConstants.KiahooshanPromissorySuccessStatus || string.IsNullOrWhiteSpace(result.data?.treasury))
            {
                return new ServiceResult<KiahooshanApiResponse<KiahooshanApiFinalizeResponse>>
                {
                    Succeeded = false,
                    Message = $"kiahooshan FinalizeAsync not succeeded with message: {result?.message}",
                    RawResponseContent = $"kiahooshan FinalizeAsync not succeeded with message: {result?.message}"
                };
            }

            return new ServiceResult<KiahooshanApiResponse<KiahooshanApiFinalizeResponse>> { Succeeded = true, SuccessResult = result };
        }

        public async ValueTask<KiahooshanApiResponse<KiahooshanApiSettlementResponse>> InitialSettlement(KiahooshanApiInitialSettlementPromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}api/dpronote/initial-settlement");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");

            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KiahooshanApiPromissoryErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.error?.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KiahooshanApiResponse<KiahooshanApiSettlementResponse>>(cancellationToken);

            if (result == null || result.status != "completed")
            {
                throw new PromissoryProviderException(orderTrackingCode.ToString(), $"kiahooshan InitialSettlement not succeeded with message: {result?.message}", false);
            }

            return result;
        }

        public async ValueTask<KiahooshanApiVideoVerifyResponse> FinalizeSettlement(KiahooshanApiFinalizeSettlementPromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}api/dpronote/finalize-settlement");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");

            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KiahooshanApiPromissoryErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.error?.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KiahooshanApiVideoVerifyResponse>(cancellationToken);

            if (result == null || string.IsNullOrWhiteSpace(result.uniqueId))
            {
                throw new PromissoryProviderException(orderTrackingCode.ToString(), $"kiahooshan FinalizeSettlement not succeeded", false);
            }

            return result;
        }

        public async ValueTask<KiahooshanApiResponse<string>> DeleteAsync(KiahooshanApiDeletePromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}api/dpronote/revoke-draft");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");

            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                return new KiahooshanApiResponse<string>() { message = message, error = message };
            }

            var result = await response.Content.ReadFromJsonAsync<KiahooshanApiResponse<string>>(cancellationToken);

            return result;
        }

    }
}
