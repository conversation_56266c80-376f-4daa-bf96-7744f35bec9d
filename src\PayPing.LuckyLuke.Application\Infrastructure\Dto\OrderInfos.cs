﻿namespace PayPing.LuckyLuke.Application.Infrastructure.Dto
{
    public class OrderInfos
    {
        public List<OrderInfo> orders {  get; set; }
        public int totalCount { get; set; }
    }

    public class OrderInfo
    {
        public Guid orderTrackingCode { get; set; }
        public DateTimeOffset createdDate { get; set; }
        public decimal creditAmount { get; set; }
        public string consumer { get; set; }
        public int totalInstallmentCount { get; set; }
        public int paidoffInstallmentCount { get; set; }
        public bool isCancelable { get; set; }
        public string mobile { get; set; }
    }
}
