﻿using System.Security.Cryptography;
using System.Text;

namespace PayPing.LuckyLuke.Application.Infrastructure.Utilities
{
    public static class StringHelpers
    {
        public static bool HasValue(this string text)
        {
            return !string.IsNullOrWhiteSpace(text);
        }

        public static string ValueOrNull(this string text) 
        {
            return !string.IsNullOrWhiteSpace(text) ? text : null;
        }

        public static string HashSHA256NationalCode(this string code)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] data = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(code));
                return Convert.ToBase64String(data);
            }
        }
    }
}
