﻿using DecimalGrpc;

namespace PayPing.LuckyLuke.Application.Infrastructure.Utilities
{
    public static class CurrencyHelpers
    {
        public static decimal ToRial(this decimal Toman)
        {
            return decimal.Multiply(<PERSON><PERSON>, 10m);
        }

        public static decimal ToToman(this decimal Rial)
        {
            return Math.Floor(decimal.Divide(Rial, 10m));
        }

        public static int ToRial(this int Toman)
        {
            return Toman * 10;
        }

        public static int ToToman(this int Rial)
        {
            return (int)Math.Floor(decimal.Divide(Rial, 10m));
        }

        public static DecimalValue ToWalletDecimalValue(this decimal value)
        {
            long units = (long)value;
            decimal fractionalPart = value - units;

            // Ensure maximum 9 decimal places
            int nanos = (int)Math.Round(fractionalPart * 1_000_000_000M);
            return new DecimalValue
            {
                Units = units,
                Nanos = nanos
            };
        }

        public static decimal ToDecimal(this DecimalValue value) => value.Units + value.Nanos / 1_000_000_000M;
    }
}
