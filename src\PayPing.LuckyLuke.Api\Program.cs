using PayPing.LuckyLuke.Api;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using Serilog;

public class Program
{
    public static void Main(string[] args)
    {
        Host.CreateDefaultBuilder(args)
            .ConfigureWebHostDefaults(webBuilder =>
            {
                webBuilder.UseStartup<Startup>();
            })
            .UseSerilog(HostBuilderExtensions.CustomSerilogConfigurator)
            .Build()
            .Run();
    }
}