﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;

namespace PayPing.LuckyLuke.Application.Features.Merchant._000_GetContract
{
    public class GetContractResponse
    {
        public GetContractResponse(bool hasValidContract, int contractId, string contractRawFileName, string contractSignedFileName, decimal minPrePaymentRate,
                                   decimal interestRate, Guid creditWalletId, decimal totalCredit,
                                   decimal availableCredit, Guid creditCode, ActivationSteps next)
        {
            this.hasValidContract = hasValidContract;
            this.contractId = contractId;
            this.signedContractFileName = contractSignedFileName;
            this.rawContractFileName = contractRawFileName;
            this.minPrePaymentRate = minPrePaymentRate;
            this.interestRate = interestRate;
            this.creditWalletId = creditWalletId;
            this.totalCredit = totalCredit;
            this.availableCredit = availableCredit;
            this.creditCode = creditCode;
            this.next = next;
        }

        public bool hasValidContract { get; set; }
        public int contractId { get; set; }
        public string signedContractFileName { get; set; }
        public string rawContractFileName { get; set; }
        public decimal minPrePaymentRate { get; set; }
        public decimal interestRate { get; set; }
        public Guid creditWalletId { get; set; }
        public Guid creditCode { get; set; }
        public decimal totalCredit { get; set; }
        public decimal availableCredit { get; set; }
        public ActivationSteps next { get; set; }
    }

    public class GetContractEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                MerchantRoutes.GetContract,
                async (
                    IGetContractRequestHandler handler,
                    CancellationToken cancellationToken) =>
                {

                    var result = await handler.HandleAsync(cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetContract")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<GetContractResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Contract Get")
            .WithDescription("Contract Get")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetContractRequestHandler : IGetContractRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly IWalletService _walletService;
        private readonly IEnumerable<IDigitalSignProvider> _digitalSignProviders;
        private readonly BNPLOptions _bnplOptions;

        public GetContractRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IWalletService walletService,
            IEnumerable<IDigitalSignProvider> digitalSignProviders,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
            _walletService = walletService;
            _digitalSignProviders = digitalSignProviders;
        }

        public async ValueTask<GetContractResponse> HandleAsync(CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var currentContract = await _dbContext.Contracts.AsNoTracking()
            .Where(p => p.MerchantUserId == userId && p.ExpireDate > DateTimeOffset.UtcNow)
            .Include(p => p.Credits)
            .FirstOrDefaultAsync(cancellationToken);

            if (currentContract == null)
            {
                return new GetContractResponse(false, default, default, default, default, default, default, default, default, default, ActivationSteps.CreateContract);
            }
            else
            {
                var merchantInfo = await _dbContext.MerchantInfos.AsNoTracking()
                    .Where(p => p.MerchantUserId == userId)
                    .FirstOrDefaultAsync(cancellationToken);

                if (merchantInfo == null)
                {
                    throw new ArgumentException("تنظیمات پذیرنده یافت نشد");
                }

                var next = currentContract.ActivationStatus.ToActivationStep();
                if (currentContract.ActivationStatus == ActivationStatus.CompleteMerchantProfileInfo)
                {
                    var dsp = ChooseProvider();

                    var signData = await dsp.GetPdfSignatureDataAsync(userId);

                    if (!string.IsNullOrWhiteSpace(merchantInfo.SignatureImageFileId) && !string.IsNullOrWhiteSpace(signData.certificate))
                    {
                        next = ActivationSteps.AcceptRegulations;
                    }
                }

                var activeCredit = currentContract.Credits.FirstOrDefault(c => c.IsActive);
                if (activeCredit != null)
                {
                    var creditInfo = await _walletService.GetWalletCreditInfoAsync(activeCredit.WalletId, cancellationToken);

                    Guid walletId = activeCredit.WalletId;

                    if (creditInfo.Success)
                    {
                        return new GetContractResponse(true, currentContract.Id, currentContract.FilledContractId, currentContract.SignedContractId, merchantInfo.MinPrePaymentRate, merchantInfo.InterestRate,
                            walletId, creditInfo.deposit, creditInfo.balance, activeCredit.Code, next);
                    }
                    else
                    {
                        return new GetContractResponse(true, currentContract.Id, currentContract.FilledContractId, currentContract.SignedContractId, merchantInfo.MinPrePaymentRate,
                            merchantInfo.InterestRate, walletId, default, default, default, next);
                    }
                }
                else
                {
                    return new GetContractResponse(true, currentContract.Id, currentContract.FilledContractId, currentContract.SignedContractId, merchantInfo.MinPrePaymentRate,
                            merchantInfo.InterestRate, Guid.Empty, default, default, default, next);
                }
            }
        }

        private IDigitalSignProvider ChooseProvider()
        {
            var provider = _digitalSignProviders.FirstOrDefault(f => f.DigitalSignType == DigitalSignType.KuknosVersion1);

            return provider ?? _digitalSignProviders.OfType<KuknosDigitalSignProvider>().FirstOrDefault()
                ?? throw new ArgumentException("No valid digital sign provider found.");
        }
    }
    public interface IGetContractRequestHandler
    {
        ValueTask<GetContractResponse> HandleAsync(CancellationToken cancellationToken);
    }
}
