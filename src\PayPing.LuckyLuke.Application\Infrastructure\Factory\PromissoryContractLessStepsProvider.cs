﻿using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Shared.Abstractions;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class PromissoryContractLessStepsProvider : IContractLessStepsProvider
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IEnumerable<IDigitalSignProvider> _digitalSignProviders;

        public PromissoryContractLessStepsProvider(ApplicationDbContext dbContext, IGuaranteeService guaranteeService, IEnumerable<IDigitalSignProvider> digitalSignProviders)
        {
            _dbContext = dbContext;
            _guaranteeService = guaranteeService;
            _digitalSignProviders = digitalSignProviders;
        }

        public GuaranteeType GuaranteeType => GuaranteeType.Promissory;

        public async ValueTask<OrderStepsDto> CreateContractLessSteps(Order order, int userId)
        {
            OrderSteps nextCode = order.Status.ToOrderStep();

            var result = new OrderStepsDto(nextCode, PreGuaranteeSteps);

            // ConsumerUserId, Which is set on Order, related dynamic steps 
            if (order.PlanIsLocked)
            {
                ConsumerInfo consumerInfo = await _dbContext.ConsumerInfos.Where(x => x.ConsumerUserId == userId).FirstOrDefaultAsync();

                DigitalSignType digitalSignType = await _guaranteeService.GetGuarantorCompliantSignatureTypeByGuarantorId(order.OrderPlan.GuarantorId);

                var provider = ChooseProvider(digitalSignType);

                bool hasSignature = await provider.UserHasValidDigitalSignatureAsync(userId);

                var dsStep = provider.GetDigitalSignStep();

                if (!hasSignature)
                {
                    result.Steps.Add(dsStep);
                }

                var gstepResult = await GetGuaranteeStepsAsync(order.OrderPlan.GuarantorId, order.OrderGuarantees);

                if (order.Status == OrderStatus.ProfileFilled)
                {
                    if (hasSignature)
                    {
                        result.Next = gstepResult.steps.First().code;
                    }
                    else
                    {
                        result.Next = dsStep.Code;
                    }
                }
                else if (order.Status == OrderStatus.DigitalSignatureCreated || order.Status == OrderStatus.GuaranteeFailed)
                {
                    result.Next = gstepResult.steps.First().code;
                }
                else if (order.Status == OrderStatus.GuaranteeInProgress && gstepResult.setNext)
                {
                    result.Next = gstepResult.next.Value;
                }

                result.Steps.AddRange(gstepResult.steps.Select(x => new OrderStepDto(x.name, x.code, true)));
            }

            if (order.Status == OrderStatus.GuaranteeSucceeded)
            {
                result.Next = OrderSteps.Quote;
            }


            result.Steps.AddRange(PostContractSteps);

            foreach (var step in result.Steps)
            {
                if ((int)result.Next > (int)step.Code)
                {
                    step.IsPassed = true;
                }
            }

            return result;
        }

        private List<OrderStepDto> PreGuaranteeSteps => new List<OrderStepDto>()
        {
            new ("انتخاب طرح اعتباری", OrderSteps.PlanList, false),
            new ("رتبه سنجی اعتباری", OrderSteps.CreditScore, true),
            new ("تکمیل اطلاعات", OrderSteps.FillProfile, true),
        };

        private List<OrderStepDto> PostContractSteps => new List<OrderStepDto>()
        {
            new ("پیش فاکتور", OrderSteps.Quote, true),
            new ("اقساط", OrderSteps.GetInstallments, false),
            new ("تسویه شده", OrderSteps.PaidOff, false),
            new ("لغو شده", OrderSteps.Canceled, false)
        };



        private async Task<(bool setNext, OrderSteps? next, List<(string name, OrderSteps code)> steps)> GetGuaranteeStepsAsync(int guarantorId, IEnumerable<OrderGuarantee> ogs)
        {
           IGuaranteeProvider provider = await _guaranteeService.GetGuaranteeProviderByGuarantorId(guarantorId);

            string data = ogs != null && ogs.Any() ? ogs.OrderByDescending(x => x.CreatedAt).FirstOrDefault()?.Data : null;
            var result = provider.GetSteps(data);

            return result;
        }

        private IDigitalSignProvider ChooseProvider(DigitalSignType digitalSignType)
        {
            return _digitalSignProviders.First(f => f.DigitalSignType == digitalSignType);
        }
    }
}
