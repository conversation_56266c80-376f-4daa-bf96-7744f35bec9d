﻿using PayPing.Tools.SdkBase.Types;
using Wallet;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients;

public interface IWalletGrpcClient
{
    Task<ServiceResult<CreateWalletResponse>> CreateWallet(int userId, Guid creditCode,
        CancellationToken cancellationToken = default);

    Task<ServiceResult<GetWalletDetailByIdResponse>> GetWalletDetailsById(Guid walletId,
        CancellationToken cancellationToken = default);

    Task<ServiceResult<GetWalletTransactionTotalAmountResponse>> GetWalletTransactionTotalAmount(Guid walletId, TransactionType transactionType,
        CancellationToken cancellationToken = default);
    
    Task<ServiceResult<GetWalletBalanceByIdResponse>> GetWalletBalanceById(Guid walletId,
        CancellationToken cancellationToken = default);

    Task<ServiceResult<BlockWalletResponse>> Freeze(Guid walletId, Guid correlationId, decimal amount,
        CancellationToken cancellationToken = default);

    Task<ServiceResult<UnBlockWalletResponse>> Unfreeze(Guid walletId, Guid correlationId,
        CancellationToken cancellationToken = default);

    Task<ServiceResult<UnBlockWalletResponse>> UnfreezeAndWithdraw(Guid walletId, Guid correlationId, decimal amount,
        CancellationToken cancellationToken = default);

    Task<ServiceResult<DepositWalletResponse>> Deposit(Guid walletId, Guid correlationId, decimal amount, string description,
        CancellationToken cancellationToken = default);

    Task<ServiceResult<WithdrawWalletResponse>> Withdraw(Guid walletId, Guid correlationId, decimal amount, string description,
        CancellationToken cancellationToken = default);
}