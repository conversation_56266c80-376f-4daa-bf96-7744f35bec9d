﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Caching;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer._08_CreditValidationInquiry
{
    public record CreditScoreInquiryRequest(Guid trackingCode);
    public record CreditScoreInquiryResponse(string Description, bool hasCredit);
    public record struct ConsumerInfo(string nationalCode, string mobileNumber);

    public class CreditScoreInquiryEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.CreditScoreInquiry,
                async (
                    CreditScoreInquiryRequest request,
                    ICreditScoreInquiryRequestHandler handler,
                    IValidator<CreditScoreInquiryRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("CreditScoreInquiry")
            .WithApiVersionSet(builder.NewApiVersionSet("CreditScore").Build())
            .Produces<CreditScoreInquiryResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Credit Score Inquiry")
            .WithDescription("Credit Score Inquiry")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CreditScoreInquiryRequestHandler : ICreditScoreInquiryRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserService _userService;
        private readonly IInquiryApiClient _inquiryApiClient;
        private readonly IDistributedCache _cache;

        public CreditScoreInquiryRequestHandler(ApplicationDbContext dbContext,
                                                      IUserContext userContext,
                                                      IOptions<BNPLOptions> bnplOptions,
                                                      IUserService userService,
                                                      IInquiryApiClient inquiryApiClient,
                                                      IDistributedCache cache)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _bnplOptions = bnplOptions.Value;
            _userService=userService;
            _inquiryApiClient = inquiryApiClient;
            _cache = cache;
        }

        public async ValueTask<CreditScoreInquiryResponse> HandleAsync(CreditScoreInquiryRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders
                .Where(o => o.TrackingCode == request.trackingCode && o.ConsumerUserId == userId)
                .FirstOrDefaultAsync(cancellationToken);

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            var consumerInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);

            if (!_cache.TryGetValue($"CreditScoreToken_{consumerInfo.NationalCode}_{consumerInfo.UserName}", out string cachedToken))
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "کد اعتبارسنجی منقضی شده است", string.Empty);
            }

            if (!_cache.TryGetValue($"CreditScoreOtpCode_{consumerInfo.NationalCode}_{consumerInfo.UserName}", out string cachedOtpCode))
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "کد اعتبارسنجی منقضی شده است", string.Empty);
            }

            if (!_cache.TryGetValue($"CreditScoreReportTryCount_{consumerInfo.NationalCode}_{cachedOtpCode}", out CreditScoreReportTryCountModel cachedTryCount))
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "کد اعتبارسنجی منقضی شده است", string.Empty);
            }

            await IncrementTryCount(cachedTryCount.Count, consumerInfo.NationalCode, cachedToken, cachedOtpCode);

            string cachedReportCode;
            if (!_cache.TryGetValue($"CreditScoreReportCode_{consumerInfo.NationalCode}_{consumerInfo.UserName}", out cachedReportCode))
            {
                var reportCodeResult = await _inquiryApiClient.GenerateReportLinkCreditScore(cachedOtpCode, cachedToken, cancellationToken);

                if (reportCodeResult == null || !reportCodeResult.Success || string.IsNullOrWhiteSpace(reportCodeResult.UniqueCode))
                {
                    // is it 5th try ?
                    if (cachedTryCount.Count >= 4)
                    {
                        return await CreateCreditScore(order, true, 3, string.Empty, string.Empty, false);
                    }

                    throw new CreditScoreNotReadyException(request.trackingCode.ToString(), $"provider error at creditscore unique code with message: {reportCodeResult?.Message}, uc: {reportCodeResult?.UniqueCode}, success: {reportCodeResult?.Success}");
                }

                await _cache.SetAsync(
                   $"CreditScoreReportCode_{consumerInfo.NationalCode}_{consumerInfo.UserName}",
                   reportCodeResult.UniqueCode,
                   new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5) });

                cachedReportCode = reportCodeResult.UniqueCode;
            }

            var creditScoreResult = await _inquiryApiClient.GetCreditScoreReport(cachedReportCode, cancellationToken);

            if (creditScoreResult == null || !creditScoreResult.Success)
            {
                // is it 5th try ?
                if (cachedTryCount.Count >= 4)
                {
                    return await CreateCreditScore(order, true, 3, string.Empty, string.Empty, false);
                }
                else
                {
                    throw new CreditScoreNotReadyException(request.trackingCode.ToString(), $"provider error at creditscore inquiry report with message: {creditScoreResult?.Description}");
                }
            }

            return await CreateCreditScore(order, false, creditScoreResult.Score, creditScoreResult.Risk, creditScoreResult.Description, creditScoreResult.HasLoans);
        }

        private async ValueTask<CreditScoreInquiryResponse> CreateCreditScore(Order order, bool byPass, int score, string risk, string desc, bool hasLoans)
        {
            OrderCreditValidation orderCreditValidation = new OrderCreditValidation
            {
                OrderId = order.Id,
                Score = score,
                Risk = risk,
                Description = desc,
                HasLoans = hasLoans
            };

            _dbContext.OrderCreditValidations.Add(orderCreditValidation);


            bool isAllowed = false;
            if (byPass || score >= ApplicationConstants.CreditScoreAllowedMargin || !hasLoans)
                isAllowed = true;

            if (isAllowed)
            {
                order.Status = Domain.Enums.OrderStatus.CreditValidationSucceeded;
            }
            else
            {
                order.Status = Domain.Enums.OrderStatus.CreditValidationFailed;
            }

            await _dbContext.SaveChangesAsync();

            return new CreditScoreInquiryResponse(desc, isAllowed);
        }

        private async ValueTask IncrementTryCount(int cachedTryCount, string nationalCode, string cachedToken, string cachedOtpCode)
        {
            int newTryCount = cachedTryCount + 1;

            await _cache.SetAsync(
                   $"CreditScoreReportTryCount_{nationalCode}_{cachedOtpCode}",
                   new CreditScoreReportTryCountModel(newTryCount),
                   new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5) });
        }
    }

    public interface ICreditScoreInquiryRequestHandler
    {
        ValueTask<CreditScoreInquiryResponse> HandleAsync(CreditScoreInquiryRequest request, CancellationToken cancellationToken);
    }

    public class CreditScoreInquiryValidator : AbstractValidator<CreditScoreInquiryRequest>
    {
        public CreditScoreInquiryValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required); ;
        }
    }

}