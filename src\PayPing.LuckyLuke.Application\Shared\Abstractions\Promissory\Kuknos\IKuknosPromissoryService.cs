﻿using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kuknos
{
    public interface IKuknosPromissoryService
    {
        ValueTask<object> HandleAsync(KuknosPromissoryContextV1 context, CancellationToken cancellationToken = default);
    }
}
