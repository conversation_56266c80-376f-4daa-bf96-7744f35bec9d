﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class MerchantActivation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Contracts_ContractTemplates_ContractTemplateId",
                table: "Contracts");

            migrationBuilder.DropTable(
                name: "ContractTemplates");

            migrationBuilder.DropIndex(
                name: "IX_Contracts_ContractTemplateId",
                table: "Contracts");

            migrationBuilder.RenameColumn(
                name: "ContractTemplateId",
                table: "Contracts",
                newName: "ActivationStatus");

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "MerchantInfos",
                type: "character varying(2048)",
                maxLength: 2048,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CityEnglish",
                table: "MerchantInfos",
                type: "character varying(64)",
                maxLength: 64,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Email",
                table: "MerchantInfos",
                type: "character varying(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MerchantInfoAgent_FatherName",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MerchantInfoAgent_FirstName",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MerchantInfoAgent_FirstNameEnglish",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "MerchantInfoAgent_IsMale",
                table: "MerchantInfos",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "MerchantInfoAgent_LastName",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MerchantInfoAgent_LastNameEnglish",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MerchantInfoAgent_MobileNumber",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MerchantInfoAgent_NationalCode",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MerchantInfoAgent_NationalIdSeries",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MerchantInfoAgent_PersianBirthDate",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PostalCode",
                table: "MerchantInfos",
                type: "character varying(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProvinceEnglish",
                table: "MerchantInfos",
                type: "character varying(64)",
                maxLength: 64,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FilledContractId",
                table: "Contracts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SignedContractId",
                table: "Contracts",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Address",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "CityEnglish",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "Email",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "MerchantInfoAgent_FatherName",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "MerchantInfoAgent_FirstName",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "MerchantInfoAgent_FirstNameEnglish",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "MerchantInfoAgent_IsMale",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "MerchantInfoAgent_LastName",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "MerchantInfoAgent_LastNameEnglish",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "MerchantInfoAgent_MobileNumber",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "MerchantInfoAgent_NationalCode",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "MerchantInfoAgent_NationalIdSeries",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "MerchantInfoAgent_PersianBirthDate",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "PostalCode",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "ProvinceEnglish",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "FilledContractId",
                table: "Contracts");

            migrationBuilder.DropColumn(
                name: "SignedContractId",
                table: "Contracts");

            migrationBuilder.RenameColumn(
                name: "ActivationStatus",
                table: "Contracts",
                newName: "ContractTemplateId");

            migrationBuilder.CreateTable(
                name: "ContractTemplates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ContractFileId = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    ContractVersion = table.Column<float>(type: "real", nullable: false),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false),
                    DeletedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    DurationInDays = table.Column<int>(type: "integer", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    LastModifiedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractTemplates", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_ContractTemplateId",
                table: "Contracts",
                column: "ContractTemplateId");

            migrationBuilder.AddForeignKey(
                name: "FK_Contracts_ContractTemplates_ContractTemplateId",
                table: "Contracts",
                column: "ContractTemplateId",
                principalTable: "ContractTemplates",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
