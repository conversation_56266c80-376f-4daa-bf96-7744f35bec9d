﻿using System.Globalization;
using System.Reflection;
using System.Resources;

namespace PayPing.LuckyLuke.Application.Shared.Resources.ResourceExtension
{
    public class ResourceExtensionManager
    {
        public static string GetEnNameResource(string valueToFind, string resourceDictionary)
        {
            string matchingKey = null;
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                // Find all embedded resource names
                var resourceNames = assembly.GetManifestResourceNames();

                // Find the first .resx file dynamically
                var resourceFile = resourceNames.FirstOrDefault(r => r.EndsWith($".{resourceDictionary}.resx", StringComparison.OrdinalIgnoreCase));

                if (resourceFile != null)
                {
                    // Get the ResourceManager for the resource file
                    var resourceManager = new ResourceManager(resourceFile.Replace(".resx", ""), assembly);

                    // Get all resources from the file
                    var resourceSet = resourceManager.GetResourceSet(System.Globalization.CultureInfo.CurrentCulture, true, true);

                    // Search for the key based on the value
                    foreach (System.Collections.DictionaryEntry entry in resourceSet)
                    {
                        if (entry.Value != null && entry.Value.ToString() == valueToFind)
                        {
                            matchingKey = entry.Key.ToString();
                            break;
                        }
                    }

                    return matchingKey;
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static string GetProvinceEnCode(string province)
        {
            var resourceManager = new ResourceManager("PayPing.LuckyLuke.Application.Shared.Resources.ProvinceResource", typeof(ResourceExtensionManager).Assembly);
            return resourceManager.GetString(province);
        }

        public static string GetCityEnCode(string city)
        {
            var resourceManager = new ResourceManager("PayPing.LuckyLuke.Application.Shared.Resources.CityResource", typeof(ResourceExtensionManager).Assembly);
            return resourceManager.GetString(city);
        }

    }

}
