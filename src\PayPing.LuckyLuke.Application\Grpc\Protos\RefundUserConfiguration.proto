﻿syntax = "proto3";

option csharp_namespace = "PayPing.Refund.WebApi.gRPC";

import "google/protobuf/timestamp.proto";
import "Grpc/Protos/Types/RefundEnumerations.proto";
import "Grpc/Protos/Types/RefundGrpcProblemDetails.proto";

package RefundGrpcServices;

service UserConfigurationService {
   rpc GetUserConfiguration (GetUserConfigurationRequest) returns (GetUserConfigurationResponse);
}

message GetUserConfigurationRequest {
	int32 userId = 1;
	int32 businessId = 2;
}

message UserConfigurationInfo {
	UserInitializationType userInitializationType = 1;
	WageType wageType = 2;
	double wageValue = 3;
	int32 minPercentageWage = 4;
	int32 maxPercentageWage = 5;
	google.protobuf.Timestamp creationDateTime = 6;
	bool autoInitializePendingRefunds = 7;
}

message GetUserConfigurationResponse {
	oneof result {
		UserConfigurationInfo success = 1;
		GrpcProblemDetails failure = 2;
	}
}