﻿using PayPing.LuckyLuke.Application.Domain.Enums;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class SalaryGuaranteeOrderStepsFactory : IOrderStepsFactory
    {
        private readonly IContractMandatoryStepsProvider _contractMandatoryStepsProvider;
        private readonly IContractLessStepsProvider _contractLessStepsProvider;

        public SalaryGuaranteeOrderStepsFactory(IEnumerable<IContractMandatoryStepsProvider> contractMandatoryStepsProviders, IEnumerable<IContractLessStepsProvider> contractLessStepsProviders)
        {
            _contractMandatoryStepsProvider = contractMandatoryStepsProviders.Where(x => x.GuaranteeType == GuaranteeType.Salary).First();
            _contractLessStepsProvider = contractLessStepsProviders.Where(x => x.GuaranteeType == GuaranteeType.Salary).First();
        }

        public GuaranteeType GuaranteeType => GuaranteeType.Salary;

        public IContractMandatoryStepsProvider CreateContractMandatory()
        {
            return _contractMandatoryStepsProvider;
        }

        public IContractLessStepsProvider CreateContractLess()
        {
            return _contractLessStepsProvider;
        }
    }
}
