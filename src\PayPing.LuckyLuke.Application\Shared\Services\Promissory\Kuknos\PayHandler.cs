﻿using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kuknos;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kuknos
{
    public class PayHandler : KuknosBaseHandler, IKuknosPromissoryService
    {
        private readonly IKuknosPromissoryService _next;
        private readonly BNPLOptions _bnplOptions;

        public PayHandler(
            IKuknosPromissoryService next,
            IOptions<BNPLOptions> bnplOptions,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _next = next;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<object> HandleAsync(KuknosPromissoryContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosPromissoryStatusV1.Pay)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var result = await _kuknosApi.PayAsync(new KuknosApiPayRequest(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.KuknosPromissoryPaymentCallBackUI)), context.PromissoryId, context.OrderTrackingCode, cancellationToken);

            if (string.IsNullOrWhiteSpace(result.data))
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "promissory payment url is empty", false);
            }

            
            context.Status = KuknosPromissoryStatusV1.Finalize;
            context.PromissoryNotePaymentStatus = PromissoryNotePaymentStatus.Unknown;

            //// save context
            await UpdateContextAsync(context);

            return new PayResult(result.data);
        }

        protected override void ValidateContext(KuknosPromissoryContextV1 context)
        {
            base.ValidateContext(context);
            
        }                             
    }
}
