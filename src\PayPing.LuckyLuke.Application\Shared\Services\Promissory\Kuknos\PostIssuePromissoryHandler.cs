﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kuknos;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kuknos
{
    public class PostIssuePromissoryHandler : Kuk<PERSON>Base<PERSON>and<PERSON>, IKuknosPromissoryService
    {
        private readonly IKuknosPromissoryService _next;
        private readonly IUploadGrpcClient _uploadGrpcClient;

        public PostIssuePromissoryHandler(
            IKuknosPromissoryService next,
            IUploadGrpcClient uploadGrpcClient,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _next = next;
            _uploadGrpcClient = uploadGrpcClient;
        }

        public async ValueTask<object> HandleAsync(KuknosPromissoryContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosPromissoryStatusV1.PostIssuePromissory)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var raw = await _kuknosApi.DownloadRawDocumentAsync(context.PromissoryRawDocumentHash, context.OrderTrackingCode);

            if (raw == null || raw.contentBytes == null || raw.contentBytes.Length == 0)
            {
                await ResetOrderPromissoryToRenewIssue(context);

                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not download raw promissory document", false);
            }

            // upload file to storage
            string validName = Path.ChangeExtension(Path.GetRandomFileName(), ".pdf");

            var uresult = await _uploadGrpcClient.UploadUserDocumentAsync(raw.contentBytes, Path.GetExtension(validName).TrimStart('.'), context.ConsumerUserId, cancellationToken);

            if (!uresult.Succeeded)
            {
                await ResetOrderPromissoryToRenewIssue(context);

                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not upload raw promissory to storage service", false);
            }

            context.Status = KuknosPromissoryStatusV1.SignDocumentWithSignerPdf;
            context.PromissoryRawDocumentFileId = uresult.SuccessResult;
            context.PromissoryRawDocumentFileContentType = raw.contentType;

            //// save context
            await UpdateContextAsync(context);

            //return new DownloadRawDocumentResult(uresult.SuccessResult);
            return await _next.HandleAsync(context, cancellationToken);
        }



        protected override void ValidateContext(KuknosPromissoryContextV1 context)
        {
            base.ValidateContext(context);
            Guard.Against.NullOrEmpty(context.PromissoryId);
            Guard.Against.NullOrEmpty(context.PromissoryRawDocumentHash);
        }
    }
}
