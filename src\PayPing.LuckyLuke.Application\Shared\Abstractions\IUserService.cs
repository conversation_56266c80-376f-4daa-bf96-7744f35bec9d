﻿using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;

namespace PayPing.LuckyLuke.Application.Shared.Abstractions
{
    public interface IUserService
    {
        ValueTask<UserInfoDto> GetConsumerInfoAsync(int consumerUserId, CancellationToken cancellationToken);
        ValueTask<UserInfoDto> GetMerchantExtraInfoAsync(int merchantUserId, CancellationToken cancellationToken);
        ValueTask<UserIsMerchantDto> IsUserMerchantAsync(int userId, CancellationToken cancellationToken);
        ValueTask<UserInfoDto> SetNewConsumerInfoAsync(int consumerUserId, ConsumerInfo consumerInfo, CancellationToken cancellationToken);
    }
}
