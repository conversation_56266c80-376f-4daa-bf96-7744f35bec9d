﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._12_PlanCreate
{
    public record TogglePlanActiveRequest(Guid planCode, bool isActive);

    public record TogglePlanActiveResponse(Guid planCode);

    public class TogglePlanActiveEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPut(
                MerchantRoutes.TogglePlanActive,
                async (TogglePlanActiveRequest request, ITogglePlanActiveRequestHandler handler, IValidator<TogglePlanActiveRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("TogglePlanActive")
            .WithApiVersionSet(builder.NewApiVersionSet("Plan").Build())
            .Produces<TogglePlanActiveResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Toggle Plan Active")
            .WithDescription("Toggle Plan Active")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class TogglePlanActiveRequestHandler : ITogglePlanActiveRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public TogglePlanActiveRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<TogglePlanActiveResponse> HandleAsync(TogglePlanActiveRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var plan = await _dbContext.Plans.Where(x => x.Code == request.planCode && x.MerchantUserId == userId).FirstOrDefaultAsync(cancellationToken);

            if (plan == null)
            {
                throw new ArgumentException("طرح اقساطی یافت نشد");
            }

            plan.IsActive = request.isActive;

            await _dbContext.SaveChangesAsync();

            return new TogglePlanActiveResponse(plan.Code);
        }

    }
    public interface ITogglePlanActiveRequestHandler
    {
        ValueTask<TogglePlanActiveResponse> HandleAsync(TogglePlanActiveRequest request, CancellationToken cancellationToken);
    }

    public class TogglePlanActiveValidator : AbstractValidator<TogglePlanActiveRequest>
    {
        public TogglePlanActiveValidator()
        {
            RuleFor(x => x.planCode).NotEmpty().WithResourceError(() => ValidatorDictionary.plan_code_is_required);
        }
    }

}
