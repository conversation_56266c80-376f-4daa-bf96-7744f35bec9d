﻿
namespace PayPing.BNPL.Domain.Models
{
    // value object
    // should be configured on ef as ComplexType
    public readonly record struct MerchantInfoAgent
    {
        public MerchantInfoAgent(
            string firstName,
            string lastName,
            string firstNameEnglish,
            string lastNameEnglish,
            string nationalCode,
            string persianBirthDate,
            bool isMale,
            string nationalIdSeries,
            string fatherName,
            string mobileNumber)
        {
            FirstName = firstName;
            LastName = lastName;
            FirstNameEnglish = firstNameEnglish;
            LastNameEnglish = lastNameEnglish;
            NationalCode = nationalCode;
            PersianBirthDate = persianBirthDate;
            IsMale = isMale;
            NationalIdSeries = nationalIdSeries;
            FatherName = fatherName;
            MobileNumber = mobileNumber;
        }

        public string FirstName { get; init; }
        public string LastName { get; init; }
        public string FirstNameEnglish { get; init; }
        public string LastNameEnglish { get; init; }
        public string NationalCode { get; init; }
        public string PersianBirthDate { get; init; }
        public bool IsMale { get; init; }
        public string NationalIdSeries { get; init; }
        public string FatherName { get; init; }
        public string MobileNumber { get; init; }

    }


}
