﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kuknos;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kuknos
{
    public class SignedHandler : Kuk<PERSON>BaseHandler, IKuknosPromissoryService
    {
        private readonly IKuknosPromissoryService _next;
        private readonly BNPLOptions _bnplOptions;

        public SignedHandler(
            IKuknosPromissoryService next,
            IOptions<BNPLOptions> bnplOptions,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _next = next;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<object> HandleAsync(KuknosPromissoryContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosPromissoryStatusV1.Signed)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var dsc = await GetValidDigitalSignAsync(context.ConsumerUserId);

            var result = await _kuknosApi.SignedAsync(new KuknosApiSignedRequest(context.PromissorySignedDocumentHash, dsc.PublicKey), context.PromissoryId, context.OrderTrackingCode, cancellationToken);

            if (!result.data)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "promissory signed result is false", false);
            }
            
            context.Status = KuknosPromissoryStatusV1.Pay;
            
            //// save context
            await UpdateContextAsync(context);

            //return new SignedResult((int)decimal.Multiply(0.00005m, context.Amount)); // one floating point is for Toman !
            return await _next.HandleAsync(context, cancellationToken);
        }

        protected override void ValidateContext(KuknosPromissoryContextV1 context)
        {
            base.ValidateContext(context);
            Guard.Against.NullOrEmpty(context.PromissorySignedDocumentHash);
            //Guard.Against.NullOrEmpty(context.PromissorySignedDocumentFileId);
            //Guard.Against.NullOrEmpty(context.PromissorySignedDocumentFileContentType);
        }
    }
}
