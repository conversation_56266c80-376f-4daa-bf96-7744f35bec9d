﻿using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kuknos;
using PayPing.LuckyLuke.Application.Shared.Mappers;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kuknos
{
    public class IssuePromissoryHandler : KuknosBaseHandler, IKuknosPromissoryService
    {
        private readonly IKuknosPromissoryService _next;

        public IssuePromissoryHandler(
            IKuknosPromissoryService next,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _next = next;
        }

        public async ValueTask<object> HandleAsync(KuknosPromissoryContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosPromissoryStatusV1.IssuePromissory)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var dsc = await GetValidDigitalSignAsync(context.ConsumerUserId);

            var result = await _kuknosApi.IssuePromissoryAsync(context.ToApiRequest(dsc) , context.OrderTrackingCode, cancellationToken);

            context.Status = KuknosPromissoryStatusV1.PostIssuePromissory;
            context.PromissoryId = result.data.id;
            context.PromissoryNotePaymentStatus = Enum.TryParse(result.data.status , out PromissoryNotePaymentStatus res) ? res : PromissoryNotePaymentStatus.Unknown;
            context.PromissoryRawDocumentHash = result.data.unsigned_document;


            //// save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

    }
}
