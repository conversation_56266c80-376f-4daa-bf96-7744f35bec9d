﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._15_OwnedCreditIncrease
{
    public record OwnedCreditIncreaseRequest(Guid walletId, decimal amount);

    public record OwnedCreditIncreaseResponse(bool success, string message, decimal newBalance, decimal newTotal);

    public class OwnedCreditIncreaseEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.OwnedCreditIncrease,
                async (OwnedCreditIncreaseRequest request, IOwnedCreditIncreaseRequestHandler handler, IValidator<OwnedCreditIncreaseRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("CreditIncrease")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<OwnedCreditIncreaseResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Credit Increase")
            .WithDescription("Credit Increase")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OwnedCreditIncreaseRequestHandler : IOwnedCreditIncreaseRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IWalletService _walletService;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public OwnedCreditIncreaseRequestHandler(ApplicationDbContext dbContext, IWalletService walletService, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _walletService = walletService;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<OwnedCreditIncreaseResponse> HandleAsync(OwnedCreditIncreaseRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var currentContract = await _dbContext.Contracts.AsNoTracking()
                .Where(p =>
                    p.MerchantUserId == userId &&
                    p.ExpireDate > DateTimeOffset.UtcNow)
                .Include(c => c.Credits)
                .OrderByDescending(c => c.ExpireDate)
                .FirstOrDefaultAsync(cancellationToken);

            if (currentContract == null)
            {
                throw new ArgumentException("قرارداد فروش اقساطی فعال یافت نشد");
            }

            var activeCredit = currentContract.Credits.FirstOrDefault(c => c.WalletId == request.walletId && c.IsActive);
            if (activeCredit == null)
            {
                throw new ArgumentException("کیف پول اعتباری فعال قرارداد یافت نشد");
            }

            var result = await _walletService.IncreaseWalletCreditAndSaveAsync(activeCredit.WalletId, request.amount, cancellationToken);

            return new OwnedCreditIncreaseResponse(result.Success, result.Error, result.NewBalance, result.newTotal);
        }

    }
    public interface IOwnedCreditIncreaseRequestHandler
    {
        ValueTask<OwnedCreditIncreaseResponse> HandleAsync(OwnedCreditIncreaseRequest request, CancellationToken cancellationToken);
    }

    public class OwnedCreditIncreaseValidator : AbstractValidator<OwnedCreditIncreaseRequest>
    {
        public OwnedCreditIncreaseValidator()
        {
            RuleFor(x => x.walletId).NotEmpty().WithResourceError(() => ValidatorDictionary.wallet_id_is_required);
            RuleFor(x => x.amount).GreaterThan(0).WithResourceError(() => ValidatorDictionary.amount_greater_zero);
        }
    }
}
