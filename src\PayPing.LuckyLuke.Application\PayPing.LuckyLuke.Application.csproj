﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Features\Admin\ConsumerLoginHistory\**" />
	  <Compile Remove="Features\Admin\ConsumerProfileUpdate\**" />
	  <Compile Remove="Features\Admin\ConsumerProfileView\**" />
	  <Compile Remove="Features\Admin\CreditCreate\**" />
	  <Compile Remove="Features\Admin\CreditScoreProviderCreate\**" />
	  <Compile Remove="Features\Admin\CreditScoreProviderDelete\**" />
	  <Compile Remove="Features\Admin\CreditScoreProviderUpdate\**" />
	  <Compile Remove="Features\Admin\InstallmentDetail\**" />
	  <Compile Remove="Features\Admin\InstallmentsList\**" />
	  <Compile Remove="Features\Admin\PaymentDetail\**" />
	  <Compile Remove="Features\Admin\PaymentsList\**" />
	  <Compile Remove="Features\Admin\Todo_PromissoryDelete\**" />
	  <Compile Remove="Features\Consumer\02_OrdersList\**" />
	  <Compile Remove="Features\Admin\ConsumerCreditScore\**" />
	  <Compile Remove="Features\Consumer\06_OrderCreditValidationCreate\**" />
	  <Compile Remove="Features\Consumer\11_Obsolete_PromissoryGuarantee\**" />
	  <Compile Remove="Features\Consumer\13_PromissoryFinalize\**" />
	  <Compile Remove="Features\Consumer\30_InstallmentsNotifyJob\**" />
	  <Compile Remove="Features\Consumer\31_InstallmentDelayJob\**" />
	  <Compile Remove="Features\Consumer\41_PurchaseHistory\**" />
	  <Compile Remove="Features\Merchant\10_AccessibleCreditsList\**" />
	  <Compile Remove="Features\Merchant\11_CreditCreate\**" />
	  <Compile Remove="Features\Merchant\14_PlanDelete\**" />
	  <Compile Remove="Features\Merchant\20_OrdersList\**" />
	  <Compile Remove="Features\Merchant\21_OrderDetail\**" />
	  <EmbeddedResource Remove="Features\Admin\ConsumerCreditScore\**" />
	  <EmbeddedResource Remove="Features\Admin\ConsumerLoginHistory\**" />
	  <EmbeddedResource Remove="Features\Admin\ConsumerProfileUpdate\**" />
	  <EmbeddedResource Remove="Features\Admin\ConsumerProfileView\**" />
	  <EmbeddedResource Remove="Features\Admin\CreditCreate\**" />
	  <EmbeddedResource Remove="Features\Admin\CreditScoreProviderCreate\**" />
	  <EmbeddedResource Remove="Features\Admin\CreditScoreProviderDelete\**" />
	  <EmbeddedResource Remove="Features\Admin\CreditScoreProviderUpdate\**" />
	  <EmbeddedResource Remove="Features\Admin\InstallmentDetail\**" />
	  <EmbeddedResource Remove="Features\Admin\InstallmentsList\**" />
	  <EmbeddedResource Remove="Features\Admin\PaymentDetail\**" />
	  <EmbeddedResource Remove="Features\Admin\PaymentsList\**" />
	  <EmbeddedResource Remove="Features\Admin\Todo_PromissoryDelete\**" />
	  <EmbeddedResource Remove="Features\Consumer\02_OrdersList\**" />
	  <EmbeddedResource Remove="Features\Consumer\06_OrderCreditValidationCreate\**" />
	  <EmbeddedResource Remove="Features\Consumer\11_Obsolete_PromissoryGuarantee\**" />
	  <EmbeddedResource Remove="Features\Consumer\13_PromissoryFinalize\**" />
	  <EmbeddedResource Remove="Features\Consumer\30_InstallmentsNotifyJob\**" />
	  <EmbeddedResource Remove="Features\Consumer\31_InstallmentDelayJob\**" />
	  <EmbeddedResource Remove="Features\Consumer\41_PurchaseHistory\**" />
	  <EmbeddedResource Remove="Features\Merchant\10_AccessibleCreditsList\**" />
	  <EmbeddedResource Remove="Features\Merchant\11_CreditCreate\**" />
	  <EmbeddedResource Remove="Features\Merchant\14_PlanDelete\**" />
	  <EmbeddedResource Remove="Features\Merchant\20_OrdersList\**" />
	  <EmbeddedResource Remove="Features\Merchant\21_OrderDetail\**" />
	  <None Remove="Features\Admin\ConsumerCreditScore\**" />
	  <None Remove="Features\Admin\ConsumerLoginHistory\**" />
	  <None Remove="Features\Admin\ConsumerProfileUpdate\**" />
	  <None Remove="Features\Admin\ConsumerProfileView\**" />
	  <None Remove="Features\Admin\CreditCreate\**" />
	  <None Remove="Features\Admin\CreditScoreProviderCreate\**" />
	  <None Remove="Features\Admin\CreditScoreProviderDelete\**" />
	  <None Remove="Features\Admin\CreditScoreProviderUpdate\**" />
	  <None Remove="Features\Admin\InstallmentDetail\**" />
	  <None Remove="Features\Admin\InstallmentsList\**" />
	  <None Remove="Features\Admin\PaymentDetail\**" />
	  <None Remove="Features\Admin\PaymentsList\**" />
	  <None Remove="Features\Admin\Todo_PromissoryDelete\**" />
	  <None Remove="Features\Consumer\02_OrdersList\**" />
	  <None Remove="Features\Consumer\06_OrderCreditValidationCreate\**" />
	  <None Remove="Features\Consumer\11_Obsolete_PromissoryGuarantee\**" />
	  <None Remove="Features\Consumer\13_PromissoryFinalize\**" />
	  <None Remove="Features\Consumer\30_InstallmentsNotifyJob\**" />
	  <None Remove="Features\Consumer\31_InstallmentDelayJob\**" />
	  <None Remove="Features\Consumer\41_PurchaseHistory\**" />
	  <None Remove="Features\Merchant\10_AccessibleCreditsList\**" />
	  <None Remove="Features\Merchant\11_CreditCreate\**" />
	  <None Remove="Features\Merchant\14_PlanDelete\**" />
	  <None Remove="Features\Merchant\20_OrdersList\**" />
	  <None Remove="Features\Merchant\21_OrderDetail\**" />
	</ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Features\Shared\PromissoryInquiry\" />
		<Folder Include="Infrastructure\Persistence\Migrations\" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Ardalis.GuardClauses" Version="5.0.0" />
		<PackageReference Include="Asp.Versioning.Http" Version="8.1.0" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
		<PackageReference Include="AspNetCore.HealthChecks.Elasticsearch" Version="9.0.0" />
		<PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="9.0.0" />
		<PackageReference Include="AspNetCore.HealthChecks.Rabbitmq" Version="9.0.0" />
		<PackageReference Include="AspNetCore.HealthChecks.Redis" Version="9.0.0" />
		<PackageReference Include="DNTPersianUtils.Core" Version="6.6.1" />
		<PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
		<PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.31.0" />
		<PackageReference Include="Elastic.Apm.SerilogEnricher" Version="8.12.3" />
		<PackageReference Include="Elastic.CommonSchema.Serilog" Version="8.12.3" />
		<PackageReference Include="EPPlus" Version="8.0.3" />
		<PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
		<PackageReference Include="Extension.OfficeOpenXml" Version="1.1.0" />
		<PackageReference Include="FluentValidation" Version="11.11.0" />
		<PackageReference Include="Google.Protobuf" Version="3.30.2" />
		<PackageReference Include="Grpc.Net.Client" Version="2.71.0" />
		<PackageReference Include="Grpc.Net.ClientFactory" Version="2.71.0" />
		<PackageReference Include="Grpc.Tools" Version="2.72.0">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="itext" Version="9.1.0" />
		<PackageReference Include="itext.bouncy-castle-adapter" Version="9.1.0" />
		<PackageReference Include="MassTransit" Version="8.4.0" />
		<PackageReference Include="MassTransit.RabbitMQ" Version="8.4.0" />
		<PackageReference Include="MicroElements.Swashbuckle.FluentValidation" Version="6.1.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.2" />
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.4" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
		<PackageReference Include="IdentityModel.AspNetCore" Version="4.3.0" />
		<PackageReference Include="IdentityModel.AspNetCore.AccessTokenValidation" Version="1.0.0-preview.3" />
		<PackageReference Include="IdentityModel.AspNetCore.OAuth2Introspection" Version="6.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
		<PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.9.0-beta.2" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
		<PackageReference Include="PayPing.Integrations.AdminSDK" Version="4.7.2" />
		<PackageReference Include="PayPing.Messaging.Contracts.IntegrationEvents" Version="0.4.0" />
		<PackageReference Include="Quartz" Version="3.14.0" />
		<PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
		<PackageReference Include="RedLock.net" Version="2.3.2" />
		<PackageReference Include="Scrutor" Version="6.0.1" />
		<PackageReference Include="Serilog" Version="4.2.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Enrichers.CorrelationId" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Span" Version="3.1.0" />
		<PackageReference Include="Serilog.Exceptions.EntityFrameworkCore" Version="8.4.0" />
		<PackageReference Include="Serilog.Exceptions.Refit" Version="8.4.0" />
		<PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
		<PackageReference Include="Serilog.HttpClient" Version="3.0.0" />
		<PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
		<PackageReference Include="Serilog.Sinks.Network" Version="********" />
		<PackageReference Include="Serilog.Sinks.Udp" Version="10.0.0" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.37" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.3" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.7.3" />
		<PackageReference Include="Unchase.Swashbuckle.AspNetCore.Extensions" Version="2.7.1" />
		<PackageReference Include="QuestPDF" Version="2025.4.3" />

	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Infrastructure\Web\Resources\GlobalErrorMessage.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>GlobalErrorMessage.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Infrastructure\Web\Resources\PropertyNameDictionary.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>PropertyNameDictionary.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Shared\Resources\CityResource.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>CityResource.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Shared\Resources\ProvinceResource.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>ProvinceResource.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Shared\Resources\ValidatorDictionary.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>ValidatorDictionary.resx</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="Infrastructure\Web\Resources\GlobalErrorMessage.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>GlobalErrorMessage.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Infrastructure\Web\Resources\PropertyNameDictionary.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>PropertyNameDictionary.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Shared\Resources\CityResource.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>CityResource.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Shared\Resources\ProvinceResource.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>ProvinceResource.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Shared\Resources\ValidatorDictionary.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>ValidatorDictionary.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>
	<ItemGroup>
		<Protobuf Include="Grpc\Protos\Enums\ServiceType.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\Enums\IpgType.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\Payment.proto" GrpcServices="Client" />
	</ItemGroup>

<ItemGroup>
		<Protobuf Include="Grpc\Protos\Upload.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\common.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\inquiry_service.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\Wallet.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\WalletType.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\Coin.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\BaseExceptionResponse.proto" GrpcServices="None" />
		<Protobuf Include="Grpc\Protos\CustomTypes.proto" GrpcServices="None" />
	</ItemGroup>
	<ItemGroup>
		<Protobuf Include="Grpc\Protos\Types\RefundEnumerations.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\Types\RefundGrpcProblemDetails.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\RefundOperation.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\RefundUserConfiguration.proto" GrpcServices="Client" />

	</ItemGroup>

<ItemGroup>
    <None Update="Fonts\YEKAN-PLUS-BOLD.ttf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
	<None Update="Fonts\YEKAN-PLUS-LIGHT.ttf">
		<CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
	<None Update="Fonts\Vazirmatn-Regular.ttf">
		<CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</None>
  </ItemGroup>

</Project>
