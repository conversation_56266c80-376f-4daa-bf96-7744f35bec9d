﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using RedLockNet;

namespace PayPing.LuckyLuke.Application.Shared.Services.Credit;
public class CreditService(
    IDistributedLockFactory distributedLock,
    ApplicationDbContext dbContext,
    ILogger<CreditService> logger) : ICreditService
{
    private readonly TimeSpan _expirationDuration = TimeSpan.FromSeconds(30);
    private readonly TimeSpan _waitDuration = TimeSpan.FromSeconds(10);
    private readonly TimeSpan _retryInterval = TimeSpan.FromSeconds(1);

    public async Task<IncreasePrePaymentSumOnCreditResponse> IncreasePrePaymentSumOnCreditAndSaveAsync(int creditId,
        bool increaseCount,
        decimal rawPrepaymentAmount,
        decimal merchWageAmount,
        decimal consumerWageAmount,
        CancellationToken cancellationToken)
    {
        // blocks until acquired or 'wait' timeout
        await using var redLock = await distributedLock.CreateLockAsync(GetCreditLockKey(creditId),
            _expirationDuration,
            _waitDuration,
            _retryInterval,
            cancellationToken);

        // make sure we got the lock
        if (!redLock.IsAcquired)
        {
            logger.LogWarning("redlock did not acquired for credit: {CreditId} on increase credit prepay running sum ", creditId);
            return new IncreasePrePaymentSumOnCreditResponse(false, "redlock did not acquired on increase credit prepay running sum", 0, 0, 0, 0);
        }

        var credit = await dbContext.Credits
            .Where(c => c.Id == creditId)
            .FirstOrDefaultAsync(cancellationToken);

        if (credit == null)
        {
            logger.LogError("could not find active credit for credit: {CreditId} on increase credit prepay running sum", creditId);
            return new IncreasePrePaymentSumOnCreditResponse(false, "could not find active credit on increase credit prepay running sum", 0, 0, 0, 0);
        }

        decimal newPrepaymentSum = decimal.Add(rawPrepaymentAmount, credit.TotalRawPrePaymentAmount);
        decimal newMerchWageSum = decimal.Add(merchWageAmount, credit.TotalMerchantWageAmount);
        decimal newConsumerWageSum = decimal.Add(consumerWageAmount, credit.TotalConsumerWageAmount);
        long newPrepayCount = credit.TotalRawPrePaymentCount;

        if (increaseCount)
        {
            newPrepayCount = credit.TotalRawPrePaymentCount + 1;
        }
        credit.TotalRawPrePaymentAmount = newPrepaymentSum;
        credit.TotalMerchantWageAmount = newMerchWageSum;
        credit.TotalConsumerWageAmount = newConsumerWageSum;
        credit.TotalRawPrePaymentCount = newPrepayCount;

        await dbContext.SaveChangesAsync(cancellationToken);

        var result = new IncreasePrePaymentSumOnCreditResponse(true, null, newPrepaymentSum, newMerchWageSum, newConsumerWageSum, newPrepayCount);

        return result;
    }

    public async Task<IncreaseInstallmentPaymentSumOnCreditResponse> IncreaseInstallmentSumOnCreditAndSaveAsync(
        int creditId,
        decimal installmentPaymentAmount,
        CancellationToken cancellationToken)
    {
        // blocks until acquired or 'wait' timeout
        await using var redLock = await distributedLock.CreateLockAsync(GetCreditLockKey(creditId),
            _expirationDuration,
            _waitDuration,
            _retryInterval,
            cancellationToken);

        // make sure we got the lock
        if (!redLock.IsAcquired)
        {
            logger.LogWarning("redlock did not acquired for credit: {CreditId} on increase credit installment running sum ", creditId);
            return new IncreaseInstallmentPaymentSumOnCreditResponse(false, "redlock did not acquired on increase credit installment running sum", 0, 0);
        }

        var credit = await dbContext.Credits
            .Where(c => c.Id == creditId)
            .FirstOrDefaultAsync(cancellationToken);

        if (credit == null)
        {
            logger.LogWarning("could not find active credit for credit: {CreditId} on increase credit installment running sum", creditId);
            return new IncreaseInstallmentPaymentSumOnCreditResponse(false, "could not find active credit on increase credit installment running sum", 0, 0);
        }

        decimal newInstallmentPaymentSum = decimal.Add(installmentPaymentAmount, credit.TotalInstallmentPaymentAmount);
        long newInstallmentPayCount = credit.TotalInstallmentPaymentCount + 1;

        credit.TotalInstallmentPaymentAmount = newInstallmentPaymentSum;
        credit.TotalInstallmentPaymentCount = newInstallmentPayCount;

        await dbContext.SaveChangesAsync(cancellationToken);

        var result = new IncreaseInstallmentPaymentSumOnCreditResponse(true, null, newInstallmentPaymentSum, newInstallmentPayCount);

        return result;
    }

    public async Task<DecreaseRawPrePaymentSumOnCreditResponse> DecreaseRawPrePaymentSumOnCreditAndSaveAsync(
        int creditId,
        decimal rawPrepaymentAmount,
        CancellationToken cancellationToken)
    {
        // blocks until acquired or 'wait' timeout
        await using var redLock = await distributedLock.CreateLockAsync(GetCreditLockKey(creditId),
            _expirationDuration,
            _waitDuration,
            _retryInterval,
            cancellationToken);

        // make sure we got the lock
        if (!redLock.IsAcquired)
        {
            logger.LogWarning("redlock did not acquired for credit: {CreditId} on decrease credit prepay running sum ", creditId);
            return new DecreaseRawPrePaymentSumOnCreditResponse(false,
                "redlock did not acquired on decrease credit prepay running sum",
                0,
                0);

        }

        var credit = await dbContext.Credits.Where(c => c.Id == creditId).FirstOrDefaultAsync(cancellationToken);

        if (credit == null)
        {
            logger.LogError("could not find active credit for credit: {CreditId} on decrease credit prepay running sum", creditId);
            return new DecreaseRawPrePaymentSumOnCreditResponse(false,
                $"could not find active credit on decrease credit prepay running sum",
                0,
                0);
        }

        decimal newPrepaymentSum = decimal.Subtract(credit.TotalRawPrePaymentAmount, rawPrepaymentAmount);
        long newPrepayCount = credit.TotalRawPrePaymentCount - 1;

        credit.TotalRawPrePaymentAmount = newPrepaymentSum;
        credit.TotalRawPrePaymentCount = newPrepayCount;

        await dbContext.SaveChangesAsync(cancellationToken);

        var result = new DecreaseRawPrePaymentSumOnCreditResponse(true, null, newPrepaymentSum, newPrepayCount);

        return result;
    }


    private static string GetCreditLockKey(int creditId) => $"{ApplicationConstants.CreditDistLockKeyPrefix}-{creditId}";
}
