﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Admin._002_GetMerchantContract
{
    public record GetMerchantContractRequest(int merchantId);

    public class GetMerchantContractResponse
    {
        public GetMerchantContractResponse()
        {
                
        }
        public GetMerchantContractResponse(MerchantContractInfo merchantContract)
        {
            this.MerchantContract = merchantContract;
        }
        public MerchantContractInfo MerchantContract { get; set; }
    }

    public class MerchantContractInfo
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string UserId { get; set; }
        public ActivationStatus ActivationStatus { get; set; }
        public DateTimeOffset CreatedDate { get; set; }
        public DateTimeOffset StartDate { get; set; }
        public DateTimeOffset ExpireDate { get; set; }
        public string SignedContractFileId { get; set; }
    }

    public class GetMerchantContractEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                AdminRoutes.GetMerchantContract,
                async ([AsParameters] GetMerchantContractRequest request, IGetMerchantContractRequestHandler handler, IValidator<GetMerchantContractRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("admin")
            .WithName("GetMerchantContract")
            .WithApiVersionSet(builder.NewApiVersionSet("MerchantContract").Build())
            .Produces<GetMerchantContractResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Merchant Contract")
            .WithDescription("Get Merchant Contract")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetMerchantContractRequestHandler : IGetMerchantContractRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;

        public GetMerchantContractRequestHandler(ApplicationDbContext dbContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<GetMerchantContractResponse> HandleAsync(GetMerchantContractRequest request, CancellationToken cancellationToken)
        {
            var now = DateTimeOffset.UtcNow;

            var merchantInfoProfile = await _dbContext.MerchantInfos.AsNoTracking()
                 .Where(p => p.MerchantUserId == request.merchantId)
                 .FirstOrDefaultAsync();

            if (merchantInfoProfile == null)
            {
                return new GetMerchantContractResponse();
            }

            var merchantContract = await (
              from merchantInfo in _dbContext.MerchantInfos
              join contract in _dbContext.Contracts
              on merchantInfo.MerchantUserId equals contract.MerchantUserId
              where merchantInfo.MerchantUserId == request.merchantId && contract.ExpireDate >= now
              select new MerchantContractInfo
              {
                  FirstName = merchantInfo.MerchantInfoAgent.FirstName,
                  LastName = merchantInfo.MerchantInfoAgent.LastName,
                  ActivationStatus = contract.ActivationStatus,
                  CreatedDate = contract.CreatedAt,
                  StartDate = contract.StartDate,
                  ExpireDate = contract.ExpireDate,
                  Email = merchantInfo.Email,
                  UserId = merchantInfo.MerchantUserId.ToString(),
                  SignedContractFileId = contract.SignedContractId 
              })
              .FirstOrDefaultAsync(cancellationToken);


            return new GetMerchantContractResponse(merchantContract);
        }

    }
    public interface IGetMerchantContractRequestHandler
    {
        ValueTask<GetMerchantContractResponse> HandleAsync(GetMerchantContractRequest request, CancellationToken cancellationToken);
    }

    public class GetMerchantContractValidator : AbstractValidator<GetMerchantContractRequest>
    {
        public GetMerchantContractValidator()
        {
            RuleFor(x => x.merchantId).NotEmpty().WithResourceError(() => ValidatorDictionary.merchant_id_is_required);
        }
    }

}