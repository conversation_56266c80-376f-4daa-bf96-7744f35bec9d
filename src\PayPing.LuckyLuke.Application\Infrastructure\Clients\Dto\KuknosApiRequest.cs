﻿namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public record KuknosApiLoginRequest(string email, string password);

    public record KuknosApiAccountRequest(string user_public, string user_mobile, string user_national_id, string signature);

    public record KuknosApiIssuePromissoryRequest(
        int amount,
        string due_date,
        string description,
        KuknosApiIssuePromissoryIssuerRequest issuer,
        KuknosApiIssuePromissoryRecipientRequest recipient,
        string predefined_recipient_name
    );

    public record KuknosApiIssuePromissoryIssuerRequest(
        string national_code,
        string mobile_number,
        string iban,
        string full_name,
        string address,
        string postal_code,
        bool is_legal
    );

    public record KuknosApiIssuePromissoryRecipientRequest(
        string national_code,
        string mobile_number,
        string full_name,
        string address,
        string postal_code,
        bool is_legal
    );

    public record KuknosApiPayRequest(string callback_url);

    public record KuknosApiSignedRequest(string signed_pdf, string signer_public);

}
