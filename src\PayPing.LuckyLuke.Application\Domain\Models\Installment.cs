﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class Installment : BaseEntity<long>, IAuditableEntity, ISoftDeletableEntity
    {
        public long OrderId { get; set; }

        public Guid Code { get; set; }

        public int Number { get; set; }

        public decimal OriginalAmount { get; set; }

        public decimal InterestAmount { get; set; }


        public decimal TotalAmount { get; set; }

        // needs a background job
        public decimal DelayPenaltyAmount { get; set; }

        // gateway amount
        public decimal FinalAmount { get; set; }

        public decimal DiscountAmount { get; set; }

        public DateTimeOffset DueDate { get; set; }

        // needs a background job
        public InstallmentStatus Status { get; set; }

        public PaymentStatus PaymentStatus { get; set; }

        public DateTimeOffset? PaymentDate { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }
        public int? LastModifiedBy { get; set; }

        public bool IsDeleted { get; set; }
        public DateTimeOffset? DeletedAt { get; set; }

        public Order Order { get; set; }

        public ICollection<OrderPayment> OrderPayments { get; } = new HashSet<OrderPayment>();

    }


}
