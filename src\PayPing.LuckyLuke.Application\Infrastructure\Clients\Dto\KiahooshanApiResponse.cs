﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public record KiahooshanApiResponse<T>
    {
        public string message { get; set; }

        public string timestamp { get; set; }

        public string uniqueId { get; set; }

        public string status { get; set; } // SUCCESS

        public object error { get; set; }

        public T data { get; init; }
    }

    public record KiahooshanApiGeneralResponse(string uniqueId);

    public record KiahooshanApiVideoVerifyResponse(string uniqueId, string message);
    public record KiahooshanApiGetVideoVerifyResultResponse(
        string uniqueId,
        string message,
        bool? matching,
        bool? liveness,
        bool? spoofing,
        bool? spoofingDoubleCheck,
        string status,
        string verifyStatus,
        string errorCode,
        string verifyStatusCode); // status: COMPLETED
    public record KiahooshanApiIssueDigiSigResponse(string uniqueId, string message, string httpStatus, string docName, string status); // status = ACCEPTED
    public record KiahooshanApiIssueDigiProNoteResponse(string unSignedPdf);
    public record KiahooshanApiFinalizeResponse(string treasury, string signedPdf);
    public record KiahooshanApiSettlementResponse(string settleUniqueId, string unSignedPdf);

    public record KiahooshanApiLoginResponse(string accessToken, string refreshToken);

    public record KiahooshanApiDigitalSignatureErrorResponse(string uniqueId, string errors);
    public record KiahooshanApiPromissoryErrorResponse
    {
        public string message { get; set; }

        public string timestamp { get; set; }

        public string uniqueId { get; set; }

        public string status { get; set; } // ERROR

        public KiahooshanApiGeneralErrorResponse error { get; set; }

        public string data { get; init; }
    }

    public record KiahooshanApiGeneralErrorResponse(string message, int code);

}
