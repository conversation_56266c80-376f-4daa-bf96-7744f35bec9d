﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedDigitalSignAndContract : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "ContractIsMandatory",
                table: "Plans",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "OrderPlan_ContractIsMandatory",
                table: "Orders",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "RawContractFileId",
                table: "Orders",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RawContractFileName",
                table: "Orders",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SignatureImageFileId",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SignatureImageFileName",
                table: "MerchantInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SignatureImageFileId",
                table: "ConsumerInfos",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SignatureImageFileName",
                table: "ConsumerInfos",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "DigitalSigns",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<int>(type: "integer", nullable: false),
                    Data = table.Column<string>(type: "jsonb", nullable: true),
                    ExpiredAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    LastModifiedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DigitalSigns", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DigitalSign_UserId",
                table: "DigitalSigns",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DigitalSigns");

            migrationBuilder.DropColumn(
                name: "ContractIsMandatory",
                table: "Plans");

            migrationBuilder.DropColumn(
                name: "OrderPlan_ContractIsMandatory",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "RawContractFileId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "RawContractFileName",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "SignatureImageFileId",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "SignatureImageFileName",
                table: "MerchantInfos");

            migrationBuilder.DropColumn(
                name: "SignatureImageFileId",
                table: "ConsumerInfos");

            migrationBuilder.DropColumn(
                name: "SignatureImageFileName",
                table: "ConsumerInfos");
        }
    }
}
