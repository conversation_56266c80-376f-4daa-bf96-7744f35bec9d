﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Application.Infrastructure.Persistence.NpgSql;
using PayPing.BNPL.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class PlanEmployeeConfiguration : IEntityTypeConfiguration<PlanEmployee>
{
    public void Configure(EntityTypeBuilder<PlanEmployee> builder)
    {
        builder
            .HasIndex(i => new { i.PlanId, i.EmployeeInfoId })
            .IsUnique();
    }
}
