﻿using MassTransit;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.Integrations.AdminSDK.Interfaces;
using PayPing.Integrations.Models.RabbitExchanges;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;

namespace PayPing.LuckyLuke.Application.Shared.Services.User
{
    public class NotifyService : INotifyService
    {
        private readonly IEventService _eventService;
        private readonly IBus _bus;
        private readonly IUserService _userService;
        private readonly ILogger<NotifyService> _logger;
        private readonly BNPLOptions _bnplOptions;

        public NotifyService(IEventService eventService, IBus bus, IUserService userService, ILogger<NotifyService> logger, IOptions<BNPLOptions> bnplOptions)
        {
            _eventService = eventService;
            _bus = bus;
            _userService = userService;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
            
        }

        public async ValueTask SendCanceledByMerchantMessage(string mobileNumber, int merchantUserId, CancellationToken cancellationToken)
        {
            try
            {
                var merchInfo = await _userService.GetMerchantExtraInfoAsync(merchantUserId, cancellationToken);

                var msg = new BnplCanceledByMerchantMessage
                {
                    PhoneNumber = mobileNumber,
                    MerchantName = merchInfo.DisplayName,
                    PanelLink = _bnplOptions.ConsumerUIBaseUrl
                };

                await _eventService.SendBnplCanceledByMerchantMessage(msg);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Error while trying to send Canceled By Merchant notification");

                _logger.LogError(ex, ex.Message);
            }
        }

        public async ValueTask SendInstallmentDefaultedMessage(string mobileNumber, int merchantUserId, string delayedDays, Guid installmentCode, CancellationToken cancellationToken)
        {
            try
            {
                var merchInfo = await _userService.GetMerchantExtraInfoAsync(merchantUserId, cancellationToken);

                var msg = new BnplInstallmentDefaultedMessage
                {
                    PhoneNumber = mobileNumber,
                    MerchantName = merchInfo.DisplayName,
                    PanelLink = _bnplOptions.ConsumerUIBaseUrl,
                    DelayedDays = delayedDays,
                    InstallmentLink = UrlHelpers.BuildFullUiUrl(
                        _bnplOptions.ConsumerUIBaseUrl,
                        ConsumerRoutes.InstallmentDetailUI(installmentCode)
                    )
                };

                await _eventService.SendBnplInstallmentDefaultedMessage(msg);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Error while trying to send Installment Defaulted notification");

                _logger.LogError(ex, ex.Message);
            }
        }

        public async ValueTask SendInstallmentDelayedMessage(string mobileNumber, int merchantUserId, string amount, Guid installmentCode, CancellationToken cancellationToken)
        {
            try
            {
                var merchInfo = await _userService.GetMerchantExtraInfoAsync(merchantUserId, cancellationToken);

                var msg = new BnplInstallmentDelayedMessage
                {
                    PhoneNumber = mobileNumber,
                    MerchantName = merchInfo.DisplayName,
                    PanelLink = _bnplOptions.ConsumerUIBaseUrl,
                    Amount = amount,
                    InstallmentLink = UrlHelpers.BuildFullUiUrl(
                        _bnplOptions.ConsumerUIBaseUrl,
                        ConsumerRoutes.InstallmentDetailUI(installmentCode)
                    )
                };

                await _eventService.SendBnplInstallmentDelayedMessage(msg);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Error while trying to send Installment Delayed notification");

                _logger.LogError(ex, ex.Message);
            }
        }

        public async ValueTask SendInstallmentDueMessage(string mobileNumber, int merchantUserId, string dueDate, string amount, Guid installmentCode, CancellationToken cancellationToken)
        {
            try
            {
                var merchInfo = await _userService.GetMerchantExtraInfoAsync(merchantUserId, cancellationToken);

                var msg = new BnplInstallmentDueMessage
                {
                    PhoneNumber = mobileNumber,
                    MerchantName = merchInfo.DisplayName,
                    PanelLink = _bnplOptions.ConsumerUIBaseUrl,
                    Amount = amount,
                    DueDate = dueDate,
                    InstallmentLink = UrlHelpers.BuildFullUiUrl(
                        _bnplOptions.ConsumerUIBaseUrl,
                        ConsumerRoutes.InstallmentDetailUI(installmentCode)
                    )
                };

                await _eventService.SendBnplInstallmentDueMessage(msg);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Error while trying to installment duedate notification");

                _logger.LogError(ex, ex.Message);
            }
        }

        public async ValueTask SendMerchantCreditFinishedMessage(int userId, CancellationToken cancellationToken)
        {
            try
            {
                var msg = new BnplMerchantCreditFinishedMessage
                {
                    UserId = userId,
                };

                await _eventService.SendBnplMerchantCreditFinishedMessage(msg);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Error while trying to Send Merchant Credit Finished notification");

                _logger.LogError(ex, ex.Message);
            }
        }

        public async ValueTask SendMerchantCreditLimitMessage(int userId, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public async ValueTask SendOrderRegisterMessage(string mobileNumber, int merchantUserId, CancellationToken cancellationToken)
        {
            try
            {
                var merchInfo = await _userService.GetMerchantExtraInfoAsync(merchantUserId, cancellationToken);

                var msg = new BnplOrderRegisterMessage
                {
                    PhoneNumber = mobileNumber,
                    MerchantName = merchInfo.DisplayName,
                    PanelLink = _bnplOptions.ConsumerUIBaseUrl
                };

                await _eventService.SendBnplOrderRegisterMessage(msg);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Error while trying to send order register notification");

                _logger.LogError(ex, ex.Message);
            }
        }

        public async ValueTask SendGoToMobileMessage(string mobileNumber, string getOrderUILink, CancellationToken cancellationToken)
        {
            try
            {
                var msg = new BnplGoToMobileMessage
                {
                    PhoneNumber = mobileNumber,
                    OrderLink = getOrderUILink
                };

                await _eventService.SendBnplGoToMobileMessage(msg);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Error while trying to send go to mobile notification");

                _logger.LogError(ex, ex.Message);
            }
        }

        public async ValueTask SendBnplContractAdminApprovedMessage(int merchantUserId, string panelLink, CancellationToken cancellationToken)
        {
            try
            {
                var msg = new BnplContractAdminApprovedMessage
                {
                    UserId = merchantUserId,
                    PanelLink = panelLink,
                };

                await _eventService.SendBnplContractAdminApprovedMessage(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error while trying to send Contract Admin Approved notification");

                _logger.LogError(ex, ex.Message);
            }
        }

    }
}
