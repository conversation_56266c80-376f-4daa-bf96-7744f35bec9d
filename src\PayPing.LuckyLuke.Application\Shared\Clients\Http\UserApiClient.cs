﻿using Ardalis.GuardClauses;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.Tools.SdkBase.Types;
using System.Net.Http.Json;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Clients.Http
{
    public class UserApiClient : IUserApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonSerializerOptions;

        public UserApiClient(HttpClient httpClient)
        {
            _httpClient = Guard.Against.Null(httpClient, nameof(httpClient));
            _jsonSerializerOptions = new JsonSerializerOptions{PropertyNameCaseInsensitive = true};
        }


        public async ValueTask<ServiceResult<UserApiGetUserExtraResponse>> GetUserExtraAsync(int userId, CancellationToken cancellationToken = default)
        {
            var url = $"Admin/UserManager/GetUserExtra?userId={userId}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
             
            if (httpResponse.IsSuccessStatusCode)
            {
                var userResult = await httpResponse.Content.ReadFromJsonAsync<UserApiGetUserExtraResponse>(_jsonSerializerOptions, cancellationToken: cancellationToken);
                return new ServiceResult<UserApiGetUserExtraResponse> { Succeeded = true, SuccessResult = userResult };
            }
            else
            {
                var resp = await httpResponse.Content.ReadAsStringAsync();
                return new ServiceResult<UserApiGetUserExtraResponse>
                {
                    Succeeded = false,
                    Message = resp
                };
            }
        }

    }
}
