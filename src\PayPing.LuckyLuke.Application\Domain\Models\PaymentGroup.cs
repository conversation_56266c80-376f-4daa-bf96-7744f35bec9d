﻿using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class PaymentGroup : BaseEntity<long>, IAuditableEntity
    {
        public int ConsumerUserId { get; set; }

        public decimal TotalAmount { get; set; }

        public string PaymentCode { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }
        public int? LastModifiedBy { get; set; }

        public ICollection<OrderPayment> OrderPayments { get; } = new HashSet<OrderPayment>();

    }
}
