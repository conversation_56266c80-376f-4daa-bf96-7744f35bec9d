﻿using System.Text.Json;
using DNTPersianUtils.Core;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kiahooshan;

namespace PayPing.LuckyLuke.Application.Infrastructure.Provider
{
    public class KiahooshanDigitalSignProvider : IDigitalSignProvider
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IUserService _userService;
        private readonly ApplicationDbContext _dbContext;
        private readonly IKiahooshanDigitalSignService _kihooDigitalSignService;
        private readonly IKiahooshanApiHttpClient _kihooApi;

        public KiahooshanDigitalSignProvider(IWebHostEnvironment environment, IUserService userService, ApplicationDbContext dbContext, IKiahooshanDigitalSignService kihooDigitalSignService, IKiahooshanApiHttpClient kihooApi)
        {
            _environment = environment;
            _userService = userService;
            _dbContext = dbContext;
            _kihooDigitalSignService = kihooDigitalSignService;
            _kihooApi = kihooApi;
        }

        public DigitalSignType DigitalSignType => DigitalSignType.KiahooshanVersion1;


        public async ValueTask<string> CreateDigitalSignAsync(
            Guid? orderTrackingCode,
            long id,
            int userId,
            bool userIsConsumer,
            CancellationToken cancellationToken)
        {
            UserInfoDto userInfo = null;
            if (userIsConsumer)
            {
                userInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);
            }
            else
            {
                userInfo = await _userService.GetMerchantExtraInfoAsync(userId, cancellationToken);
            }

            var context = new KiahooshanDigitalSignContextV1
            {
                OrderTrackingCode = orderTrackingCode,
                DigitalSignId = id,
                FullNameFa = userInfo.FullName?.Trim(),
                Address = userInfo.Address?.Trim(),
                Mobile = userInfo.UserName,
                NationalCode = userInfo.NationalCode?.Trim(),
                BirthDate = userInfo.BirthDate.Value.ToShortPersianDateString(),
                PostalCode = userInfo.PostalCode?.Trim(),
                UserId = userId,
                Status = KiahooshanDigitalSignStatusV1.VideoVerify,
                Iban = userInfo.IBan?.Trim(),
            };

            return JsonSerializer.Serialize(context);
        }

        public async ValueTask<DigitalSignCheckPointResponse> PushToNextCheckPointAsync(string contextData, CancellationToken cancellationToken)
        {
            var context = JsonSerializer.Deserialize<KiahooshanDigitalSignContextV1>(contextData);

            var resp = await _kihooDigitalSignService.HandleAsync(context, cancellationToken);

            var result = resp switch
            {
                GetCertificateResult getCertificateResult => new DigitalSignCheckPointResponse(true, string.Empty, OrderSteps.IssuePromissory, getCertificateResult.signatureRefId),
                _ => throw new NotImplementedException("at PushToNextCheckPointAsync, unknown handle result")
            };

            return result;
        }

        public async ValueTask<bool> UserHasValidDigitalSignatureAsync(int userId)
        {
            return await _dbContext.DigitalSigns.AsNoTracking()
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                .AnyAsync();
        }

        public async ValueTask<DigitalSign> GetUserValidDigitalSignatureAsync(int userId, bool updateable)
        {
            if (updateable)
            {
                return await _dbContext.DigitalSigns
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                .OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync();
            }
            else
            {
                return await _dbContext.DigitalSigns.AsNoTracking()
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                .OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync();
            }
        }
        public async ValueTask<DigitalSign> GetUserInProgressOrValidDigitalSignatureAsync(int userId, bool updateable)
        {
            if (updateable)
            {
                return await _dbContext.DigitalSigns
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && x.ExpiredAt == null)
                .OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync();
            }
            else
            {
                return await _dbContext.DigitalSigns.AsNoTracking()
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && x.ExpiredAt == null)
                .OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync();
            }
        }

        public async ValueTask<(string privateKey, string certificate)> GetPdfSignatureDataAsync(int userId)
        {
            var ds = await _dbContext.DigitalSigns.AsNoTracking()
                .Where(x => x.UserId == userId && x.Type == DigitalSignType.KiahooshanVersion1 &&
                            !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                .OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync();

            if (ds == null || string.IsNullOrWhiteSpace(ds.Data))
            {
                throw new PromissoryProviderException(null, $"kihoo digital sign not found for user: {userId}", false);
            }

            var context = JsonSerializer.Deserialize<KiahooshanDigitalSignContextV1>(ds.Data);
            return new ValueTuple<string, string>(null ,context.SignatureUniqueId);
        }

        public OrderStepDto GetDigitalSignStep()
        {
            return new OrderStepDto("احراز هویت تصویری", OrderSteps.UploadVideoAndSignature, true);
        }

    }
}
