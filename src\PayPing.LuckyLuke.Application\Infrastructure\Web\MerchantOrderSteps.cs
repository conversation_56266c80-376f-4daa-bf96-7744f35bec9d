﻿using PayPing.LuckyLuke.Application.Infrastructure.Web.Attributes;
using System.ComponentModel;

namespace PayPing.LuckyLuke.Application.Infrastructure.Web
{
    public enum MerchantOrderSteps
    {
        [Description("تکمیل نشده")]
        Init = 0,
        [Description("در حال پرداخت")]
        Payment,
        [Description("تسویه شده")]
        PaidOff,
        [Description("لغو شده")]
        Canceled,
        [Description("در انتظار لغو")]
        SemiCanceledRefundInactive,
        [Description("در انتظار لغو")]
        SemiCanceledRefundBalanceDeficit,
        [Description("در انتظار لغو")]
        SemiCanceledRefundInProgress,
        [OpenApiIgnore]
        Unknown = 100
    }
}
