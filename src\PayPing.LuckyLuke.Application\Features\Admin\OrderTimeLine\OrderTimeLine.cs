﻿using Elastic.Apm.Api;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.Contracts;

namespace PayPing.LuckyLuke.Application.Features.Admin.OrderTimeLine
{
    public record OrderTimeLineRequest(int merchantUserId, Guid trackingCode);

    public class OrderTimeLineResponse
    {
        public long Id { get; set; }

        public string TrackingCode { get; set; }

        public EventLogType LogType { get; set; }

        public OrderStatus? Status { get; set; }

        public int UserId { get; set; }

        public int Code { get; set; }

        public string Name { get; set; }

        public string Message { get; set; }

        public DateTimeOffset EventDate { get; set; }
    }


    public class OrderTimeLineEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                AdminRoutes.OrderTimeLine,
                async (
                    [AsParameters] OrderTimeLineRequest request,
                    IOrderTimeLineRequestHandler handler,
                    IValidator<OrderTimeLineRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
                .RequireAuthorization("admin")
                .WithName("OrderTimeLine")
                .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
                .Produces<List<OrderTimeLineResponse>>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("OrderTime Line")
                .WithDescription("OrderTime Line")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OrderTimeLineRequestHandler : IOrderTimeLineRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;

        public OrderTimeLineRequestHandler(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async ValueTask<List<OrderTimeLineResponse>> HandleAsync(OrderTimeLineRequest request, CancellationToken cancellationToken)
        {
            var logs = await _dbContext.EventLogs.AsNoTracking()
                .Where(x => x.RefId == request.trackingCode.ToString())
                .OrderBy(x => x.CreatedAt)
                .Select(x => new OrderTimeLineResponse
                {
                    Id = x.Id,
                    TrackingCode = x.RefId,
                    LogType = x.LogType,
                    Status = x.Status,
                    UserId = x.UserId,
                    Code = x.Code,
                    Name = x.Name,
                    Message = x.Message,
                    EventDate = x.CreatedAt,
                })
                .ToListAsync();

            return logs;
        }

    }

    public interface IOrderTimeLineRequestHandler
    {
        ValueTask<List<OrderTimeLineResponse>> HandleAsync(OrderTimeLineRequest request, CancellationToken cancellationToken);
    }

    public class OrderTimeLineValidator : AbstractValidator<OrderTimeLineRequest>
    {
        public OrderTimeLineValidator()
        {
            RuleFor(x => x.merchantUserId).NotEmpty().WithResourceError(() => ValidatorDictionary.merchant_user_id_is_required); 
        }
    }
}
