﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Options;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Clients.Http
{
    public class S3ServiceApiClient : IS3ServiceApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonSerializerOptions;

        public S3ServiceApiClient(HttpClient httpClient)
        {
            _httpClient = Guard.Against.Null(httpClient, nameof(httpClient));
            _jsonSerializerOptions = new JsonSerializerOptions{PropertyNameCaseInsensitive = true};
        }


        public async ValueTask<Stream> DownloadAsync(string fileId, int userId, CancellationToken cancellationToken)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_httpClient.BaseAddress}{userId}/misc/{fileId}");
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStreamAsync(cancellationToken);
        }

        public async ValueTask<Stream> DownloadAsync(string url, CancellationToken cancellationToken)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, url);
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStreamAsync(cancellationToken);
        }
    }

}
