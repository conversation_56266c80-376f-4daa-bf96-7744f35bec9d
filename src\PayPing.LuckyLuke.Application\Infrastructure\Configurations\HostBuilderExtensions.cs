﻿using Elastic.Apm.SerilogEnricher;
using Elastic.CommonSchema.Serilog;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Core;
using Serilog.Events;
using Serilog.Sinks.Network;

namespace PayPing.LuckyLuke.Application.Infrastructure.Configurations
{
    public static class HostBuilderExtensions
    {
        public static void CustomSerilogConfigurator(HostBuilderContext context, LoggerConfiguration loggerConfiguration)
        {
            //var serilogOptions = context.Configuration.GetSection(SerilogOptions.SectionName).Get<SerilogOptions>();
            var elasticOptions = context.Configuration.Get<ElasticOptions>();

            LogEventLevel level = 
                !string.IsNullOrWhiteSpace(elasticOptions.LogLevel) &&
                Enum.TryParse(elasticOptions.LogLevel, true, out level) ? level : LogEventLevel.Information;

            loggerConfiguration
                .MinimumLevel.ControlledBy(new LoggingLevelSwitch(level))
                .Enrich.FromLogContext()
                .Enrich.WithElasticApmCorrelationInfo()
                .Enrich.WithProperty("IndexName", "payping.bnpl")
                .WriteTo.Console();
            
            if (elasticOptions.Logstash_Type == "TCP")
            {
                loggerConfiguration = loggerConfiguration
                    .WriteTo.TCPSink(
                        elasticOptions.Logstash_Address,
                        int.Parse(elasticOptions.Logstash_Port),
                        new EcsTextFormatter()
                    );
            }
            else
            {
                loggerConfiguration = loggerConfiguration
                    .WriteTo.UDPSink(
                        elasticOptions.Logstash_Address,
                        int.Parse(elasticOptions.Logstash_Port),
                        new EcsTextFormatter()
                    );
            }
        }
    }
}
