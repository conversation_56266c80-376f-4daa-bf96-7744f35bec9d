﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Application.Infrastructure.Persistence.NpgSql;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class DigitalSignConfiguration : IEntityTypeConfiguration<DigitalSign>
{
    public void Configure(EntityTypeBuilder<DigitalSign> builder)
    {
        builder.HasIndex(x => x.UserId).HasDatabaseName("IX_DigitalSign_UserId");
        builder.Property(x => x.ExpiredAt).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);

        builder.Property(nameof(DigitalSign.SignatureRefId)).HasMaxLength(128);
        builder.HasIndex(x => x.SignatureRefId).HasDatabaseName("IX_DigitalSign_SignatureRefId");

        builder
            .Property(b => b.Data)
            .HasColumnType("jsonb");
    }
}
