﻿namespace PayPing.LuckyLuke.Application.Infrastructure.Services.Context
{
    public struct KiahooshanDigitalSignContextV1
    {
        public Guid? OrderTrackingCode { get; set; }
        public long DigitalSignId { get; set; }
        public string SignatureUniqueId { get; set; }
        public int UserId { get; set; }
        public KiahooshanDigitalSignStatusV1 Status { get; set; }
        public string FullNameFa { get; set; }
        public string Address { get; set; }
        public string Mobile { get; set; }
        public string NationalCode { get; set; }
        public string Iban { get; set; }

        /// <summary>
        /// format  yyyy/mm/dd
        /// </summary>
        public string BirthDate { get; set; }
        public string PostalCode { get; set; }
        public string SelfieVideoUniqueId { get; set; }
    }

    public enum KiahooshanDigitalSignStatusV1
    {
        VideoVerify = 0,
        VideoResult = 10,
        CreateAccount = 20,
    }
}
