﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
using PayPing.LuckyLuke.Application.Shared.Clients.Http;
using System.Security.Cryptography;

namespace PayPing.LuckyLuke.Application.Shared.Services.DigitalSign.<PERSON><PERSON><PERSON>han
{
    public class KiaSignatureHandler : KiahooshanBaseD<PERSON>talSign<PERSON><PERSON><PERSON>, IKiahooshanDigitalSignService
    {

        public KiaSignatureHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKiahooshanApiHttpClient kihooApi,
            IS3ServiceApiClient s3ServiceApiClient,
            IUploadGrpcClient uploadGrpcClient,
            IMemoryCache memoryCache,
            IOptions<KiahooshanOptions> kihooOptions)
            : base(dbContext, userContext, kihooApi, kihooOptions, s3ServiceApiClient, uploadGrpcClient, memoryCache)
        {

        }

        public async ValueTask<object> HandleAsync(KiahooshanDigitalSignContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KiahooshanDigitalSignStatusV1.CreateAccount)
                throw new Exception("certificate not in CreateAccount status");

            ValidateContext(context);

            // download selfie video from storage

            var consumerInfo = await _dbContext.ConsumerInfos.AsNoTracking().Where(x => x.ConsumerUserId == context.UserId).FirstOrDefaultAsync();

            var sigVideo = await GetCachedSelfieVideoAsync(context, consumerInfo.SignatureVideoFileId, cancellationToken);


            var result = await _kihooApi.IssueDigiSig(context.BirthDate, context.Mobile, context.NationalCode, consumerInfo.SignatureVideoRandomSentence, sigVideo, consumerInfo.SignatureVideoFileName, context.SelfieVideoUniqueId, context.OrderTrackingCode, cancellationToken);

            if (result.status != ApplicationConstants.KiahooshanDigitalSignatureSuccessStatus)
            {
                throw new DigitalSignatureProviderException(context.OrderTrackingCode?.ToString(), $"kiahooshan KiaSignatureHandler not accepted with message: {result.message}", false);
            }

            context.SignatureUniqueId = result.uniqueId;


            // save context
            await UpdateContextAsync(context);

            return new GetCertificateResult(context.SignatureUniqueId);
        }

        protected override void ValidateContext(KiahooshanDigitalSignContextV1 context)
        {
            base.ValidateContext(context);
        }
    }
}
