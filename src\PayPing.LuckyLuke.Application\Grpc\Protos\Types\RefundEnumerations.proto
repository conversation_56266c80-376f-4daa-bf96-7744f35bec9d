﻿syntax = "proto3";

option csharp_namespace = "PayPing.Refund.WebApi.gRPC.Types";

package RefundGrpcServices;

enum WagePayer {
    Payer = 0; // کارمزد با پرداخت کننده
    Business = 1; // کارمزد با کسب و کار دریافت کننده پرداخت
}

enum WageType {
    Fixed = 0; // مبلغ ثابت
    Percentage = 1; // درصدی
}

enum UserInitializationType {
    AllIpgTypesAndPanInquiry = 0; // استفاده از همه درگاه های پرداخت و استفاده از اسعلام شماره کارت برای درگاه پرداخت های پشتیبانی نشده
    LimitUserToSupportedIpgTypesOnly = 1; // محدود کردن کاربر به استفاده از درگاه پرداخت های پشتیبانی شده توسط سرویس ریفاند
}

enum RefundOperationState {
    Cancelled = 0;
    Created = 1;
    PendingBlockage = 2;
    PreparedToProcess = 3;
    ProcessingRefund = 4;
    RefundFailed = 5;
    RefundSucceeded = 6;
}