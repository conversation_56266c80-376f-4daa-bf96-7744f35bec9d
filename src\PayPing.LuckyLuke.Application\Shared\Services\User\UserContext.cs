﻿using Microsoft.AspNetCore.Http;
using PayPing.BNPL.Application.Shared.Extensions;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Shared.Services.User
{
    public class UserContext : IUserContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserContext(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public int? CurrentUserId => _httpContextAccessor.HttpContext?.User?.FindFirst("sub")?.Value.ToInt();

        public string CurrentUserBusinessname => _httpContextAccessor.HttpContext?.User?.FindFirst("businessname")?.Value;

        public string CurrentUserFirstname => _httpContextAccessor.HttpContext?.User?.FindFirst("firstname")?.Value;

        public string CurrentUserLastname => _httpContextAccessor.HttpContext?.User?.FindFirst("lastname")?.Value;

        public string CurrentUserName => _httpContextAccessor.HttpContext?.User?.FindFirst("username")?.Value;

        public string CurrentUserNationalCode => _httpContextAccessor.HttpContext?.User?.FindFirst("nationalcode")?.Value;

        public string CurrentUserPersianBirthDate => _httpContextAccessor.HttpContext?.User?.FindFirst("birthday")?.Value;

        public string CurrentUserFatherName => _httpContextAccessor.HttpContext?.User?.FindFirst("fathername")?.Value;

    }
}
