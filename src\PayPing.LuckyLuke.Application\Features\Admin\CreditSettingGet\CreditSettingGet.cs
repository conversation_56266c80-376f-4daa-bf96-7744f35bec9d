﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Admin.CreditSettingGet
{
    public record CreditSettingGetRequest(int merchantUserId, int creditId);

    public class CreditSettingGetResponse
    {
        public int MerchantUserId { get; set; }
        public int CreditId { get; set; }
        public Guid WalletId { get; set; }
        public bool IsCreditActive { get; set; }
        public decimal InterestRate {  get; set; }
        public decimal MinPrePaymentRate { get; set; }
        public string PredefinedCode { get; set; }

        public decimal CreditBalanceAmount { get; set; }
        public decimal TotalSalesAmount { get; set; }
        public long TotalSalesCount { get; set; }
        public decimal TotalWagesAmount { get; set; }

        public decimal MerchantOperationCost { get; set; }
        public MerchantOperationCostStrategy MerchantOperationCostStrategy { get; set; }


        [Required]
        public List<PlanDetail> PlanDetails {  get; set; }
    }

    public class PlanDetail
    {
        public DateTimeOffset CreatedDate { get; set; }
        public bool IsActive { get; set; }
        public GuaranteeType GuaranteeType { get; set; }
        public decimal MaxCreditAmount { get; set; }
        public int InstallmentCount { get; set; }
    }

    public class CreditSettingGetEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                AdminRoutes.CreditSettingGet,
                async (
                    [AsParameters] CreditSettingGetRequest request,
                    ICreditSettingGetRequestHandler handler,
                    IValidator<CreditSettingGetRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
                .RequireAuthorization("admin")
                .WithName("CreditSettingGet")
                .WithApiVersionSet(builder.NewApiVersionSet("Credit").Build())
                .Produces<CreditSettingGetResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Credit Get Setting")
                .WithDescription("Credit Get Setting")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CreditSettingGetRequestHandler : ICreditSettingGetRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IWalletService _walletService;

        public CreditSettingGetRequestHandler(ApplicationDbContext dbContext, IWalletService walletService)
        {
            _dbContext = dbContext;
            _walletService = walletService;
        }

        public async ValueTask<CreditSettingGetResponse> HandleAsync(CreditSettingGetRequest request, CancellationToken cancellationToken)
        {
            var merchant = await _dbContext.MerchantInfos.AsNoTracking()
               .Where(m => m.MerchantUserId == request.merchantUserId)
               .FirstOrDefaultAsync();

            if(merchant == null)
            {
                throw new ArgumentException("پذیرنده یافت نشد");
            }

            var credit = await _dbContext.Credits.AsNoTracking()
               .Where(c => c.Id == request.creditId && c.Contract.MerchantUserId == request.merchantUserId)
               .Include(cc => cc.Contract)
               .OrderByDescending(cc => cc.Contract.ExpireDate)
               .FirstOrDefaultAsync();

            if (credit == null)
            {
                throw new ArgumentException("کیف پول اعتباری یافت نشد");
            }

            var plans = await _dbContext.Plans.AsNoTracking()
               .Where(p => p.CreditId == request.creditId && p.MerchantUserId == request.merchantUserId)
               .Select(p=> new PlanDetail
               {
                   CreatedDate = p.CreatedAt,
                   GuaranteeType = p.GuaranteeType,
                   InstallmentCount = p.InstallmentCount,
                   IsActive = p.IsActive,
                   MaxCreditAmount = p.MaxCreditAmount
               })
            .ToListAsync();

            var creditInfo = await _walletService.GetWalletCreditInfoAsync(credit.WalletId, cancellationToken);

            return new CreditSettingGetResponse
            {
                CreditId = credit.Id,
                MerchantUserId = merchant.MerchantUserId,
                IsCreditActive = credit.IsActive,
                WalletId = credit.WalletId,
                MinPrePaymentRate = merchant.MinPrePaymentRate,
                InterestRate = merchant.InterestRate,
                CreditBalanceAmount = creditInfo.balance,
                TotalSalesAmount = decimal.Add(creditInfo.prepaySum, creditInfo.installmentpaySum),
                TotalSalesCount = creditInfo.prepayCount,
                TotalWagesAmount = decimal.Add(creditInfo.consumerwageSum, creditInfo.merchwageSum),
                PredefinedCode = merchant.PredefinedCode,
                PlanDetails = plans,
                MerchantOperationCost = credit.Contract.OperationCost,
                MerchantOperationCostStrategy = credit.Contract.OperationCostStrategy,
            };
           
        }
    }

    public interface ICreditSettingGetRequestHandler
    {
        ValueTask<CreditSettingGetResponse> HandleAsync(CreditSettingGetRequest request, CancellationToken cancellationToken);
    }

    public class CreditSettingGetValidator : AbstractValidator<CreditSettingGetRequest>
    {
        public CreditSettingGetValidator()
        {
            RuleFor(x => x.merchantUserId).NotEmpty().WithResourceError(() => ValidatorDictionary.merchant_user_id_is_required); 
            RuleFor(x => x.creditId).NotEmpty().WithResourceError(() => ValidatorDictionary.credit_id_is_required); 
        }
    }
}
