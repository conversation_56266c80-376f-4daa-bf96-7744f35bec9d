﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedSignatureRefId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "SignatureRefId",
                table: "DigitalSigns",
                type: "character varying(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DigitalSign_SignatureRefId",
                table: "DigitalSigns",
                column: "SignatureRefId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_DigitalSign_SignatureRefId",
                table: "DigitalSigns");

            migrationBuilder.DropColumn(
                name: "SignatureRefId",
                table: "DigitalSigns");
        }
    }
}
