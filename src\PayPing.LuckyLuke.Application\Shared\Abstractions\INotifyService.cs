﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Shared.Abstractions
{
    public interface INotifyService
    {

        ValueTask SendOrderRegisterMessage(string mobileNumber, int merchantUserId, CancellationToken cancellationToken);

        ValueTask SendInstallmentDueMessage(string mobileNumber, int merchantUserId, string dueDate, string amount, Guid installmentCode, CancellationToken cancellationToken);

        ValueTask SendInstallmentDelayedMessage(string mobileNumber, int merchantUserId, string amount, Guid installmentCode, CancellationToken cancellationToken);

        ValueTask SendInstallmentDefaultedMessage(string mobileNumber, int merchantUserId, string delayedDays, Guid installmentCode, CancellationToken cancellationToken);

        ValueTask SendCanceledByMerchantMessage(string mobileNumber, int merchantUserId, CancellationToken cancellationToken);

        ValueTask SendMerchantCreditLimitMessage(int userId, CancellationToken cancellationToken);

        ValueTask SendMerchantCreditFinishedMessage(int userId, CancellationToken cancellationToken);

        ValueTask SendGoToMobileMessage(string mobileNumber, string getOrderUILink, CancellationToken cancellationToken);
        ValueTask SendBnplContractAdminApprovedMessage(int merchantUserId, string panelLink, CancellationToken cancellationToken);
    }
}
