﻿using DNTPersianUtils.Core;
using FluentValidation;
using Grpc.Core;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace PayPing.LuckyLuke.Application.Features.Merchant.InstallmentsCalendar
{
    public enum MerchantInstallmentCalendarType
    {
        [Description("ماهانه")]
        Monthly = 0,
    }

    public record InstallmentsCalendarRequest(MerchantInstallmentCalendarType type, int year, int month);


    public class InstallmentsCalendarResponse
    {
        public InstallmentsCalendarResponse()
        {
            installments = new List<InstallmentsCalendarInstallmentInfo>();
        }

        public decimal totalPaidAmount { get; set; }
        public decimal totalDueAmount { get; set; }
        public decimal totalDelayedAmount { get; set; }

        [Required]
        public List<InstallmentsCalendarInstallmentInfo> installments { get; set; }
    }

    public enum MerchantInstallmentStatus
    {
        [Description("پرداخت شده")]
        Paid = 0,
        [Description("پیش رو")]
        Due = 1,
        [Description("معوقه")]
        Delayed = 2,
        [Description("در انتظار کسر از حقوق")]
        SalaryDeduction = 3
    }

    public class InstallmentsCalendarInstallmentInfo
    {
        public string consumerName { get; set; }
        public decimal finalAmount { get; set; }
        public MerchantInstallmentStatus status { get; set; }
        public DateTimeOffset dueDate { get; set; }
        public Guid code { get; set; }
        public string duePersianDate { get; set; }

    }

    public class InstallmentDto
    {
        public string ConsumerName { get; set; }
        public decimal FinalAmount { get; set; }
        public InstallmentStatus Status { get; set; }
        public DateTimeOffset DueDate { get; set; }
        public Guid Code { get; set; }
        public Guid OrderTrackingCode { get; set; }
    }

    public class InstallmentsCalendarEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                MerchantRoutes.InstallmentsCalendar,
                async (
                    [AsParameters] InstallmentsCalendarRequest request,
                    IInstallmentsCalendarRequestHandler handler,
                    IValidator<InstallmentsCalendarRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("InstallmentsCalendar")
            .WithApiVersionSet(builder.NewApiVersionSet("Installment").Build())
            .Produces<InstallmentsCalendarResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Installments Calendar")
            .WithDescription("Installments Calendar")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class InstallmentsCalendarRequestHandler : IInstallmentsCalendarRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly BNPLOptions _bnplOptions;

        public InstallmentsCalendarRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<InstallmentsCalendarResponse> HandleAsync(InstallmentsCalendarRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;
            var prepaidOrdersStatus = OrderStatusProvider.GetPrePaid();

            var result = new InstallmentsCalendarResponse()
            {

            };

            DateTimeOffset from = DateTimeOffset.UtcNow;
            DateTimeOffset to = DateTimeOffset.UtcNow.AddMonths(1);
            var pc = new PersianCalendar();

            switch (request.type)
            {
                case MerchantInstallmentCalendarType.Monthly:

                    var pmonth = request.year.GetPersianMonthStartAndEndDates(request.month);
                    var startDay = pmonth.StartDate;
                    var endDate = pmonth.EndDate;

                    // adjust iran start of day time with utc
                    from = new DateTimeOffset(startDay.Year, startDay.Month, startDay.Day, 0, 0, 0, 0, TimeSpan.Zero).AddTicks(new TimeSpan(-3, 30, 0).Ticks);

                    // adjust iran end of day time with utc
                    to = new DateTimeOffset(endDate.Year, endDate.Month, endDate.Day, 23, 59, 59, 999, TimeSpan.Zero).AddTicks(new TimeSpan(-3, 30, 0).Ticks);

                    break;
                default:
                    throw new NotImplementedException("wrong type");
            }

            var installments = await _dbContext.Installments.AsNoTracking()
                .Where(x => x.Order.MerchantUserId == userId && x.DueDate >= from && x.DueDate <= to &&
                 prepaidOrdersStatus.Contains(x.Order.Status))
                .OrderBy(x => x.DueDate)
                .Select(x => new InstallmentDto
                {
                    ConsumerName = x.Order.ConsumerName,
                    DueDate = x.DueDate,
                    FinalAmount = x.FinalAmount,
                    Status = x.Status,
                    Code = x.Code,
                    OrderTrackingCode = x.Order.TrackingCode,
                })
                .ToListAsync();

            foreach (var item in installments)
            {
                var instvm = new InstallmentsCalendarInstallmentInfo
                {
                    code = item.Code,
                    consumerName = item.ConsumerName,
                    dueDate = item.DueDate,
                    finalAmount = item.FinalAmount,
                    duePersianDate = item.DueDate.ToShortPersianDateString()
                };

                switch (item.Status)
                {
                    case InstallmentStatus.Delayed:
                    case InstallmentStatus.Defaulted:
                        {
                            instvm.status = MerchantInstallmentStatus.Delayed;
                            result.totalDelayedAmount = decimal.Add(result.totalDelayedAmount, item.FinalAmount);
                            break;
                        }
                    case InstallmentStatus.SalaryDeductionWaiting:
                        {
                            instvm.status = MerchantInstallmentStatus.SalaryDeduction;
                            result.totalDelayedAmount = decimal.Add(result.totalDelayedAmount, item.FinalAmount);
                            break;
                        }
                    case InstallmentStatus.PaidOff:
                    case InstallmentStatus.SalaryDeductionPaidOff:
                        {
                            instvm.status = MerchantInstallmentStatus.Paid;
                            result.totalPaidAmount = decimal.Add(result.totalPaidAmount, item.FinalAmount);
                            break;
                        }
                    case InstallmentStatus.Waiting:
                    default:
                        {
                            instvm.status = MerchantInstallmentStatus.Due;
                            result.totalDueAmount = decimal.Add(result.totalDueAmount, item.FinalAmount);
                            break;
                        }
                }

                result.installments.Add(instvm);
            }

            return result;
        }

    }

    public interface IInstallmentsCalendarRequestHandler
    {
        ValueTask<InstallmentsCalendarResponse> HandleAsync(InstallmentsCalendarRequest request, CancellationToken cancellationToken);
    }

    public class InstallmentsCalendarValidator : AbstractValidator<InstallmentsCalendarRequest>
    {
        public InstallmentsCalendarValidator()
        {
            RuleFor(x => x.year).GreaterThanOrEqualTo(1403).WithResourceError(() => ValidatorDictionary.year_greater_1403);
            RuleFor(x => x.month).ExclusiveBetween(1, 12).WithResourceError(() => ValidatorDictionary.month_between_1_12);
        }
    }
}
