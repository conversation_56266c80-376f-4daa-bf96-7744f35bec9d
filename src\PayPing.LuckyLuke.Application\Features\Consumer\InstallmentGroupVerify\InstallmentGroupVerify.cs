﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Extensions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Features.Consumer.InstallmentGroupVerify
{
    public record InstallmentGroupVerifyRequest(int status, string errorCode, string data);

    public record InstallmentGroupVerifyDataRequest(string clientRefId, string paymentCode, long amount, long paymentRefId);

    public record InstallmentGroupVerifyResponse(string redirectUrl);

    public class InstallmentGroupVerifyEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.InstallmentGroupPaymentVerify,
                async (
                    [FromForm] InstallmentGroupVerifyRequest request,
                    IInstallmentGroupVerifyHandler handler,
                    IValidator<InstallmentGroupVerifyRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(string.Empty, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Extensions.Html(@$"<!doctype html>
                        <html>
                            <head><title>در حال ارسال ...</title></head>
                            <body>
                                <h3>در حال ارسال ...</h3>
                                
                            </body>
                            <script type='text/javascript'>
                                window.location.replace('{result.redirectUrl}');
                            </script>
                        </html>");
                })
            .AllowAnonymous()
            .DisableAntiforgery()
            .RequireCors(c => c.AllowAnyOrigin())
            .WithName("InstallmentGroupVerify")
            .WithApiVersionSet(builder.NewApiVersionSet("Installment").Build())
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Installment Group Verify")
            .WithDescription("Installment Group Verify")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class InstallmentGroupVerifyHandler : IInstallmentGroupVerifyHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IWalletService _walletService;
        private readonly IPaymentGrpcClient _paymentGrpcClient;
        private readonly ICreditService _creditService;
        private readonly ILogger<InstallmentGroupVerifyHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public InstallmentGroupVerifyHandler(ApplicationDbContext dbContext,
            IWalletService walletService,
            IPaymentGrpcClient paymentGrpcClient,
            ICreditService creditService,
            IOptions<BNPLOptions> bnplOptions,
            ILogger<InstallmentGroupVerifyHandler> logger)
        {
            _dbContext = dbContext;
            _walletService = walletService;
            _paymentGrpcClient = paymentGrpcClient;
            _creditService = creditService;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        /// <summary>
        /// verify payment groups which are installments of An Order
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async ValueTask<InstallmentGroupVerifyResponse> HandleAsync(InstallmentGroupVerifyRequest request, CancellationToken cancellationToken)
        {
            var data = JsonSerializer.Deserialize<InstallmentGroupVerifyDataRequest>(request.data);
            if (data == null)
            {
                _logger.LogWarning($"At InstallmentGroupVerify, bank data is null, request data: {request.data}");
                return new InstallmentGroupVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new("success", "false"), new("message", "اطلاعات ارسالی نامعتبر است"), new("amount", "0"), new("code", string.Empty)]));
            }

            var paymentGroup = await _dbContext.PaymentGroups
                .Where(x => x.PaymentCode == data.paymentCode && x.Id.ToString() == data.clientRefId)
                .Include(x => x.OrderPayments)
                .ThenInclude(x => x.Installment)
                .FirstOrDefaultAsync();

            string message = string.Empty;
            if (paymentGroup == null)
            {
                return new InstallmentGroupVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new("success", "false"), new("message", "اطلاعات ارسالی نامعتبر است"), new("amount", "0"), new("code", data.paymentCode)]));
            }

            if (paymentGroup.OrderPayments.Any(op => op.PaymentType != OrderPaymentType.Installment || !op.InstallmentId.HasValue))
            {
                return new InstallmentGroupVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new("success", "false"), new("message", "پرداخت باید از نوع پرداخت قسط باشد"), new("amount", "0"), new("code", data.paymentCode)]));
            }

            if (paymentGroup.OrderPayments.Any(op => op.PaymentStatus != OrderPaymentStatus.Init))
            {
                return new InstallmentGroupVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new("success", "false"), new("message", "اطلاعات ارسالی تکراریست"), new("amount", "0"), new("code", data.paymentCode)]));
            }


            var order = await _dbContext.Orders.Where(x => x.Id == paymentGroup.OrderPayments.First().OrderId).FirstOrDefaultAsync();
            if (order == null)
            {
                return new InstallmentGroupVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new("success", "false"), new("message", "سفارش یافت نشد"), new("amount", "0"), new("code", data.paymentCode)]));
            }

            var installmentPaymentAcceptableStatuses = OrderStatusProvider.GetInstallmentPaymentAcceptable();
            if (!installmentPaymentAcceptableStatuses.Contains(order.Status))
            {
                return new InstallmentGroupVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new("success", "false"), new("message", "سفارش در مرحله پرداخت قسط نیست"), new("amount", "0"), new("code", data.paymentCode)]));
            }

            var verifyResult = await _paymentGrpcClient.VerifyPaymentAsync(new PaymentVerifyGrpcRequest(order.MerchantUserId, data.paymentRefId, paymentGroup.PaymentCode));

            var now = DateTimeOffset.UtcNow;
            if (verifyResult != null)
            {
                foreach (var orderPayment in paymentGroup.OrderPayments.OrderBy(op => op.Installment.Number))
                {
                    var ipRunningSumResult = await _creditService.IncreaseInstallmentSumOnCreditAndSaveAsync(
                        orderPayment.CreditId,
                        orderPayment.Amount,
                        cancellationToken);

                    if (!ipRunningSumResult.Success)
                    {
                        await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                            orderPayment.CreditId,
                            orderPayment.WalletId,
                            orderPayment.Amount,
                            CreditTransactionType.InstallmentPaymentRS,
                            null,
                            ipRunningSumResult.Error);
                    }

                    if (orderPayment.Installment.Number == order.OrderPlan.InstallmentCount)
                    {
                        // if inst is last inst, we should set order to paidoff
                        order.Status = OrderStatus.PaidOff;
                    }
                    else if (order.Status != OrderStatus.InstallmentsPaymentInProgress)
                    {
                        // if order status is PrePaymentSucceeded we are at first inst paid, set to InstallmentsPaymentInProgress
                        // if order status is one of InstallmentsPaymentDelayed or InstallmentsPaymentDefaulted, set to InstallmentsPaymentInProgress
                        // if there is more delayed or defaulted installments, related jobs would set them accordingly
                        order.Status = OrderStatus.InstallmentsPaymentInProgress;
                    }

                    orderPayment.PaymentStatus = OrderPaymentStatus.Paid;
                    orderPayment.PayDate = now;
                    orderPayment.CardNumber = verifyResult.cardNumber;


                    orderPayment.Installment.Status = InstallmentStatus.PaidOff;
                    orderPayment.Installment.PaymentStatus = PaymentStatus.PaymentSucceeded;
                    orderPayment.Installment.PaymentDate = now;
                    orderPayment.Installment.DiscountAmount = orderPayment.DiscountAmount;
                }

                await _dbContext.SaveChangesAsync();

                return new InstallmentGroupVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new("success", "true"), new("message", ""), new("amount", paymentGroup.TotalAmount.ToString()), new("code", data.paymentCode)]));
            }
            else
            {
                _logger.LogWarning($"At InstallmentGroupVerify, verify failed for payment group id: {paymentGroup.Id}, order id: {order.Id}");

                foreach (var orderPayment in paymentGroup.OrderPayments.OrderBy(op => op.Installment.Number))
                {
                    orderPayment.PaymentStatus = OrderPaymentStatus.Failed;
                    orderPayment.Installment.PaymentStatus = PaymentStatus.PaymentFailed;
                }

                await _dbContext.SaveChangesAsync();

                return new InstallmentGroupVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new("success", "false"), new("message", string.Empty), new("amount", "0"), new("code", data.paymentCode)]));
            }
        }
    }

    public interface IInstallmentGroupVerifyHandler
    {
        ValueTask<InstallmentGroupVerifyResponse> HandleAsync(InstallmentGroupVerifyRequest request, CancellationToken cancellationToken);
    }

    public class InstallmentGroupVerifyValidator : AbstractValidator<InstallmentGroupVerifyRequest>
    {
        public InstallmentGroupVerifyValidator()
        {
            RuleFor(x => x.data).NotEmpty().WithResourceError(() => ValidatorDictionary.installment_verify_data_is_required);
        }
    }

}
