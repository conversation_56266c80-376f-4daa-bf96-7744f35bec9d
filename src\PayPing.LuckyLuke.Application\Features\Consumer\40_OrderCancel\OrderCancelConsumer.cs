﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer._40_OrderCancel
{
    public record OrderCancelConsumerRequest(Guid trackingCode);

    public record OrderCancelConsumerResponse(Guid trackingCode);


    public class OrderCancelConsumerEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.OrderCancelConsumer,
                async (
                    OrderCancelConsumerRequest request,
                    IOrderCancelConsumerRequestHandler handler,
                    IValidator<OrderCancelConsumerRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("OrderCancelConsumer")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<OrderCancelConsumerResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Cancel Order By Consumer")
            .WithDescription("Cancel Order By Consumer")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OrderCancelConsumerRequestHandler : IOrderCancelConsumerRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IWalletService _walletService;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IUserContext _userContext;
        private readonly ILogger<OrderCancelConsumerRequestHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public OrderCancelConsumerRequestHandler(ApplicationDbContext dbContext, IWalletService walletService, IGuaranteeService guaranteeService, IUserContext userContext, IOptions<BNPLOptions> bnplOptions, ILogger<OrderCancelConsumerRequestHandler> logger)
        {
            _dbContext = dbContext;
            _walletService = walletService;
            _guaranteeService = guaranteeService;
            _userContext = userContext;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<OrderCancelConsumerResponse> HandleAsync(OrderCancelConsumerRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.Where(o => o.TrackingCode == request.trackingCode)
                .Include(o => o.OrderGuarantees)
                .FirstOrDefaultAsync();
            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ConsumerUserId.HasValue && order.ConsumerUserId.Value != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }

            var cancelableStatuses = OrderStatusProvider.GetConsumerCancelable();
            if (!cancelableStatuses.Contains(order.Status))
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "امکان لغو سفارش وجود ندارد", string.Empty);
            }

            if (order.PlanIsLocked)
            {
                if (!string.IsNullOrWhiteSpace(order.CreditLockId))
                {
                    // unfreeze as much as credited amount
                    var unlockResult = await _walletService.UnLockCreditAndSaveAsync(
                        order.OrderPlan.WalletId,
                        order.CreditLockId,
                        order.CreditedAmount,
                        cancellationToken);

                    // add a suspecious credit on failure
                    if (!unlockResult.Success)
                    {
                        await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                            order.OrderPlan.CreditId,
                            order.OrderPlan.WalletId,
                            order.CreditedAmount,
                            CreditTransactionType.UnFreeze,
                            order.CreditLockId,
                            unlockResult.Error);
                    }
                }

                if (order.OrderGuarantees != null && order.OrderGuarantees.Any())
                {
                    var guaranteeProvider = await _guaranteeService.GetGuaranteeProviderByGuarantorId(order.OrderPlan.GuarantorId);

                    // revoke guarantee
                    var guarantee = order.OrderGuarantees.OrderByDescending(g => g.CreatedAt).First();
                    var revokeResult = await guaranteeProvider.DeleteGuaranteeAsync(guarantee.Data);
                    if (!revokeResult.success)
                    {
                        _logger.LogWarning($"could not revoke guarantee order id: {order.Id}, message: {revokeResult.error}");

                        _dbContext.OrderExternalEventOutboxes.Add(new OrderExternalEventOutbox()
                        {
                            OrderId = order.Id,
                            EventType = OrderExternalEventType.RevokeGuarantee,
                            HasFailed = true,
                            FailCount = 1,
                            Message = revokeResult.error
                        });
                    }

                    order.GuaranteeRevoked = revokeResult.success;
                }
            }

            order.Status = OrderStatus.CanceledByConsumer;

            // schedule a webhook for client with clientrefid
            if (!string.IsNullOrWhiteSpace(order.ClientCancelUrl))
            {
                _dbContext.OrderExternalEventOutboxes.Add(new OrderExternalEventOutbox()
                {
                    OrderId = order.Id,
                    ClientRefId = order.ClientRefId,
                    ClientCancelUrl = order.ClientCancelUrl,
                    EventType = OrderExternalEventType.CancelHook
                });
            }

            await _dbContext.SaveChangesAsync();

            return new OrderCancelConsumerResponse(order.TrackingCode);
        }
    }

    public interface IOrderCancelConsumerRequestHandler
    {
        ValueTask<OrderCancelConsumerResponse> HandleAsync(OrderCancelConsumerRequest request, CancellationToken cancellationToken);
    }

    public class OrderCancelConsumerValidator : AbstractValidator<OrderCancelConsumerRequest>
    {
        public OrderCancelConsumerValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}
