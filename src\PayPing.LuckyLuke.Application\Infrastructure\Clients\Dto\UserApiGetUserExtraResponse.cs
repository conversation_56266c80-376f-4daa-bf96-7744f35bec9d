﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public class UserApiGetUserExtraResponse
    {
        public int UserId { get; set; }

        public string UserName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string FatherName { get; set; }
        public string BusinessName { get; set; }
        public string NationalCode { get; set; }
        public DateTime? BirthDate { get; set; }
        public bool IsLegal { get; set; }
        public bool IsBusiness { get; set; }
        public string ClientId { get; set; }
        public string Shaba { get; set; }
        public bool? ShabaVerifyed { get; set; }
        public string State { get; set; }
        public string City { get; set; }
        public string Address { get; set; }
        public string PostalCode { get; set; }
        public string LocalPhone { get; set; }
        public bool? LocalPhoneVerified { get; set; }
        public string EconomicCode { get; set; }
        public string BNationalCode { get; set; }
        public bool? BNationalCodeLock { get; set; }
        public string CompanyName { get; set; }
        public bool? IsOfficialSubmited { get; set; }
    }
}
