﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Web;

namespace PayPing.LuckyLuke.Application.Infrastructure.Provider
{
    public interface IGuaranteeProvider
    {
        public GuaranteeType GuaranteeType { get; }
        (bool setNext, OrderSteps? next, List<(string name, OrderSteps code)> steps) GetSteps(string context);
        ValueTask<(bool success, string error)> DeleteGuaranteeAsync(string contextData);
        string GetFinalGuaranteeDocumentId(string contextData);
        string GetFinalSettlementGuaranteeDocumentId(string contextData);

    }
}
