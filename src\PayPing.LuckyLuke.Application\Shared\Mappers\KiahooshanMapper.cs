﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;

namespace PayPing.LuckyLuke.Application.Shared.Mappers
{
    public static class KiahooshanMapper
    {
        public static KiahooshanApiIssuePromissoryRequest ToApiRequest(this KiahooshanPromissoryContextV1 c, KiahooshanDigitalSignContextV1 d)
        {
            KiahooshanApiIssuePromissoryRecipientRequest recipient = new(c.Recipient.NationalCode, c.Recipient.MobileNumber, c.Recipient.FullName, c.Recipient.IsLegal);
            KiahooshanApiIssuePromissoryIssuerRequest issuer = new(d.NationalCode, d.Mobile, d.FullNameFa, c.IbanWithIR, d.Address, d.PostalCode, c.IsLegal);
            return new(c.<PERSON>ou<PERSON>, c.<PERSON>, c.Description, issuer, recipient, c.Recipient.Address);
        }
    }
}
