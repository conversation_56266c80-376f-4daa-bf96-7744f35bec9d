﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedCreditPaymentRunningSum : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "TotalConsumerWageAmount",
                table: "Credits",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalInstallmentPaymentAmount",
                table: "Credits",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<long>(
                name: "TotalInstallmentPaymentCount",
                table: "Credits",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalMerchantWageAmount",
                table: "Credits",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalRawPrePaymentAmount",
                table: "Credits",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<long>(
                name: "TotalRawPrePaymentCount",
                table: "Credits",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TotalConsumerWageAmount",
                table: "Credits");

            migrationBuilder.DropColumn(
                name: "TotalInstallmentPaymentAmount",
                table: "Credits");

            migrationBuilder.DropColumn(
                name: "TotalInstallmentPaymentCount",
                table: "Credits");

            migrationBuilder.DropColumn(
                name: "TotalMerchantWageAmount",
                table: "Credits");

            migrationBuilder.DropColumn(
                name: "TotalRawPrePaymentAmount",
                table: "Credits");

            migrationBuilder.DropColumn(
                name: "TotalRawPrePaymentCount",
                table: "Credits");
        }
    }
}
