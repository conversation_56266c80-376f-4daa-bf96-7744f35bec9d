﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kuknos;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kuknos
{
    public class DoneHandler : KuknosBaseHandler, IKuknosPromissoryService
    {
        private readonly BNPLOptions _bnplOptions;

        public DoneHandler(
            IOptions<BNPLOptions> bnplOptions,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<object> HandleAsync(KuknosPromissoryContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosPromissoryStatusV1.Done)
                throw new Exception("promissory not in done status");

            ValidateContext(context);

            return new DoneResult(JsonSerializer.Serialize(new { treasuryId = context.PromissoryTreasuryId, signedPdf = context.PromissoryFinalDocumentFileId }));
        }

        protected override void ValidateContext(KuknosPromissoryContextV1 context)
        {
            base.ValidateContext(context);
            Guard.Against.NullOrEmpty(context.PromissoryFinalDocumentHash);

        }
    }
}
