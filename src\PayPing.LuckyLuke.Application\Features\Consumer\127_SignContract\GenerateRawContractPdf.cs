﻿using System.Globalization;
using DNTPersianUtils.Core;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Services.User;
using QuestPDF;
using QuestPDF.Drawing;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using Document = QuestPDF.Fluent.Document;

namespace PayPing.LuckyLuke.Application.Features.Consumer._127_SignContract;

public interface IRawContractPdfGenerator
{
    Task<Stream> GenerateAsync(Order order, CancellationToken cancellationToken = default);
}

public class RawContractPdfGenerator : IRawContractPdfGenerator
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IUserService _userService;

    public RawContractPdfGenerator(ApplicationDbContext dbContext, IUserService userService)
    {
        _dbContext = dbContext;
        _userService = userService;

        // Set QuestPDF license
        Settings.License = LicenseType.Community;

        // Register fonts
        var fontPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts", "Vazirmatn-Regular.ttf");

        FontManager.RegisterFont(File.OpenRead(fontPath));
    }

    public async Task<Stream> GenerateAsync(Order order, CancellationToken cancellationToken = default)
    {
        // Load necessary data
        var consumer = await _dbContext.ConsumerInfos
                           .AsNoTracking()
                           .FirstOrDefaultAsync(c => c.ConsumerUserId == order.ConsumerUserId, cancellationToken)
                       ?? throw new ArgumentException("اطلاعات مشتری یافت نشد");

        var merchant = await _dbContext.MerchantInfos
                           .AsNoTracking()
                           .Where(m => m.MerchantUserId == order.MerchantUserId)
                           .FirstOrDefaultAsync(cancellationToken)
                       ?? throw new ArgumentException("اطلاعات فروشنده یافت نشد");

        var merchantExtraInfo = await _userService.GetMerchantExtraInfoAsync(order.MerchantUserId, cancellationToken) ?? throw new ArgumentException("اطلاعات فروشنده یافت نشد");

        var plan = order.OrderPlan;

        var merchantName = merchantExtraInfo.DisplayName ?? string.Empty;
        var merchantNationalCode = merchantExtraInfo.DisplayNationalCode ?? string.Empty;
        var merchantWebSite = order.ClientCallbackUrl.ExtractDomain();
        var merchantAddress = merchant.Address ?? string.Empty;

        var consumerName = $"{consumer.FirstName} {consumer.LastName}";
        var consumerFatherName = consumer.FatherName ?? "";
        var consumerNationalCode = consumer.NationalCode ?? "";
        var consumerAddress = consumer.Address ?? "";

        var installment = order.Installments.FirstOrDefault();
        
        // Format currency values
        var totalAmount = order.OrderTotalAmount.ToString("N0", new CultureInfo("fa-IR"));
        var prePayment = order.PrePaymentAmount.ToString("N0", new CultureInfo("fa-IR"));
        var creditAmount = order.CreditedAmount.ToString("N0", new CultureInfo("fa-IR"));
        var installmentAmount = installment?.TotalAmount.ToString("N0", new CultureInfo("fa-IR"));

        // Format dates
        var shortPersianDateNow = DateTimeOffset.UtcNow.ToShortPersianDateString();
        var firstInstallmentDueDate = installment?.DueDate.ToShortPersianDateString() ?? "";
        var dayOfMonth = installment?.DueDate.GetPersianDayOfMonth();

        // Define font names
        const string fontInternalName = "Vazirmatn";

        var memoryStream = new MemoryStream();

        Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);

                    page.Content()
                        .ContentFromRightToLeft()
                        .DefaultTextStyle(style => style
                            .FontFamily(fontInternalName)
                            .FontSize(10)
                            .Light()
                            .DirectionFromRightToLeft())
                        .PaddingVertical(20)
                        .Column(column =>
                        {
                            // Title
                            column.Item()
                                .AlignCenter()
                                .Text(text =>
                                {
                                    text.Span("قرارداد فروش اقساطی")
                                        .FontSize(13)
                                        .Bold();
                                });

                            // Contract parties section
                            column.Item().PaddingTop(20)
                                .Text(text =>
                                {
                                    text.Span("ماده‌ی 1) طرفین قرارداد:")
                                        .Bold();
                                });

                            // Merchant info
                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span($@"1-1) شركت / آقای / خانم {merchantName} به شماره ملی {merchantNationalCode} ، به نشانی: {merchantAddress} که از این پس در این قرارداد «فروشنده» نـامیـده می‌شود.");
                                });

                            // Consumer info
                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span($@"2-1) آقای/خانم {consumerName} ، فرزند {consumerFatherName} به شماره ملی {consumerNationalCode} ، به نشانی: {consumerAddress} ،که از این پس در این قرارداد «خریدار» نـامیـده می‌شود.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"تبصره‌ی 1) هویت «خریدار» از طریق اطلاعات اعلامی در زمان تشکیل حساب کاربری نیز بررسی و تأیید می‌گردد و لذا «خریدار» موظف است جهت ایجاد حساب کاربری و احراز هویت، کد‌ملی و تاریخ تولد خود را جهت استعلام و مطابقت با شماره‌ی تلفن همراه ثبت‌شده و اعلام شده و همچنین در صورت نیاز، علاوه بر موارد مذکور، سریال پشت کارت ملی، کد‌پستی، شماره‌ی شبا و غیره خود را جهت تکمیل اطلاعات، احراز هویت، صدور گواهی امضای دیجیتال، تدوین قرارداد و انجام هر امری که از نظر «پی‌پینگ» یا «فروشنده» لازم باشد و اقدامات مرتبط با انجام تعهدات، را به درستی اعلام و ثبت نماید. این اطلاعات، با حفظ شرایط محرمانگی اطلاعات، جهت احراز هویت «خریدار» و سایر موارد فوق‌الذکر استفاده می‌شود.");
                                });

                            // Contract subject section
                            column.Item().PaddingTop(20)
                                .Text(text =>
                                {
                                    text.Span("ماده‌ی 2) موضوع و مدت قرارداد:")
                                        .Bold();
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span($@"2-1) موضوع قرارداد عبارت است خرید کالا یا خدمات توسط «خریدار» از «فروشنده» طبق موارد اعلامی در .حساب کاربری و فاکتور دیجیتالی ارائه شده، با استفاده از سرویس  «خرید اقساطی» در بستر پلتفرم ارائه شده از سوی «پی‌پینگ»، از وبسایت «فروشنده» به آدرس {merchantWebSite} .");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"تبصره‌ی 1) «پی‌پینگ» صرفاً به عنوان تسهیل‌گر و دروازه‌ی ارتباط «فروشنده» با «خریدار»، ارائه‌ی کالا و خدمات و دریافت و پرداخت اقساط مورد توافق ایشان بوده و لذا طرفین مطلع و موافق هستند که «پی‌پینگ» هیچ مسئولیتی در قبال تعهدات، کالا، محصولات و خدمات ارائه شده توسط «فروشنده»  و همچنین تعهدات «خریدار»، اقساط تعهد شده، وصول مطالبات و غیره ندارد. لازم به ذکر است که صدور چک یا سایر وثایق و تضامین در وجه «پی‌پینگ»، تحویل آن‌ها به «پی‌پینگ» و وجود آن‌ها در ید «پی‌پینگ»، به هیچ‌وجه به معنای مسئولیت «پی‌پینگ» در خصوص کالا یا خدمت خریداری شده و نیز بازپرداخت اقساط نبوده و حقی برای «فروشنده» یا «خریدار» نسبت به «پی‌پینگ» ایجاد نمی‌نماید.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span($@"2-2) شروع این قرارداد از مورخ {shortPersianDateNow} لغایت پرداخت آخرین قسط یا وصول آن به هر صورتی، خواهد بود.");
                                });

                            // Payment details section
                            column.Item().PaddingTop(20)
                                .Text(text =>
                                {
                                    text.Span("ماده‌ی 3) مبلغ قرارداد و نحوه‌ی پرداخت:")
                                        .Bold();
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span($@"مبلغ قرارداد برابر است با جمع مبالغ مندرج در فاکتور مثبوت در حساب کاربری خریدار، به میزان {totalAmount} ريال که مورد اطلاع طرفین می‌باشد و به شرح ذیل پرداخت خواهد شد:");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span($@"3-1) مبلغ {prePayment} ريال، به عنوان پیش‌پرداخت خرید انجام گرفته که همزمان با تکمیل سفارش، توسط «خریدار» نقداً پرداخت می‌گردد.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span($@"3-2) مبلغ {creditAmount} ريال، الباقی مبلغ خرید که طی {plan.InstallmentCount} فقره قسط ماهانه، هر یک به مبلغ {installmentAmount} ريال، در روز {dayOfMonth} هر ماه پرداخت خواهد شد. سررسید اولین قسط، به تاریخ {firstInstallmentDueDate} می‌باشد.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"تبصره‌ی 2) فاکتورهای دیجیتالی مثبوت در حساب کاربری «خریدار» و مندرجات آن‌ها، جزء لاینفک این قرارداد محسوب می‌گردند.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"تبصره‌ی 3) شرایط استرداد و مرجوعی کالا یا خدمت دریافت شده، مطابق با قوانین و مقررات جاری کشور و همچنین مقررات مثبوت در بخش قوانین و مقررات وبسایت «فروشنده» می‌باشد و «پی‌پینگ» مسئولیتی در قبال آن (از جمله در قبال استرداد کالا و یا استرداد وجوه پرداختی) ندارد و تضامین تحویلی به پی‌پینگ، صرفاً پس از تأیید «فروشنده» و با درخواست کتبی وی، قابل استرداد می‌باشد.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"تبصره‌ی 4) در جهت کنترل و مدیریت ریسک فروش اقساطی، ممکن است که «فروشنده»، یک یا چند فقره چک یا سفته (بابت ضمانت دریافت اعتبار خرید اقساطی) از خریدار اخذ نماید و «فروشنده» اختیار دارد تا در صورت تخلف «خریدار» از انجام تعهدات قراردادی، نسبت به وصول مطالبات، وجه التزام‌های قراردادی و خسارات مربوطه از طریق این تضامین اقدام نماید.");
                                });


                            // Terms and conditions
                            column.Item().PaddingTop(20)
                                .Text(text =>
                                {
                                    text.Span("ماده‌ی 4) شروط ضمن قرارداد و تعهدات طرفین:")
                                        .Bold();
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-1) «فروشنده» متعهد است تا کالای خریداری شده توسط خریدار را پس از دریافت مبالغ نقدی و هزینه‌های اعلامی و همچنین انجام تعهدات «خریدار» (از قبیل صدور و تحویل تضامین اقساط)، به صورت کامل و صحیح و سالم، به طریق مقتضی تحویل وی نموده و از هرگونه تأخیر غیر موجه در انجام این تعهد پرهیز نماید.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-2) «خریدار» مکلف است تا مطابق توافق انجام گرفته، علاوه بر پرداخت مبالغ پیش‌پرداخت و هزینه‌های اعلام شده از سوی «فروشنده»، ضمن تنظیم و امضاء قراردادهای مربوطه، تضامین و وثایق مورد توافق را طبق موارد اعلامی، صادر و حسب مورد، تحویل «پی‌پینگ» یا «فروشنده» نماید. بدیهیست که نهایی گشتن فروش، منوط به انجام کامل مراحل فوق و تحویل وثایق است.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-3) در صورت عدم پرداخت هریک از اقساط مورد توافق در سررسید مقرر و گذشت مدت 10 روز از تاریخ سررسید و عدم پرداخت در این مدت، کلیه‌ی دیون مؤجل و اقساط آینده‌ی «خریدار»، تبدیل به دین حال گشته و لذا «فروشنده» و «پی‌پینگ»، حسب مورد به صورت مجتمعاً و یا منفرداً، حق دارند تا نسبت به وصول دیون پرداخت نشده توسط «خریدار» از هر طریق مقتضی (اعم از وثایق، چک، سفته، قراردادها و غیره) اقدام نمایند. بدیهیست که پرداخت هرگونه هزینه‌ی مترتبه بر اقدامات لازم جهت وصول مطالبات بر عهده‌ی «خریدار» (بدهکار) بوده و همچنین خسارات ناشی از تأخیر در تأدیه‌ی دیون نیز بر ایشان بار می‌گردد.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-4) وثایق و اسناد تجاری صادره بابت این قرارداد، پس از پرداخت کامل اقساط و تسویه‌ی تمامی حساب‌ها و بدهی‌های «خریدار»، به ایشان مسترد خواهد شد و چنان‌چه این اسناد و وثایق نزد «پی‌پینگ» باشد، صرفاً با درخواست و اعلام کتبی «فروشنده»، قابل استرداد توسط «پی‌پینگ» خواهند بود.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-5) طرفین با امضاء این قرارداد، مطلع گردیدند و توافق نمودند که با توجه به اینکه وظیفه و نقش «پی‌پینگ»، صرفاً تسهیل‌گیری در خرید و فروش کالا و دریافت و پرداخت اقساطی است و «فروشنده» با استفاده از این پلتفرم، کالا یا خدمات خود را با شرایط اقساطی به «خریداران» ارائه می‌نماید و تعهد «پی‌پینگ»، صرفاً تسهیل‌گری در ارائه و دریافت تسهیلات با هدف فروش اقساطی توسط «فروشنده» و خرید اقساطی توسط «خریدار» می‌باشد، لذا لازم به توجه است که:");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-5-1) پی‌پینگ به هیچ عنوان اطلاعی از نوع خرید «خریدار» از «فروشنده» و کالا یا خدمات ارائه شده، ندارد و مسئولیت آن تماماً با «فروشنده و خریدار» است. همچنین مسئولیت هر گونه مغایرت در ارائه‌ی کالا یا خدمات، تحویل به موقع کالا یا خدمات، اصالت و گارانتی کالا و پرداخت به موقع پیش‌پرداخت و اقساط، بر عهده‌ی «فروشنده» و «خریدار» می‌باشد و «پی‌پینگ» هیچ‌گونه تعهد و مسئولیتی اعم از حقوقی، جزائی و مالی، نسبت به این موارد نخواهند داشت؛");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-5-2) روابط قراردادی و غیرقراردادی «فروشنده» با «خریدار» که خارج از موضوع و اطلاعات این قرارداد باشد، از قبیل تراکنش مالی و تخفیف یا اضافه پرداخت و غیره، ارتباطی با «پی‌پینگ» ندارد؛");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-5-3) نظر به عدم اطلاع «پی‌پینگ» از فرآیند خرید و فروش و روابط میان فروشنده و خریدار، پی‌پینگ به هیچ عنوان تعهد و مسئولیتی در خصوص وصول مطالبات، پرداخت اقساط و سایر موارد از این دست و به طور کلی هر امر دیگر ناشی از ارائه و یا فروش کالا و خدمات توسط فروشنده به خریدار، ندارد؛");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-5-4) بدیهی است در صورت تأخیر در پرداخت اقساط و یا عدم پرداخت اقساط و مطالبات توسط خریدار،‌ عدم دسترسی به خریدار،‌ وقوع قوه‌ی قاهره از قبیل سیل، زلزله،‌ جنگ، شیوع بیماری و غیره و مانند آن، «فروشنده» و «خریدار» حق مطالبه‌ی هیچ‌گونه خسارت احتمالی از پی‌پینگ را ندارند؛");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-5-5) چنان‌چه به هر علتی (اعم از الزامات و محدودیت‌های سیستمی در صدور، درخواست فروشنده و یا هر علت دیگر) تضامین دریافتی از خریداران، به نام «پی‌پینگ» (شرکت ماناتدبیرآواتک) صادر و در سیستم بانکی ثبت گردند، به هیچ‌وجه و به هیچ میزانی موجب مسئولیت «پی‌پینگ» در قبال بازپرداخت اقساط نخواهد بود. لیکن با توجه به ثبت تضامین به نام «شرکت ماناتدبیرآواتک»، در صورت عدم بازپرداخت اقساط توسط خریدار و درخواست کتبی «فروشنده»، «پی‌پینگ» همکاری لازمه جهت اجرای تضامین و وصول مطالبات «فروشنده» از خریدار یا ضامن را خواهد نمود.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-6) استرداد کالا براساس قوانین تجارت الکترونیک و توافقات احتمالی با خریدار است و «فروشنده» متعهد به رعایت این قوانین و توافقات می‌باشد. همچنین درصورتی‌که بازگشت، مرجوعی یا استرداد سفارش اقساطی، طبق قانون انجام گیرد، «فروشنده» موظف است مبلغ نقدی پرداخت شده توسط کاربر که متعلق به این خرید است را به حساب بانکی «خریدار» بازگشت داده و تضامین احتمالی دریافت شده از خریدار (اعم از چک یا سفته) را حسب مورد مسترد یا از اعتبار ساقط نماید.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-7) اعتبار تخصیص یافته در خرید اقساطی، صرفاً قابل استفاده در سفارش کالا و فروش اقساطی یا اعتباری کالا است و قابلیت واریز نقدی به حساب بانکی «خریدار» (تحت هیچ عنوان و با هیچ روشی) را نخواهد داشت. همچنین در صورت لغو سفارش خرید، مبلغ اعتبار تخصیص داده شده، قابلیت پرداخت نقدی یا اعتباری به خریدار را ندارد و «خریدار»، حق طرح هرگونه ادعا یا اعتراضی در این خصوص را از خود سلب و ساقط نمود.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span(@"4-8) محاسبه‌ی اقساط و زمان تودیع آن‌ها از لحظه‌ی خرید شروع شده و مطابق با تاریخ‌های اعلام شده در ماده‌ی «3» این قرارداد خواهد بود و ارتباطی با زمان تحویل سفارش به «خریدار» ندارد.");
                                });

                            column.Item().PaddingTop(10)
                                .Text(text =>
                                {
                                    text.Span($@"4-9) كليه‌ی تذکرات و قوانین مندرج در قسمت قوانين و مقررات وبسايت «پی‌پینگ» به آدرس payping.ir ، و همچنین قوانین و مقرراتی که هنگام ایجاد حساب کاربری «پی‌پینگ» ارائه داده می‌شوند و نیز قوانین و مقررات مندرج در وبسایت «فروشنده» به آدرس: {merchantWebSite} ، جزئي از اين قرارداد تلقي شده و «طرفین» متعهد به رعایت آن‌ها همچون مفاد این قرارداد می‌باشد. همچنین در صورت بروز تغییر در قوانین یا ملاحظات «پی‌پینگ»، قوانین جدید و تغییرات یافته، در این قرارداد جاری می‌باشند.");
                                });
                            
                            // Signatures section
                            column.Item()
                                .DefaultTextStyle(x => x.FontSize(8).ExtraLight())
                                .PaddingTop(55)
                                .AlignCenter()
                                .Table(table =>
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn();
                                        columns.RelativeColumn();
                                        columns.RelativeColumn();
                                    });

                                    // Header
                                    table.Header(header =>
                                    {
                                        header.Cell().BorderBottom(0.3f).Padding(8).Text("خریدار").AlignRight();
                                        header.Cell().Text(string.Empty);
                                        header.Cell().BorderBottom(0.3f).Padding(8).Text("فروشنده").AlignRight();
                                    });

                                    // Content
                                    table.Cell().Padding(8).Text(consumerName).AlignRight();
                                    table.Cell().Text(string.Empty);
                                    table.Cell().Padding(8).Text(merchantName).AlignRight();
                                });
                        });

                    page.Footer()
                        .DefaultTextStyle(x => x.FontFamily(fontInternalName).ExtraLight())
                        .AlignCenter()
                        .Text(text =>
                        {
                            text.CurrentPageNumber().FontSize(6);
                            text.Span(" / ").FontSize(6);
                            text.TotalPages().FontSize(6);
                        });
                });
            })
            .GeneratePdf(memoryStream);

        memoryStream.Position = 0;
        return memoryStream;
    }
}