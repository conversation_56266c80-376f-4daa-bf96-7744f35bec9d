﻿namespace PayPing.BNPL.Domain.Models
{
    // value object
    // should be configured on ef as ComplexType
    public readonly record struct OrderCancellation
    {
        public OrderCancellation(bool isRefundNeeded, bool isRefundActive, bool isRefundBalanceEnough, string refundRefId, string reason, string description, DateTimeOffset canceledDate)
        {
            IsRefundNeeded = isRefundNeeded;
            IsRefundActive = isRefundActive;
            IsRefundBalanceEnough = isRefundBalanceEnough;
            RefundRefId = refundRefId;
            Reason = reason;
            Description = description;
            CanceledDate = canceledDate;
        }

        public bool IsRefundNeeded { get; init; }
        public bool IsRefundActive { get; init; }
        public bool IsRefundBalanceEnough { get; init; }
        public string RefundRefId { get; init; }
        public string Reason { get; init; }
        public string Description { get; init; }
        public DateTimeOffset CanceledDate { get; init; }
    }


}
