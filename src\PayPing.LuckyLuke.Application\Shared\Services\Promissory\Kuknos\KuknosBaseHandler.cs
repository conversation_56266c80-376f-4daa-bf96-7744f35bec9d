﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kuknos
{
    public abstract class KuknosBaseHandler
    {
        protected readonly ApplicationDbContext _dbContext;
        protected readonly IUserContext _userContext;
        protected readonly IKuknosApiHttpClient _kuknosApi;
        protected readonly KuknosOptions _kuknosOptions;
        protected KuknosBaseHandler(ApplicationDbContext dbContext, IUserContext userContext, IKuknosApiHttpClient kuknosApi, IOptions<KuknosOptions> kuknosOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _kuknosApi = kuknosApi;
            _kuknosOptions = kuknosOptions.Value;
        }

        protected string DigitalSignContextString { get; set; }

        //protected string RSASign(RSA rsa, string content)
        //{
        //    Guard.Against.NullOrEmpty(content, "signature content");
        //    byte[] bytes = Encoding.UTF8.GetBytes(content);
        //    return Convert.ToBase64String(rsa.SignData(bytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1));
        //}

        protected async ValueTask<int> UpdateContextAsync(KuknosPromissoryContextV1 context)
        {
            var og = await _dbContext.OrderGuarantees.FindAsync(context.OrderGuaranteeId);

            Guard.Against.Null(og);

            og.Data = JsonSerializer.Serialize(context);

            return await _dbContext.SaveChangesAsync();
        }

        protected async ValueTask<int> ResetOrderPromissoryToRenewIssue(KuknosPromissoryContextV1 context)
        {
            if (context.Status == KuknosPromissoryStatusV1.Done)
            {
                return 0;
            }

            if (!string.IsNullOrWhiteSpace(context.PromissoryId))
            {
                var deleteResult = await _kuknosApi.DeleteAsync(context.PromissoryId, context.OrderTrackingCode);
            }

            var ogs = await _dbContext.OrderGuarantees.Where(g => g.OrderId == context.OrderId).ToListAsync();

            _dbContext.RemoveRange(ogs);

            return await _dbContext.SaveChangesAsync();
        }

        protected async ValueTask<KuknosDigitalSignContextV1> GetValidDigitalSignAsync(int userId)
        {
            if (string.IsNullOrEmpty(DigitalSignContextString))
            {
                var ds = await _dbContext.DigitalSigns.AsNoTracking()
                    .Where(x => x.UserId == userId && x.Type == DigitalSignType.KuknosVersion1 && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                    .OrderByDescending(x => x.CreatedAt)
                    .FirstOrDefaultAsync();

                if (ds == null || string.IsNullOrWhiteSpace(ds.Data))
                {
                    throw new PromissoryProviderException(null, $"kuknos digital sign not found for user: {userId}", false);
                }

                DigitalSignContextString = ds.Data;
            }

            return JsonSerializer.Deserialize<KuknosDigitalSignContextV1>(DigitalSignContextString);
        }

        protected virtual void ValidateContext(KuknosPromissoryContextV1 context)
        {
            Guard.Against.Null(context);

            Guard.Against.Default<int>(context.Amount);
            Guard.Against.NullOrEmpty(context.DueDate);
            Guard.Against.NullOrEmpty(context.IbanWithIR);


            Guard.Against.Null(context.Recipient);
            Guard.Against.NullOrEmpty(context.Recipient.NationalCode);
            Guard.Against.NullOrEmpty(context.Recipient.MobileNumber);
            Guard.Against.NullOrEmpty(context.Recipient.FullName);
            Guard.Against.NullOrEmpty(context.Recipient.Address);
            Guard.Against.NullOrEmpty(context.Recipient.PostalCode);

            Guard.Against.NullOrEmpty(context.PredefinedRecipientName);

            Guard.Against.Default<long>(context.OrderGuaranteeId);
            Guard.Against.Default<int>(context.RecipientUserId);
            Guard.Against.Default<int>(context.ConsumerUserId);
            Guard.Against.Expression<KuknosPromissoryContextV1>(x => x.ConsumerUserId != _userContext.CurrentUserId.Value, context, $"consumer differs from current user, current user: {_userContext.CurrentUserId.Value}, consumer: {context.ConsumerUserId}");

        }


    }
}
