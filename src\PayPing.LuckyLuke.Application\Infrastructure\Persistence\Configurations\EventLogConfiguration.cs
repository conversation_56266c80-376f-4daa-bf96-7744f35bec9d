﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;
using System.Reflection.Emit;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class EventLogConfiguration : IEntityTypeConfiguration<EventLog>
{
    public void Configure(EntityTypeBuilder<EventLog> builder)
    {
        builder.Property(nameof(EventLog.Message)).HasMaxLength(10000);
        builder.Property(nameof(EventLog.Name)).HasMaxLength(1024);
        builder.Property(nameof(EventLog.RefId)).HasMaxLength(128);

        builder.HasIndex(x => x.RefId).HasDatabaseName("IX_EventLog_RefId");

    }
}
