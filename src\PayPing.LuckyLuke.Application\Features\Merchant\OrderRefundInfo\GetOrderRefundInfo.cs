﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Features.Merchant.OrderRefundInfo
{
    public record GetOrderRefundInfoRequest(Guid trackingCode);

    public class GetOrderRefundInfoResponse
    {
        public GetOrderRefundInfoResponse(decimal prePaymentRefundableAmount, bool refundIsActive, bool prePaymentIsRefundable)
        {
            PrePaymentRefundableAmount = prePaymentRefundableAmount;
            RefundIsActive = refundIsActive;
            PrePaymentIsRefundable = prePaymentIsRefundable;
        }

        public bool PrePaymentIsRefundable { get; set; }
        public decimal PrePaymentRefundableAmount { get; set; }
        public bool RefundIsActive { get; set; }
    }

    public class GetOrderRefundInfoEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                MerchantRoutes.GetOrderRefundInfo,
                async (
                    [AsParameters] GetOrderRefundInfoRequest request,
                    IGetOrderRefundInfoRequestHandler handler,
                    IValidator<GetOrderRefundInfoRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetOrderRefundInfo")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<GetOrderRefundInfoResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Order Refund Info")
            .WithDescription("Get Order Refund Info")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetOrderRefundInfoRequestHandler : IGetOrderRefundInfoRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly IRefundGrpcClient _refundGrpcClient;
        private readonly BNPLOptions _bnplOptions;

        public GetOrderRefundInfoRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IRefundGrpcClient refundGrpcClient, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _refundGrpcClient = refundGrpcClient;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<GetOrderRefundInfoResponse> HandleAsync(GetOrderRefundInfoRequest request, CancellationToken cancellationToken)
        {
            int merchUserId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.AsNoTracking().Where(o => o.TrackingCode == request.trackingCode)
                .FirstOrDefaultAsync();

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.MerchantUserId != merchUserId)
            {
                throw new ArgumentException("سفارش متعلق به پذیرنده نیست");
            }

            var beforeStatuses = OrderStatusProvider.GetMerchantBeforePaymentCancelable();
            var afterStatuses = OrderStatusProvider.GetMerchantAfterPaymentCancelable();

            if (!beforeStatuses.Contains(order.Status) && !afterStatuses.Contains(order.Status))
            {
                return new GetOrderRefundInfoResponse(decimal.Zero, false, false);
            }

            var refundInfo = await _refundGrpcClient.GetUserConfigurationAsync(
                new Refund.WebApi.gRPC.GetUserConfigurationRequest() 
                {
                    BusinessId = order.MerchantUserId,
                    UserId = order.MerchantUserId
                },
                cancellationToken);

            switch (refundInfo)
            {
                case RefundGrpcUserConfigurationResponseSuccess refundGrpcUserConfigurationResponseSuccess:
                    return new GetOrderRefundInfoResponse(order.PrePaymentAmount, true, order.PrePaymentAmount != decimal.Zero);
                case RefundGrpcResponseError refundGrpcResponseError:
                    return new GetOrderRefundInfoResponse(order.PrePaymentAmount, false, order.PrePaymentAmount != decimal.Zero);
                default:
                    throw new Exception("refund service is not accessible");
            }
        }
    }

    public interface IGetOrderRefundInfoRequestHandler
    {
        ValueTask<GetOrderRefundInfoResponse> HandleAsync(GetOrderRefundInfoRequest request, CancellationToken cancellationToken);
    }

    public class GetOrderRefundInfoValidator : AbstractValidator<GetOrderRefundInfoRequest>
    {
        public GetOrderRefundInfoValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}
