﻿namespace PayPing.LuckyLuke.Application.Infrastructure.Exceptions;

/// <summary>
/// Exception thrown when a distributed lock cannot be acquired for a wallet operation
/// </summary>
/// <remarks>
/// Creates a new instance of WalletLockAcquisitionException
/// </remarks>
/// <param name="walletId">The ID of the wallet for which the lock could not be acquired</param>
/// <param name="operationName">The name of the operation that was attempted</param>
public class WalletLockAcquisitionException(
   Guid walletId,
   string operationName) : BaseException(walletId.ToString(), $"Could not acquire lock for wallet {walletId} on {operationName}")
{


    /// <summary>
    /// The ID of the wallet for which the lock could not be acquired
    /// </summary>
    public Guid WalletId { get; } = walletId;


    /// <summary>
    /// The name of the operation that was attempted
    /// </summary>
    public string OperationName { get; } = operationName;
}
