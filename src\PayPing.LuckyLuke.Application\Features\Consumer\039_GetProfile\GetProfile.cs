﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Factory;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Validators;

namespace PayPing.LuckyLuke.Application.Features.Consumer._039_GetProfile
{
    public record GetProfileResponse(bool isFilled, int userId, string mobileNumber, string firstName, string lastName, string fatherName, string fullName, string nationalCode, DateTime? birthDate, string postalCode, string address, string firstNameEnglish, string lastNameEnglish, bool isMale, string nationalIdSeries, string cardNumber, string email, string provinceEnglish, string cityEnglish);

    public class GetProfileEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.GetProfile,
                async (
                    IGetProfileRequestHandler handler,
                    CancellationToken cancellationToken) =>
                {
                    var result = await handler.HandleAsync(cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetProfile")
            .WithApiVersionSet(builder.NewApiVersionSet("Profile").Build())
            .Produces<GetProfileResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Profile")
            .WithDescription("Get Profile")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetProfileRequestHandler : IGetProfileRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly IUserService _userService;
        private readonly IEnumerable<IGuaranteeFactory> _guaranteeFactories;
        private readonly BNPLOptions _bnplOptions;

        public GetProfileRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IUserService userService,
            IEnumerable<IGuaranteeFactory> guaranteeFactories,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _userService = userService;
            _guaranteeFactories = guaranteeFactories;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<GetProfileResponse> HandleAsync(CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var consumerInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);

            var validator = new UserInfoDtoValidator().Validate(consumerInfo);

            return new GetProfileResponse(
                validator.IsValid,
                consumerInfo.UserId,
                consumerInfo.UserName,
                consumerInfo.FirstName,
                consumerInfo.LastName,
                consumerInfo.FatherName,
                consumerInfo.FullName,
                consumerInfo.NationalCode,
                consumerInfo.BirthDate,
                consumerInfo.PostalCode,
                consumerInfo.Address,
                consumerInfo.FirstNameEnglish,
                consumerInfo.LastNameEnglish,
                consumerInfo.IsMale,
                consumerInfo.NationalIdSeries,
                consumerInfo.CardNumber,
                consumerInfo.Email,
                consumerInfo.ProvinceEnglish,
                consumerInfo.CityEnglish);
        }
    }
    public interface IGetProfileRequestHandler
    {
        ValueTask<GetProfileResponse> HandleAsync(CancellationToken cancellationToken);
    }
}
