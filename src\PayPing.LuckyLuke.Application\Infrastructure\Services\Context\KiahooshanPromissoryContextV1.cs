﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Services.Context
{
    public struct KiahooshanPromissoryContextV1
    {
        public long OrderId { get; set; }
        public Guid OrderTrackingCode { get; set; }
        public long OrderGuaranteeId { get; set; }
        public KiahooshanPromissoryStatusV1 Status { get; set; }
        public int ConsumerUserId { get; set; }
        public int RecipientUserId { get; set; }
        public int Amount { get; set; }
        public string DueDate { get; set; } // yyyy/mm/dd
        public string Description { get; set; }
        public string IbanWithIR { get; set; }
        public bool IsLegal { get; set; } // حقوقی
        public KiahooshanPromissoryContextRecipientV1 Recipient { get; set; }


        public string PromissoryUniqueId { get; set; }
        public string PromissoryRawDocumentFileId { get; set; }
        public string PromissoryRawDocumentFileContentType { get; set; }

        public string PromissorySignedDocumentFileId { get; set; }
        public string PromissorySignedDocumentFileContentType { get; set; }

        public string PromissoryFinalDocumentFileId { get; set; }
        public string PromissoryFinalDocumentFileContentType { get; set; }

        public string PromissoryTreasuryId { get; set; }

    }

    public struct KiahooshanPromissoryContextRecipientV1
    {
        public string NationalCode { get; set; }
        public string MobileNumber { get; set; }
        public string FullName { get; set; }
        public bool IsLegal { get; set; }
        public string Address { get; set; }
    }

    public enum KiahooshanPromissoryStatusV1
    {
        IssuePromissory = 0,
        Sign = 25,
        Finalize = 50,
        Done = 100,
    }
}
