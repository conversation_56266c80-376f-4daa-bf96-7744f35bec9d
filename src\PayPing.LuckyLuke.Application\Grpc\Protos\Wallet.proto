syntax = "proto3";

option csharp_namespace = "Wallet";

import "Grpc/Protos/BaseExceptionResponse.proto";
import "Grpc/Protos/CustomTypes.proto";
import "google/protobuf/wrappers.proto";

// WalletGrpc service provides operations for managing digital wallets
service WalletGrpc {
  // Wallet Creation and Management
  rpc CreateWallet (CreateWalletRequest) returns (CreateWalletResponse);
  rpc BlockWallet (BlockWalletRequest) returns (BlockWalletResponse);
  rpc UnBlockWallet (UnBlockWalletRequest) returns (UnBlockWalletResponse);

  // Balance Queries
  rpc GetWalletBalanceByType (GetWalletBalanceByTypeRequest) returns (GetWalletBalanceByTypeResponse);
  rpc GetWalletBalanceById (GetWalletBalanceByIdRequest) returns (GetWalletBalanceByIdResponse);
  rpc GetWalletsBalanceByIds (GetWalletsBalanceByIdsRequest) returns (GetWalletsBalanceByIdsResponse);
  rpc GetWalletDetailById (GetWalletDetailByIdRequest) returns (GetWalletDetailByIdResponse);
  rpc GetWalletTransactionTotalAmount (GetWalletTransactionTotalAmountRequest) returns (GetWalletTransactionTotalAmountResponse);

  // Transaction Operations
  rpc DepositWallet (DepositWalletRequest) returns (DepositWalletResponse);
  rpc WithdrawWallet (WithdrawWalletRequest) returns (WithdrawWalletResponse);
  rpc Transfer (TransferRequest) returns (TransferResponse);
}

// ===== Common Types =====

// TransactionSubject defines the type of transaction being performed
enum TransactionSubject {
  Fee = 0;  // Transaction is a fee
  Main = 1; // Transaction is a main operation
}

// UnblockActionType defines the type of action to take when unblocking funds
enum UnblockActionType {
  Withdraw = 0; // Unblock and withdraw funds
  Transfer = 1; // Unblock and transfer funds
}

// TransactionType defines the type of transaction
enum TransactionType {
  TransactionDeposit = 0; // Transaction is a deposit
  TransactionWithdraw = 1; //Transaction is a withdraw
}

// ===== Wallet Creation =====

// CreateWalletRequest contains information needed to create a new wallet
message CreateWalletRequest {
  google.protobuf.Int32Value clientId = 1;    // Optional client identifier
  google.protobuf.StringValue businessId = 2;  // Optional business identifier
  google.protobuf.StringValue nationalId = 3;  // Optional national ID
  google.protobuf.StringValue firstName = 4;   // Optional first name
  google.protobuf.StringValue lastName = 5;    // Optional last name
  google.protobuf.StringValue phoneNumber = 6; // Optional phone number
  google.protobuf.StringValue clientTrackId = 7; // Optional client tracking ID
  google.protobuf.StringValue coinId = 8;        // Optional coin identifier
  google.protobuf.StringValue coinSymbol = 9;    // Optional coin identifier
  string walletTypeId = 10;                    // Required wallet type identifier
  repeated string policies = 11;              // List of policies to apply
}

message CreateWalletResponse {
  string walletId = 1;                       // ID of the created wallet
  baseExceptionResponse.ProblemDetail problemDetail = 2;
}

// ===== Balance Queries =====

message GetWalletBalanceByTypeRequest {
  google.protobuf.Int32Value clientId = 1;    // Optional client identifier
  google.protobuf.StringValue businessId = 2;  // Optional business identifier
  string symbol = 3;                          // Coin symbol
  string walletTypeId = 4;                    // Wallet type identifier
}

message GetWalletBalanceByTypeResponse {
  customTypes.DecimalValue balance = 1;      // Current balance
  baseExceptionResponse.ProblemDetail problemDetail = 2;
}

message GetWalletBalanceByIdRequest {
  string walletId = 1;                       // Wallet identifier
}

message GetWalletBalanceByIdResponse {
  customTypes.DecimalValue balance = 1;      // Current balance
  string coinId = 2;                         // Coin identifier
  string coinSymbol = 3;                     // Coin symbol
  baseExceptionResponse.ProblemDetail problemDetail = 4;
}

message GetWalletsBalanceByIdsRequest {
  repeated string walletIds = 1;             // List of wallet identifiers
}

message GetWalletsBalanceByIdsResponse {
  repeated GetWalletsBalanceByIdsDetail walletsBalanceDetails = 1;
  baseExceptionResponse.ProblemDetail problemDetail = 2;
}

message GetWalletsBalanceByIdsDetail {
  string walletId = 1;                       // Wallet identifier
  customTypes.DecimalValue balance = 2;      // Current balance
}

message GetWalletDetailByIdRequest {
  string walletId = 1;                       // Wallet identifier
}

message GetWalletDetailByIdResponse {
  google.protobuf.Int32Value clientId = 1;     // Optional client identifier
  google.protobuf.StringValue businessId = 2;  // Optional business identifier
  string walletId = 3;                         // Wallet identifier
  string walletTypeId = 4;                     // Wallet type identifier
  string coinId = 5;                           // Coin identifier
  string coinSymbol = 6;                       // Coin symbol
  customTypes.DecimalValue blockBalance = 7;   // Blocked balance
  customTypes.DecimalValue totalBalance = 8;   // Total balance
  customTypes.DecimalValue accessBalance = 9;  // Accessible balance
  int32 status = 10;                           // Wallet status
  baseExceptionResponse.ProblemDetail problemDetail = 11;
}

message GetWalletTransactionTotalAmountRequest {
  string walletId = 1;                       // Wallet identifier
  TransactionType type = 2;                  // Transaction type
}

message GetWalletTransactionTotalAmountResponse {
  customTypes.DecimalValue totalAmount = 1;   // Total amount
  baseExceptionResponse.ProblemDetail problemDetail = 2;
}

// ===== Transaction Operations =====

message DepositWalletRequest {
  string walletId = 1;                         // Target wallet identifier
  string correlationId = 2;                    // Transaction correlation ID
  customTypes.DecimalValue amount = 3;         // Deposit amount
  TransactionSubject subject = 4;              // Transaction subject
  google.protobuf.StringValue description = 5; // Optional description
}

message DepositWalletResponse {
  bool success = 1;                            // Operation success status
  customTypes.DecimalValue accessBalance = 2;  // Accessible balance
  baseExceptionResponse.ProblemDetail problemDetail = 3;
}

message WithdrawWalletRequest {
  string walletId = 1;                       // Source wallet identifier
  string correlationId = 2;                  // Transaction correlation ID
  customTypes.DecimalValue amount = 3;       // Withdrawal amount
  TransactionSubject subject = 4;            // Transaction subject
  google.protobuf.StringValue description = 5; // Optional description
}

message WithdrawWalletResponse {
  bool success = 1;                          // Operation success status
  customTypes.DecimalValue accessBalance = 2;  // Accessible balance
  baseExceptionResponse.ProblemDetail problemDetail = 3;
}

message BlockWalletRequest {
  string walletId = 1;                       // Target wallet identifier
  string correlationId = 2;                  // Transaction correlation ID
  customTypes.DecimalValue amount = 3;       // Amount to block
}

message BlockWalletResponse {
  bool success = 1;                          // Operation success status
  customTypes.DecimalValue accessBalance = 2;// Accessible balance
  customTypes.DecimalValue blockBalance = 3; // Block balance
  baseExceptionResponse.ProblemDetail problemDetail = 4;
}

message UnBlockWalletRequest {
  string walletId = 1;                       // Target wallet identifier
  string correlationId = 2;                  // Transaction correlation ID
  repeated UnBlockAction UnBlockActions = 3; // List of unblock actions
}

message UnBlockAction {
  UnblockActionType type = 1;                // Type of unblock action
  google.protobuf.StringValue destinationWalletId = 2; // Optional destination wallet
  customTypes.DecimalValue amount = 3;       // Amount to unblock
  TransactionSubject subject = 4;            // Transaction subject
}

message UnBlockWalletResponse {
  bool success = 1;                          // Operation success status
  customTypes.DecimalValue accessBalance = 2;// Accessible balance
  customTypes.DecimalValue blockBalance = 3; // Block balance
  baseExceptionResponse.ProblemDetail problemDetail = 4;
}

message TransferRequest {
  string sourceWalletId = 1;                 // Source wallet identifier
  string destinationWalletId = 2;            // Destination wallet identifier
  string correlationId = 3;                  // Transaction correlation ID
  customTypes.DecimalValue amount = 4;       // Transfer amount
  google.protobuf.StringValue masterWalletId = 5; // Optional master wallet identifier
  customTypes.DecimalValue wage = 6;         // Transaction fee
  google.protobuf.StringValue sourceWalletDescription = 7;      // Optional source description
  google.protobuf.StringValue destinationWalletDescription = 8; // Optional destination description
  google.protobuf.StringValue masterWalletDescription = 9;      // Optional master wallet description
}

message TransferResponse {
  bool success = 1;                          // Operation success status
  baseExceptionResponse.ProblemDetail problemDetail = 2;
}