﻿using Microsoft.AspNetCore.Authorization.Policy;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System.Net;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Resources;
using PayPing.LuckyLuke.Application.Infrastructure.Security;

namespace PayPing.LuckyLuke.Application.Infrastructure.Web
{
    public class CustomAuthorizationMiddlewareResultHandler : IAuthorizationMiddlewareResultHandler
    {
        private readonly AuthorizationMiddlewareResultHandler defaultHandler = new();

        public async Task HandleAsync(
            RequestDelegate next,
            HttpContext context,
            AuthorizationPolicy policy,
            PolicyAuthorizationResult authorizeResult)
        {
            if (!authorizeResult.Succeeded && authorizeResult.Forbidden)
            {
                var paypingError = new PaypingError(() => GlobalErrorMessage.security_forbidden_request0403);
                var problemDetails = new UnauthorizedProblemDetails(details: paypingError.Message);
                problemDetails.Instance = context.Request.Path;
                context.Response.StatusCode = problemDetails.Status.Value;
                await context.Response.WriteAsync(JsonSerializer.Serialize(problemDetails));
            }
            else if (!authorizeResult.Succeeded && authorizeResult.Challenged)
            {

                var paypingError = new PaypingError(() => GlobalErrorMessage.security_unkown_request0401);

                var problemDetails = new UnauthenticatedProblemDetails(details: paypingError.Message);
                context.Response.StatusCode = problemDetails.Status.Value;
                context.Response.StatusCode = problemDetails.Status.Value;
                await context.Response.WriteAsync(JsonSerializer.Serialize(problemDetails));
            }
            else
            {
                // Fall back to the default implementation.
                await defaultHandler.HandleAsync(next, context, policy, authorizeResult);
            }


        }
    }

    public class PaypingError
    {
        private readonly string _validationErrorValue;
        private readonly string _validationErrorName;
        private readonly List<string> _errorCode;

        /// <summary>
        /// Initializes a new instance of the <see cref="PaypingError"/> class.
        /// </summary>
        /// <param name="expression">expression must be in this pattern: domain.error_error_error.400(httpStatusCode)</param>
        public PaypingError(Expression<Func<string>> expression)
        {
            _validationErrorName = ((MemberExpression)expression.Body).Member.Name;
            _validationErrorValue = expression.Compile()();
            _errorCode = _validationErrorName.Split('0').ToList();
            _errorCode = _errorCode[0].Split('_').ToList();
            _errorCode.RemoveAt(0);
            _errorCode = string.Join("_", _errorCode).Split('_').ToList();
        }
        /// <summary>
        /// Initializes a new instance of the <see cref="PaypingError"/> class.
        /// </summary>
        /// <param name="validationErrorName">Name of the validation error. must follow this pattern : domain.error_error_error0400</param>
        /// <param name="validationErrorValue">The validation error value.</param>
        public PaypingError(string validationErrorName, string validationErrorValue)
        {
            _validationErrorName = validationErrorName;
            _validationErrorValue = validationErrorValue;
            var errorCode = _validationErrorName.Split('_').ToList();
            errorCode.RemoveAt(0);
        }

        public string DomainName => _validationErrorName.Split('_')[0];

        public string Code => $"{_validationErrorName.Split('_')[0]}.{string.Join("_", _errorCode)}";
        public string Message => _validationErrorValue;

        public HttpStatusCode StatusCode => (HttpStatusCode)(Convert.ToInt32(_validationErrorName.Substring(_validationErrorName.Length - 3, 3)));
    }

    public record PaypingParameterError
    {
        public PaypingParameterError(string code, string message)
        {
            Code = code;
            Message = message;
        }

        public PaypingParameterError(string code, string message, string field) : this(code, message)
        {
            Field = field;
        }

        public string Code { get; set; }
        public string Message { get; set; }
        public string Field { get; set; }
    }
}
