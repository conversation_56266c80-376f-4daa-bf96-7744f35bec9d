﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer._095_PromissoryIssue
{
    public record PromissoryIssueRequest(Guid trackingCode);

    public class PromissoryIssueEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.PromissoryIssue,
                async (
                    PromissoryIssueRequest request,
                    IPromissoryIssueRequestHandler handler,
                    IValidator<PromissoryIssueRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("PromissoryIssue")
            .WithApiVersionSet(builder.NewApiVersionSet("Promissory").Build())
            .Produces<PromissoryCheckPointResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Promissory Issue")
            .WithDescription("Promissory Issue")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class PromissoryIssueRequestHandler(
        ApplicationDbContext dbContext,
        IGuaranteeService guaranteeService,
        IUserContext userContext,
        IWalletService walletService,
        IEnumerable<IDigitalSignProvider> digitalSignProviders,
        IOptions<BNPLOptions> bnplOptions)
        : IPromissoryIssueRequestHandler
    {
        private readonly BNPLOptions _bnplOptions = bnplOptions.Value;

        public async ValueTask<PromissoryCheckPointResponse> HandleAsync(PromissoryIssueRequest request, CancellationToken cancellationToken)
        {
            int userId = userContext.CurrentUserId.Value;

            var order = await dbContext.Orders
                .Where(o => o.TrackingCode == request.trackingCode)
                .Include(o => o.OrderGuarantees)
                .FirstOrDefaultAsync(cancellationToken);

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            {
                throw new OrderExpiredException(request.trackingCode.ToString(), "سفارش منقضی شده است");
            }

            if (order.ConsumerUserId != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }


            IPromissoryGuaranteeProvider provider = await guaranteeService.GetPromissoryGuaranteeProviderByGuarantorId(order.OrderPlan.GuarantorId);

            var validateResult = await provider.ValidateIssuanceAsync(order);

            if (!validateResult.isValid)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), validateResult.error, string.Empty);
            }

            OrderGuarantee orderGuarantee = null;
            if (validateResult.guaranteeId.HasValue)
            {
                orderGuarantee = order.OrderGuarantees.First(o => o.Id == validateResult.guaranteeId.Value);
            }
            else
            {
                var merchInfo = await dbContext.MerchantInfos.AsNoTracking()
                    .Where(o => o.MerchantUserId == order.MerchantUserId)
                    .FirstOrDefaultAsync(cancellationToken);

                if (merchInfo == null)
                {
                    throw new NotFoundException(order.MerchantUserId.ToString(), "merchInfo");
                }

                var guarantorId = await provider.CurrentGuarantorIdAsync(cancellationToken);

                orderGuarantee = new OrderGuarantee()
                {
                    OrderId = order.Id,
                    GuarantorId = guarantorId
                };

                dbContext.OrderGuarantees.Add(orderGuarantee);

                var result = await dbContext.SaveChangesAsync(cancellationToken);
                if (result == 0)
                {
                    throw new Exception("At PromissoryIssueRequestHandler; could not save OrderGuarantee");
                }

                int recipientUserId = order.MerchantUserId;

                var wallet = await walletService.GetWalletCreditInfoAsync(order.OrderPlan.WalletId, cancellationToken);
                if (!wallet.Success)
                {
                    throw new Exception("At PromissoryIssueRequestHandler; wallet not found");
                }

                var ogDto = await provider.CreateOrderGuaranteeAsync(
                    orderGuarantee.Id,
                    order.Id,
                    order.TrackingCode,
                    guarantorId,
                    userId,
                    recipientUserId,
                    (int)order.GuaranteeAmount,
                    order.DueDate.Value,
                    merchInfo.PredefinedCode,
                    cancellationToken);

                orderGuarantee.Data = ogDto.Data;

                var ogDataResult = await dbContext.SaveChangesAsync(cancellationToken);
                if (ogDataResult == 0)
                {
                    throw new Exception("At PromissoryIssueRequestHandler; could not save OrderGuarantee Data");
                }
            }

            var checkPoint = await provider.PushToNextCheckPointAsync(orderGuarantee.Data, cancellationToken);

            orderGuarantee.GuaranteeRefId = provider.GetPromissoryRefId(orderGuarantee.Data);

            if (checkPoint.isDone)
            {
                order.Status = OrderStatus.GuaranteeSucceeded;

                await dbContext.SaveChangesAsync();

                return new PromissoryCheckPointResponse(true, true, string.Empty, Infrastructure.Web.OrderSteps.Quote, checkPoint.data);
            }
            else
            {
                order.Status = OrderStatus.GuaranteeInProgress;

                await dbContext.SaveChangesAsync(cancellationToken);

                return checkPoint;
            }
        }

        private IDigitalSignProvider ChooseDigitalSignProvider(DigitalSignType digitalSignType)
        {
            return digitalSignProviders.First(f => f.DigitalSignType == digitalSignType);
        }
    }

    public interface IPromissoryIssueRequestHandler
    {
        ValueTask<PromissoryCheckPointResponse> HandleAsync(PromissoryIssueRequest request, CancellationToken cancellationToken);
    }

    public class PromissoryIssueValidator : AbstractValidator<PromissoryIssueRequest>
    {
        public PromissoryIssueValidator()
        {
            RuleFor(x => x.trackingCode).NotEqual(Guid.Empty).WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}
