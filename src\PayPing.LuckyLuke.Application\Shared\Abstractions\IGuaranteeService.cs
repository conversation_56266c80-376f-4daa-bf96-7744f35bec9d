﻿using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;

namespace PayPing.LuckyLuke.Application.Shared.Abstractions
{
    public interface IGuaranteeService
    {
        ValueTask<Guarantor> FindGuarantorForType(GuaranteeType guaranteeType, int merchantUserId, CancellationToken cancellationToken);
        Task<IGuaranteeProvider> GetGuaranteeProviderByGuarantorId(int guarantorId);
        IGuaranteeProvider GetGuaranteeProviderByTypes(GuaranteeType guaranteeType, GuaranteeProvider providerType);
        ValueTask<DigitalSignType> GetGuarantorCompliantSignatureTypeByGuarantorId(int guarantorId);
        ValueTask<(GuaranteeType guaranteeType, GuaranteeProvider providerType)> GetGuaranteeTypesByGuarantorId(int guarantorId);
        Task<IPromissoryGuaranteeProvider> GetPromissoryGuaranteeProviderByGuarantorId(int guarantorId);
        IPromissoryGuaranteeProvider GetPromissoryProviderByProvider(GuaranteeProvider providerType);
        ValueTask<DigitalSignType> GetGuarantorCompliantSignatureTypeByGuaranteeType(GuaranteeType guaranteeType, int merchantUserId, CancellationToken cancellationToken);
    }
}
