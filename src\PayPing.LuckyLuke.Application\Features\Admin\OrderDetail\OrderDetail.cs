﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Services.Guarantee;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Admin.OrderDetail
{
    public record OrderDetailRequest(int OrderId, int MerchantId);

    public class OrderDetailResponse
    {
        public OrderDetailResponse(ConsumerInfo consumerInfo, GuaranteeType guaranteeType, int installmentCount, DateTimeOffset orderCreatedDate, Guid trackingCode, OrderSteps orderStep, decimal totalBasketAmount, decimal prepayment, decimal creditAmount, string creditScoreDescription, decimal totalPayment, InstallmentInfo installmentInfo, List<InstallmentDetail> installmentDetails, bool isCancelable, List<string> guaranteeFileIds)
        {
            ConsumerInfo = consumerInfo;
            GuaranteeType = guaranteeType;
            InstallmentCount = installmentCount;
            OrderCreatedDate = orderCreatedDate;
            TrackingCode = trackingCode;
            OrderStep = orderStep;
            TotalBasketAmount = totalBasketAmount;
            Prepayment = prepayment;
            CreditAmount = creditAmount;
            CreditScoreDescription = creditScoreDescription;
            TotalPayment = totalPayment;
            InstallmentInfo = installmentInfo;
            InstallmentDetails = installmentDetails;
            IsCancelable = isCancelable;
            GuaranteeFileIds = guaranteeFileIds;
        }

        [Required]
        public ConsumerInfo ConsumerInfo { get; set; }
        public GuaranteeType GuaranteeType { get; set; }
        public int InstallmentCount { get; set; }
        public DateTimeOffset OrderCreatedDate { get; set; }
        public Guid TrackingCode { get; set; }
        public OrderSteps OrderStep { get; set; }
        public decimal TotalBasketAmount { get; set; }
        public decimal Prepayment { get; set; }
        public decimal CreditAmount { get; set; }
        public string CreditScoreDescription { get; set; }
        public decimal TotalPayment { get; set; }
        public bool IsCancelable { get; set; }
        public List<string> GuaranteeFileIds { get; set; }

        [Required]
        public InstallmentInfo InstallmentInfo { get; set; }

        [Required]
        public List<InstallmentDetail> InstallmentDetails { get; set; }
    }

    public class ConsumerInfo
    {
        public ConsumerInfo(string firstName, string lastName, string nationalCode, DateTime? birthDate, string mobileNumber)
        {
            FirstName = firstName;
            LastName = lastName;
            NationalCode = nationalCode;
            BirthDate = birthDate;
            MobileNumber = mobileNumber;
        }

        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string NationalCode { get; set; }
        public DateTime? BirthDate { get; set; }
        public string MobileNumber { get; set; }
    }

    public class InstallmentInfo
    {
        public InstallmentInfo(int paidInstallmentCount, int remainedInstallmentCount, decimal paidInstallmentAmount, decimal remainedInstallmentAmount, decimal totalInterest, decimal interestPerMonth, decimal interestRate)
        {
            PaidInstallmentCount = paidInstallmentCount;
            RemainedInstallmentCount = remainedInstallmentCount;
            PaidInstallmentAmount = paidInstallmentAmount;
            RemainedInstallmentAmount = remainedInstallmentAmount;
            TotalInterest = totalInterest;
            InterestPerMonth = interestPerMonth;
            InterestRate = interestRate;
        }

        public int PaidInstallmentCount { get; set; }
        public int RemainedInstallmentCount { get; set; }
        public decimal PaidInstallmentAmount { get; set; }
        public decimal RemainedInstallmentAmount { get; set; }
        public decimal TotalInterest { get; set; }
        public decimal InterestPerMonth { get; set; }
        public decimal InterestRate { get; set; }
    }

    public class InstallmentDetail
    {
        public InstallmentDetail(DateTimeOffset? paymentDate, DateTimeOffset dueDate, InstallmentStatus status, decimal finalAmount, string paymentCode)
        {
            PaymentDate = paymentDate;
            DueDate = dueDate;
            Status = status;
            FinalAmount = finalAmount;
            PaymentCode = paymentCode;
        }

        public DateTimeOffset? PaymentDate { get; set; }
        public DateTimeOffset DueDate { get; set; }
        public InstallmentStatus Status { get; set; }
        public decimal FinalAmount { get; set; }
        public string PaymentCode { get; set; }
    }

    public class OrderDetailEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                    AdminRoutes.OrderDetail,
                    async (
                        [AsParameters] OrderDetailRequest request,
                        IOrderDetailRequestHandler handler,
                        IValidator<OrderDetailRequest> validator,
                        CancellationToken cancellationToken) =>
                    {
                        var validationResult = await validator.ValidateAsync(request, cancellationToken);
                        if (!validationResult.IsValid)
                        {
                            throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                        }

                        var result = await handler.HandleAsync(request, cancellationToken);
                        return Results.Ok(result);
                    })
                .RequireAuthorization("admin")
                .WithName("OrderDetail")
                .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
                .Produces<OrderDetailResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Order Detail")
                .WithDescription("Order Detail")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OrderDetailRequestHandler : IOrderDetailRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserService _userService;
        private readonly IGuaranteeService _guaranteeService;

        public OrderDetailRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IUserService userService,
            IGuaranteeService guaranteeService,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _userService = userService;
            _guaranteeService = guaranteeService;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<OrderDetailResponse> HandleAsync(
            OrderDetailRequest request,
            CancellationToken cancellationToken)
        {
            var order = await _dbContext.Orders
                .Where(o => o.Id == request.OrderId && o.MerchantUserId == request.MerchantId)
                .Include(o => o.Installments).ThenInclude(ii => ii.OrderPayments)
                .Include(o => o.OrderCreditValidations)
                .Include(o => o.OrderGuarantees).ThenInclude(g => g.Guarantor)
                .FirstOrDefaultAsync(cancellationToken);

            if (order == null)
            {
                throw new ArgumentException("سفارش یافت نشد");
            }

            var consumer = await GetConsumerInfoAsync(order.ConsumerUserId, cancellationToken);
            var installmentInfo = GetInstallmentInfo(order.Installments?.OrderBy(x => x.DueDate), order.OrderPlan.InterestRate);
            var installmentDetails = GetInstallmentDetails(order.Installments?.OrderBy(x => x.DueDate));

            string guaranteeFileId = string.Empty;
            if (order.OrderGuarantees != null && order.OrderGuarantees.Count > 0)
            {
                var og = order.OrderGuarantees.OrderByDescending(x => x.CreatedAt).First();
                var provider = await _guaranteeService.GetGuaranteeProviderByGuarantorId(og.GuarantorId);

                if (order.Status == OrderStatus.FullyCanceledAfterPaymentByMerchant && og.SettlementData.HasValue())
                {
                    guaranteeFileId = provider.GetFinalSettlementGuaranteeDocumentId(og.SettlementData);
                }
                else
                {
                    guaranteeFileId = provider.GetFinalGuaranteeDocumentId(og.Data);
                }
            }

            var cancelableStatuses = OrderStatusProvider.GetMerchantBeforePaymentCancelable();

            return new OrderDetailResponse(
                consumerInfo: consumer,
                installmentInfo: installmentInfo,
                installmentDetails: installmentDetails,
                creditAmount: order.CreditedAmount,
                guaranteeType: order.OrderGuarantees.OrderByDescending(c => c.CreatedAt).FirstOrDefault()?.Guarantor.GuaranteeType ?? GuaranteeType.Promissory,
                guaranteeFileIds: new List<string> { guaranteeFileId },
                installmentCount: order.OrderPlan.InstallmentCount,
                orderCreatedDate: order.CreatedAt,
                orderStep: order.Status.ToOrderStep(),
                prepayment: order.PrePaymentAmount,
                trackingCode: order.TrackingCode,
                totalBasketAmount: order.OrderTotalAmount,
                totalPayment: decimal.Add(order.PrePaymentAmount, order.Installments?.Sum(i => i.FinalAmount) ?? default),
                creditScoreDescription: order.OrderCreditValidations.OrderByDescending(c => c.CreatedAt).FirstOrDefault()?.Description ?? default,
                isCancelable: cancelableStatuses.Contains(order.Status)
            );
        }

        private async Task<ConsumerInfo> GetConsumerInfoAsync(int? consumerUserId, CancellationToken cancellationToken)
        {
            if (consumerUserId == null)
            {
                return default;
            }

            var consumerInfo = await _userService.GetConsumerInfoAsync(consumerUserId.Value, cancellationToken);

            return new ConsumerInfo(
                firstName: consumerInfo.FirstName,
                lastName: consumerInfo.LastName,
                nationalCode: consumerInfo.NationalCode,
                birthDate: consumerInfo.BirthDate,
                mobileNumber: consumerInfo.UserName
            );
        }

        private static InstallmentInfo GetInstallmentInfo(IEnumerable<Installment> installments, decimal interestRate)
        {
            if (installments == null)
            {
                return null;
            }

            return new InstallmentInfo(
                paidInstallmentCount: installments.Count(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff),
                remainedInstallmentCount: installments.Count(i => i.Status != InstallmentStatus.PaidOff && i.Status != InstallmentStatus.SalaryDeductionPaidOff),
                paidInstallmentAmount: installments.Where(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff).Sum(i => i.FinalAmount),
                remainedInstallmentAmount: installments.Where(i => i.Status != InstallmentStatus.PaidOff && i.Status != InstallmentStatus.SalaryDeductionPaidOff).Sum(i => i.FinalAmount),
                totalInterest: installments.Sum(i => i.InterestAmount),
                interestPerMonth: installments.FirstOrDefault()?.InterestAmount ?? default,
                interestRate: interestRate
            );
        }

        private static List<InstallmentDetail> GetInstallmentDetails(IEnumerable<Installment> installments)
        {
            if (installments == null)
            {
                return null;
            }

            return installments.Select(i => new InstallmentDetail
            (
                paymentDate: i.PaymentDate,
                dueDate: i.DueDate,
                status: i.Status,
                finalAmount: i.FinalAmount,
                paymentCode: i.OrderPayments.Where(x => x.PaymentType == OrderPaymentType.Installment && x.PaymentStatus == OrderPaymentStatus.Paid).FirstOrDefault()?.PaymentCode ?? default
            )).ToList();
        }

    }

    public interface IOrderDetailRequestHandler
    {
        ValueTask<OrderDetailResponse> HandleAsync(OrderDetailRequest request, CancellationToken cancellationToken);
    }

    public class OrderDetailValidator : AbstractValidator<OrderDetailRequest>
    {
        public OrderDetailValidator()
        {
            RuleFor(x => x.OrderId).NotEmpty().WithResourceError(() => ValidatorDictionary.order_id_is_required);
        }
    }
}
