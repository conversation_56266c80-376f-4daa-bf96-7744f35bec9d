﻿using Microsoft.Extensions.DependencyInjection;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public interface IMerchantCancelFactory
    {
        IMerchantCanceler Create(OrderStatus status);
    }

    public class MerchantCancelFactory : IMerchantCancelFactory
    {
        private readonly IEnumerable<IMerchantCanceler> _cancelers;

        public MerchantCancelFactory(IEnumerable<IMerchantCanceler> cancelers)
        {
            _cancelers = cancelers;
        }

        public IMerchantCanceler Create(OrderStatus status)
        {
            var prepayCancelableStatuses = OrderStatusProvider.GetMerchantBeforePaymentCancelable();
            var afterPayCancelableStatuse = OrderStatusProvider.GetMerchantAfterPaymentCancelableOrSemiCanceled();

            if (prepayCancelableStatuses.Contains(status))
            {
                return _cancelers.Where(s => s is MerchantBeforePaymentCanceler).First();
            }
            else if (afterPayCancelableStatuse.Contains(status))
            {
                return _cancelers.Where(s => s is MerchantAfterPaymentCanceler).First();
            }
            else
            {
                throw new ArgumentOutOfRangeException($"At MerchantCancelFactory; wrong status {status}");
            }
        }
    }
}
