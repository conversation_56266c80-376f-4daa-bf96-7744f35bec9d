﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Services.Context
{
    public struct KuknosPromissoryContextV1
    {
        public long OrderId { get; set; }
        public Guid OrderTrackingCode { get; set; }
        public long OrderGuaranteeId { get; set; }
        public KuknosPromissoryStatusV1 Status { get; set; }
        public int ConsumerUserId { get; set; }
        public int RecipientUserId { get; set; }

        public int Amount { get; set; }
        public string DueDate { get; set; } // yyyy/mm/dd
        public string Description { get; set; }
        public string IbanWithIR { get; set; }
        
        public bool IsLegal { get; set; } // حقوقی
        public KuknosPromissoryContextRecipientV1 Recipient { get; set; }
        public string PredefinedRecipientName { get; set; }

        public string PromissoryId { get; set; }


        public string PromissoryRawDocumentHash { get; set; }
        public string PromissoryRawDocumentFileId { get; set; }
        public string PromissoryRawDocumentFileContentType { get; set; }

        public string PromissorySignedDocumentHash { get; set; }


        public string PromissoryFinalDocumentHash { get; set; }
        public string PromissoryFinalDocumentFileId { get; set; }
        public string PromissoryFinalDocumentFileContentType { get; set; }

        public string PromissoryTreasuryId { get; set; }
        public PromissoryNotePaymentStatus PromissoryNotePaymentStatus { get; set; }

        

    }

    public struct KuknosPromissoryContextRecipientV1
    {
        public string NationalCode { get; set; }
        public string MobileNumber { get; set; }
        public string FullName { get; set; }
        public string Address { get; set; }
        public string PostalCode { get; set; }
        public bool IsLegal { get; set; }
    }

    public enum KuknosPromissoryStatusV1
    {
        IssuePromissory = 0,
        PostIssuePromissory = 10, // return
        SignDocumentWithSignerPdf = 50,
        Signed = 60, // return
        Pay = 70, // return
        Finalize = 80,
        Done = 90 // return
    }

    public enum PromissoryNotePaymentStatus
    {
        Unknown = -100,
        Failed = -2,
        Paid = 0,
    }
}
