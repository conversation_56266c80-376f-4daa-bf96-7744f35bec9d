﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;
using System.ComponentModel;

namespace PayPing.BNPL.Domain.Models
{
    public class Contract : BaseEntity<int>, IAuditableEntity
    {
        public int MerchantUserId { get; set; }
        public ActivationStatus ActivationStatus { get; set; }

        public DateTimeOffset StartDate { get; set; }

        public DateTimeOffset ExpireDate { get; set; }

        public decimal OperationCost { get; set; }
        public MerchantOperationCostStrategy OperationCostStrategy { get; set; }

        public string SignedContractId { get; set; }
        public string FilledContractId { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }
        public int? LastModifiedBy { get; set; }

        public ICollection<Credit> Credits { get; } = new HashSet<Credit>();
    }

    public enum MerchantOperationCostStrategy
    {
        [Description("درصد از فروش اقساطی")]
        PercentOfOrderTotalAmount = 0
    }
}
