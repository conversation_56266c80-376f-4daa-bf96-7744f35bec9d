﻿using Microsoft.Extensions.DependencyInjection;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class KiahooshanGuaranteeFactory : IGuaranteeFactory
    {
        private readonly IServiceProvider _serviceProvider;

        public KiahooshanGuaranteeFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public GuaranteeProvider GuaranteeProvider => GuaranteeProvider.Kiahooshan;

        public IPromissoryGuaranteeProvider CreatePromissory()
        {
            return _serviceProvider.GetServices<IPromissoryGuaranteeProvider>().Where(s => s.GuaranteeProvider == GuaranteeProvider).First();
        }

        public IChequeGuaranteeProvider CreateCheque()
        {
            throw new NotImplementedException();
        }

        public INoneGuaranteeProvider CreateNoneGuarantor()
        {
            throw new NotImplementedException();
        }

        public ISalaryGuaranteeProvider CreateSalaryGuarantor()
        {
            throw new NotImplementedException();
        }
    }
}
