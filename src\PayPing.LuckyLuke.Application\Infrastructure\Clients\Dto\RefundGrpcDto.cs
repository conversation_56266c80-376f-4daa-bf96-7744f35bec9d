﻿using PayPing.Refund.WebApi.gRPC.Types;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public record RefundGrpcResponseBase(bool success);
    public record RefundGrpcResponseError(bool success, string title, string detail, int errorCode, string Instance, string paypingTraceId, int statusCode, problemDetailsError[] errors) : RefundGrpcResponseBase(success);
    public record RefundGrpcCreateResponseSuccess(bool success, string code, bool isPendingRecharge) : RefundGrpcResponseBase(success);
    public record RefundGrpcGetDetailsResponseSuccess(
        bool success,
        string BankTrackingCode,
        DateTimeOffset CancellationDateTime,
        string Code,
        DateTimeOffset CreationDateTime,
        DateTimeOffset FailureDateTime,
        string LocalizedState,
        string LocalizedWagePayer,
        string MaskedPAN,
        string PaymentCode,
        DateTimeOffset PreparationDateTime,
        DateTimeOffset ProcessStartDateTime,
        int RefundAmount,
        int RefundWage,
        int RequestedAmount,
        RefundOperationState State,
        DateTimeOffset SuccessDateTime,
        string TransferReference,
        WagePayer WagePayer) : RefundGrpcResponseBase(success);

    public record RefundGrpcUserConfigurationResponseSuccess(
        bool success,
        bool autoInitializePendingRefunds,
        DateTimeOffset creationDateTime,
        int maxPercentageWage,
        int minPercentageWage,
        double wageValue,
        WageType wageType,
        UserInitializationType userInitializationType): RefundGrpcResponseBase(success);
}