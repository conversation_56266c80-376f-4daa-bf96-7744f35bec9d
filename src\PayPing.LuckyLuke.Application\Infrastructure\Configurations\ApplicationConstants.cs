﻿namespace PayPing.BNPL.Application.Infrastructure.Configurations
{
    public static class ApplicationConstants
    {
        public const string CorsPolicy = "CorsPolicy";
        public const string MerchantCorsPolicy = "MerchantCorsPolicy";
        public const string AdminCorsPolicy = "AdminCorsPolicy";
        public const string AutomaticAccessTokenManagementName = "bnplclient";
        public const string KuknosTokenCacheKey = "IMCache_Kuknos_Token";
        public const int MaximumInProgressOrders = 3;
        public const decimal ConsumerOperationCostRatioOfCredit = 0.01m;
        public const decimal ConsumerMinOperationCostAmount = 32_000m;
        public const int OrderExpireDurationMinutes = 30;
        public const string WalletDistLockKeyPrefix = "BNPL-Wallet-REDLOCK-KEY";
        public const string CreditDistLockKeyPrefix = "BNPL-Credit-REDLOCK-KEY";
        public const decimal PromissoryMinAmount = 2000_000m;
        public const long SignatureImageMaxSizeInBytes = 51200;
        public static readonly string[] SignatureImageValidExtensions = [".jpg", ".jpeg"];
        public static readonly string[] SignatureVideoValidExtensions = [".webm", ".mp4"];
        public const long SignatureVideoMaxSizeInBytes = 20_971_520;
        public const int InstallmentDefaultDays = 60;
        public const int InstallmentSalaryDeductionDays = 30;
        public const int CreditScoreAllowedMargin = 500;
        public const string PromissoryDoneLogMessageKey = "Promissory_Done_{OrderGuaranteeId}_{OrderId}";
               
        public const string KiahooshanSelfieVideoSuccessStatus = "COMPLETED";
        public const string KiahooshanDigitalSignatureSuccessStatus = "ACCEPTED";
        public const string KiahooshanPromissorySuccessStatus = "SUCCESS";
        public const string KiahooshanTokenCacheKey = "IMCache_Kiahooshan_Token";
        public const string KiahooshanVideoCacheKey = "IMCache_Kiahooshan_SelfieVideo";

        public static readonly string[] EmployeesEXcelValidExtensions = [".xlsx", ".xls"];
        public const long EmployeesEXcelMaxSizeInBytes = 1_048_576; // 1 MB

        public const string RefundFanoutQueueNameForBNPLMerchant = "refund-bnpl-merchant-queue";
    }

    public static class InfrastructureExcelConstants
    {
        public const string PersianOrderList = "تراکنش";
        public const string PersianOrderTrackingCode = "کد رهگیری";
        public const string PersianCreatedDate = "تاریخ ایجاد";
        public const string PersianCreditAmount = "اعتبار قابل ارایه";
        public const string PersianConsumer = "خریدار";
        public const string PersianTotalInstallmentCount = "تعداد کل اقساط";
        public const string PersianPaidoffInstallmentCount = "تعداد اقساط پرداخت شده";
        public const string PersianIsCancelable = "امکان کنسل شدن";
        public const string PersianMobile = "تلفن همراه";
    }
}
