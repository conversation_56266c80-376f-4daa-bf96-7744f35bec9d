﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Services.User;

namespace PayPing.LuckyLuke.Application.Features.Admin.GuarantorUpdate
{
    public record GuarantorUpdateRequest(GuaranteeType? GuaranteeType, GuaranteeProvider? GuaranteeProvider, Guid Code, string Name, string Domain);

    public class GuarantorUpdateResponse
    {
        public GuaranteeType GuaranteeType { get; set; }
        public GuaranteeProvider GuaranteeProvider { get; set; }
        public Guid Code { get; set; }
        public string Name { get; set; }
        public string Domain { get; set; }
    }

    public class GuarantorUpdateEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPut(
                AdminRoutes.GuarantorUpdate,
                async (
                    [FromBody] GuarantorUpdateRequest request,
                    IGuarantorUpdateRequestHandler handler,
                    IValidator<GuarantorUpdateRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
                .RequireAuthorization("admin")
                .WithName("GuarantorUpdate")
                .WithApiVersionSet(builder.NewApiVersionSet("Guarantor").Build())
                .Produces<GuarantorUpdateResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Guarantor Update")
                .WithDescription("Guarantor Update")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GuarantorUpdateRequestHandler : IGuarantorUpdateRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;

        public GuarantorUpdateRequestHandler(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async ValueTask<GuarantorUpdateResponse> HandleAsync(GuarantorUpdateRequest request, CancellationToken cancellationToken)
        {
            var guarantor = await _dbContext.Guarantors
               .Where(m => m.Code == request.Code)
               .FirstOrDefaultAsync();

            if (guarantor == null)
            {
                throw new ArgumentException("ضامن یافت نشد");
            }

            if (!string.IsNullOrEmpty(request.Domain))
                guarantor.Domain = request.Domain;

            if (!string.IsNullOrEmpty(request.Name))
                guarantor.Name = request.Name;

            if (request.GuaranteeProvider != null)
                guarantor.GuaranteeProvider = request.GuaranteeProvider.Value;

            if (request.GuaranteeType != null)
                guarantor.GuaranteeType = request.GuaranteeType.Value;

            await _dbContext.SaveChangesAsync();

            return new GuarantorUpdateResponse
            {
                Code = guarantor.Code,
                Domain = guarantor.Domain,
                GuaranteeProvider = guarantor.GuaranteeProvider,
                GuaranteeType = guarantor.GuaranteeType,
                Name = guarantor.Name
            };
        }

    }

    public interface IGuarantorUpdateRequestHandler
    {
        ValueTask<GuarantorUpdateResponse> HandleAsync(GuarantorUpdateRequest request, CancellationToken cancellationToken);
    }

    public class GuarantorUpdateValidator : AbstractValidator<GuarantorUpdateRequest>
    {
        public GuarantorUpdateValidator()
        {
            RuleFor(x => x.Code).NotEmpty();

        }
    }
}
