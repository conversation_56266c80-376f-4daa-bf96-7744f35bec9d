﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public record KuknosApiResponse<T>
    {
        public KuknosApiMetaResponse meta { get; init; }

        public T data { get; init; }
    }

    public record KuknosApiMetaResponse(bool success, string data_type, string description, bool has_pagination, object link);

    public record KuknosApiLoginDataResponse(string access_token, string refresh_token, bool totp_required, bool change_password_required);

    public record KuknosApiCreateCertificateDataResponse(bool finished, string tracking_code, string certificate);

    public record KuknosApiIssuePromissoryDataResponse(string id, string status, string unsigned_document);

    public record KuknosApiFileDownloadResponse(byte[] contentBytes, string contentType);

    public record KuknosApiFinalizeResponse(string final_pdf_document, string treasury_id);

    public record KuknosApiErrorResponse
    {
        public KuknosApiErrorMetaResponse meta { get; init; }

        public string message { get; init; }
    }
    public record KuknosApiErrorMetaResponse(bool success, string error_type, string error_code, object[] error_target, string help);


    public record KuknosApiIssueSettlementDataResponse(string unsigned_document);
    public record KuknosApiFinalizeSettlementResponse(string final_pdf_document);


}
