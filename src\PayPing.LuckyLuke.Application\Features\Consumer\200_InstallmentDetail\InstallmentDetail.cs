﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel.DataAnnotations;
using System.Runtime.CompilerServices;

namespace PayPing.LuckyLuke.Application.Features.Consumer.InstallmentDetail
{
    public record InstallmentDetailRequest(Guid installmentCode);
    public class InstallmentDetailResponse
    {
        public decimal installmentAmount { get; set; }
        public decimal delayPenaltyAmount { get; set; }
        public DateTimeOffset dueDate { get; set; }

        [Required]
        public InstallmentStatus installmentStatus { get; set; }
        [Required]
        public PaymentStatus paymentStatus { get; set; }
        public int installmentCount { get; set; }
        public int paidffInstallmentCount { get; set; }
        public Guid trackingCode { get; set; }

        public bool isPayable { get; set; }
        public string merchantName { get; set; }
        public int number { get; set; }
    }

    public class InstallmentDetailDto
    {
        public long id { get; set; }
        public decimal installmentAmount { get; set; }
        public decimal delayPenaltyAmount { get; set; }
        public DateTimeOffset dueDate { get; set; }
        public InstallmentStatus installmentStatus { get; set; }
        public PaymentStatus paymentStatus { get; set; }
        public int installmentCount { get; set; }
        public int paidffInstallmentCount { get; set; }
        public Guid trackingCode { get; set; }
        public long orderId { get; set; }
        public string merchantName { get; set; }
        public int number { get; set; }
    }


    public class InstallmentDetailEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.InstallmentDetail,
                async (
                    [AsParameters] InstallmentDetailRequest request,
                    IInstallmentDetailRequestHandler handler,
                    IValidator<InstallmentDetailRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .AllowAnonymous()
            .WithName("InstallmentDetail")
            .WithApiVersionSet(builder.NewApiVersionSet("Installment").Build())
            .Produces<InstallmentDetailResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Installment Detail")
            .WithDescription("Installment Detail")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class InstallmentDetailRequestHandler : IInstallmentDetailRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserService _userService;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public InstallmentDetailRequestHandler(ApplicationDbContext dbContext, IUserService userService, IOptions<BNPLOptions> bnplOptions, IUserContext userContext)
        {
            _dbContext = dbContext;
            _userService = userService;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<InstallmentDetailResponse> HandleAsync(InstallmentDetailRequest request, CancellationToken cancellationToken)
        {
            var installment = await _dbContext.Installments.Where(i => i.Code == request.installmentCode)
                .Select(i => new InstallmentDetailDto
                {
                    id = i.Id,
                    delayPenaltyAmount = i.DelayPenaltyAmount,
                    dueDate = i.DueDate,
                    installmentAmount = i.FinalAmount,
                    installmentStatus = i.Status,
                    paymentStatus = i.PaymentStatus,
                    installmentCount = i.Order.Installments.Count(),
                    paidffInstallmentCount = i.Order.Installments.Count(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff),
                    trackingCode = i.Order.TrackingCode,
                    orderId = i.OrderId,
                    number = i.Number,
                    merchantName = i.Order.MerchantName
                })
                .FirstOrDefaultAsync();

            if (installment == null)
            {
                throw new NotFoundException(request.installmentCode.ToString(), "installment");
            }

            var firstNotPaid = await _dbContext.Installments.AsNoTracking()
                .Where(i => 
                    i.OrderId == installment.orderId &&
                    i.Status != InstallmentStatus.PaidOff &&
                    i.Status != InstallmentStatus.SalaryDeductionPaidOff &&
                    i.Status != InstallmentStatus.SalaryDeductionWaiting)
                .OrderBy(x => x.DueDate)
                .FirstOrDefaultAsync();

            
            return ToResponse(installment, firstNotPaid);
        }

        private InstallmentDetailResponse ToResponse(InstallmentDetailDto req, Installment firstNotPaid)
        {
            if (req == null) return null;

            return new InstallmentDetailResponse()
            {
                delayPenaltyAmount = req.delayPenaltyAmount,
                dueDate = req.dueDate,
                installmentAmount = req.installmentAmount,
                installmentStatus = req.installmentStatus,
                paymentStatus = req.paymentStatus,
                installmentCount = req.installmentCount,
                paidffInstallmentCount = req.paidffInstallmentCount,
                trackingCode = req.trackingCode,
                isPayable = firstNotPaid != null && firstNotPaid.Id == req.id,
                number = req.number,
                merchantName = req.merchantName,
            };
        }
    }

    public interface IInstallmentDetailRequestHandler
    {
        ValueTask<InstallmentDetailResponse> HandleAsync(InstallmentDetailRequest request, CancellationToken cancellationToken);
    }

    public class InstallmentDetailValidator : AbstractValidator<InstallmentDetailRequest>
    {
        public InstallmentDetailValidator()
        {
            RuleFor(x => x.installmentCode).NotEmpty().WithResourceError(() => ValidatorDictionary.installment_code_is_required); 
        }
    }
}