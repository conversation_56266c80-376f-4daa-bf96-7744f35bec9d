﻿using Microsoft.AspNetCore.Server.IISIntegration;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class Guarantor : BaseEntity<int>, IAuditableEntity, ISoftDeletableEntity
    {
        public GuaranteeType GuaranteeType { get; set; }
        public GuaranteeProvider GuaranteeProvider { get; set; }
        public Guid Code { get; set; }
        public string Name { get; set; }
        public string Domain { get; set; }
        public TimeSpan WaitTime { get; }

        public DateTimeOffset? LastModifiedAt { get; set; }
        public int? LastModifiedBy { get; set; }

        public bool IsDeleted { get; set; }
        public DateTimeOffset? DeletedAt { get; set; }

        public ICollection<OrderGuarantee> OrderGuarantees { get; } = new HashSet<OrderGuarantee>();
    }


}
