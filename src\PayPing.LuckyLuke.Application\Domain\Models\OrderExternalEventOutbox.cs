﻿using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class OrderExternalEventOutbox : BaseEntity<long>
    {
        public long OrderId { get; set; }

        public string ClientRefId { get; set; }

        public string ClientCancelUrl { get; set; }

        public OrderExternalEventType EventType { get; set; }

        public bool HasFailed { get; set; }

        public int FailCount { get; set; }

        public string Message { get; set; }

        public Order Order { get; set; }
    }

    public enum OrderExternalEventType
    {
        CancelHook = 0,
        RevokeGuarantee = 1
    }
}
