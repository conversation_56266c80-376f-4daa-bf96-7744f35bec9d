﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._12_PlanCreate
{
    public record ConfigurationsUpdateRequest(decimal minPrePaymentRate, decimal interestRate);

    public record ConfigurationsUpdateResponse(decimal minPrePaymentRate, decimal interestRate);

    public class ConfigurationsUpdateEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPut(
                MerchantRoutes.ConfigurationsUpdate,
                async (ConfigurationsUpdateRequest request, IConfigurationsUpdateRequestHandler handler, IValidator <ConfigurationsUpdateRequest> validator, CancellationToken cancellationToken) =>
            {

                var validationResult = await validator.ValidateAsync(request);
                if (!validationResult.IsValid)
                {
                    throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                }

                var result = await handler.HandleAsync(request, cancellationToken);

                return Results.Ok(result);
            })
            .RequireAuthorization("write")
            .WithName("ConfigurationsUpdate")
            .WithApiVersionSet(builder.NewApiVersionSet("Configurations").Build())
            .Produces<ConfigurationsUpdateResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Update Configurations")
            .WithDescription("Update Configurations")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class ConfigurationsUpdateRequestHandler : IConfigurationsUpdateRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public ConfigurationsUpdateRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<ConfigurationsUpdateResponse> HandleAsync(ConfigurationsUpdateRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var merchantInfo = await _dbContext.MerchantInfos.Where(x => x.MerchantUserId == userId).FirstOrDefaultAsync(cancellationToken);

            if (merchantInfo == null)
            {
                throw new ArgumentException("تنظیمات اعتباری پذیرنده یافت نشد");
            }

            merchantInfo.MinPrePaymentRate = request.minPrePaymentRate;
            merchantInfo.InterestRate = request.interestRate;

            var plans = await _dbContext.Plans.Where(p => p.IsActive && p.MerchantUserId == userId).ToListAsync();
            if (plans.Count > 0)
            {
                plans.ForEach(plan =>
                {
                    plan.MinPrePaymentRate = request.minPrePaymentRate;
                    plan.InterestRate = request.interestRate;
                });
            }

            await _dbContext.SaveChangesAsync();

            return new ConfigurationsUpdateResponse(request.minPrePaymentRate, request.interestRate);
        }

    }
    public interface IConfigurationsUpdateRequestHandler
    {
        ValueTask<ConfigurationsUpdateResponse> HandleAsync(ConfigurationsUpdateRequest request, CancellationToken cancellationToken);
    }

    public class ConfigurationsUpdateValidator : AbstractValidator<ConfigurationsUpdateRequest>
    {
        public ConfigurationsUpdateValidator()
        {
            RuleFor(x => x.minPrePaymentRate)
                .GreaterThanOrEqualTo(decimal.Zero).WithResourceError(() => ValidatorDictionary.min_prePayment_rate_greater)
                .LessThan(100m).WithResourceError(() => ValidatorDictionary.min_prePayment_rate_lessthan);
            RuleFor(x => x.interestRate)
                .GreaterThanOrEqualTo(decimal.Zero).WithResourceError(() => ValidatorDictionary.interest_rate_greater).LessThan(100m);
        }
    }

}
