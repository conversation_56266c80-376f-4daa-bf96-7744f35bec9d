﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Merchant.GetOptions
{
    public class GetOptionsResponse
    {
        public GetOptionsResponse(List<GuarantorOptionsResponse> guarantors)
        {
            this.credits = new List<CreditOptionsResponse>();
            this.guarantors = guarantors;
        }

        public GetOptionsResponse(int contractId, List<CreditOptionsResponse> credits, List<GuarantorOptionsResponse> guarantors)
        {
            this.contractId = contractId;
            this.credits = credits;
            this.guarantors = guarantors;
        }

        public int contractId { get; set; }

        [Required]
        public List<CreditOptionsResponse> credits { get; set; }

        [Required]
        public List<GuarantorOptionsResponse> guarantors { get; set; }
    }

    public class CreditOptionsResponse
    {
        public Guid code { get; set; }
        public string name { get; set; }
        public Guid walletId { get; set; }
        public bool canModify { get; set; }
    }

    public class GuarantorOptionsResponse
    {
        public Guid code { get; set; }
        public string name { get; set; }

        [Required]
        public GuaranteeType guaranteeType { get; set; }
    }

    public class GetOptionsEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                MerchantRoutes.GetOptions,
                async (
                    IGetOptionsRequestHandler handler,
                    CancellationToken cancellationToken) =>
                {

                    var result = await handler.HandleAsync(cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetOptions")
            .WithApiVersionSet(builder.NewApiVersionSet("Options").Build())
            .Produces<GetOptionsResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Options Get")
            .WithDescription("Options Get")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetOptionsRequestHandler : IGetOptionsRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly BNPLOptions _bnplOptions;

        public GetOptionsRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<GetOptionsResponse> HandleAsync(CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var guarantors = await _dbContext.Guarantors.AsNoTracking()
                .Select(x => new GuarantorOptionsResponse()
                {
                    code = x.Code,
                    name = x.Name,
                    guaranteeType = x.GuaranteeType
                })
                .ToListAsync(cancellationToken);

            var currentContract = await _dbContext.Contracts.AsNoTracking()
                .Where(p => p.MerchantUserId == userId && p.ExpireDate > DateTimeOffset.UtcNow)
                .FirstOrDefaultAsync(cancellationToken);

            if (currentContract == null)
            {
                return new GetOptionsResponse(guarantors);
            }

            var creditResult = new List<CreditOptionsResponse>();
            var credits = await _dbContext.Credits.AsNoTracking()
                .Where(x => x.ContractId == currentContract.Id && x.IsActive)
                .ToListAsync(cancellationToken);

            if (credits is { Count: not 0 })
            {
                foreach (var item in credits)
                {
                    creditResult.Add(new()
                    {
                        name = $"صندوق اعتباری {item.Code}",
                        code = item.Code,
                        walletId = item.WalletId,
                        canModify = true,
                    });
                }
            }

            return new GetOptionsResponse(currentContract.Id, creditResult, guarantors);
        }

    }
    public interface IGetOptionsRequestHandler
    {
        ValueTask<GetOptionsResponse> HandleAsync(CancellationToken cancellationToken);
    }
}
