﻿

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public record PaymentApiClientPayResponse(string paymentCode, string url);
    
    public record PaymentApiPayResponse(bool success, int errorKey, string errorMessage, string errorTitle, int errorStatus, string errorType, string paymentCode, string url);
    
    public record PaymentApiClientVerifyResponse(long amount, string cardNumber, string cardHashPan, string clientRefId, long paymentRefId);
    public record PaymentApiErrorResponse(int Key, string Message, string Title, int Status, string Type);
                                                                   
    public record PaymentApiVerifyResponse(bool success, int errorKey, string errorMessage, string errorTitle, int errorStatus, string errorType, long amount, string cardNumber, string clientRefId, long paymentRefId);

}
