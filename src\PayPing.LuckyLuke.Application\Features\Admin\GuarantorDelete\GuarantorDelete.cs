﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Services.User;

namespace PayPing.LuckyLuke.Application.Features.Admin.GuarantorDelete
{
    public record GuarantorDeleteRequest(Guid GuarantorCode);

    public class GuarantorDeleteResponse
    {
        public Guid Code { get; set; }
    }

    public class GuarantorDeleteEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapDelete(
                AdminRoutes.GuarantorDelete,
                async (
                    [FromBody] GuarantorDeleteRequest request,
                    IGuarantorDeleteRequestHandler handler,
                    IValidator<GuarantorDeleteRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
                .RequireAuthorization("admin")
                .WithName("GuarantorDelete")
                .WithApiVersionSet(builder.NewApiVersionSet("Guarantor").Build())
                .Produces<GuarantorDeleteResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Guarantor delete")
                .WithDescription("Guarantor delete")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GuarantorDeleteRequestHandler : IGuarantorDeleteRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;

        public GuarantorDeleteRequestHandler(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async ValueTask<GuarantorDeleteResponse> HandleAsync(GuarantorDeleteRequest request, CancellationToken cancellationToken)
        {
            var guarantor = await _dbContext.Guarantors
               .Where(m => m.Code == request.GuarantorCode)
               .FirstOrDefaultAsync();

            if (guarantor == null)
            {
                throw new ArgumentException("ضامن یافت نشد");
            }

            guarantor.IsDeleted = true;

            await _dbContext.SaveChangesAsync();

            return new GuarantorDeleteResponse
            {
                Code = guarantor.Code,
            };
        }

    }

    public interface IGuarantorDeleteRequestHandler
    {
        ValueTask<GuarantorDeleteResponse> HandleAsync(GuarantorDeleteRequest request, CancellationToken cancellationToken);
    }

    public class GuarantorDeleteValidator : AbstractValidator<GuarantorDeleteRequest>
    {
        public GuarantorDeleteValidator()
        {
            RuleFor(x => x.GuarantorCode).NotEmpty();

        }
    }
}
