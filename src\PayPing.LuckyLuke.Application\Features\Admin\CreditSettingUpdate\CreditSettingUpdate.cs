﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Admin.CreditSettingUpdate
{
    public record CreditSettingUpdateRequest(int merchantUserId, int creditId, bool isActive, decimal interestRate, decimal minPrePaymentRate, string predefinedCode);

    public class CreditSettingUpdateResponse
    {
        public int MerchantUserId { get; set; }
        public int CreditId { get; set; }
        public bool IsActive { get; set; }
        public decimal InterestRate {  get; set; }
        public decimal MinPrePaymentRate { get; set; }
        public string PredefinedCode { get; set; }
    }

    public class CreditSettingUpdateEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPut(
                AdminRoutes.CreditSettingUpdate,
                async (
                    [FromBody] CreditSettingUpdateRequest request,
                    ICreditSettingUpdateRequestHandler handler,
                    IValidator<CreditSettingUpdateRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
                .RequireAuthorization("admin")
                .WithName("CreditSettingUpdate")
                .WithApiVersionSet(builder.NewApiVersionSet("Credit").Build())
                .Produces<CreditSettingUpdateResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Credit Update Setting")
                .WithDescription("Credit Update Setting")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CreditSettingUpdateRequestHandler : ICreditSettingUpdateRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;

        public CreditSettingUpdateRequestHandler(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async ValueTask<CreditSettingUpdateResponse> HandleAsync(CreditSettingUpdateRequest request, CancellationToken cancellationToken)
        {
            var merchant = await _dbContext.MerchantInfos
               .Where(m => m.MerchantUserId == request.merchantUserId)
               .FirstOrDefaultAsync();

            if (merchant == null)
            {
                throw new ArgumentException("پذیرنده یافت نشد");
            }

            var credit = await _dbContext.Credits
               .Where(c => c.Id == request.creditId && c.Contract.MerchantUserId == request.merchantUserId)
               .OrderByDescending(cc => cc.Contract.ExpireDate)
               .FirstOrDefaultAsync();

            if (credit == null)
            {
                throw new ArgumentException("کیف پول اعتباری یافت نشد");
            }

            credit.IsActive = request.isActive;
            merchant.MinPrePaymentRate = request.minPrePaymentRate;
            merchant.InterestRate = request.interestRate;
            merchant.PredefinedCode = request.predefinedCode;

            if (merchant.MinPrePaymentRate != request.minPrePaymentRate || merchant.InterestRate != request.interestRate)
            {
                var plans = await _dbContext.Plans.Where(p => p.IsActive && p.MerchantUserId == request.merchantUserId).ToListAsync();
                if (plans.Count > 0)
                {
                    plans.ForEach(plan =>
                    {
                        plan.MinPrePaymentRate = request.minPrePaymentRate;
                        plan.InterestRate = request.interestRate;
                    });
                }
            }
            
            await _dbContext.SaveChangesAsync();

            return new CreditSettingUpdateResponse
            {
                CreditId = credit.Id,
                MerchantUserId = merchant.MerchantUserId,
                IsActive = credit.IsActive,
                MinPrePaymentRate = merchant.MinPrePaymentRate,
                InterestRate = merchant.InterestRate,
                PredefinedCode = merchant.PredefinedCode,
            };
           
        }
    }

    public interface ICreditSettingUpdateRequestHandler
    {
        ValueTask<CreditSettingUpdateResponse> HandleAsync(CreditSettingUpdateRequest request, CancellationToken cancellationToken);
    }

    public class CreditSettingUpdateValidator : AbstractValidator<CreditSettingUpdateRequest>
    {
        public CreditSettingUpdateValidator()
        {
            RuleFor(x => x.merchantUserId).NotEmpty().WithResourceError(() => ValidatorDictionary.merchant_user_id_is_required); 
            RuleFor(x => x.creditId).NotEmpty().WithResourceError(() => ValidatorDictionary.credit_id_is_required);
            RuleFor(x => x.minPrePaymentRate).GreaterThanOrEqualTo(0).WithResourceError(() => ValidatorDictionary.min_prePayment_rate_greater)
                .LessThan(100m).WithResourceError(() => ValidatorDictionary.min_prePayment_rate_lessthan);
            RuleFor(x => x.interestRate).GreaterThanOrEqualTo(0).WithResourceError(() => ValidatorDictionary.interest_rate_greater).LessThan(100m);
        }
    }
}
