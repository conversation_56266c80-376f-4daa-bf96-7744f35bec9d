﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedInstallmentCode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ClientId",
                table: "Plans");

            migrationBuilder.DropColumn(
                name: "ClientId",
                table: "Contracts");

            migrationBuilder.DropColumn(
                name: "ClientToken",
                table: "Contracts");

            migrationBuilder.AddColumn<string>(
                name: "ClientToken",
                table: "Orders",
                type: "character varying(2046)",
                maxLength: 2046,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "Code",
                table: "Installments",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_Installment_Code",
                table: "Installments",
                column: "Code",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Installment_Code",
                table: "Installments");

            migrationBuilder.DropColumn(
                name: "ClientToken",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "Installments");

            migrationBuilder.AddColumn<Guid>(
                name: "ClientId",
                table: "Plans",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<Guid>(
                name: "ClientId",
                table: "Contracts",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<string>(
                name: "ClientToken",
                table: "Contracts",
                type: "text",
                nullable: true);
        }
    }
}
