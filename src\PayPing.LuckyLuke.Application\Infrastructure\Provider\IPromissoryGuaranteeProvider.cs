﻿using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Provider
{

    public interface IPromissoryGuaranteeProvider : IGuaranteeProvider
    {
        public GuaranteeProvider GuaranteeProvider { get; }

        ValueTask<OrderGuaranteeDto> CreateOrderGuaranteeAsync(long id, long orderId, Guid orderTrackingCode, int guarantorId, int consumerId, int recepientId, int promissoryAmount, DateTimeOffset dueDate, string predefinedRecipientName, CancellationToken cancellationToken);
        ValueTask<int> CurrentGuarantorIdAsync(CancellationToken cancellationToken);
        (bool hasPayment, int amountToman) GetPromissoryPayment(decimal orderGuaranteeAmount);
        string GetPromissoryRefId(string data);
        bool IsPaymentSuccessful(string status);
        ValueTask<PromissoryCheckPointResponse> PushToNextCheckPointAsync(string contextData, CancellationToken cancellationToken);
        string SetPaymentStatusToGuaranteeData(string data, bool success);
        ValueTask<(bool isValid, string error, long? guaranteeId)> ValidateIssuanceAsync(Order order);
        ValueTask<(bool success, string error, string data)> SettleGuaranteeAsync(string contextData, CancellationToken cancellationToken);
        string RenewSettlementContextIfNeeded(OrderGuarantee orderGuarantee);
    }
}
