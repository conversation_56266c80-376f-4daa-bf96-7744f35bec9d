﻿using Ardalis.GuardClauses;
using DNTPersianUtils.Core;
using FluentValidation;
using IdentityModel.Client;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Features.Consumer._03_GetSteps;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Factory;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Services.User;
using PayPing.LuckyLuke.Application.Shared.Validators;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Features.Consumer._039_Dummy
{
    public record DummyResponse(string cctoken, string matched);

    public class DummyEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.Dummy,
                async (
                    IDummyRequestHandler handler,
                    CancellationToken cancellationToken) =>
                {
                    var result = await handler.HandleAsync(cancellationToken);

                    return Results.Ok(result);
                })
            .AllowAnonymous()
            .WithName("Dummy")
            .WithApiVersionSet(builder.NewApiVersionSet("Dummy").Build())
            .Produces<DummyResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Profile")
            .WithDescription("Get Profile")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class DummyRequestHandler : IDummyRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IInquiryApiClient _inquiryApiClient;
        private readonly IUserService _userService;
        private readonly IEnumerable<IGuaranteeFactory> _guaranteeFactories;
        private readonly INotifyService _notifyService;
        private readonly IUserContext _userContext;
        private readonly IInquiryGrpcClient _inquiryGrpcClient;
        private readonly IdentityServerOptions _ioptions;
        private readonly BNPLOptions _bnplOptions;

        public DummyRequestHandler(
            ApplicationDbContext dbContext,
            IInquiryApiClient inquiryApiClient,
            IUserService userService,
            IEnumerable<IGuaranteeFactory> guaranteeFactories,
            IOptions<BNPLOptions> bnplOptions,
            INotifyService notifyService,
            IUserContext userContext,
            IInquiryGrpcClient inquiryGrpcClient,
            IOptions<IdentityServerOptions> ioptions)
        {
            _dbContext = dbContext;
            _inquiryApiClient = inquiryApiClient;
            _userService = userService;
            _guaranteeFactories = guaranteeFactories;
            _notifyService = notifyService;
            _userContext = userContext;
            _inquiryGrpcClient = inquiryGrpcClient;
            _ioptions = ioptions.Value;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<DummyResponse> HandleAsync(CancellationToken cancellationToken)
        {
            //UserInfoDto infoDto = new UserInfoDto(
            //            _userContext.CurrentUserId.Value,
            //            _userContext.CurrentUserName,
            //            _userContext.CurrentUserName,
            //            _userContext.CurrentUserFirstname,
            //            _userContext.CurrentUserLastname,
            //            _userContext.CurrentUserNationalCode,
            //            _userContext.CurrentUserPersianBirthDate.ToGregorianDateTime());

            //var res1 = await _inquiryGrpcClient.MachingMobileWithNationalCodeAsync(new RobinHood.Services.ServiceManager.Protos.MachingMobileWithNationalCodeRequest
            //{
            //    MobileNumber = "09125095953",
            //    NationalCode = "0080187528"
            //});

            var res = await _inquiryGrpcClient.NationalCodeInquiryWithPersonalInfoAsync(new RobinHood.Services.ServiceManager.Protos.NationalCodeInquiryWithPersonalInfoRequest
            {
                BirthDate = "13790221",
                NationalCode = "0925255084"
            });

            var response = await new HttpClient().RequestClientCredentialsTokenAsync(new ClientCredentialsTokenRequest
            {
                Address = $"{_ioptions.Identity_Address}/connect/token",

                ClientId = _bnplOptions.ClientId,
                ClientSecret = _bnplOptions.ClientSecret
            });

            //var a = await _inquiryApiClient.IsMatchingMobileWithNationalCode(, );

            //await _notifyService.SendOrderRegisterMessage(168527, 168437, cancellationToken);

            return new DummyResponse(response.AccessToken, res?.FirstName);
        }
    }
    public interface IDummyRequestHandler
    {
        ValueTask<DummyResponse> HandleAsync(CancellationToken cancellationToken);
    }
}
