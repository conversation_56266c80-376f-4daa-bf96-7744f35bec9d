﻿using iText.Bouncycastle.Crypto;
using iText.Bouncycastle.X509;
using iText.Commons.Bouncycastle.Cert;
using iText.Commons.Bouncycastle.Crypto;
using iText.Forms.Form.Element;
using iText.IO.Image;
using iText.IO.Source;
using iText.Kernel.Crypto;
using iText.Kernel.Geom;
using iText.Kernel.Pdf;
using iText.Signatures;
using Microsoft.EntityFrameworkCore;
using Org.BouncyCastle.Asn1.Pkcs;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Utilities.IO.Pem;
using Org.BouncyCastle.X509;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.FileManager.Grpc;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.Tools.SdkBase.Types;

namespace PayPing.LuckyLuke.Application.Shared.Services.PdfSigner;

public class KuknosPdfSignerService(
    ApplicationDbContext dbContext,
    IEnumerable<IDigitalSignProvider> digitalSignProviders,
    IS3ServiceApiClient s3ServiceApiClient,
    IUploadGrpcClient uploadGrpcClient)
    : IPdfSignerService
{
    public DigitalSignType DigitalSignType => DigitalSignType.KuknosVersion1;

    public async Task<ServiceResult<string>> SignPdfDocumentAsync(int userId,
        Stream rawContract,
        Guid orderTrackingCode,
        CancellationToken cancellationToken)
    {
        var consumerInfo = await dbContext.ConsumerInfos.AsNoTracking()
            .Where(x => x.ConsumerUserId == userId)
            .FirstOrDefaultAsync(cancellationToken);

        var dsp = ChooseProvider(DigitalSignType);

        var sigData = await dsp.GetPdfSignatureDataAsync(userId);

        if (string.IsNullOrWhiteSpace(consumerInfo.SignatureImageFileId) ||
            string.IsNullOrWhiteSpace(sigData.certificate))
            throw new CustomValidationException(orderTrackingCode.ToString(), "سفارش متعلق به کاربر نیست",
                string.Empty);

        var imageDlResult = await uploadGrpcClient.GetPresignedUrlAsync(consumerInfo.SignatureImageFileId, userId,
            UploadTypeEnum.Misc, cancellationToken);

        if (!imageDlResult.Succeeded)
            throw new CustomValidationException(orderTrackingCode.ToString(), "خطا در دانلود فایل تصویر امضا",
                string.Empty);

        await using var byteArrayOutputStream = new ByteArrayOutputStream();

        await using var sigImageStream =
            await s3ServiceApiClient.DownloadAsync(imageDlResult.SuccessResult, cancellationToken);

        var sigImageArray = await sigImageStream.ToByteArrayAsync();

        var imgData = ImageDataFactory.Create(sigImageArray);

        // sign pdf
        SignDocumentSignature(rawContract, byteArrayOutputStream, imgData, sigData.privateKey, sigData.certificate);

        var finalPdf = await byteArrayOutputStream.ToByteArrayAsync();

        // upload to fileManager
        var uploadResult = await uploadGrpcClient.UploadUserDocumentAsync(finalPdf, "pdf", userId, cancellationToken);

        if (!uploadResult.Succeeded)
            throw new CustomValidationException(orderTrackingCode.ToString(), "خطا در ذخیره فایل قرارداد",
                string.Empty);

        return uploadResult;
    }

    private IDigitalSignProvider ChooseProvider(DigitalSignType digitalSignType)
    {
        return digitalSignProviders.First(f => f.DigitalSignType == digitalSignType);
    }

    private void SignDocumentSignature(Stream rawContract, ByteArrayOutputStream byteArrayOutputStream,
        ImageData imageData, string pkey, string certStr)
    {
        var pdfSigner = new iText.Signatures.PdfSigner(new PdfReader(rawContract),
            new PdfWriter(byteArrayOutputStream),
            new StampingProperties());

        var signerProperties = new SignerProperties();
        signerProperties.SetCertificationLevel(AccessPermissions.NO_CHANGES_PERMITTED);

        // Set the name indicating the field to be signed.
        // The field can already be present in the document but shall not be signed
        signerProperties.SetFieldName("signature");

        pdfSigner.SetSignerProperties(signerProperties);

        // If you create new signature field (or use SetFieldName(System.String) with
        // the name that doesn't exist in the document or don't specify it at all) then
        // the signature is invisible by default.
        var appearance =
            new SignatureFieldAppearance(SignerProperties.IGNORED_ID).SetContent(imageData);

        var document = pdfSigner.GetDocument();

        signerProperties.SetPageNumber(document.GetNumberOfPages())
            .SetPageRect(new Rectangle(150, 150, 150, 150))
            .SetSignatureAppearance(appearance);

        IExternalSignature pks = GetPrivateKeySignature(pkey);
        var chain = LoadCertificateChainFromBase64(certStr);

        // Sign the document using the detached mode, CMS or CAdES equivalent.
        // This method closes the underlying pdf document, so the instance
        // of PdfSigner cannot be used after this method call
        pdfSigner.SignDetached(
            new BouncyCastleDigest(),
            pks,
            chain,
            null,
            null,
            null,
            0,
            iText.Signatures.PdfSigner.CryptoStandard.CMS);
    }

    private PrivateKeySignature GetPrivateKeySignature(string pkey)
    {
        IPrivateKey pk = new PrivateKeyBC(ReadPrivateKey(pkey));
        return new PrivateKeySignature(pk, DigestAlgorithms.SHA256);
    }

    private static AsymmetricKeyParameter ReadPrivateKey(string privateKey)
    {
        var pkPem = PemFormatUtil.GetPkcs8PrivateKeyFormat(privateKey);

        using var sr = new StringReader(pkPem);

        var pemReader = new PemReader(sr);
        var pemObject = pemReader.ReadPemObject();
        var rsa = RsaPrivateKeyStructure.GetInstance(pemObject.Content);

        return new RsaPrivateCrtKeyParameters(rsa.Modulus,
            rsa.PublicExponent, rsa.PrivateExponent,
            rsa.Prime1, rsa.Prime2, rsa.Exponent1,
            rsa.Exponent2, rsa.Coefficient);
    }

    private IX509Certificate[] LoadCertificateChainFromBase64(string certificateBase64)
    {
        // Step 1: Decode the Base64 string into a byte array
        var certBytes = Convert.FromBase64String(certificateBase64);

        // Step 2: Parse the certificate using BouncyCastle
        var certParser = new X509CertificateParser();
        var certificate = certParser.ReadCertificate(certBytes);

        // Step 3: Construct the certificate chain
        var certificateChain = new List<IX509Certificate> { new X509CertificateBC(certificate) };

        // Optional: If you have intermediate certificates, you can load and add them to the chain
        // Add each intermediate certificate to the chain (if you have multiple certificates)
        // For example, assuming you have Base64-encoded intermediate certificates:
        // string[] base64IntermediateCerts = { intermediateCert1, intermediateCert2 };
        // foreach (string intermediateCert in base64IntermediateCerts)
        // {
        //     byte[] intermediateCertBytes = Convert.FromBase64String(intermediateCert);
        //     X509Certificate intermediateCertificate = certParser.ReadCertificate(intermediateCertBytes);
        //     certificateChain.Add(intermediateCertificate);
        // }

        // Step 4: Return the certificate chain as an array of IX509Certificate
        return certificateChain.ToArray();
    }
}