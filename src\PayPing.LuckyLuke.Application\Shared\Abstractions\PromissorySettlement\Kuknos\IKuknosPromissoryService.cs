﻿using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kuknos
{
    public interface IKuknosPromissorySettlementService
    {
        ValueTask<object> HandleAsync(KuknosPromissorySettlementContextV1 context, CancellationToken cancellationToken = default);
    }
}
