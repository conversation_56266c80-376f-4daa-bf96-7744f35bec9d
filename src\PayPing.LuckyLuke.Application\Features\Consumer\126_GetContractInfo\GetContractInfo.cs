using Ardalis.GuardClauses;
using DNTPersianUtils.Core;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Services.Guarantee;
using PayPing.LuckyLuke.Application.Shared.Services.User;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Consumer._126_GetContractInfo;

public sealed record GetContractInfoRequest([Required] Guid orderTrackingCode);

public sealed record GetContractInfoResponse(
    string MerchantName,
    string MerchantNationalCode,
    string MerchantWebSite,
    string MerchantAddress,
    string ConsumerName,
    string ConsumerFatherName,
    string ConsumerNationalCode,
    string ConsumerAddress,
    decimal TotalAmount,
    decimal PrePayment,
    decimal CreditAmount,
    decimal? InstallmentAmount,
    int PlanInstallmentCount,
    string ShortPersianDateNow,
    string FirstInstallmentDueDate,
    int? DayOfMonth
);

public class GetContractInfoEndpoint : IMinimalEndpoint
{
    public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
    {
        builder.MapGet(
                ConsumerRoutes.GetContractInfo,
                async (
                    [AsParameters] GetContractInfoRequest request,
                    IGetContractInfoRequestHandler handler,
                    IValidator<GetContractInfoRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.orderTrackingCode.ToString(),
                            new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);
                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetContractInfo")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<GetContractInfoResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Contract Info")
            .WithDescription("Get Contract Info")
            .WithOpenApi()
            .HasApiVersion(1.0);

        return builder;
    }
}

public class GetContractInfoRequestHandler(
    ApplicationDbContext dbContext,
    IUserContext userContext, IGuaranteeService guaranteeService, IUserService userService) : IGetContractInfoRequestHandler
{
    public async Task<GetContractInfoResponse> HandleAsync(
        GetContractInfoRequest request,
        CancellationToken cancellationToken)
    {
        var userId = userContext.CurrentUserId.GetValueOrDefault();

        var order = await dbContext.Orders.AsNoTracking()
                        .Where(o => o.TrackingCode == request.orderTrackingCode)
                        .Include(o => o.Installments)
                        .FirstOrDefaultAsync(cancellationToken)
                    ?? throw new NotFoundException(request.orderTrackingCode.ToString(), "order");

        if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            throw new OrderExpiredException(request.orderTrackingCode.ToString(), "سفارش منقضی شده است");

        if (order.ConsumerUserId != userId)
            throw new CustomValidationException(request.orderTrackingCode.ToString(), "سفارش متعلق به کاربر نیست",
            string.Empty);

        var gtypes = await guaranteeService.GetGuaranteeTypesByGuarantorId(order.OrderPlan.GuarantorId);
        switch (gtypes.guaranteeType)
        {
            case GuaranteeType.None:
            case GuaranteeType.Salary:
                if (order.Status != OrderStatus.CreditValidationSucceeded)
                    throw new CustomValidationException(request.orderTrackingCode.ToString(),
                        "سفارش در مرحله امضای قرارداد نیست", string.Empty);
                break;
            case GuaranteeType.Promissory:
            case GuaranteeType.Cheque:
            default:
                if (order.Status != OrderStatus.GuaranteeSucceeded)
                    throw new CustomValidationException(request.orderTrackingCode.ToString(),
                        "سفارش در مرحله امضای قرارداد نیست", string.Empty);
                break;
        }
        
        var consumer = await dbContext.ConsumerInfos
                           .AsNoTracking()
                           .Where(c => c.ConsumerUserId == order.ConsumerUserId)
                           .FirstOrDefaultAsync(cancellationToken)
                       ?? throw new ArgumentException("اطلاعات مشتری یافت نشد");

        var merchant = await dbContext.MerchantInfos
                           .AsNoTracking()
                           .Where(m => m.MerchantUserId == order.MerchantUserId)
                           .FirstOrDefaultAsync(cancellationToken)
                       ?? throw new ArgumentException("اطلاعات فروشنده یافت نشد");

        var merchantExtraInfo = await userService.GetMerchantExtraInfoAsync(order.MerchantUserId, cancellationToken) ?? throw new ArgumentException("اطلاعات فروشنده یافت نشد");

        var plan = order.OrderPlan;

        var merchantName = merchantExtraInfo.DisplayName;
        var merchantNationalCode = merchantExtraInfo.DisplayNationalCode;
        var merchantWebSite = order.ClientCallbackUrl.ExtractDomain();
        var merchantAddress = merchant.Address;

        var consumerName = consumer.FullName;
        var consumerFatherName = consumer.FatherName;
        var consumerNationalCode = consumer.NationalCode;
        var consumerAddress = consumer.Address;

        var installment = order.Installments.FirstOrDefault();

        var totalAmount = order.OrderTotalAmount;
        var prePayment = order.PrePaymentAmount;
        var creditAmount = order.CreditedAmount;
        var installmentAmount = installment?.TotalAmount;
        var planInstallmentCount = plan.InstallmentCount;

        var shortPersianDateNow = DateTimeOffset.UtcNow.ToShortPersianDateString();
        var firstInstallmentDueDate = installment?.DueDate.ToShortPersianDateString();
        var dayOfMonth = installment?.DueDate.GetPersianDayOfMonth();

        return new GetContractInfoResponse(merchantName,
            merchantNationalCode,
            merchantWebSite,
            merchantAddress,
            consumerName,
            consumerFatherName,
            consumerNationalCode,
            consumerAddress,
            totalAmount,
            prePayment,
            creditAmount,
            installmentAmount,
            planInstallmentCount,
            shortPersianDateNow,
            firstInstallmentDueDate,
            dayOfMonth);
    }
}

public interface IGetContractInfoRequestHandler
{
    Task<GetContractInfoResponse> HandleAsync(GetContractInfoRequest request, CancellationToken cancellationToken);
}

public class GetContractInfoValidator : AbstractValidator<GetContractInfoRequest>
{
    public GetContractInfoValidator()
    {
        RuleFor(x => x.orderTrackingCode).NotEmpty()
            .WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
    }
}