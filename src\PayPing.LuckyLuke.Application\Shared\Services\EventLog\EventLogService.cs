﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Shared.Abstractions;

namespace PayPing.LuckyLuke.Application.Shared.Services.EventLog
{
    public class EventLogService : IEventLogService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly ILogger<EventLogService> _logger;
        private readonly BNPLOptions _bnplOptions;


        public EventLogService(ApplicationDbContext dbContext, IUserContext userContext, ILogger<EventLogService> logger, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask AddOrderStateChangeEventLogAsync(Guid trackingCode, OrderStatus status)
        {
            var oel = new PayPing.BNPL.Domain.Models.EventLog()
            {
                RefId = trackingCode.ToString(),
                LogType = Domain.Enums.EventLogType.OrderStatusChanged,
                Name = "payping.order_status_changed",
                UserId = _userContext.CurrentUserId ?? default,
                Status = status
            };

            _dbContext.EventLogs.Add(oel);

            await _dbContext.SaveChangesAsync();
        }
    }
}
