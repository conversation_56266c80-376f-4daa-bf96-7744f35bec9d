﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedWallet : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Wallets",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Deposit = table.Column<decimal>(type: "numeric", nullable: false),
                    Freezed = table.Column<decimal>(type: "numeric", nullable: false),
                    Balance = table.Column<decimal>(type: "numeric", nullable: false),
                    LastModifiedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Wallets", x => x.Id);
                });

            migrationBuilder.Sql(@"
INSERT INTO ""Wallets"" (
""Id"",
""Deposit"",
""Freezed"",
""Balance"",
""LastModifiedAt"",
""LastModifiedBy"",
""CreatedAt"",
""CreatedBy""
)
SELECT c.""WalletId"" , c.""Deposit"" , c.""Freezed"" , c.""Balance"" , c.""LastModifiedAt"" , c.""LastModifiedBy"" , c.""CreatedAt"" , c.""CreatedBy"" 
FROM ""Credits"" c;
");

            migrationBuilder.DropColumn(
                name: "Balance",
                table: "Credits");

            migrationBuilder.DropColumn(
                name: "Deposit",
                table: "Credits");

            migrationBuilder.DropColumn(
                name: "Freezed",
                table: "Credits");

            migrationBuilder.RenameIndex(
                name: "IX_Credit_WalletId",
                table: "Credits",
                newName: "IX_Credits_WalletId");


            migrationBuilder.AddForeignKey(
                name: "FK_Credits_Wallets_WalletId",
                table: "Credits",
                column: "WalletId",
                principalTable: "Wallets",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Credits_Wallets_WalletId",
                table: "Credits");

            migrationBuilder.DropTable(
                name: "Wallets");

            migrationBuilder.RenameIndex(
                name: "IX_Credits_WalletId",
                table: "Credits",
                newName: "IX_Credit_WalletId");

            migrationBuilder.AddColumn<decimal>(
                name: "Balance",
                table: "Credits",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "Deposit",
                table: "Credits",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "Freezed",
                table: "Credits",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);
        }
    }
}
