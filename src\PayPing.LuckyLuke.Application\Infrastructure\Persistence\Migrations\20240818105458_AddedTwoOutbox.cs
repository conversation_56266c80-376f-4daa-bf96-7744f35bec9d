﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedTwoOutbox : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PaymentId",
                table: "Installments");

            migrationBuilder.DropColumn(
                name: "Balance",
                table: "CreditTransactions");

            migrationBuilder.DropColumn(
                name: "Freezed",
                table: "CreditTransactions");

            migrationBuilder.DropColumn(
                name: "TotalDeposit",
                table: "CreditTransactions");

            migrationBuilder.AddColumn<string>(
                name: "ClientCancelUrl",
                table: "Orders",
                type: "character varying(512)",
                maxLength: 512,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "GuaranteeRevoked",
                table: "Orders",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "OrderPlan_CreditId",
                table: "Orders",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "OrderPlan_GuarantorId",
                table: "Orders",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<Guid>(
                name: "OrderPlan_WalletId",
                table: "Orders",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<int>(
                name: "CreditId",
                table: "OrderPayments",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<long>(
                name: "InstallmentId",
                table: "OrderPayments",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "WalletId",
                table: "OrderPayments",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<decimal>(
                name: "Balance",
                table: "Credits",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "Deposit",
                table: "Credits",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "Freezed",
                table: "Credits",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.CreateTable(
                name: "CreditTransactionOutboxes",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CreditId = table.Column<int>(type: "integer", nullable: false),
                    WalletId = table.Column<Guid>(type: "uuid", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric", nullable: false),
                    LockId = table.Column<string>(type: "text", nullable: true),
                    TransactionType = table.Column<int>(type: "integer", nullable: false),
                    HasFailed = table.Column<bool>(type: "boolean", nullable: false),
                    FailCount = table.Column<int>(type: "integer", nullable: false),
                    Message = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CreditTransactionOutboxes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CreditTransactionOutboxes_Credits_CreditId",
                        column: x => x.CreditId,
                        principalTable: "Credits",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OrderExternalEventOutboxes",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    OrderId = table.Column<long>(type: "bigint", nullable: false),
                    ClientRefId = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: true),
                    ClientCancelUrl = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: true),
                    EventType = table.Column<int>(type: "integer", nullable: false),
                    HasFailed = table.Column<bool>(type: "boolean", nullable: false),
                    FailCount = table.Column<int>(type: "integer", nullable: false),
                    Message = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderExternalEventOutboxes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderExternalEventOutboxes_Orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OrderPayments_InstallmentId",
                table: "OrderPayments",
                column: "InstallmentId");

            migrationBuilder.CreateIndex(
                name: "IX_CreditTransactionOutbox_WalletId",
                table: "CreditTransactionOutboxes",
                column: "WalletId");

            migrationBuilder.CreateIndex(
                name: "IX_CreditTransactionOutboxes_CreditId",
                table: "CreditTransactionOutboxes",
                column: "CreditId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderExternalEventOutboxes_OrderId",
                table: "OrderExternalEventOutboxes",
                column: "OrderId");

            migrationBuilder.AddForeignKey(
                name: "FK_OrderPayments_Installments_InstallmentId",
                table: "OrderPayments",
                column: "InstallmentId",
                principalTable: "Installments",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderPayments_Installments_InstallmentId",
                table: "OrderPayments");

            migrationBuilder.DropTable(
                name: "CreditTransactionOutboxes");

            migrationBuilder.DropTable(
                name: "OrderExternalEventOutboxes");

            migrationBuilder.DropIndex(
                name: "IX_OrderPayments_InstallmentId",
                table: "OrderPayments");

            migrationBuilder.DropColumn(
                name: "ClientCancelUrl",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "GuaranteeRevoked",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "OrderPlan_CreditId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "OrderPlan_GuarantorId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "OrderPlan_WalletId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "CreditId",
                table: "OrderPayments");

            migrationBuilder.DropColumn(
                name: "InstallmentId",
                table: "OrderPayments");

            migrationBuilder.DropColumn(
                name: "WalletId",
                table: "OrderPayments");

            migrationBuilder.DropColumn(
                name: "Balance",
                table: "Credits");

            migrationBuilder.DropColumn(
                name: "Deposit",
                table: "Credits");

            migrationBuilder.DropColumn(
                name: "Freezed",
                table: "Credits");

            migrationBuilder.AddColumn<Guid>(
                name: "PaymentId",
                table: "Installments",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<decimal>(
                name: "Balance",
                table: "CreditTransactions",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "Freezed",
                table: "CreditTransactions",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalDeposit",
                table: "CreditTransactions",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);
        }
    }
}
