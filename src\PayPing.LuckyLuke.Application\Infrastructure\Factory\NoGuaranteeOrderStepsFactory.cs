﻿using PayPing.LuckyLuke.Application.Domain.Enums;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class NoGuaranteeOrderStepsFactory : IOrderStepsFactory
    {
        private readonly IContractMandatoryStepsProvider _contractMandatoryStepsProvider;
        private readonly IContractLessStepsProvider _contractLessStepsProvider;

        public NoGuaranteeOrderStepsFactory(IEnumerable<IContractMandatoryStepsProvider> contractMandatoryStepsProviders, IEnumerable<IContractLessStepsProvider> contractLessStepsProviders)
        {
            _contractMandatoryStepsProvider = contractMandatoryStepsProviders.Where(x => x.GuaranteeType == GuaranteeType.None).First();
            _contractLessStepsProvider = contractLessStepsProviders.Where(x => x.GuaranteeType == GuaranteeType.None).First();
        }

        public GuaranteeType GuaranteeType => GuaranteeType.None;

        public IContractMandatoryStepsProvider CreateContractMandatory()
        {
            return _contractMandatoryStepsProvider;
        }

        public IContractLessStepsProvider CreateContractLess()
        {
            return _contractLessStepsProvider;
        }
    }
}
