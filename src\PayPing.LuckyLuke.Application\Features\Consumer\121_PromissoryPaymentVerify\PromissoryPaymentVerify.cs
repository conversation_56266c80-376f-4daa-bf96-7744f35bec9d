﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Factory;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Services.Guarantee;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Features.Consumer._121_PromissoryPaymentVerifyVerify
{
    public record PromissoryPaymentVerifyRequest(string id, string status);

    public class PromissoryPaymentVerifyEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.PromissoryPaymentVerify,
                async (
                    PromissoryPaymentVerifyRequest request,
                    IPromissoryPaymentVerifyRequestHandler handler,
                    IValidator<PromissoryPaymentVerifyRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("PromissoryPaymentVerify")
            .WithApiVersionSet(builder.NewApiVersionSet("Promissory").Build())
            .Produces<PromissoryCheckPointResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Promissory Payment Verify")
            .WithDescription("Promissory Payment Verify")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class PromissoryPaymentVerifyRequestHandler : IPromissoryPaymentVerifyRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IUserContext _userContext;
        private readonly ILogger<PromissoryPaymentVerifyRequestHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public PromissoryPaymentVerifyRequestHandler(
            ApplicationDbContext dbContext,
            IGuaranteeService guaranteeService,
            IUserContext userContext,
            IOptions<BNPLOptions> bnplOptions,
            ILogger<PromissoryPaymentVerifyRequestHandler> logger)
        {
            _dbContext = dbContext;
            _guaranteeService = guaranteeService;
            _userContext = userContext;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<PromissoryCheckPointResponse> HandleAsync(PromissoryPaymentVerifyRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var orderGuarantee = await _dbContext.OrderGuarantees
                .Where(x => x.GuaranteeRefId == request.id)
                .Include(x => x.Order)
                .Include(x => x.Guarantor)
                .FirstOrDefaultAsync();

            if (orderGuarantee == null)
            {
                throw new NotFoundException(request.id.ToString(), "orderGuarantee");
            }

            var order = orderGuarantee.Order;

            IPromissoryGuaranteeProvider provider = ChooseProviderWithGuarantor(orderGuarantee);

            if (provider.IsPaymentSuccessful(request.status))
            {
                orderGuarantee.Data = provider.SetPaymentStatusToGuaranteeData(orderGuarantee.Data, true);

                await _dbContext.SaveChangesAsync();

                var checkPoint = await provider.PushToNextCheckPointAsync(orderGuarantee.Data, cancellationToken);

                order.Status = OrderStatus.GuaranteeSucceeded;

                await _dbContext.SaveChangesAsync();

                _logger.LogInformation(message: ApplicationConstants.PromissoryDoneLogMessageKey, orderGuarantee.Id, order.Id);

                return checkPoint;
            }
            else
            {
                orderGuarantee.Data = provider.SetPaymentStatusToGuaranteeData(orderGuarantee.Data, false);

                order.Status = OrderStatus.GuaranteeFailed;

                await _dbContext.SaveChangesAsync();

                var revokeResult = await provider.DeleteGuaranteeAsync(orderGuarantee.Data);

                return new PromissoryCheckPointResponse(false, false, "پرداخت هزینه سفته ناموفق بود", Infrastructure.Web.OrderSteps.IssuePromissory, string.Empty);
            }
        }

        private OrderGuarantee CurrentGuarantee(ICollection<OrderGuarantee> guarantees)
        {
            return guarantees.OrderByDescending(x => x.CreatedAt).FirstOrDefault();
        }

        private IPromissoryGuaranteeProvider ChooseProviderWithGuarantor(OrderGuarantee guarantee)
        {
            var pt = guarantee.Guarantor.GuaranteeProvider;
            return _guaranteeService.GetPromissoryProviderByProvider(pt);
        }
    }

    public interface IPromissoryPaymentVerifyRequestHandler
    {
        ValueTask<PromissoryCheckPointResponse> HandleAsync(PromissoryPaymentVerifyRequest request, CancellationToken cancellationToken);
    }

    public class PromissoryPaymentVerifyValidator : AbstractValidator<PromissoryPaymentVerifyRequest>
    {
        public PromissoryPaymentVerifyValidator()
        {
            RuleFor(x => x.id).NotEmpty().WithResourceError(() => ValidatorDictionary.guarantee_reference_id_is_required);
            RuleFor(x => x.status).NotEmpty().WithResourceError(() => ValidatorDictionary.promissory_payment_status_is_required); ;
        }
    }
}
