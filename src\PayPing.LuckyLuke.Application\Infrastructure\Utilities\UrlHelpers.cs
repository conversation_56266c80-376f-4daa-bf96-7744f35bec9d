﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Utilities
{
    public static class UrlHelpers
    {
        public static string BuildFullApiUrl(string baseUrl, string path, int apiVersion)
        {
            return $"{baseUrl}/{path}".Replace("{version:apiVersion}", apiVersion.ToString());
        }

        public static string BuildFullUiUrl(string baseUrl, string path, params Tuple<string, string>[] queryStrings)
        {
            var url = new StringBuilder($"{baseUrl}/{path}");

            if (queryStrings != null)
            {
                for (int i = 0; i < queryStrings.Length; i++)
                {
                    if (i == 0) url.Append("?");
                    else url.Append("&");

                    var qs = queryStrings[i];
                    url.Append($"{qs.Item1}={qs.Item2}");
                }
            }
            
            return url.ToString();
        }

        public static string ExtractDomain(this string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return url;

            var uri = new Uri(url);
            return $"{uri.Scheme}://{uri.Host}{(!uri.IsDefaultPort ? $":{uri.Port}" : "")}";
        }

        public static string BuildCancelUrl(this string url, Guid orderTrackingCode, string clientRefId, string clientCallbackUrl)
        {
            if (string.IsNullOrWhiteSpace(url))
                return clientCallbackUrl.ExtractDomain();

            var uri = new Uri(url);
            if (url.Contains('?'))
            {
                return $"{url}&trackingCode={orderTrackingCode}&refId={clientRefId}";
            }
            else
            {
                return $"{url}?trackingCode={orderTrackingCode}&refId={clientRefId}";
            }
        }

        public static bool IsValidUrl(string url)
        {
            return Uri.TryCreate(url, UriKind.Absolute, out Uri uriResult)
                   && (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps);
        }
    }
}
