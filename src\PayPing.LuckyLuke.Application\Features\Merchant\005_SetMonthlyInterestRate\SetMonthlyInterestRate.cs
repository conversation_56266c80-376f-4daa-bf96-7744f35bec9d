﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._005_SetMonthlyInterestRate
{
    public record SetMonthlyInterestRateRequest(int contractId, decimal monthlyInterestRate);

    public record SetMonthlyInterestRateResponse(int contractId);

    public class SetMonthlyInterestRateEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.SetMonthlyInterestRate,
                async (SetMonthlyInterestRateRequest request, ISetMonthlyInterestRateRequestHandler handler, IValidator<SetMonthlyInterestRateRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("SetMonthlyInterestRate")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<SetMonthlyInterestRateResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Set Monthly Interest Rate")
            .WithDescription("Set Monthly Interest Rate")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class SetMonthlyInterestRateRequestHandler : ISetMonthlyInterestRateRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public SetMonthlyInterestRateRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<SetMonthlyInterestRateResponse> HandleAsync(SetMonthlyInterestRateRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var contract = await _dbContext.Contracts
               .Where(c => c.Id == request.contractId)
               .Include(p => p.Credits)
               .FirstOrDefaultAsync();

            if (contract == null)
            {
                throw new ArgumentException("قرارداد فروش اقساطی فعال یافت نشد");
            }

            var merchantInfo = await _dbContext.MerchantInfos
                .Where(p => p.MerchantUserId == userId)
                .FirstOrDefaultAsync();

            if (merchantInfo == null)
            {
                throw new ArgumentException("اطلاعات مرچنت یافت نشد");
            }

            merchantInfo.InterestRate = request.monthlyInterestRate;

            contract.ActivationStatus = ActivationStatus.SetMonthlyInterestRate;

            await _dbContext.SaveChangesAsync();

            return new SetMonthlyInterestRateResponse(contract.Id);
        }

    }
    public interface ISetMonthlyInterestRateRequestHandler
    {
        ValueTask<SetMonthlyInterestRateResponse> HandleAsync(SetMonthlyInterestRateRequest request, CancellationToken cancellationToken);
    }

    public class SetMonthlyInterestRateValidator : AbstractValidator<SetMonthlyInterestRateRequest>
    {
        public SetMonthlyInterestRateValidator()
        {
            RuleFor(c => c.contractId)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.contract_id_is_required);
            RuleFor(x => x.monthlyInterestRate).GreaterThanOrEqualTo(0).WithResourceError(() => ValidatorDictionary.interest_rate_greater);
        }
    }

}
