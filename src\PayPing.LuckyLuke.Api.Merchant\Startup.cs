﻿using PayPing.BNPL.Application.Infrastructure.Configurations;

namespace PayPing.LuckyLuke.Api.Merchant
{
    public class Startup
    {
        private IWebHostEnvironment WebHostEnvironment { get; }
        private IConfiguration Configuration { get; }

        public Startup(
            IWebHostEnvironment webHostEnvironment,
            IConfiguration configuration)
        {
            WebHostEnvironment = webHostEnvironment;
            Configuration = configuration;
        }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddMerchantApplication(Configuration, WebHostEnvironment);
            services.AddInfrastructure(Configuration, WebHostEnvironment);

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseMerchantApplication(env);
        }
    }
}