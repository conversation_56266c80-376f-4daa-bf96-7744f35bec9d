﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Shared.Mappers
{
    public static class KuknosMapper
    {
        public static KuknosApiIssuePromissoryRequest ToApiRequest(this KuknosPromissoryContextV1 c, KuknosDigitalSignContextV1 d)
        {
            KuknosApiIssuePromissoryRecipientRequest recipient = new (c.Recipient.NationalCode, c.Recipient.MobileNumber, c.Recipient.FullName, c.Recipient.Address, c.Recipient.PostalCode, c.Recipient.IsLegal);
            KuknosApiIssuePromissoryIssuerRequest issuer = new(d.NationalCode, d.Mobile, c.IbanWithIR, d.<PERSON>a, d.<PERSON>dress, d.PostalCode, c.<PERSON>);
            return new (c<PERSON>, c.<PERSON>, c.Description, issuer, recipient, c.Predefined<PERSON>ame);
        }
    }
}
