﻿using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class CreditTransactionOutbox : BaseEntity<long>
    {
        public int CreditId { get; set; }
        public Guid WalletId { get; set; }

        public decimal Amount { get; set; }

        public string LockId { get; set; }

        public CreditTransactionType TransactionType { get; set; }

        public bool HasFailed { get; set; }

        public int FailCount { get; set; }

        public string Message { get; set; }

        public Credit Credit { get; set; }
    }
}
