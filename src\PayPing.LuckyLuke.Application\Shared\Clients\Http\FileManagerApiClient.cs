﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Options;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using System.Net.Http.Json;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Clients.Http
{
    public class FileManagerApiClient : IFileManagerApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonSerializerOptions;

        public FileManagerApiClient(HttpClient httpClient)
        {
            _httpClient = Guard.Against.Null(httpClient, nameof(httpClient));
            _jsonSerializerOptions = new JsonSerializerOptions{PropertyNameCaseInsensitive = true};
        }


        public async ValueTask<string> UploadAsync(byte[] file, string name, string extension, int userId, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}Admin/AdminUploadToMisc");

            var content = new MultipartFormDataContent
            {
                { new StringContent(userId.ToString()), "userId" },
                { new StringContent(extension), "extension" },
                { new StreamContent(new MemoryStream(file)), "file", name }
            };

            request.Content = content;
            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            return await response.Content.ReadFromJsonAsync<string>(cancellationToken);
        }

        public async ValueTask<string> GetMiscPresignedUrlAsync(int userId, string fileId, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_httpClient.BaseAddress}Admin/ReturnFilePath?userId={userId}&uploadType=3&fileName={fileId}");
            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();

            return await response.Content.ReadFromJsonAsync<string>(cancellationToken);
        }
    }
}
