﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kuknos;

namespace PayPing.LuckyLuke.Application.Shared.Services.DigitalSign.Kuknos
{
    public class GetCertificateHandler : KuknosBaseDigitalSignHandler, IKuknosDigitalSignService
    {
        public GetCertificateHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
        }

        public async ValueTask<object> HandleAsync(KuknosDigitalSignContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosDigitalSignStatusV1.GetCertificate)
                throw new Exception("certificate not in done status");

            if (string.IsNullOrWhiteSpace(context.Certificate))
            {
                ValidateContext(context);

                var result = await _kuknosApi.GetCertificateAsync(context.PublicKey, context.CertificateTrackingCode, context.OrderTrackingCode, cancellationToken);

                if (!result.meta.success || string.IsNullOrWhiteSpace(result.data))
                {
                    throw new CertificateNotReadyException(context.OrderTrackingCode?.ToString(), "در حال آماده سازی گواهی دیجیتال");
                }

                context.Certificate = result.data;
            }

            context.Status = KuknosDigitalSignStatusV1.CertificateDone;

            //// save context
            await UpdateContextAsync(context);

            return new GetCertificateResult(context.CertificateTrackingCode);
        }

        protected override void ValidateContext(KuknosDigitalSignContextV1 context)
        {
            base.ValidateContext(context);
            Guard.Against.NullOrEmpty(context.CertificateTrackingCode);
            Guard.Against.NullOrEmpty(context.CertificateSigningRequest);
        }
    }
}
