﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedBalanceAndConsumedAtTransactionToCreditTransaction : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Balance",
                table: "CreditTransactions",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "BalanceAtTransaction",
                table: "CreditTransactions",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "ConsumedAtTransaction",
                table: "CreditTransactions",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "Freezed",
                table: "CreditTransactions",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalDeposit",
                table: "CreditTransactions",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Balance",
                table: "CreditTransactions");

            migrationBuilder.DropColumn(
                name: "BalanceAtTransaction",
                table: "CreditTransactions");

            migrationBuilder.DropColumn(
                name: "ConsumedAtTransaction",
                table: "CreditTransactions");

            migrationBuilder.DropColumn(
                name: "Freezed",
                table: "CreditTransactions");

            migrationBuilder.DropColumn(
                name: "TotalDeposit",
                table: "CreditTransactions");
        }
    }
}
