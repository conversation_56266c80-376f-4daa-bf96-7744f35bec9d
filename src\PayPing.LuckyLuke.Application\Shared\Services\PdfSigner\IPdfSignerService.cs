﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.Tools.SdkBase.Types;

namespace PayPing.LuckyLuke.Application.Shared.Services.PdfSigner;

public interface IPdfSignerService
{
    /// <summary>
    ///     Gets the type of digital signature provider this service works with
    /// </summary>
    DigitalSignType DigitalSignType { get; }

    /// <summary>
    ///     Signs a PDF document using the appropriate digital signature provider
    /// </summary>
    /// <param name="userId">The user ID of the signer</param>
    /// <param name="rawContract">The raw PDF document stream to sign</param>
    /// <param name="orderTrackingCode">The tracking code of the order</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A service result containing the file ID of the signed document</returns>
    Task<ServiceResult<string>> SignPdfDocumentAsync(int userId,
        Stream rawContract,
        Guid orderTrackingCode,
        CancellationToken cancellationToken);
}