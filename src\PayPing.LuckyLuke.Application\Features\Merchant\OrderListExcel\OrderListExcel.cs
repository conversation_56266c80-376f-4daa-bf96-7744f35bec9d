﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.ComponentModel;

namespace PayPing.LuckyLuke.Application.Features.Merchant.OrderListExcel
{
    public record OrderListExcelRequest(DateTime? fromDate, DateTime? toDate, string consumerName, string mobile, decimal? creditAmount, Guid? trackingCode, OrderStatusExcelRequest? orderStatusRequest);

    public enum OrderStatusExcelRequest
    {
        [Description("در حال پرداخت")]
        Paying = 1,
        [Description("تکمیل شده")]
        Completed = 2,
        [Description("معوق")]
        Delayed = 3,
        [Description("نکول شده")]
        Defaulted = 4
    }
    public record OrderListExcelResponse(bool success, string filePath, string message);

    public class OrderListExcelEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                MerchantRoutes.OrderListExcel,
                async (
                    [AsParameters] OrderListExcelRequest request,
                    IOrderListExcelRequestHandler handler,
                    IValidator<OrderListExcelRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("OrderListExcel")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<OrderListExcelResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Order List Excel")
            .WithDescription("Order List Excel")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OrderListExcelRequestHandler : IOrderListExcelRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IExcelService _excelService;
        private readonly IUploadGrpcClient _uploadGrpcClient;

        public OrderListExcelRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions, IExcelService excelService, IUploadGrpcClient uploadGrpcClient)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _bnplOptions = bnplOptions.Value;
            _excelService = excelService;
            _uploadGrpcClient = uploadGrpcClient;
        }

        public async ValueTask<OrderListExcelResponse> HandleAsync(OrderListExcelRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;
            var prepaidOrdersStatus = OrderStatusProvider.GetPrePaidOrMerchantCanceled();

            var q = _dbContext.Orders.AsNoTracking().Where(o => o.MerchantUserId == userId);

            switch (request.orderStatusRequest)
            {
                case OrderStatusExcelRequest.Paying:
                    q = q.Where(o =>
                        o.Status == OrderStatus.PrePaymentSucceeded ||
                        o.Status == OrderStatus.InstallmentsPaymentInProgress);
                    break;
                case OrderStatusExcelRequest.Completed:
                    q = q.Where(o => o.Status == OrderStatus.PaidOff);
                    break;
                case OrderStatusExcelRequest.Delayed:
                    q = q.Where(o => o.Status == OrderStatus.InstallmentsPaymentDelayed);
                    break;
                case OrderStatusExcelRequest.Defaulted:
                    q = q.Where(o => o.Status == OrderStatus.InstallmentsPaymentDefaulted);
                    break;
                default:
                    q = q.Where(o => prepaidOrdersStatus.Contains(o.Status));
                    break;
            }

            if (request.fromDate.HasValue)
            {
                q = q.Where(o => o.CreatedAt >= request.fromDate);
            }

            if (request.toDate.HasValue)
            {
                q = q.Where(o => o.CreatedAt <= request.toDate);
            }

            if (!string.IsNullOrEmpty(request.consumerName))
            {
                q = q.Where(o => o.ConsumerUserId.HasValue && o.ConsumerName.Contains(request.consumerName));
            }

            if (!string.IsNullOrEmpty(request.mobile))
            {
                q = q.Where(o => o.ConsumerUserName == request.mobile);
            }

            if (request.creditAmount.HasValue)
            {
                q = q.Where(o => o.CreditedAmount == request.creditAmount);
            }

            if (request.trackingCode != null)
            {
                q = q.Where(o => o.TrackingCode == request.trackingCode);
            }

            var total = await q.LongCountAsync(cancellationToken);

            var beforePayCancelableStatuses = OrderStatusProvider.GetMerchantBeforePaymentCancelable();
            var afterPayCancelableStatuses = OrderStatusProvider.GetMerchantAfterPaymentCancelable();

            var orders = await q
            .OrderByDescending(o => o.CreatedAt)
            .Take(10000)
            .Select(o => new OrderInfo
            {
                orderTrackingCode = o.TrackingCode,
                createdDate = o.CreatedAt,
                creditAmount = o.CreditedAmount,
                consumer = o.ConsumerName,
                totalInstallmentCount = o.Installments.Where(x => true).Count(),
                paidoffInstallmentCount = o.Installments.Count(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff),
                isCancelable = beforePayCancelableStatuses.Contains(o.Status) || afterPayCancelableStatuses.Contains(o.Status),
                mobile = o.ConsumerUserName
            })
             .ToListAsync(cancellationToken);

            if (!orders.Any())
                return new(false, string.Empty, "سفارش خرید اقساطی با فیلتر های انتخابی یافت نشد");

            OrderInfos orderInfos = new OrderInfos
            {
                orders = orders,
                totalCount = orders.Count()
            };
            var orderListExcelFileStream = await _excelService.CreateOrderInfoExcelFile(orderInfos, cancellationToken);

            var filePathResult = await _uploadGrpcClient.UploadExcelAsync(orderListExcelFileStream, userId, cancellationToken);

            if (!filePathResult.Succeeded)
                return new(false, string.Empty, "خظایی در دریافت گزارش رخ داد");

            return new (true, filePathResult.SuccessResult, string.Empty);
        }
    }

    public interface IOrderListExcelRequestHandler
    {
        ValueTask<OrderListExcelResponse> HandleAsync(OrderListExcelRequest request, CancellationToken cancellationToken);
    }

    public class OrderListExcelValidator : AbstractValidator<OrderListExcelRequest>
    {
        public OrderListExcelValidator()
        {
            RuleFor(x => x.consumerName).MinimumLength(3).When(x => !string.IsNullOrWhiteSpace(x.consumerName));
        }
    }
}
