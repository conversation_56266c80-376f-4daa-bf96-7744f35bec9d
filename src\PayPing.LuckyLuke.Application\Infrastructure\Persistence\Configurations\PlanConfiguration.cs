﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class PlanConfiguration : IEntityTypeConfiguration<Plan>
{
    public void Configure(EntityTypeBuilder<Plan> builder)
    {
        builder.HasIndex(x => x.Code).IsUnique().HasDatabaseName("IX_Plan_Code");

        builder
            .HasMany(e => e.Orders)
            .WithOne(e => e.Plan)
            .HasForeignKey(e => e.PlanId);

        builder
            .HasMany(e => e.OrderTargetPlans)
            .WithOne(e => e.Plan)
            .HasForeignKey(e => e.PlanId)
            .IsRequired();

        builder
            .HasMany(e => e.PlanEmployees)
            .WithOne(e => e.Plan)
            .HasForeignKey(e => e.PlanId)
            .IsRequired();
    }
}