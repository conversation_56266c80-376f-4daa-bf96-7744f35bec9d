﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kuknos;

namespace PayPing.LuckyLuke.Application.Shared.Services.PromissorySettlement.Kuknos
{
    public class SignSettlementHandler : KuknosSettlementBaseHandler, IKuknosPromissorySettlementService
    {
        private readonly IKuknosPromissorySettlementService _next;

        public SignSettlementHandler(
            IKuknosPromissorySettlementService next,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _next = next;
        }

        public async ValueTask<object> HandleAsync(KuknosPromissorySettlementContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosPromissorySettlementStatusV1.Sign)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);


            KuknosApiFileDownloadResponse rawDoc = null;
            if (base.SettlementRawDocBytes != null && base.SettlementRawDocBytes.Length > 0)
            {
                rawDoc = new KuknosApiFileDownloadResponse(base.SettlementRawDocBytes, context.SettlementRawDocumentFileContentType);
            }
            else
            {
                rawDoc = await _kuknosApi.DownloadRawDocumentAsync(context.SettlementRawDocumentHash, context.OrderTrackingCode);
            }

            if (rawDoc == null || rawDoc.contentBytes == null || rawDoc.contentBytes.Length == 0)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not download raw promissory settlement document", false);
            }

            var dsc = await GetValidDigitalSignAsync(context.ConsumerUserId);

            Guard.Against.NullOrEmpty(dsc.Certificate);

            var result = await _kuknosApi.SignDocumentWithSignerPdfAsync(dsc.Certificate, dsc.PrivateKey, rawDoc.contentBytes, context.OrderTrackingCode, cancellationToken);

            if (result == null || result.contentBytes == null || result.contentBytes.Length == 0)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not sign raw promissory settlement document", false);
            }

            // upload file to kuknos ipfs
            var ipfsHash = await _kuknosApi.UploadAsync(result.contentBytes, $"signed-settlement-{context.OrderGuaranteeId}.pdf", context.OrderTrackingCode, cancellationToken);

            if (string.IsNullOrWhiteSpace(ipfsHash.data))
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not upload signed promissory settlement to kuknos ipfs service", false);
            }

            var signResult = await _kuknosApi.SignSettlementAsync(context.PromissoryId, ipfsHash.data, context.OrderTrackingCode, cancellationToken);
            if (!signResult.data)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not send signed settlement", false);
            }

            context.Status = KuknosPromissorySettlementStatusV1.Finalize;
            context.SettlementSignedDocumentHash = ipfsHash.data;


            //// save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

        protected override void ValidateContext(KuknosPromissorySettlementContextV1 context)
        {
            base.ValidateContext(context);
            Guard.Against.NullOrEmpty(context.SettlementRawDocumentHash);
            Guard.Against.NullOrEmpty(context.SettlementRawDocumentFileContentType);
        }
    }
}
