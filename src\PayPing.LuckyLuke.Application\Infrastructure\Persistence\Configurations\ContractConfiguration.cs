﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Application.Infrastructure.Persistence.NpgSql;
using PayPing.BNPL.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class ContractConfiguration : IEntityTypeConfiguration<Contract>
{
    public void Configure(EntityTypeBuilder<Contract> builder)
    {
        builder.HasIndex(x => x.MerchantUserId).HasDatabaseName("IX_Contract_MerchantUserId");
        builder.Property(nameof(Contract.StartDate)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);
        builder.Property(nameof(Contract.ExpireDate)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);



        builder
            .HasMany(e => e.Credits)
            .WithOne(e => e.Contract)
            .HasForeignKey(e => e.ContractId)
            .IsRequired();
    }
}
