﻿using Ardalis.GuardClauses;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using System.Net.Http.Json;
using System.Text.Json;
using Newtonsoft.Json.Linq;

namespace PayPing.LuckyLuke.Application.Shared.Clients.Http
{
    public class PaymentIpgApiClient : IPaymentIpgApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonSerializerOptions;


        public PaymentIpgApiClient(HttpClient httpClient)
        {
            _httpClient = Guard.Against.Null(httpClient, nameof(httpClient));
            _jsonSerializerOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
        }


        public async ValueTask<PaymentApiPayResponse> PayInstallmentAsync(PaymentIpgApiPayRequest model, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}admin/pay");

            request.Content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");

            var response = await _httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var rc = await response.Content.ReadFromJsonAsync<PaymentIpgApiClientPayResponse>(_jsonSerializerOptions, cancellationToken);
                return new PaymentApiPayResponse(true, default, null, null, default, null, rc.Code, $"{_httpClient.BaseAddress}/v1/pay/gotoipg/{rc.Code}");
            }
            else
            {
                var ec = await response.Content.ReadAsStringAsync(cancellationToken);
                return new PaymentApiPayResponse(false, default, ec, ec, default, default, null, null);
            }
        }

        public async ValueTask<PaymentApiVerifyResponse> VerifyInstallmentAsync(PaymentIpgApiVerifyRequest model, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}admin/pay/verify");

            request.Content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");

            var response = await _httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var rc = await response.Content.ReadFromJsonAsync<PaymentIpgApiClientVerifyResponse>(_jsonSerializerOptions, cancellationToken);
                return new PaymentApiVerifyResponse(true, default, null, null, default, null, rc.Amount, null, null, default);
            }
            else
            {
                var ec = await response.Content.ReadAsStringAsync(cancellationToken);
                return new PaymentApiVerifyResponse(false, default, ec, ec, default, default, default, null, null, default);
            }
        }
    }
}
