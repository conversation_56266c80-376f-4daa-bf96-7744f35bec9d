﻿syntax = "proto3";

option csharp_namespace = "PayPing.Refund.WebApi.gRPC.Types";

import "google/protobuf/wrappers.proto";

package RefundGrpcServices;

message GrpcProblemDetails {
	string paypingTraceId = 1;
	string instance = 2;
	string title = 3;
	string detail = 4;
	int32 statusCode = 5;
	int32 errorCode = 6;
	repeated problemDetailsError errors = 7;
}

message problemDetailsError {
	string key = 1;
	google.protobuf.StringValue value = 2;
}