﻿using DNTPersianUtils.Core;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kuknos;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Infrastructure.Provider
{
    public class KuknosDigitalSignProvider : IDigitalSignProvider
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IUserService _userService;
        private readonly ApplicationDbContext _dbContext;
        private readonly IKuknosDigitalSignService _kuknosDigitalSignService;
        private readonly IKuknosApiHttpClient _kuknosApi;

        public KuknosDigitalSignProvider(IWebHostEnvironment environment, IUserService userService, ApplicationDbContext dbContext, IKuknosDigitalSignService kuknosDigitalSignService, IKuknosApiHttpClient kuknosApi)
        {
            _environment = environment;
            _userService = userService;
            _dbContext = dbContext;
            _kuknosDigitalSignService = kuknosDigitalSignService;
            _kuknosApi = kuknosApi;
        }

        public DigitalSignType DigitalSignType => DigitalSignType.KuknosVersion1;


        public async ValueTask<string> CreateDigitalSignAsync(
            Guid? orderTrackingCode,
            long id,
            int userId,
            bool userIsConsumer,
            CancellationToken cancellationToken)
        {
            if (userIsConsumer)
            {
                var userInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);
                var context = new KuknosDigitalSignContextV1
                {
                    OrderTrackingCode = orderTrackingCode,
                    DigitalSignId = id,
                    FullNameFa = userInfo.FullName?.Trim(),
                    Address = userInfo.Address?.Trim(),
                    Mobile = userInfo.UserName,
                    NationalCode = userInfo.NationalCode?.Trim(),
                    Province = userInfo.ProvinceEnglish,
                    City = userInfo.CityEnglish,
                    Email = userInfo.Email,
                    BirthDate = userInfo.BirthDate.Value.ToShortPersianDateString(),
                    NationalCardSeries = userInfo.NationalIdSeries?.Trim(),
                    PostalCode = userInfo.PostalCode?.Trim(),
                    FirstNameEn = userInfo.FirstNameEnglish?.Trim(),
                    LastNameEn = userInfo.LastNameEnglish?.Trim(),
                    FirstNameFa = userInfo.FirstName?.Trim(),
                    LastNameFa = userInfo.LastName?.Trim(),
                    FatherNameFa = userInfo.FatherName?.Trim(),
                    UserId = userId,

                    IsMale = userInfo.IsMale,
                    Status = KuknosDigitalSignStatusV1.CreateAccount,
                };
                return JsonSerializer.Serialize(context);
            }
            else
            {
                //userInfo = await _userService.GetMerchantExtraInfoAsync(userId, cancellationToken);
                var userProfile = await _dbContext.MerchantInfos.AsNoTracking().Where(x => x.MerchantUserId == userId).FirstOrDefaultAsync();

                var context = new KuknosDigitalSignContextV1
                {
                    OrderTrackingCode = orderTrackingCode,
                    DigitalSignId = id,
                    FullNameFa = $"{userProfile.MerchantInfoAgent.FirstName} {userProfile.MerchantInfoAgent.LastName}"?.Trim(),
                    Mobile = userProfile.MerchantInfoAgent.MobileNumber?.Trim(),
                    NationalCode = userProfile.MerchantInfoAgent.NationalCode?.Trim(),
                    BirthDate = userProfile.MerchantInfoAgent.PersianBirthDate?.Trim(),
                    FirstNameFa = userProfile.MerchantInfoAgent.FirstName?.Trim(),
                    LastNameFa = userProfile.MerchantInfoAgent.LastName?.Trim(),
                    FatherNameFa = userProfile.MerchantInfoAgent.FatherName?.Trim(),
                    UserId = userId,
                    Status = KuknosDigitalSignStatusV1.CreateAccount,

                    City = userProfile.CityEnglish,
                    Province = userProfile.ProvinceEnglish,
                    NationalCardSeries = userProfile.MerchantInfoAgent.NationalIdSeries?.Trim(),
                    FirstNameEn = userProfile.MerchantInfoAgent.FirstNameEnglish?.Trim(),
                    LastNameEn = userProfile.MerchantInfoAgent.LastNameEnglish?.Trim(),
                    Address = userProfile.Address?.Trim(),
                    Email = userProfile.Email,
                    PostalCode = userProfile.PostalCode?.Trim(),
                    IsMale = userProfile.MerchantInfoAgent.IsMale,
                };
                return JsonSerializer.Serialize(context);
            }
        }

        public async ValueTask<DigitalSignCheckPointResponse> PushToNextCheckPointAsync(string contextData, CancellationToken cancellationToken)
        {
            var context = JsonSerializer.Deserialize<KuknosDigitalSignContextV1>(contextData);

            var resp = await _kuknosDigitalSignService.HandleAsync(context, cancellationToken);

            var result = resp switch
            {
                GetCertificateResult getCertificateResult => new DigitalSignCheckPointResponse(true, string.Empty, OrderSteps.IssuePromissory, getCertificateResult.signatureRefId),
                _ => throw new NotImplementedException("at PushToNextCheckPointAsync, unknown handle result")
            };

            return result;
        }

        public async ValueTask<bool> UserHasValidDigitalSignatureAsync(int userId)
        {
            return await _dbContext.DigitalSigns.AsNoTracking()
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                .AnyAsync();
        }

        public async ValueTask<DigitalSign> GetUserValidDigitalSignatureAsync(int userId, bool updateable)
        {
            if (updateable)
            {
                return await _dbContext.DigitalSigns
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                .OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync();
            }
            else
            {
                return await _dbContext.DigitalSigns.AsNoTracking()
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                .OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync();
            }
        }

        public async ValueTask<DigitalSign> GetUserInProgressOrValidDigitalSignatureAsync(int userId, bool updateable)
        {
            if (updateable)
            {
                return await _dbContext.DigitalSigns
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && x.ExpiredAt == null)
                .OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync();
            }
            else
            {
                return await _dbContext.DigitalSigns.AsNoTracking()
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && x.ExpiredAt == null)
                .OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync();
            }
        }

        public async ValueTask<(string privateKey, string certificate)> GetPdfSignatureDataAsync(int userId)
        {
            var ds = await _dbContext.DigitalSigns.AsNoTracking()
                .Where(x => x.UserId == userId && x.Type == DigitalSignType && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                .OrderByDescending (x => x.CreatedAt)
                .FirstOrDefaultAsync();

            if (ds == null || string.IsNullOrWhiteSpace(ds.Data))
            {
                return new(null, null);
            }

            var context = JsonSerializer.Deserialize<KuknosDigitalSignContextV1>(ds.Data);

            return new(context.PrivateKey, context.Certificate);
        }

        public OrderStepDto GetDigitalSignStep()
        {
            return new OrderStepDto("امضا دیجیتال", OrderSteps.UploadSignature, true);
        }

    }
}
