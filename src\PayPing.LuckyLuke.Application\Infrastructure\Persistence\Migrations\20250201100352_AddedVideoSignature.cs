﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedVideoSignature : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "SignatureVideoFileId",
                table: "ConsumerInfos",
                type: "character varying(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SignatureVideoFileName",
                table: "ConsumerInfos",
                type: "character varying(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SignatureVideoRandomSentence",
                table: "ConsumerInfos",
                type: "character varying(2048)",
                maxLength: 2048,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SignatureVideoFileId",
                table: "ConsumerInfos");

            migrationBuilder.DropColumn(
                name: "SignatureVideoFileName",
                table: "ConsumerInfos");

            migrationBuilder.DropColumn(
                name: "SignatureVideoRandomSentence",
                table: "ConsumerInfos");
        }
    }
}
