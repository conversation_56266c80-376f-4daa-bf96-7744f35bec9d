﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Builder;
using PayPing.LuckyLuke.Application.Infrastructure.Caching;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.Security.Cryptography;
using System.Text;

namespace PayPing.LuckyLuke.Application.Features.Consumer._01_SetPlan
{
    public record SetPlanRequest(Guid trackingCode, int planId);

    public record SetPlanResponse(Guid trackingCode);


    public class SetPlanEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.SetPlan,
                async (
                    SetPlanRequest request,
                    ISetPlanRequestHandler handler,
                    IValidator<SetPlanRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("SetPlan")
            .WithApiVersionSet(builder.NewApiVersionSet("Plan").Build())
            .Produces<SetPlanResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Set Plan")
            .WithDescription("Set Plan")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class SetPlanRequestHandler : ISetPlanRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IWebHostEnvironment _environment;
        private readonly INotifyService _notifyService;
        private readonly IDistributedCache _cache;
        private readonly ILogger<SetPlanRequestHandler> _logger;
        private readonly IUserService _userService;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IUserContext _userContext;
        private readonly IWalletService _walletService;
        private readonly BNPLOptions _bnplOptions;

        public SetPlanRequestHandler(ApplicationDbContext dbContext,
            IWebHostEnvironment environment,
            INotifyService notifyService,
            IDistributedCache cache,
            ILogger<SetPlanRequestHandler> logger,
            IUserService userService,
            IGuaranteeService guaranteeService,
            IUserContext userContext,
            IWalletService walletClient,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _environment = environment;
            _notifyService = notifyService;
            _cache = cache;
            _logger = logger;
            _userService = userService;
            _guaranteeService = guaranteeService;
            _userContext = userContext;
            _walletService = walletClient;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<SetPlanResponse> HandleAsync(SetPlanRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.Where(o => o.TrackingCode == request.trackingCode).Include(o => o.OrderTargetPlans).FirstOrDefaultAsync();
            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.PlanIsLocked)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "طرح اعتباری قبلا انتخاب شده است", string.Empty);
            }

            if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            {
                throw new OrderExpiredException(request.trackingCode.ToString(), "سفارش منقضی شده است");
            }

            if (order.ConsumerUserId.HasValue && order.ConsumerUserId.Value != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }

            if (order.OrderTargetPlans != null && order.OrderTargetPlans.Count > 0 && !order.OrderTargetPlans.Any(t => t.PlanId == request.planId))
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "طرح اعتباری در این سفارش قابل انتخاب نیست", string.Empty);
            }

            var userIsMerch = await _userService.IsUserMerchantAsync(userId, cancellationToken);
            if (userIsMerch.isMerch)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "کاربر فروشگاهی امکان خرید اقساطی ندارد", string.Empty);
            }

            var plan = await _dbContext.Plans.AsNoTracking()
                .Where(p => p.Id == request.planId && p.IsActive)
                .Include(p => p.Credit)
                .ThenInclude(xx => xx.Contract)
                .FirstOrDefaultAsync();
            if (plan == null)
            {
                throw new NotFoundException(request.planId.ToString(), "plan");
            }

            if (plan.MerchantUserId != order.MerchantUserId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "طرح اعتباری متعلق به پذیرنده نیست", string.Empty);
            }

            if (order.OrderTotalAmount < plan.MinOrderAmount)
            {
                throw new PlanMinOrderAmountException(request.trackingCode.ToString(), "مبلغ سفارش کمتر از حداقل قابل قبول طرح اعتباریست");
            }

            if (plan.GuaranteeType == GuaranteeType.Salary)
            {
                // check user belongs to plan employees
                var employeeExists = await _dbContext.PlanEmployees.AsNoTracking()
                    .Where(x => x.PlanId == plan.Id && x.EmployeeInfo.MobileNumber == _userContext.CurrentUserName)
                    .AnyAsync();

                if (!employeeExists)
                {
                    throw new EmployeeUnknownException(request.trackingCode.ToString(), "شماره موبایل کاربر در بین مشتریان طرح با ضمانت کسر از حقوق یافت نشد");
                }
            }


            var nathash = _userContext.CurrentUserNationalCode.HashSHA256NationalCode();

            await CheckOrderLimitsAsync(plan, request, order, nathash, cancellationToken);

            ConsumerInfo consumerInfo = await _dbContext.ConsumerInfos.Where(x => x.ConsumerUserId == userId).FirstOrDefaultAsync();
            if (consumerInfo == null)
            {
                consumerInfo = new ConsumerInfo();
                consumerInfo.ConsumerUserId = userId;
                consumerInfo.UserName = _userContext.CurrentUserName.Trim();
                consumerInfo.PhoneNumber = _userContext.CurrentUserName.Trim();
                consumerInfo.FirstName = _userContext.CurrentUserFirstname.Trim();
                consumerInfo.LastName = _userContext.CurrentUserLastname.Trim();
                consumerInfo.NationalCode = _userContext.CurrentUserNationalCode.Trim();
                consumerInfo.PersianBirthDate = _userContext.CurrentUserPersianBirthDate.Trim();
                consumerInfo.FullName = $"{consumerInfo.FirstName} {consumerInfo.LastName}".Trim();
                consumerInfo.FatherName = _userContext.CurrentUserFatherName.Trim();

                _dbContext.ConsumerInfos.Add(consumerInfo);
            }

            var guarantor = await _guaranteeService.FindGuarantorForType(plan.GuaranteeType, order.MerchantUserId, cancellationToken);

            var orderFinancialModel = new OrderFinancialBuilder(
                order.OrderTotalAmount,
                plan.MaxCreditAmount,
                plan.MinPrePaymentRate,
                plan.InstallmentCount,
                plan.InstallmentPeriodInMonths,
                plan.InterestRate,
                plan.Credit.Contract.OperationCost,
                plan.Credit.Contract.OperationCostStrategy,
                plan.GuaranteeType,
                _environment.IsDevelopment())
                .SetPrePayment()
                .SetGuaranteeAmount()
                .SetInstallments()
                .Build();

            var lockResult = await _walletService.LockCreditAndSaveAsync(plan.Credit.WalletId, orderFinancialModel.CreditedAmount, cancellationToken);
            if (!lockResult.Success)
            {
                _logger.LogWarning($"could not lock credit, with message: {lockResult.Error}, order trackingCode: {order.TrackingCode}");

                if (lockResult.balanceDeficit)
                {
                    int recipientUserId = order.MerchantUserId;
                    await SendMerchantCreditFinishedMessage(recipientUserId, plan.Credit.WalletId, cancellationToken);
                }

                throw new CreditTransactionException(request.trackingCode.ToString(), $"متأسفانه، در حال حاضر فروشگاه {order.MerchantName} اعتبار کافی ندارد. لطفاً در زمان دیگری تلاش کنید یا از روش‌های دیگر پرداخت  استفاده نمایید.");
            }

            order.ExpireDate = _environment.IsDevelopment() ? DateTimeOffset.UtcNow.AddHours(8) : DateTimeOffset.UtcNow.AddMinutes(ApplicationConstants.OrderExpireDurationMinutes);
            order.CreditLockId = lockResult.lockId;

            order.Status = OrderStatus.PlanLocked;
            order.PlanId = plan.Id;
            order.PlanIsLocked = true;
            order.ConsumerUserId = userId;
            order.ConsumerNationalHashId = nathash;
            order.ConsumerName = consumerInfo.FullName;
            order.ConsumerUserName = consumerInfo.UserName;
            order.PrePaymentAmount = orderFinancialModel.RawPrePaymentAmount;
            order.CreditedAmount = orderFinancialModel.CreditedAmount;
            order.MerchantOperationCostAmount = orderFinancialModel.MerchantOperationCostAmount;
            order.ConsumerOperationCostAmount = orderFinancialModel.ConsumerOperationCostAmount;
            order.GuaranteeAmount = orderFinancialModel.GuaranteeAmount;
            order.InstallmentsTotalRawAmount = orderFinancialModel.InstallmentsTotalRawAmount;
            order.DueDate = orderFinancialModel.Installments.First().DueDate;

            order.OrderPlan = new OrderPlan(
                plan.MaxCreditAmount,
                plan.InstallmentCount,
                plan.InterestRate,
                plan.InstallmentPeriodInMonths,
                plan.HasInstallmentDelayPenalty,
                plan.InstallmentDelayPenaltyRatePerDay,
                plan.InstallmentDelayPenaltyFreeInDays,
                guarantor.Id,
                plan.CreditId,
                plan.Credit.WalletId,
                plan.ContractIsMandatory);

            for (int i = 0; i < orderFinancialModel.Installments.Count; i++)
            {
                var inst = orderFinancialModel.Installments[i];
                order.Installments.Add(new Installment()
                {
                    OriginalAmount = inst.OriginalAmount,
                    InterestAmount = inst.InterestAmount,
                    TotalAmount = inst.TotalAmount,
                    DelayPenaltyAmount = inst.DelayPenaltyAmount,
                    FinalAmount = inst.FinalAmount,
                    DueDate = inst.DueDate,
                    PaymentStatus = PaymentStatus.Waiting,
                    Status = InstallmentStatus.Waiting,
                    Code = Guid.NewGuid(),
                    Number = i + 1
                });
            }
            await _dbContext.SaveChangesAsync(cancellationToken);

            await _userService.SetNewConsumerInfoAsync(userId, consumerInfo, cancellationToken);

            return new SetPlanResponse(order.TrackingCode);
        }

        private async ValueTask CheckOrderLimitsAsync(Plan plan, SetPlanRequest request, Order order, string nathash, CancellationToken cancellationToken)
        {
            var nonexpirableinprogressStatuses = OrderStatusProvider.GetNonExpirableInProgress();

            var userPrePaidOrders = await _dbContext.Orders.AsNoTracking()
                .Where(o => o.ConsumerNationalHashId == nathash && nonexpirableinprogressStatuses.Contains(o.Status))
                .Select(x => new { x.Id, x.MerchantUserId, x.Status })
                .ToListAsync();

            if (userPrePaidOrders != null && userPrePaidOrders.Count > 0)
            {
                if (userPrePaidOrders.Count >= ApplicationConstants.MaximumInProgressOrders)
                {
                    throw new ConsumerOrderParallelLimitException(request.trackingCode.ToString(), $"کاربر گرامی، در حال حاضر {ApplicationConstants.MaximumInProgressOrders} خرید اقساطی برای شما در سیستم وجود دارد و امکان ثبت فرایند جدید نخواهد بود.شما می توانید خریدهای اقساطی قبلی را تسویه  و  مجدداً اقدام نمایید.");
                }

                if (userPrePaidOrders.Any(x => x.MerchantUserId == plan.MerchantUserId))
                {
                    throw new ConsumerOrderSameMerchantLimitException(request.trackingCode.ToString(), "امکان خرید اقساطی جدید به علت داشتن خرید اقساطی فعال از این فروشگاه وجود ندارد.برای ثبت خرید اقساطی جدید از این فروشگاه، می توانید خرید قبلی را تسویه  و  مجدداً اقدام نمایید.", order.MerchantUserId.ToString());
                }

                if (userPrePaidOrders.Any(x => x.Status == OrderStatus.InstallmentsPaymentDelayed || x.Status == OrderStatus.InstallmentsPaymentDefaulted || x.Status == OrderStatus.InstallmentsWaitingForSalaryDeduction))
                {
                    throw new ConsumerOrderDelayedInstallmentLimitException(request.trackingCode.ToString(), $"امکان خرید اقساطی جدید به علت داشتن بازپرداخت معوق وجود ندارد.برای خرید اقساطی جدید، ابتدا نسبت به بازپرداخت اقساط خود اقدام نمایید.");
                }
            }

            var expirableinprogressStatuses = OrderStatusProvider.GetExpirable();

            var userInProgressOrders = await _dbContext.Orders.AsNoTracking()
                .Where(o => 
                    o.ConsumerNationalHashId == nathash &&
                    o.Id != order.Id &&
                    expirableinprogressStatuses.Contains(o.Status) &&
                    o.ExpireDate > DateTimeOffset.UtcNow)
                .Select(x => new { x.Id })
                .ToListAsync();

            if (userInProgressOrders != null && userInProgressOrders.Count > 0)
            {
                throw new ConsumerOrderInProgressLimitException(request.trackingCode.ToString(), $"در حال حاضر دارای {userInProgressOrders.Count} فرایند فعال از این فروشگاه هستید. یکی از فرایندها را انتخاب کرده و ادامه دهید.");
            }
        }

        private async ValueTask<bool> SendMerchantCreditFinishedMessage(int userId, Guid walletId, CancellationToken cancellationToken)
        {
            var result = await _cache.GetOrSetAsync(
                $"Wallet_Balance_Deficit_Notification_{walletId}",
                async () =>
                {
                    await _notifyService.SendMerchantCreditFinishedMessage(userId, cancellationToken);
                    return true;
                },
                new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1) });

            return result;
        }

        
    }

    public interface ISetPlanRequestHandler
    {
        ValueTask<SetPlanResponse> HandleAsync(SetPlanRequest request, CancellationToken cancellationToken);
    }

    public class SetPlanValidator : AbstractValidator<SetPlanRequest>
    {
        public SetPlanValidator()
        {
            RuleFor(x => x.planId).GreaterThan(0).WithResourceError(() => ValidatorDictionary.plan_id_is_required);
        }
    }
}
