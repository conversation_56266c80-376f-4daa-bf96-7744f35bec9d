using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class CreditTransaction : BaseEntity<long>
    {
        public int CreditId { get; set; }
        public Guid WalletId { get; set; }
        public required Guid CorrelationId { get; set; }

        public Guid CreditCode { get; set; }

        public decimal Amount { get; set; }

        public CreditTransactionType TransactionType { get; set; }

        public string Description { get; set; }

        public Credit Credit { get; set; }
    }

    public enum CreditTransactionType
    {
        Deposit = 0,
        Withdraw = 1,
        Freeze = 2,
        UnFreeze = 3,
        PrepaymentRS = 4,
        MerchantWageRS = 5,
        ConsumerWageRS = 6,
        InstallmentPaymentRS = 7,
        PrepaymentRSNegative = 8,
    }
}
