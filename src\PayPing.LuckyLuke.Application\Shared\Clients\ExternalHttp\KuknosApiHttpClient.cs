﻿using Ardalis.GuardClauses;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using System.Net.Http.Json;
using Microsoft.Extensions.Options;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using System.Text.Json;
using Microsoft.Extensions.Caching.Memory;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using Microsoft.Extensions.Logging;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;

namespace PayPing.LuckyLuke.Application.Shared.Clients.ExternalHttp
{
    public class KuknosApiHttpClient : IKuknosApiHttpClient
    {
        private readonly HttpClient _httpClient;
        private readonly KuknosOptions _options;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<KuknosApiHttpClient> _logger;

        public KuknosApiHttpClient(HttpClient httpClient, IOptions<KuknosOptions> options, IMemoryCache memoryCache,
            ILogger<KuknosApiHttpClient> logger)
        {
            _httpClient = Guard.Against.Null(httpClient, nameof(httpClient));
            _options = options.Value;
            _memoryCache = memoryCache;
            _logger = logger;
        }

        public async ValueTask<string> GetTokenAsync(Guid? orderTrackingCode, CancellationToken cancellationToken = default)
        {
            if (!_memoryCache.TryGetValue(ApplicationConstants.KuknosTokenCacheKey, out string token))
            {
                var cacheEntryOptions = new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(25) };

                KuknosApiResponse<KuknosApiLoginDataResponse> tokenResult = await LoginAsync(orderTrackingCode, cancellationToken);

                if (tokenResult == null)
                {
                    throw new PromissoryProviderException(orderTrackingCode?.ToString(), $"kuknos login result is null", false);
                }

                if (!tokenResult.meta.success)
                {
                    throw new PromissoryProviderException(orderTrackingCode?.ToString(), $"kuknos login not succeeded with message: {tokenResult.meta.description}", false);
                }

                _memoryCache.Set(ApplicationConstants.KuknosTokenCacheKey, tokenResult.data.access_token, cacheEntryOptions);

                return tokenResult.data.access_token;
            }

            return token;
        }

        private async ValueTask<KuknosApiResponse<KuknosApiLoginDataResponse>> LoginAsync(Guid? orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/authorization/login");
            
            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept", "application/json");
            
            var content = JsonSerializer.Serialize<KuknosApiLoginRequest>(new (_options.Email, _options.Password));
            
            request.Content = new StringContent(content);
            
            var response = await _httpClient.SendAsync(request, cancellationToken);
            
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                throw new PromissoryProviderException(orderTrackingCode?.ToString(), message, false);
            }
            
            return await response.Content.ReadFromJsonAsync<KuknosApiResponse<KuknosApiLoginDataResponse>>(cancellationToken);
        }

        public async ValueTask<KuknosApiResponse<KuknosApiIssuePromissoryDataResponse>> IssuePromissoryAsync(KuknosApiIssuePromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/safte/issue");

            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept-Language", "fa-ir");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");
            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();
                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KuknosApiResponse<KuknosApiIssuePromissoryDataResponse>>(cancellationToken);

            if (!result.meta.success)
            {
                throw new PromissoryProviderException(orderTrackingCode.ToString(), $"kuknos Issue Promissory not succeeded with message: {result.meta.description}", false);
            }

            return result;
        }

        public async ValueTask<KuknosApiFileDownloadResponse> DownloadRawDocumentAsync(string docHash, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_httpClient.BaseAddress}microservice/ipfs/{docHash}");
            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept", "*/*");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }
            var contentType = response.Content.Headers.ContentType.ToString().ToLower();
            var bytes = await response.Content.ReadAsByteArrayAsync(cancellationToken);
            
            return new (bytes, contentType);
        }

        public async ValueTask<KuknosApiResponse<bool>> CreateAccountAsync(string publicKey, string mobile, string nationalCode, string userSignature, Guid? orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/federation/v2/account");
            
            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept", "application/json");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new StringContent(JsonSerializer.Serialize<KuknosApiAccountRequest>(new (publicKey, mobile, nationalCode, userSignature)), null, "application/json");
            request.Content = content;
            
            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();
                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), err.message, true);
                }
                else
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), message, false);

            }

            var result =  await response.Content.ReadFromJsonAsync<KuknosApiResponse<bool>>(cancellationToken);

            if (!result.meta.success)
            {
                throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), $"kuknos Create Account not succeeded with message: {result.meta.description}", false);
            }

            return result;
        }

        public async ValueTask<KuknosApiResponse<KuknosApiCreateCertificateDataResponse>> CreateCertificateAsync(
            string publicKey,
            string birthDate,
            string certificateSigningRequest,
            string nationalCode,
            string nationalCardSeries,
            string postalCode,
            string firstnameEn,
            string lastnameEn,
            string firstNameFa,
            string lastNameFa,
            string fathernameFa,
            bool isMale,
            Stream signatureImage,
            string signatureImageName,
            Guid? orderTrackingCode,
            CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/v2/sejel/digital-cert-sync-with-info");

            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Public", publicKey);

            request.Headers.Add("Accept", "application/json");
            request.Headers.AcceptLanguage.Add(new System.Net.Http.Headers.StringWithQualityHeaderValue("fa"));
            request.Headers.AcceptLanguage.Add(new System.Net.Http.Headers.StringWithQualityHeaderValue("en"));

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new MultipartFormDataContent
            {
                { new StringContent(birthDate.Replace("/", "")), "birth_date" }, //"yyyymmdd"
                { new StringContent(nationalCode), "national_id" },
                { new StringContent(certificateSigningRequest), "csr" },
                { new StringContent(nationalCardSeries), "national_card_series" },
                { new StringContent(postalCode), "postal_code" },
                { new StringContent(firstnameEn), "en_first_name" },
                { new StringContent(lastnameEn), "en_last_name" },
                { new StringContent(firstNameFa), "first_name" },
                { new StringContent(lastNameFa), "last_name" },
                { new StringContent(fathernameFa), "father_name" },
                { new StringContent(isMale ? "1" : "0"), "gender" },
                { new StreamContent(signatureImage), "signature_image", signatureImageName }
            };
            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), err.message, true);
                }
                else
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KuknosApiResponse<KuknosApiCreateCertificateDataResponse>>(cancellationToken);

            if (!result.meta.success)
            {
                throw new DigitalSignatureProviderException(orderTrackingCode.ToString(), $"kuknos create certificate not succeeded with message: {result.meta.description}", false);
            }

            return result;
        }

        public async ValueTask<KuknosApiResponse<string>> GetCertificateAsync(string publicKey, string trackingCode, Guid? orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_httpClient.BaseAddress}microservice/v2/sejel/digital-cert?tracking_code={trackingCode}");
            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Public", publicKey);
            request.Headers.Add("Accept", "application/json");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), err.message, true);
                }
                else
                    throw new DigitalSignatureProviderException(orderTrackingCode?.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KuknosApiResponse<string>>(cancellationToken);

            return result;
        }

        public async ValueTask<KuknosApiFileDownloadResponse> SignDocumentWithSignerPdfAsync(string certificate, string privateKey, byte[] rawDoc, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/pdf-signer/v2/sign-pdf");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new MultipartFormDataContent
            {
                { new StringContent(certificate), "cert" },
                { new StringContent(privateKey), "secret" },
                { new StreamContent(new MemoryStream(rawDoc)), "file", "document.pdf" }
            };
            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }
            var contentType = response.Content.Headers.ContentType.ToString().ToLower();
            var bytes = await response.Content.ReadAsByteArrayAsync(cancellationToken);

            return new(bytes, contentType);
        }

        public async ValueTask<KuknosApiResponse<string>> UploadAsync(byte[] file, string name, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/ipfs/file");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            request.Headers.Add("Platform-Version", _options.Platform);

            var content = new MultipartFormDataContent
            {
                { new StreamContent(new MemoryStream(file)), "file", name }
            };

            request.Content = content;
            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            return await response.Content.ReadFromJsonAsync<KuknosApiResponse<string>>(cancellationToken);
        }

        public async ValueTask<Stream> DownloadAsync(string fileHash, Guid orderTrackingCode, CancellationToken cancellationToken)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_httpClient.BaseAddress}microservice/ipfs/{fileHash}");
            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            request.Headers.Add("Platform-Version", _options.Platform);

            var response = await _httpClient.SendAsync(request, cancellationToken);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }
            return await response.Content.ReadAsStreamAsync(cancellationToken);
        }

        public async ValueTask<KuknosApiResponse<bool>> SignedAsync(KuknosApiSignedRequest model, string promissoryId, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/safte/signed/{promissoryId}");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept-Language", "fa-ir");

            var content = new StringContent(
                JsonSerializer.Serialize(model),
                null,
                "application/json");
            request.Content = content;
            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KuknosApiResponse<bool>>(cancellationToken);

            if (!result.meta.success)
            {
                throw new PromissoryProviderException(orderTrackingCode.ToString(), $"kuknos signed not succeeded with message: {result.meta.description}", false);
            }

            return result;
        }

        public async ValueTask<KuknosApiResponse<string>> PayAsync(KuknosApiPayRequest model, string promissoryId, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/safte/pay/{promissoryId}");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");
            request.Headers.Add("Accept", "*/*");

            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept-Language", "fa-ir");

            var content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");
            request.Content = content;
            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KuknosApiResponse<string>>(cancellationToken);

            if (!result.meta.success)
            {
                throw new PromissoryProviderException(orderTrackingCode.ToString(), $"kuknos pay not succeeded with message: {result.meta.description}", false);
            }

            return result;
        }

        public async ValueTask<KuknosApiResponse<KuknosApiFinalizeResponse>> FinalizeAsync(string promissoryId, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/safte/finalize/{promissoryId}");
            
            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");
            
            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept-Language", "fa-ir");

            var content = new StringContent("", null, "text/plain");
            request.Content = content;
            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KuknosApiResponse<KuknosApiFinalizeResponse>>(cancellationToken);

            if (!result.meta.success)
            {
                throw new PromissoryProviderException(orderTrackingCode.ToString(), $"kuknos finalize not succeeded with message: {result.meta.description}", false);
            }

            return result;
        }

        public async ValueTask<KuknosApiResponse<bool>> DeleteAsync(string promissoryId, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Delete, $"{_httpClient.BaseAddress}microservice/safte/delete/{promissoryId}");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept-Language", "fa-ir");

            var content = new StringContent("", null, "text/plain");
            request.Content = content;
            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                return new KuknosApiResponse<bool>() { data = false, meta = new KuknosApiMetaResponse(false, default, message, false, string.Empty) };
            }

            var result = await response.Content.ReadFromJsonAsync<KuknosApiResponse<bool>>(cancellationToken);

            return result;
        }

        public async ValueTask<KuknosApiResponse<KuknosApiIssueSettlementDataResponse>> IssueSettlementAsync(string promissoryId, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/safte/settlement/request/{promissoryId}");

            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept-Language", "fa-ir");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new StringContent("", null, "text/plain");
            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();
                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KuknosApiResponse<KuknosApiIssueSettlementDataResponse>>(cancellationToken);

            if (!result.meta.success)
            {
                throw new PromissoryProviderException(orderTrackingCode.ToString(), $"kuknos Issue Promissory settlement not succeeded with message: {result.meta.description}", false);
            }

            return result;
        }
        public async ValueTask<KuknosApiResponse<bool>> SignSettlementAsync(string promissoryId, string signedPdfHash, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/safte/settlement/signed/{promissoryId}");

            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept-Language", "fa-ir");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            var content = new StringContent(JsonSerializer.Serialize(new { signed_pdf = signedPdfHash }), null, "application/json");
            request.Content = content;

            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KuknosApiResponse<bool>>(cancellationToken);

            if (!result.meta.success)
            {
                throw new PromissoryProviderException(orderTrackingCode.ToString(), $"kuknos Sign Promissory settlement not succeeded with message: {result.meta.description}", false);
            }

            return result;
        }
        public async ValueTask<KuknosApiResponse<KuknosApiFinalizeSettlementResponse>> FinalizeSettlementAsync(string promissoryId, Guid orderTrackingCode, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}microservice/safte/settlement/finalize/{promissoryId}");

            string token = await GetTokenAsync(orderTrackingCode, cancellationToken);
            request.Headers.Add("Authorization", $"Bearer {token}");

            request.Headers.Add("Platform-Version", _options.Platform);
            request.Headers.Add("Accept-Language", "fa-ir");

            var content = new StringContent("", null, "text/plain");
            request.Content = content;
            var response = await _httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var message = await response.Content.ReadAsStringAsync();

                if (JsonHelpers.TryDeserializeJson(message, out KuknosApiErrorResponse err))
                {
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), err.message, true);
                }
                else
                    throw new PromissoryProviderException(orderTrackingCode.ToString(), message, false);
            }

            var result = await response.Content.ReadFromJsonAsync<KuknosApiResponse<KuknosApiFinalizeSettlementResponse>>(cancellationToken);

            if (!result.meta.success)
            {
                throw new PromissoryProviderException(orderTrackingCode.ToString(), $"kuknos finalize settlement not succeeded with message: {result.meta.description}", false);
            }

            return result;
        }


    }
}
