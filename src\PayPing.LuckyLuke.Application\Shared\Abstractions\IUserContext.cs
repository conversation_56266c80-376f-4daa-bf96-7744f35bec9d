﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Shared.Abstractions
{
    public interface IUserContext
    {
        int? CurrentUserId { get; }
        string CurrentUserBusinessname { get; }
        string CurrentUserFirstname { get; }
        string CurrentUserLastname { get; }
        string CurrentUserName { get; }
        string CurrentUserNationalCode { get; }
        string CurrentUserPersianBirthDate { get; }
        string CurrentUserFatherName { get; }
    }
}
