﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class OrderExternalEventOutboxConfiguration : IEntityTypeConfiguration<OrderExternalEventOutbox>
{
    public void Configure(EntityTypeBuilder<OrderExternalEventOutbox> builder)
    {
        builder.Property(nameof(OrderExternalEventOutbox.ClientRefId)).HasMaxLength(512);
        builder.Property(nameof(OrderExternalEventOutbox.ClientCancelUrl)).HasMaxLength(512);

    }
}