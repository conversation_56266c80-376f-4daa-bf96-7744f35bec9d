﻿using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
using PayPing.LuckyLuke.Application.Shared.Mappers;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kiahooshan
{
    public class KiaIssuePromissoryHandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IKiahooshanPromissoryService
    {
        private readonly IKiahooshanPromissoryService _next;
        private readonly IUploadGrpcClient _uploadGrpcClient;

        public KiaIssuePromissoryHandler(
            IKiahooshanPromissoryService next,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IUploadGrpcClient uploadGrpcClient,
            IKiahooshanApiHttpClient kihooApi,
            IOptions<KiahooshanOptions> kihooOptions)
            : base(dbContext, userContext, kihooApi, kihooOptions)
        {
            _next = next;
            _uploadGrpcClient = uploadGrpcClient;
        }

        public async ValueTask<object> HandleAsync(KiahooshanPromissoryContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KiahooshanPromissoryStatusV1.IssuePromissory)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var dsc = await GetValidDigitalSignAsync(context.ConsumerUserId);

            var result = await _kihooApi.IssueDigiProNote(context.ToApiRequest(dsc) , context.OrderTrackingCode, cancellationToken);

            // upload file to storage
            string validName = Path.ChangeExtension(Path.GetRandomFileName(), ".pdf");

            byte[] pdfBytes = Convert.FromBase64String(result.data.unSignedPdf);

            var uresult = await _uploadGrpcClient.UploadUserDocumentAsync(pdfBytes, Path.GetExtension(validName).TrimStart('.'), context.ConsumerUserId, cancellationToken);

            if (!uresult.Succeeded)
            {
                await ResetOrderPromissoryToRenewIssue(context);

                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not upload raw promissory to storage service", false);
            }


            context.Status = KiahooshanPromissoryStatusV1.Sign;
            context.PromissoryUniqueId = result.uniqueId;
            context.PromissoryRawDocumentFileId = uresult.SuccessResult;
            context.PromissoryRawDocumentFileContentType = "application/pdf";

            //// save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

    }
}
