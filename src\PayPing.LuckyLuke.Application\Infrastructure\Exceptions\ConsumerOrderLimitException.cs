﻿using Microsoft.AspNetCore.Mvc;

namespace PayPing.LuckyLuke.Application.Infrastructure.Exceptions;

public class ConsumerOrderSameMerchantLimitException : BaseException
{
    public ConsumerOrderSameMerchantLimitException(string eventId, string message, string merchantId) : base(eventId, message)
    {
        MerchantId = merchantId;
    }

    public string MerchantId { get; set; }
}

public class ConsumerOrderParallelLimitException : BaseException
{
    public ConsumerOrderParallelLimitException(string eventId, string message) : base(eventId, message)
    {
    }

}

public class ConsumerOrderDelayedInstallmentLimitException : BaseException
{
    public ConsumerOrderDelayedInstallmentLimitException(string eventId, string message) : base(eventId, message)
    {
    }

}

public class ConsumerOrderInProgressLimitException : BaseException
{
    public ConsumerOrderInProgressLimitException(string eventId, string message) : base(eventId, message)
    {
    }

}
