﻿using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using System.IO;


namespace PayPing.LuckyLuke.Application.Features.Admin._003_AdminContractUpdate
{
    public interface IGeneratePdfFile
    {
        byte[] GenerateQuestPdfFromTextContent(string fullName, string fatherName, string nationalCode,
            string address, decimal merchantOperationCost, string contractStartDate, string contractExpireDate);
    }

    public class GeneratePdfFile : IGeneratePdfFile
    {
        public GeneratePdfFile()
        {
            QuestPDF.Settings.License = LicenseType.Community;
            string bPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts", "YEKAN-PLUS-BOLD.ttf");
            string lPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fonts", "YEKAN-PLUS-LIGHT.ttf");

            QuestPDF.Drawing.FontManager.RegisterFont(File.OpenRead(bPath));
            QuestPDF.Drawing.FontManager.RegisterFont(File.OpenRead(lPath));

        }

        public byte[] GenerateQuestPdfFromTextContent(string fullName, string fatherName, string nationalCode,
            string address, decimal merchantOperationCost, string contractStartDate, string contractExpireDate)
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                var boldFontInternalName = "YEKAN PLUS";
                var lightFontInternalName = "B Yekan+";

                Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(2, Unit.Centimetre);

                        page.Content()
                            .ContentFromRightToLeft()
                            .DefaultTextStyle(TextStyle.Default.FontFamily(lightFontInternalName).DirectionFromRightToLeft())
                            .PaddingVertical(20)
                            .Column(column =>
                            {
                                // Persian text with RTL support
                                column.Item()
                                .AlignCenter()
                                    .Text(text =>
                                    {
                                        text.Span("قرارداد ارائه‌ی سرویس اقساطی به پذیرنده")
                                            .FontFamily(boldFontInternalName) // Use a font supporting Persian
                                            .FontSize(16)
                                            .DirectionFromRightToLeft();
                                    });

                                
                                column.Item().PaddingTop(40)
                                    .Text(text =>
                                    {
                                        
                                        string st2 = @"ماده‌ی 1) طرفین قرارداد:
1-1) شرکت مانا تدبیر آواتک به شناسه ملی 14005304514 و شماره ثبت 480932  به نمایندگی و امضاء مجاز آقای احمد منصوری رشتی با سمت مدیرعامل و عضو هیئت مدیره، به نشانی: استان تهران، شهرستان تهران، بخش مركزي، شهر تهران، قبا، خيابان زيبا، خيابان جوادي، پلاك 4، طبقه 1 کد پستی 1948847413و آدرس ایمیل <EMAIL> که از این پس در این قرارداد «پی‌پینگ» نـامیـده می‌شود.";
                                                               
                                        text.Span(st2)
                                            .FontSize(12)
                                            .DirectionFromRightToLeft();
                                    });

                                column.Item().PaddingTop(20)
                                    .Text(text =>
                                    {

                                        string st3 = string.Format(
                                                            "1. 1-2 ) آقای/خانم/شرکت {0}، فرزند {1} به شماره/شناسه ملی {2}، به نشانی: " +
                                                            "{3}، که از این پس در این قرارداد «پذیرنده» نـامیـده می‌شود حسب این قرارداد که تا تاریخ {4} " +
                                                            "معتبر است، مجاز به انجام فعالیت می‌باشد.",
                                                            fullName, fatherName, nationalCode, address, contractExpireDate);
                                        text.Span(st3)
                                            .FontSize(12)
                                            .DirectionFromRightToLeft();
                                    });

                                column.Item().PaddingTop(20)
                                    .Text(text =>
                                    {
                                        string st4 = @"تبصره‌ی 1) هویت «پذیرنده» از طریق اطلاعات اعلامی در زمان تشکیل حساب کاربری نیز بررسی و تأیید می‌گردد و لذا «پذیرنده» موظف است جهت ایجاد حساب کاربری و احراز هویت، کد‌ملی و تاریخ تولد خود را جهت استعلام و مطابقت با شماره‌ی تلفن همراه ثبت‌شده و اعلام شده در پی‌پینگ و همچنین در صورت نیاز، علاوه بر موارد مذکور، سریال پشت کارت ملی، کد‌پستی، شماره‌ی شبا و غیره خود را جهت تکمیل اطلاعات، احراز هویت، صدور گواهی امضای دیجیتال، تدوین قرارداد و انجام هر امری که از نظر پی‌پینگ لازم باشد و اقدامات مرتبط با انجام تعهدات، در اختیار پی‌پینگ قرار دهد. این اطلاعات، با حفظ شرایط محرمانگی اطلاعات، جهت احراز هویت پذیرنده و سایر موارد فوق‌الذکر استفاده می‌شود.";

                                        text.Span(st4)
                                            .FontSize(12)
                                            .DirectionFromRightToLeft();
                                    });
                                column.Item().PaddingTop(20)
                                    .Text(text =>
                                    {
                                        string st5 = @"ماده‌ی 2) موضوع و مدت قرارداد:
2-1) موضوع قرارداد عبارت است از همکاری طرفین به شرح آتی برای ارائه‌ی کالا و خدمات به صورت اقساطی به کاربران بر بستر پلتفرم ارائه شده از سوی «پی‌پینگ»، به گونه‌ای که کاربر بتواند به صورت خرید اقساطی از کالاها و یا خدمات متعلق به «پذیرنده» بهره‌مند شده و بازپرداخت اقساط مربوط به این خرید اقساطی را از طریق پلتفرم «پی‌پینگ» به انجام رساند.
این امر از طریق اتصال فروشگاه متعلق به پذیرنده به پلتفرم «پی‌پینگ» به انجام رسیده و پس از معرفی کالاها یا خدمات «پذیرنده» به کاربران و شناسایی کاربران از طریق پلتفرم «پی‌پینگ»، امکان خرید قسطی از «پذیرنده» برای کاربران فراهم می‌گردد.
                                        ";
                                        text.Span(st5)
                                            .FontSize(12)
                                            .DirectionFromRightToLeft();
                                    });
                                column.Item().PaddingTop(20)
                                    .Text(text =>
                                    {

                                        string st6 = $"تبصره‌ی 1) «پی‌پینگ» صرفاً به عنوان تسهیل‌گر" +
                                            $" و دروازه‌ی ارتباط «پذیرنده» با " +
                                            $"«کاربر»، ارائه‌ی کالا و خدمات و دریافت و پرداخت اقساط مورد توافق ایشان بوده و همانگونه که هیچ مسئولیتی در قبال تعهدات، کالا، محصولات و خدمات ارائه شده توسط «پذیرنده» ندارد، هیچ‌گونه مسئولیتی در قبال تعهدات «کاربر»، اقساط تعهد شده، وصول مطالبات و غیره ندارد. " +
                                            $"در صورت تحویل چک یا سایر وثایق و تضامین به «پی‌پینگ» و وجود آن‌ها در ید «پی‌پینگ»، به هیچ‌وجه به معنای مسئولیت «پی‌پینگ» در بازپرداخت اقساط نبوده و حقی برای «پذیرنده» ایجاد نمی‌نماید. بدیهیست در صورت انجام تعهدات «پذیرنده» به طور کامل و " +
                                            $"عدم انجام تعهدات از سوی کاربر، عنداللزوم، تضامین دریافتی به «پذیرنده» تحویل خواهد شد تا نسبت به وصول مطالبات خود از محل این تضمینات اقدام نماید.";

                                        text.Span(st6)
                                            .FontSize(12)
                                            .DirectionFromRightToLeft();
                                    });

                                column.Item().PaddingTop(20)
                                    .Text(text =>
                                    {
                                        string st7 = string.Format(
                                            @"2-2) شروع  این قرارداد از مورخ {0} لغایت {1} خواهد بود.",
                                            contractStartDate, contractExpireDate);
                                        text.Span(st7)
                                            .FontSize(12)
                                            .DirectionFromRightToLeft();
                                    });
                                column.Item().PaddingTop(20)
                                   .Text(text =>
                                   {
                                       string st71 = $"تبصره‌ی 2) با توجه به مدت قرارداد، نوع همکاری و اقتضائات موضوع، طرفین مقرر و توافق نمودند که پس از انقضای مدت، این قرارداد به خودی خود، با همین شرایط، برای دوره‌ی بعدی (به مدت همین قرارداد) تمدید گردد، مگر آن‌که هریک از طرفین تمایل به تمدید قرارداد نداشته باشند که در این صورت، می‌بایست مراتب را کتباً به طرف دیگر اعلام و رسید اخذ نماید. لازم به ذکر است که این تمدید خودکار، تا زمان اعلام انصراف هریک از طرفین، برای هر دوره به قوت خود باقیست.\r\nبدیهیست که در صورت بروز هرگونه تغییر در هر یک از بندهای قرارداد، الحاقیه‌ای به قرارداد اضافه خواهد شد.\r\n";

                                       text.Span(st71)
                                           .FontSize(12)
                                           .DirectionFromRightToLeft();
                                   });
                                column.Item().PaddingTop(20)
                                   .Text(text =>
                                   {
                                       string st8 = $"ماده‌ی 3) مبلغ قرارداد و نحوه‌ی پرداخت:\r\nمبلغ قرارداد به شرح و شرایط ذیل بین طرفین تعیین و توافق گردید:\r\n";

                                       text.Span(st8)
                                           .FontSize(12)
                                           .DirectionFromRightToLeft();
                                   });
                                column.Item().PaddingTop(5)
                                   .Text(text =>
                                   {
                                       string st9 = $"بر اساس توافق میان طرفین با عقد این قرارداد، مبلغ کارمزد پی پینگ از فروش اقساطی پذیرنده {merchantOperationCost.ToString()} ٪ خواهد بود. این درصد کارمزد بر اساس مبلغ کل سبد خریدار محاسبه و از پذیرنده کسر خواهد شد.";
                                       text.Span(st9)
                                           .FontSize(12)
                                           .DirectionFromRightToLeft();
                                   });
                                column.Item().PaddingTop(20)
                                   .Text(text =>
                                   {
                                       string st10 = $"تبصره‌ی 3) چنانچه در اثنای مدت قرارداد، «پذیرنده» اقدام به فسخ قرارداد کرده یا قرارداد به درخواست وی اقاله گردد و یا اینکه موضوع قرارداد به دلیلی که مربوط به «پی‌پینگ» نیست، قابل انجام نباشد و یا با ایجاد حق فسخ برای «پی‌پینگ» و اعمال آن توسط نامبرده قرارداد فسخ گردد و یا به علت تخلفات «پذیرنده» از انجام تعهدات در قبال «پی‌پینگ» یا کاربران، ارائه‌ی خدمات به «پذیرنده» متوقف گردد، مبلغ قرارداد قابل استرداد نمی باشد و چنانچه تعهدات مالی «پذیرنده» به طور کامل انجام نگرفته باشد، نامبرده مکلف به تسویه و پرداخت نقدی آن خواهد بود.";

                                       text.Span(st10)
                                           .FontSize(12)
                                           .DirectionFromRightToLeft();
                                   });
                                column.Item().PaddingTop(20)
                                   .Text(text =>
                                   {
                                       string st11 = $"ماده‌ی 4) شروط ضمن قرارداد و تعهدات طرفین:\r\n4-1) «پی‌پینگ» متعهد است تا خدمات ارائه شده در راستای این قرارداد، همواره قابل دسترسی باشد و پشتیبانی لازم از خدمات خود را همواره ارائه دهد و هرگونه اختلال مربوط به فعالیت خود را که از طریق شماره تلفن امداد 91001520-021  به مرکز پشتیبانی اعلام گردد را در اسرع وقت پشتیبانی نموده و حسب مورد رفع ایراد کرده یا اقدامات مربوطه را به انجام رساند، مگر در صورتیکه خدمات ارائه شده از سمت سرویس دهنده اصلی قطع باشد.\r\nتبصره‌ی 4) با توجه به مشكلات و اختلالات احتمالي در شبكه‌ی بانكي، مسئولیت بروز هرگونه اشكال، قطعي یا تاخیر در خدمات «پی‌پینگ»، شبكه‌ی بانكي یا در سیستم تسویه‌ی شاپرک، متوجه «پی‌پینگ» نمي‌باشد.\r\n2-4) «پی‌پینگ» در تمام مدت قرارداد و ارائه‌ی خدمات به «پذیرنده»، حق دارد نظارت خود بر فعالیت‌های «پذیرنده» را اعمال نموده و انجام تعهدات وی در قبال خود و همچنین کاربران را به هرگونه‌ای بررسی نماید. لذا «پذیرنده» متعهد گردید تا در هر زمان، هر گونه اطلاعاتی در این خصوص که از سوی «پی‌پینگ» مطالبه شود را بدون هیچ قید و شرطی در اختیار «پی‌پینگ» قرار دهد.\r\n3-4) استفاده از سرویس‌های ارائه شده توسط «پی‌پینگ»، مشمول قوانین جاری کشور در تمام زمینه‌ها می‌باشد. لذا «پذیرنده» متعهد به اجرای کامل قوانین جاری کشور، از جمله مقررات و قوانین مبارزه با پولشوئی کشور و تأمین مالی تروریسم و همچنین اخذ مجوزهای لازمه جهت فعالیت‌های خود می‌باشد. بدیهیست مسئولیت عدم اخذ مجوزات و هرگونه فعالیت خلاف مقررات کشور، برعهده‌ی «پذیرنده» بوده و مسئولیتی بدین جهت متوجه «پی‌پینگ» نخواهد بود. تخلف «پذیرنده» از مقررات این بند یا هر اقدامی بر خلاف قوانین جاری کشور، موجد حق فسخ یک‌طرفه‌ی قرارداد برای «پی‌پینگ»، قطع ادامه‌ی ارائه‌ی خدمات و مسدودی دسترسی «پذیرنده» به حساب کاربری بدون نیاز به هرگونه تشریفاتی، خواهد بود.\r\n4-4) «پی‌پینگ» تنها وظیفه‌ی در دسترس بودن و اجرای سرویس ارائه شده بر اساس موضوع قرارداد را دارد و هیچ گونه مسئولیتی در قبال تخصیص اعتبار به کاربران، مبلغ اعتبار تخصیص داده شده، تحویل کالا و خدمات، گارانتی و اصالت کالا و همچنین بازپرداخت اقساط خرید توسط کاربران ندارد.\r\n4-5) چنان‌چه مشاهده و معلوم گردد که «پذیرنده» از سرویس ارائه شده، جهت انجام اعمال مجرمانه و یا فعالیت‌های ممنوعه بهره‌برداری و استفاده می‌نماید، «پی‌پینگ» مجاز است تا ضمن توقف ارائه‌ی خدمات به «پذیرنده»، دسترسی وی به حساب کاربری را مسدود نموده و قرارداد را به صورت یک طرفه فسخ نماید.\r\n4-6) هر شخص حقیقی تنها می‌تواند یک حساب پذیرنده سرویس اقساطی در پی‌پینگ داشته باشد و چنانچه پذیرنده، شماره‌ی موبایل یا حساب کاربری خود را در پی‌پینگ تغییر دهد، کلیه‌ی بدهی‌ها و تعهدات قبلی، به حساب کاربری جدید او منتقل خواهد شد.\r\n";

                                       text.Span(st11)
                                           .FontSize(12)
                                           .DirectionFromRightToLeft();
                                   });
                                column.Item().PaddingTop(1)
                                   .Text(text =>
                                   {
                                       string st12 = $"4-7) با توجه به اینکه وظیفه و نقش «پی‌پینگ»، صرفاً تسهیل‌گیری در خرید و فروش کالا و دریافت و پرداخت اقساطی است و «پذیرنده» با استفاده از این پلتفرم، کالا یا خدمات خود را با شرایط اقساطی به کاربران پی‌پینگ ارائه می‌نماید و تعهد «پی‌پینگ» وفق این قرارداد، «صرفاً» تسهیل‌گری در ارائه و دریافت تسهیلات با هدف فروش اقساطی توسط «پذیرنده» می‌باشد، لذا لازم به توجه است که:\r\n4-7-1) پی‌پینگ به هیچ عنوان اطلاعی از نوع خرید «کاربر» از «پذیرنده» و کالا یا خدمات ارائه شده، ندارد و مسئولیت آن تماماً با «پذیرنده» است. همچنین مسئولیت هر گونه مغایرت در ارائه‌ی کالا یا خدمات، تحویل به موقع کالا یا خدمات، اصالت و گارانتی کالا، بر عهده‌ی «پذیرنده» و «کاربر» می‌باشد و «پی‌پینگ» هیچ‌گونه تعهد و مسئولیتی اعم از حقوقی، جزائی و مالی، نسبت به این موارد نخواهند داشت؛\r\n4-7-2) روابط قراردادی و غیرقراردادی «پذیرنده» با «کاربر» که خارج از موضوع و اطلاعات این قرارداد باشد، از قبیل تراکنش مالی و تخفیف یا اضافه پرداخت و غیره، ارتباطی با «پی‌پینگ» ندارد؛\r\n4-7-3) نظر به عدم اطلاع «پی‌پینگ» از فرآیند خرید و فروش و روابط میان پذیرنده و کاربر، پی‌پینگ به هیچ عنوان تعهد و مسئولیتی در خصوص وصول مطالبات، پرداخت اقساط و سایر موارد از این دست و به طور کلی هر امر دیگر ناشی از ارائه و یا فروش کالا و خدمات توسط پذیرنده به کاربر، ندارد؛\r\n4-7-4) بدیهی است در صورت تأخیر در پرداخت اقساط و یا عدم پرداخت اقساط و مطالبات توسط کاربر و خریدار،‌ عدم دسترسی به کاربر،‌ وقوع قوه‌ی قاهره از قبیل سیل، زلزله،‌ جنگ، شیوع بیماری و غیره و تأخیر در اجرای تعهدات «پی‌پینگ» به دلایل مذکور و مانند آن، پذیرنده حق مطالبه‌ی هیچ‌گونه خسارت احتمالی از پی‌پینگ را ندارد؛\r\n4-7-5) با توجه بدان‌که «پی‌پینگ» هیچگونه مسئولیتی در قبال پرداخت مطالبات «پذیرنده» و بازپرداخت اقساط توسط خریداران کالا یا خدمات از طریق سرویس «پرداخت اقساطی» پی‌پینگ ندارد و مسئولیت کلیه‌ی مراتب مذکور، تماماً بر عهده‌ی کاربران است، ضروریست که «پذیرنده»، پس از کسب اطمینان به هر طریقی و بررسی شرایط هر کاربر، نسبت به ارائه‌‌ی خدمات یا فروش کالا به ایشان اقدام نماید. بدیهیست که مسئولیت انتخاب کاربر و ارائه‌ی محصولات و خدمات، متوجه « پذیرنده» خواهد بود.\r\n4-7-6) چنان‌چه به هر علتی (اعم از الزامات و محدودیت‌های سیستمی در صدور، درخواست پذیرنده و یا هر علت دیگر) تضامین دریافتی از خریداران، به نام «پی‌پینگ» (شرکت ماناتدبیرآواتک) صادر و در سیستم بانکی ثبت گردند، به هیچ‌وجه و به هیچ میزانی موجب مسئولیت «پی‌پینگ» در قبال بازپرداخت اقساط نخواهد بود. لیکن با توجه به ثبت تضامین به نام «شرکت ماناتدبیرآواتک»، در صورت عدم بازپرداخت اقساط توسط خریدار و درخواست کتبی «پذیرنده»، «پی‌پینگ» همکاری لازمه جهت اجرای تضامین و وصول مطالبات «پذیرنده» از خریدار یا ضامن را خواهد نمود.\r\nدر این راستا، ضروریست که «پذیرنده» همزمان با ارائه‌ی درخواست کتبی برای اجرای تضامین به «پی‌پینگ»، مبلغی معادل 20% سند ضمانت را بابت هزینه‌های وصول مطالبات و اقدامات حقوقی، به حساب معرفی شده از سوی «پی‌پینگ» واریز نماید تا اقدامات لازمه در این خصوص آغاز گردد. عدم ارائه‌ی درخواست کتبی و یا عدم واریز مبلغ مذکور، به منزله‌ی عدم تمایل «پذیرنده» به اجرای تضامین بوده و لذا «پی‌پینگ» هیچ‌گونه اقدامی در این خصوص به انجام نخواهد رساند. لازم به ذکر و توجه است که در هر صورت، تعهد «پی‌پینگ» صرفاً طرح دعاوی لازمه و پیگیری آن تا صدور رأی مقتضی و حسب مورد وصول پرونده به واحد اجرا می‌باشد و تعهدی در خصوص وصول وجه از خریدار یا ضامن نداشته و چنان‌چه به هر دلیل (از قبیل عدم دسترسی به مدیون) امکان وصول وجه پس از اجرای تضامین فراهم نگردد، مسئولیتی متوجه «پی‌پینگ» در هیچ موردی (علی‌الخصوص پرداخت بدهی خریدار یا ضامن) نخواهد بود.\r\n4-8) استرداد کالا براساس قوانین تجارت الکترونیک و توافقات احتمالی با کاربر است و «پذیرنده» مکلف به رعایت این قوانین و توافقات بوده و از طرفی «پی‌پینگ» مسئولیتی در این خصوص ندارد. همچنین درصورتی‌که بازگشت، مرجوعی یا استرداد سفارش اقساطی، طبق قانون انجام گیرد، «پذیرنده» موظف است مبلغ نقدی پرداخت شده توسط کاربر که متعلق به این خریداست را به حساب بانکی «کاربر» بازگشت داده و تضامین دریافت شده از کاربر (اعم از چک یا سفته) را حسب مورد مسترد یا از اعتبار ساقط نماید.\r\n";

                                       text.Span(st12)
                                           .FontSize(12)
                                           .DirectionFromRightToLeft();
                                   });
                                column.Item().PaddingTop(1)
                                   .Text(text =>
                                   {
                                       string st13 = $"4-9) پی‌پینگ در صورت نیاز و در محدوده‌ی توانایی و امکانات خود، سابقه‌ی اعتباری کاربران را استعلام کرده از نهادهای رتبه‌بندی اعتباری مجاز تحت نظارت بانک مرکزی جمهوری اسلامی ایران، سابقه‌ی اعلامی را در فرآیند اعطای اعتبار، در نظر خواهد گرفت. تمام اطلاعات این بخش، سوابق یک‌پارچه‌ای است که از تمام بانک‌ها و به کمک سرویس بانک مرکزی جمع آوری می‌شود. لیکن قابل توجه است که با این وجود، «پی‌پینگ» هیچ‌گونه تعهدی در قبال اعتبار اعلامی از سوی نهادهای مذکور و همچنین وصول مطالبات و پرداخت اقساط توسط کاربر نخواهد داشت.\r\n4-10) در جهت کنترل و مدیریت ریسک فروش اقساطی برای پذیرندگان، حسب مورد و به تشخیص «پی‌پینگ»، یک یا چند فقره چک یا سفته (بابت ضمانت دریافت اعتبار خرید اقساطی) از کاربران اخذ و عنداللزوم به «پذیرنده»، تحویل می‌گردد و «پذیرنده» اختیار دارد تا در صورت تخلف کاربر از تعهدات قراردادی، نسبت به وصول مطالبات، وجه التزام‌های قراردادی و خسارات مربوطه از طریق این تضامین اقدام نماید.\r\n4-11) اعتبار تخصیص یافته در خرید اقساطی، صرفاً قابل استفاده در سفارش کالا و فروش اقساطی کالا و خدمات است و قابلیت واریز نقدی به حساب بانکی کاربر را ندارد و لذا «پذیرنده» حق واریز یا پرداخت وجه اعتبار تخصیص داده شده به کاربر (تحت هیچ عنوان و با هیچ روشی) را نخواهد داشت. همچنین در صورت لغو سفارش خرید کاربر، مبلغ اعتبار تخصیص داده شده، قابلیت پرداخت نقدی یا اعتباری به کاربر را نداشته و در صورت تخلف «پذیرنده» از مراتب، نه تنها مسئولیت عواقب آن بر عهده‌ی «پذیرنده» است، بلکه «پی‌پینگ» حق قطع همکاری با پذیرنده (بدون طی هرگونه تشریفاتی) و قطع دسترسی پذیرنده به سیستم و فسخ یک طرفه‌ی قرارداد را خواهد داشت و «پذیرنده» حق طرح هرگونه ادعا یا اعتراضی در این خصوص را از خود سلب و ساقط نمود.\r\n4-12) پی‌پینگ می‌تواند در هنگام ارائه‌ی سرویس، علاوه بر مبلغ مندرج در ماده‌ی 3 این قرارداد، مبالغی را بابت ارائه‌ی خدمات مربوطه و همچنین بابت تأمین هزینه‌های مرتبط (از قبیل اعتبار سنجی و غیره)، برای فعالیت‌ها و اقداماتی که در راستای تسهیل خرید و الحاق پذیرندگان در جهت توسعه و گسترش شبکه‌ی فروش پذیرنده و نیز حق انتخاب کاربران و خرید و فروش اقساطی انجام می‌دهد، دریافت کند. لازم به توجه است که این مبلغ، هیچ‌گونه ارتباطی به اعتبار دریافتی کاربران و قیمت کالاهای اعلامی از سوی پذیرندگان ندارد و به صورت جداگانه محاسبه شده و به صورت نقدی از کاربران دریافت می‌شود. در صورت انصراف کاربر از خرید اقساطی یا استرداد کالا یا عدم انجام تعهدات «پذیرنده» (به هر دلیل و عنوان که باشد)، مبالغ پرداختی بابت موارد فوق‌الذکر قابل استرداد نخواهد بود و «پذیرنده» و کاربر حق طرح هرگونه ادعایی در این خصوص را از خود سلب و ساقط نمودند.\r\n4-13) محاسبه‌ی اقساط و زمان تودیع آن‌ها از لحظه‌ی خرید شروع شده و مطابق با تاریخ‌های اعلام شده توسط «پی‌پینگ» خواهد بود و ارتباطی با زمان تحویل سفارش توسط «پذیرنده» به «کاربر» ندارد. لذا «پذیرنده» متعهد و مکلف است تا تحویل کالا و ارائه‌ی خدمات را در کمترین زمان ممکن به انجام رساند و چنان‌چه به جهت عدم انجام به موقع تعهدات «پذیرنده»، خسارتی به «پی‌پینگ» یا «کاربر» وارد آید، مسئولیت آن متوجه «پذیرنده» بوده و متعهد به اخذ رضایت کاربر و پاسخگویی در مقابل کاربر می‌باشد.\r\n4-14) «پذیرنده» متعهد است تا وظایف خود (از قبیل تحویل به موقع کالا و خدمات، حفظ سلامت فیزیکی، اصالت کالا، مرجوعی و غیره) را با دقت کامل و بدون هرگونه تأخیر به انجام رساند. در غیر اینصورت، «پی‌پینگ» اختیار دارد تا علاوه‌بر خودداری از ادامه‌ی ارائه‌ی خدمات به پذیرنده، مسدود نمودن حساب کاربری وی و فسخ یک طرفه‌ی قرارداد، هرگونه همکاری لازمه با کاربر برای وصول حقوق و رفع مشکل را به انجام رسانده و عنداللزوم، اطلاعات «پذیرنده» را به مراجع قانونی از جمله قوه‌ی قضائیه و غیره، ارائه نماید. «پذیرنده» با آگاهی از این موضوع و اعلام رضایت خود، حق هرگونه اعتراضی را نسبت به اعمال این شرایط از خود سلب و ساقط می‌کند. \r\n4-15) از آن‌جاکه در صورت تسویه زودتر از موعد اقساط توسط کاربر، نامبرده مشمول تخفیف مطابق قوانین نظام بانکی کشور خواهد بود، «پذیرنده» مکلف به رعایت تخفیفات و کسر آن از کل بدهی کاربر می‌باشد و در این صورت، حقی در خصوص مطالبه‌ی این تخفیفات از «پی‌پینگ» یا کاربر نخواهد داشت.\r\n4-16) در سرویس پرداخت اقساطی با تضمین چک صیادی، چنان‌چه تضمینات به هر دلیلی تحویل «پذیرنده» گردند، نامبرده مکلف است در موارد ذیل، تضمینات مذکور را به «پی‌پینگ» تحویل نموده و در صورت ثبت چک به نام پذیرنده در سامانه‌ی صیادی، عودت چک را در آن سامانه نیز ثبت نماید:\r\n* چنان‌چه کاربر قبل از اعطای اعتبار نهایی و یا قبل از تکمیل فرآیند خرید، از درخواست خود انصراف دهد؛\r\n* پس از پرداخت کامل اقساط و تسویه‌ی کامل حساب‌ها توسط کاربر؛\r\nعدم اقدام به موقع «پذیرنده» در استرداد لاشه‌ی چک یا عودت آن در سامانه‌ی صیادی، موجب مسئولیت وی در مقابل صادرکننده خواهد بود و «پی‌پینگ»، هیچ‌گونه مسئولیتی در این خصوص نخواهد داشت.\r\n";

                                       text.Span(st13)
                                           .FontSize(12)
                                           .DirectionFromRightToLeft();
                                   });
                                column.Item().PaddingTop(1)
                                   .Text(text =>
                                   {
                                       string st14 = string.Format(
                                           @"4-17) كليه‌ی تذکرات و قوانین مندرج در قسمت قوانين و مقررات وب سايت «پی‌پینگ» به آدرس {0} ، و همچنین قوانین و مقرراتی که هنگام ایجاد حساب کاربری ارائه داده می‌شوند، جزئي از اين قرارداد تلقي شده و «پذیرنده» متعهد به رعایت آن‌ها همچون مفاد این قرارداد می‌باشد. همچنین در صورت بروز تغییر در قوانین مذکور یا ملاحظات «پی‌پینگ»، قوانین جدید و تغییرات یافته، در این قرارداد جاری می‌باشند."
                                           , "payping.com");
                                       text.Span(st14)
                                           .FontSize(12)
                                           .DirectionFromRightToLeft();
                                   });
                                column.Item().PaddingTop(1)
                                   .Text(text =>
                                   {
                                       string st15 = $"4-18) علاوه بر موارد حق فسخ مندرج در مواد این قرارداد، چنانچه «پذیرنده» در هر زمان و مرحله‌ای، از انجام تعهدات و وظایف قراردادی یا قانونی خود (در قبال «پی‌پینگ» یا در قبال کاربران) امتناع ورزد، «پی‌پینگ» حق قطع ادامه‌ی خدمات و مسدود نمودن دسترسی «پذیرنده» به خدمات و حساب کاربری، به صورت یک طرفه و عنداللزوم، فسخ یک‌طرفه‌ی قرارداد حاضر ـ بدون طی هرگونه تشریفات ـ را خواهد داشت.\r\n4-19) آدرس مندرج در مقدمه قرارداد و همچنین نشانی اعلام شده توسط پذیرنده در حساب کاربری، به عنوان نشانی پذیرنده تلقی می‌گردد و در صورتيكه «پذیرنده»، اقامتگاه قانوني خود را تغيير دهد بايد نشاني خود را ظرف مدت ١٥روز به «پی‌پینگ» اطلاع دهد و در حساب کاربری خود نیز آدرس جدید را اعلام نماید. تا زماني كه نشاني جديد به «پی‌پینگ» اعلام نشده است، كليه مكاتبات به نشاني قبلي ارسال مي‌شود و دريافت شده تلقي مي‌گردد.\r\n* لازم به ذکر است که طرفین توافق دارند که مراجع صالحه جهت طرح هرگونه دعوایی که مربوط به سرویس خرید اقساطی پی‌پینگ باشد (تحت هر عنوان و دلیل که باشد)، مراجع قضائی شهرهای «تهران» یا «مشهد» می‌باشند و پی‌پینگ مخیر در انتخاب هر یک از مراجع مذکور خواهد بود.\r\n";

                                       text.Span(st15)
                                           .FontSize(12)
                                           .DirectionFromRightToLeft();
                                   });
                                column.Item()
                                    
                                    .PaddingTop(400)
                                    .AlignCenter()
                                    .Table(table =>
                                    {
                                        table.ColumnsDefinition(columns =>
                                        {
                                            columns.RelativeColumn();
                                            columns.RelativeColumn();

                                        });

                                        // Header (automatically RTL due to default style)
                                        table.Header(header =>
                                        {
                                            header.Cell().Border(1).Padding(5).Text("پی پینگ").AlignCenter();
                                            header.Cell().Border(1).Padding(5).Text("پذیرنده").AlignCenter();
                                        });

                                        // Content
                                        table.Cell().Border(1).PaddingTop(100).PaddingBottom(5).Text("مدیرعامل\r\nاحمد منصوری رشتی").AlignCenter();
                                        table.Cell().Border(1).PaddingTop(100).PaddingBottom(5).Text("");
                                    });
                                //.Table(table =>
                                //{
                                //    table.ColumnsDefinition(columns =>
                                //    {
                                //        columns.ConstantColumn(20); // Left spacer
                                //        columns.RelativeColumn();   // Main content (will center automatically)
                                //        columns.ConstantColumn(20); // Right spacer
                                //    });

                                //    table.Cell().Border(0);

                                //    // Actual table structure (nested table approach)
                                //    table.Cell() // Row 2, Column 2 (center column)
                                        //.Table(table =>
                                        // {
                                        //     table.ColumnsDefinition(columns =>
                                        //     {
                                        //         columns.RelativeColumn();
                                        //         columns.RelativeColumn();

                                        //     });

                                        //     // Header (automatically RTL due to default style)
                                        //     table.Header(header =>
                                        //     {
                                        //         header.Cell().Border(1).Padding(5).Text("پی پینگ").AlignCenter();
                                        //         header.Cell().Border(1).Padding(5).Text("پذیرنده").AlignCenter();
                                        //     });

                                        //     // Content
                                        //     table.Cell().Border(1).Padding(5).Text("احمد منصوری رشتی \r\nمدیرعامل").AlignCenter();
                                        //     table.Cell().Border(1).Padding(5).Text("");
                                        // });

                                //    table.Cell().Border(0);
                                //});
                            });
                    });
                })
                .GeneratePdf(memoryStream);

                memoryStream.Position = 0;
                return memoryStream.ToArray();
            }
        }
    }
}
