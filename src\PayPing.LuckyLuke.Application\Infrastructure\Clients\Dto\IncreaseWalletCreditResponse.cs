﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public record IncreaseWalletCreditResponse(bool Success, string Error, decimal NewBalance, decimal newTotal);
    public record IncreasePrePaymentSumOnCreditResponse(bool Success, string Error, decimal newPrepaymentSum, decimal newMerchWageSum, decimal newConsumerWageSum, long newPrepaymentCount);
    public record IncreaseInstallmentPaymentSumOnCreditResponse(bool Success, string Error, decimal newInstallmentPaymentSum, long newInstallmentPaymentCount);
    public record DecreaseRawPrePaymentSumOnCreditResponse(bool Success, string Error, decimal newPrepaymentSum, long newPrepaymentCount);

}
