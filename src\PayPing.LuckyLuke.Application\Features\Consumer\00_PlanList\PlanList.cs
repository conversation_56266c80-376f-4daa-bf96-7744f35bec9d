﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Builder;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Consumer._00_PlanList
{
    public record PlanListRequest(Guid trackingCode);
    public class PlanListResponse
    {
        public PlanListResponse(string merchantName, string merchantRedirectUrl, decimal basketTotalAmount, List<PlanInfo> plansInfo)
        {
            this.merchantName = merchantName;
            this.merchantRedirectUrl = merchantRedirectUrl;
            this.basketTotalAmount = basketTotalAmount;
            this.plansInfo = plansInfo;
        }

        public string merchantName { get; set; }
        public string merchantRedirectUrl { get; }
        public decimal basketTotalAmount { get; set; }

        [Required]
        public List<PlanInfo> plansInfo { get; set; }
    }
    public class PlanInfo
    {
        public PlanInfo(int planId, decimal creditAmount, int installmentsCount, decimal totalFinalPayment, decimal prepayment, GuaranteeType guaranteeType, decimal eachInstallmentPayment, decimal consumerOperationCost)
        {
            this.planId = planId;
            this.creditAmount = creditAmount;
            this.installmentsCount = installmentsCount;
            this.totalFinalPayment = totalFinalPayment;
            this.prepayment = prepayment;
            this.guaranteeType = guaranteeType;
            this.eachInstallmentPayment = eachInstallmentPayment;
            this.consumerOperationCost = consumerOperationCost;
        }

        public int planId { get; set; }
        public decimal creditAmount { get; set; }
        public int installmentsCount { get; set; }
        public decimal totalFinalPayment { get; set; }
        public decimal prepayment { get; set; }
        public GuaranteeType guaranteeType { get; set; }
        public decimal eachInstallmentPayment { get; set; }
        public decimal consumerOperationCost { get; set; }
    }

    public class PlanListEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.PlanList,
                async (
                    [AsParameters] PlanListRequest request,
                    IPlanListRequestHandler handler,
                    IValidator<PlanListRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .AllowAnonymous()
            .WithName("PlanList")
            .WithApiVersionSet(builder.NewApiVersionSet("Plan").Build())
            .Produces<PlanListResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Plan List")
            .WithDescription("Plan List")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class PlanListRequestHandler : IPlanListRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserService _userService;
        private readonly IWebHostEnvironment _environment;
        private readonly BNPLOptions _bnplOptions;

        public PlanListRequestHandler(ApplicationDbContext dbContext, IUserService userService, IWebHostEnvironment environment, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userService = userService;
            _environment = environment;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<PlanListResponse> HandleAsync(PlanListRequest request, CancellationToken cancellationToken)
        {
            var order = await _dbContext.Orders.Where(o => o.TrackingCode == request.trackingCode).Include(o => o.OrderTargetPlans).FirstOrDefaultAsync();
            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            {
                throw new OrderExpiredException(request.trackingCode.ToString(), "سفارش منقضی شده است");
            }

            var activeContractId = await _dbContext.Contracts
                .Where(x =>
                    x.MerchantUserId == order.MerchantUserId &&
                    x.ActivationStatus == ActivationStatus.Final &&
                    x.ExpireDate > DateTimeOffset.UtcNow)
                .OrderByDescending(x => x.CreatedAt)
                .Select(x => x.Id)
                .FirstOrDefaultAsync(cancellationToken);

            if (activeContractId == default)
            {
                throw new ArgumentException("قرارداد فعالی یافت نشد");
            }

            var plansQuery = _dbContext.Plans.AsNoTracking()
                .Where(p =>
                    p.MerchantUserId == order.MerchantUserId &&
                    p.IsActive &&
                    p.Credit.IsActive &&
                    p.Credit.ContractId == activeContractId);

            if (order.OrderTargetPlans != null && order.OrderTargetPlans.Count > 0)
            {
                plansQuery = plansQuery.Where(p => p.OrderTargetPlans.Any(pp => pp.OrderId == order.Id));
            }

            var plans = await plansQuery.Include(p => p.Credit).ThenInclude(c => c.Contract).ToListAsync();

            var planInfos = new List<PlanInfo>();
            if (plans.Any())
            {
                foreach (var plan in plans)
                {
                    var orderFinancialModel = new OrderFinancialBuilder(
                        order.OrderTotalAmount,
                        plan.MaxCreditAmount,
                        plan.MinPrePaymentRate,
                        plan.InstallmentCount,
                        plan.InstallmentPeriodInMonths,
                        plan.InterestRate,
                        plan.Credit.Contract.OperationCost,
                        plan.Credit.Contract.OperationCostStrategy,
                        plan.GuaranteeType,
                        _environment.IsDevelopment())
                    .SetPrePayment()
                    .SetGuaranteeAmount()
                    .SetInstallments()
                    .Build();

                    var totalFinalPayment = decimal.Add(
                        decimal.Add(orderFinancialModel.RawPrePaymentAmount, orderFinancialModel.ConsumerOperationCostAmount),
                        orderFinancialModel.Installments.Sum(x => x.TotalAmount));

                    planInfos.Add(new PlanInfo(
                        plan.Id,
                        orderFinancialModel.CreditedAmount,
                        plan.InstallmentCount,
                        totalFinalPayment,
                        orderFinancialModel.RawPrePaymentAmount,
                        plan.GuaranteeType,
                        orderFinancialModel.Installments.First().TotalAmount,
                        orderFinancialModel.ConsumerOperationCostAmount));
                }
            }

            string cancelUrl = order.ClientCancelUrl.BuildCancelUrl(order.TrackingCode, order.ClientRefId, order.ClientCallbackUrl);

            return new PlanListResponse(order.MerchantName, cancelUrl, order.OrderTotalAmount, planInfos);
        }

    }
    public interface IPlanListRequestHandler
    {
        ValueTask<PlanListResponse> HandleAsync(PlanListRequest request, CancellationToken cancellationToken);
    }

    public class PlanListValidator : AbstractValidator<PlanListRequest>
    {
        public PlanListValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}

