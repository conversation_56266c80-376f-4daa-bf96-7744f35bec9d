﻿using DNTPersianUtils.Core;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Admin._003_AdminContractUpdate
{
    public record AdminContractUpdateRequest(decimal merchantOperationCost, string uid, int merchantId);

    public record AdminContractUpdateResponse();

    public class AdminContractUpdateEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                AdminRoutes.AdminContractUpdate,
                async (AdminContractUpdateRequest request, IAdminContractUpdateRequestHandler handler, IValidator<AdminContractUpdateRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("admin")
            .WithName("AdminContractUpdate")
            .WithApiVersionSet(builder.NewApiVersionSet("MerchantContract").Build())
            .Produces<AdminContractUpdateResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Admin Contract Update")
            .WithDescription("Admin Contract Update")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class AdminContractUpdateRequestHandler : IAdminContractUpdateRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IEnumerable<IDigitalSignProvider> _digitalSignProviders;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly IS3ServiceApiClient _s3ServiceApiClient;
        private readonly IUserService _userService;
        private readonly INotifyService _notifyService;
        private readonly IGeneratePdfFile _pdfGenerator;

        public AdminContractUpdateRequestHandler(ApplicationDbContext dbContext, IOptions<BNPLOptions> bnplOptions,
            IEnumerable<IDigitalSignProvider> digitalSignProviders, IUploadGrpcClient uploadGrpcClient,
            IS3ServiceApiClient s3ServiceApiClient, IUserService userService, INotifyService notifyService, IGeneratePdfFile pdfGenerator)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _digitalSignProviders = digitalSignProviders;
            _uploadGrpcClient = uploadGrpcClient;
            _s3ServiceApiClient = s3ServiceApiClient;
            _userService = userService;
            _notifyService = notifyService;
            _pdfGenerator = pdfGenerator;
        }

        public async ValueTask<AdminContractUpdateResponse> HandleAsync(AdminContractUpdateRequest request, CancellationToken cancellationToken)
        {
            var now = DateTimeOffset.UtcNow;

            var merchantInfo = await _dbContext.MerchantInfos
                 .Where(p => p.MerchantUserId == request.merchantId)
                 .FirstOrDefaultAsync();

            if (merchantInfo == null)
            {
                throw new ArgumentException("اطلاعات مرچنت یافت نشد");
            }

            var contract = await _dbContext.Contracts
               .Where(c => c.MerchantUserId == request.merchantId && c.ExpireDate >= now)
               .Include(p => p.Credits)
               .FirstOrDefaultAsync();

            if (contract == null)
            {
                throw new ArgumentException("قرارداد فروش اقساطی فعال یافت نشد");
            }

            if (contract.ActivationStatus != ActivationStatus.AcceptRegulations)
            {
                throw new ArgumentException("قرارداد در مرحله تایید ادمین نمی باشد");
            }

            if (!string.IsNullOrEmpty(request.uid))
                merchantInfo.PredefinedCode = request.uid;


            byte[] filledContract = _pdfGenerator.GenerateQuestPdfFromTextContent(
                $"{merchantInfo.MerchantInfoAgent.FirstName} {merchantInfo.MerchantInfoAgent.LastName}",
                merchantInfo.MerchantInfoAgent.FatherName,
                merchantInfo.MerchantInfoAgent.NationalCode,
                merchantInfo.Address,
                request.merchantOperationCost,
                contract.StartDate.UtcDateTime.ToShortPersianDateString(),
                contract.ExpireDate.UtcDateTime.ToShortPersianDateString());

            var uploadResult = await _uploadGrpcClient.UploadUserDocumentAsync(filledContract, "pdf", request.merchantId, cancellationToken);

            if (!uploadResult.Succeeded)
            {
                throw new ArgumentException("خطا در ذخیره فایل قرارداد");
            }

            contract.OperationCost = request.merchantOperationCost;
            contract.FilledContractId = uploadResult.SuccessResult;
            contract.ActivationStatus = ActivationStatus.AdminContractWageSet;

            await _dbContext.SaveChangesAsync();

            await _notifyService.SendBnplContractAdminApprovedMessage(
                merchantInfo.MerchantUserId,
                UrlHelpers.BuildFullUiUrl(_bnplOptions.MerchantUIBaseUrl, MerchantRoutes.PanelInstallmentLandingUI),
                cancellationToken);

            return new AdminContractUpdateResponse();
        }
           

    }
    public interface IAdminContractUpdateRequestHandler
    {
        ValueTask<AdminContractUpdateResponse> HandleAsync(AdminContractUpdateRequest request, CancellationToken cancellationToken);
    }

    public class AdminContractUpdateValidator : AbstractValidator<AdminContractUpdateRequest>
    {
        public AdminContractUpdateValidator()
        {
            RuleFor(x => x.merchantOperationCost).GreaterThan(decimal.Zero).WithResourceError(() => ValidatorDictionary.interest_rate_greater);
            RuleFor(x => x.merchantId).NotEmpty().WithResourceError(() => ValidatorDictionary.merchant_id_is_required);
        }
    }

}