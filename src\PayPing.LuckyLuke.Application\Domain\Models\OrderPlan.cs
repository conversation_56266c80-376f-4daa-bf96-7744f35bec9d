﻿namespace PayPing.BNPL.Domain.Models
{
    // value object
    // should be configured on ef as ComplexType
    public readonly record struct OrderPlan
    {
        public OrderPlan(
            decimal creditAmount,
            int installmentCount,
            decimal interestRate,
            int installmentPeriodInMonths,
            bool hasInstallmentDelayPenalty,
            decimal installmentDelayPenaltyRatePerDay,
            int installmentDelayPenaltyFreeInDays,
            int guarantorId,
            int creditId,
            Guid walletId,
            bool contractIsMandatory)
        {
            CreditAmount = creditAmount;
            InstallmentCount = installmentCount;
            InterestRate = interestRate;
            InstallmentPeriodInMonths = installmentPeriodInMonths;
            HasInstallmentDelayPenalty = hasInstallmentDelayPenalty;
            InstallmentDelayPenaltyRatePerDay = installmentDelayPenaltyRatePerDay;
            InstallmentDelayPenaltyFreeInDays = installmentDelayPenaltyFreeInDays;
            GuarantorId = guarantorId;
            CreditId = creditId;
            WalletId = walletId;
            ContractIsMandatory = contractIsMandatory;
        }

        public decimal CreditAmount { get; init; }

        public int InstallmentCount { get; init; }

        public decimal InterestRate { get; init; }

        public int InstallmentPeriodInMonths { get; init; }

        public bool HasInstallmentDelayPenalty { get; init; }

        public decimal InstallmentDelayPenaltyRatePerDay { get; init; }

        public int InstallmentDelayPenaltyFreeInDays { get; init; }

        public int GuarantorId { get; init; }

        public int CreditId { get; init; }

        public Guid WalletId { get; init; }

        public bool ContractIsMandatory { get; init; }
    }


}
