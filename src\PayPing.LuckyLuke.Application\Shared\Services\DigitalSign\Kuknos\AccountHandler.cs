﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kuknos;
using System.Security.Cryptography;

namespace PayPing.LuckyLuke.Application.Shared.Services.DigitalSign.Kuknos
{
    public class AccountHandler : KuknosBaseDigitalSignHandler, IKuknosDigitalSignService
    {
        private readonly IKuknosDigitalSignService _next;


        public AccountHandler(
            IKuknosDigitalSignService next,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _next = next;
        }

        public async ValueTask<object> HandleAsync(KuknosDigitalSignContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosDigitalSignStatusV1.CreateAccount)
                return await _next.HandleAsync(context, cancellationToken);

            // here we should have signature image stored in storage

            ValidateContext(context);

            using var rsa = new RSACryptoServiceProvider(2048);

            // RSA keys in PKCS#1 format
            string publicPrivateKey = rsa.ExportRSAPrivateKeyPem().RemoveFormat();
            string publicOnlyKey = rsa.ExportRSAPublicKeyPem().RemoveFormat();

            var sign = RSASign(rsa, context.Mobile.TrimStart('0'));

            var result = await _kuknosApi.CreateAccountAsync(publicOnlyKey, context.Mobile, context.NationalCode, sign, context.OrderTrackingCode, cancellationToken);

            if (!result.data)
            {
                throw new DigitalSignatureProviderException(context.OrderTrackingCode?.ToString(), "kuknos could not create account", false);
            }

            context.UserId = _userContext.CurrentUserId.Value;
            context.Status = KuknosDigitalSignStatusV1.CreateCertificate;
            context.PublicKey = publicOnlyKey;
            context.PrivateKey = publicPrivateKey;
            context.UserMobileSignature = sign;


            // save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

        protected override void ValidateContext(KuknosDigitalSignContextV1 context)
        {
            base.ValidateContext(context);
            //Guard.Against.NullOrEmpty(context.SignatureImageFileId);
            //Guard.Against.NullOrEmpty(context.SignatureImageFileName);
        }
    }
}
