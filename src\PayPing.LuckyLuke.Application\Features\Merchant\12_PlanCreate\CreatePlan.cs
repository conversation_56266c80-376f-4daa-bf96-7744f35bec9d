﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Features.Consumer._085_UploadSignature;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.Data;
using System.Text.RegularExpressions;

namespace PayPing.LuckyLuke.Application.Features.Merchant._12_PlanCreate
{
    public class CreatePlanRequest
    {
        public Guid CreditCode { get; set; }
        public GuaranteeType GuaranteeType { get; set; }
        public decimal MaxCreditAmount { get; set; }
        public int InstallmentCount { get; set; }
        public bool contractIsMandatory { get; set; }
        public List<CreatePlanEmployeeRequest> employees { get; set; }
        public IFormFile employeesFile { get; set; }
    }

    public class CreatePlanEmployeeRequest
    {
        public string mobile { get; set; }
        public string nationalCode { get; set; }
    }

    public record CreatePlanResponse(Guid planCode);

    public record EmployeeExcelReaderModel(string mobile, string nationalCode);

    public class CreatePlanEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.CreatePlan,
                async ([FromForm] CreatePlanRequest request, ICreatePlanRequestHandler handler, IValidator<CreatePlanRequest> validator, CancellationToken cancellationToken) =>
            {

                var validationResult = await validator.ValidateAsync(request);
                if (!validationResult.IsValid)
                {
                    throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                }

                var result = await handler.HandleAsync(request, cancellationToken);

                return Results.Ok(result);
            })
            .RequireAuthorization("write")
            .DisableAntiforgery()
            .WithName("CreatePlan")
            .WithApiVersionSet(builder.NewApiVersionSet("Plan").Build())
            .Produces<CreatePlanResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .Accepts<UploadSignatureRequest>("multipart/form-data")
            .WithSummary("Create Plan")
            .WithDescription("Create Plan")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CreatePlanRequestHandler : ICreatePlanRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;
        private readonly IExcelService _excelService;

        public CreatePlanRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IExcelService excelService, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
            _excelService = excelService;
        }

        public async ValueTask<CreatePlanResponse> HandleAsync(CreatePlanRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var merchantInfo = await _dbContext.MerchantInfos.AsNoTracking()
                .Where(p => p.MerchantUserId == userId)
                .FirstOrDefaultAsync();

            if (merchantInfo == null)
            {
                throw new NotFoundException(userId.ToString(), "پذیرنده با این شناسه یافت نشد");
            }

            var credit = await _dbContext.Credits.AsNoTracking()
                .Where(c =>
                    c.Code == request.CreditCode &&
                    c.IsActive &&
                    c.Contract.MerchantUserId == userId &&
                    c.Contract.StartDate <= DateTimeOffset.UtcNow &&
                    c.Contract.ExpireDate > DateTimeOffset.UtcNow)
                .Include(p => p.Contract)
                .FirstOrDefaultAsync();

            if (credit == null)
            {
                throw new NotFoundException(request.CreditCode.ToString(), "صندوق اعتباری با این شناسه یافت نشد");
            }

            List<EmployeeExcelReaderModel> excelList = new List<EmployeeExcelReaderModel>();
            if (request.GuaranteeType == GuaranteeType.Salary)
            {
                if ((request.employees == null || request.employees.Count == 0) && (request.employeesFile == null || request.employeesFile.Length == 0))
                {
                    throw new CustomValidationException(null, "لطفا اطلاعات مشتریان را وارد کنید", string.Empty);
                }

                if (request.employeesFile != null && request.employeesFile.Length > 0)
                {
                    ModelStateDictionary fileValidationDic = new ModelStateDictionary();
                    var validExcel = await FileHelpers.ProcessFormFile(request.employeesFile, fileValidationDic, ApplicationConstants.EmployeesEXcelValidExtensions, ApplicationConstants.EmployeesEXcelMaxSizeInBytes);

                    if (validExcel.Length == 0 || !fileValidationDic.IsValid)
                    {
                        throw new ArgumentException($"اکسل مشتریان معتبر نیست. {string.Join(", ", fileValidationDic.Values.SelectMany(v => v.Errors).Select(x => x.ErrorMessage))}");
                    }

                    (bool hasError, List<EmployeeExcelReaderModel> list) datasetModel = ExtractEmployeeInfoFromExcel(request.employeesFile);
                    if (datasetModel.hasError)
                    {
                        throw new ArgumentException($"فایل اکسل مشتریان معتبر نیست");
                    }
                    else
                    {
                        excelList = datasetModel.list;
                    }
                }
            }

            Plan plan = new Plan
            {
                Code = Guid.NewGuid(),
                CreditId = credit.Id,
                GuaranteeType = request.GuaranteeType,
                InstallmentCount = request.InstallmentCount,
                MaxCreditAmount = request.MaxCreditAmount,
                IsActive = true,
                MerchantUserId = userId,

                InstallmentPeriodInMonths = 1,
                HasInstallmentDelayPenalty = true,
                InstallmentDelayPenaltyFreeInDays = 5,
                InstallmentDelayPenaltyRatePerDay = 6,
                MinOrderAmount = 0,

                InterestRate = merchantInfo.InterestRate,
                MinPrePaymentRate = merchantInfo.MinPrePaymentRate,

                ContractIsMandatory = false /*request.contractIsMandatory,*/
            };

            _dbContext.Plans.Add(plan);
            await _dbContext.SaveChangesAsync(cancellationToken);

            if (request.GuaranteeType == GuaranteeType.Salary)
            {
                List<PlanEmployee> employees = new List<PlanEmployee>();

                if (request.employees != null && request.employees.Count > 0)
                {
                    foreach (var item in request.employees)
                    {
                        if (!excelList.Any(x => x.mobile == item.mobile && x.nationalCode == item.nationalCode))
                        {
                            var employeeInfoId = await InsertIfNotDuplicateOrGetAsync(merchantInfo.MerchantUserId, item.mobile, item.nationalCode, userId);
                            if (employeeInfoId != 0)
                            {
                                employees.Add(new PlanEmployee
                                {
                                    EmployeeInfoId = employeeInfoId,
                                    PlanId = plan.Id
                                });
                            }
                        }
                    }
                }

                foreach (var item in excelList)
                {
                    var employeeInfoId = await InsertIfNotDuplicateOrGetAsync(merchantInfo.MerchantUserId, item.mobile, item.nationalCode, userId);
                    if (employeeInfoId != 0)
                    {
                        employees.Add(new PlanEmployee
                        {
                            EmployeeInfoId = employeeInfoId,
                            PlanId = plan.Id
                        });
                    }
                }

                _dbContext.PlanEmployees.AddRange(employees);
                await _dbContext.SaveChangesAsync();
            }

            return new CreatePlanResponse(plan.Code);
        }

        public async ValueTask<int> InsertIfNotDuplicateOrGetAsync(int employerUserId, string mobileNumber, string nationalCode, int currentUserId)
        {
            string employerUserIdColumn = nameof(EmployeeInfo.EmployerUserId);
            string mobileColumn = nameof(EmployeeInfo.MobileNumber);
            string nationalcodeColumn = nameof(EmployeeInfo.NationalCode);

            var sql = @$"
                INSERT INTO public.""EmployeeInfos"" (""{employerUserIdColumn}"", ""{mobileColumn}"", ""{nationalcodeColumn}"", ""CreatedAt"", ""CreatedBy"")
                VALUES (@p0, @p1, @p2, @p3, @p4)
                ON CONFLICT (""{employerUserIdColumn}"", ""{mobileColumn}"", ""{nationalcodeColumn}"") DO NOTHING
                RETURNING public.""EmployeeInfos"".""Id"", public.""EmployeeInfos"".""EmployerUserId"", public.""EmployeeInfos"".""MobileNumber"", public.""EmployeeInfos"".""NationalCode"", public.""EmployeeInfos"".""CreatedAt"", public.""EmployeeInfos"".""CreatedBy"";";

            var res = await _dbContext.EmployeeInfos
                .FromSqlRaw(sql, employerUserId, mobileNumber, nationalCode, DateTimeOffset.UtcNow, currentUserId)
                .IgnoreQueryFilters()
                .ToListAsync();

            var addedId = res?.FirstOrDefault()?.Id;

            if (addedId.HasValue && addedId.Value != default)
            {
                return addedId.Value;
            }

            var existingId = await _dbContext.EmployeeInfos
                .Where(x => x.EmployerUserId == employerUserId && x.MobileNumber == mobileNumber && x.NationalCode == nationalCode)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            if (existingId == default)
            {
                throw new Exception($"couldnt find duplicate employee with info; employer {employerUserId}, mobile {mobileNumber}, nc {nationalCode}");
            }

            return existingId;
        }

        private (bool hasError, List<EmployeeExcelReaderModel> list) ExtractEmployeeInfoFromExcel(IFormFile excel)
        {
            var result = new List<EmployeeExcelReaderModel>();
            try
            {
                using Stream es = excel.OpenReadStream();

                var dataset = _excelService.ReadExcelFileIntoDataSet(es);

                var rows = dataset.Tables[0].Rows.Cast<DataRow>().Skip(1);

                foreach (var row in rows)
                {
                    string mobile = row[0].ToString().Trim();
                    string nc = row[1].ToString().Trim();

                    if (Regex.IsMatch(mobile, "^09[0-9]{9}$") && Regex.IsMatch(nc, "^[0-9]{10}$"))
                    {
                        result.Add(new EmployeeExcelReaderModel(mobile, nc));
                    }
                }

                return (false, result);
            }
            catch
            {
                return (true, default);
            }
        }

    }
    public interface ICreatePlanRequestHandler
    {
        ValueTask<CreatePlanResponse> HandleAsync(CreatePlanRequest request, CancellationToken cancellationToken);
    }

    public class CreatePlanValidator : AbstractValidator<CreatePlanRequest>
    {
        public CreatePlanValidator()
        {
            RuleFor(x => x.CreditCode).NotEmpty().WithResourceError(() => ValidatorDictionary.credit_code_is_required);
            RuleFor(x => x.GuaranteeType).IsInEnum().WithResourceError(() => ValidatorDictionary.guarantee_type_must_enum);
            RuleFor(x => x.MaxCreditAmount).NotEmpty().WithResourceError(() => ValidatorDictionary.max_credit_amount_is_required);
            RuleFor(x => x.InstallmentCount).GreaterThan(0).WithResourceError(() => ValidatorDictionary.installment_count_greater_zero);

            RuleFor(x => x.employees)
                .ForEach(item => item.SetValidator(new CreatePlanEmployeeRequestValidator()));
        }
    }

    public class CreatePlanEmployeeRequestValidator : AbstractValidator<CreatePlanEmployeeRequest>
    {
        public CreatePlanEmployeeRequestValidator()
        {
            RuleFor(x => x.mobile)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.mobile_is_required)
                 .Matches("^09[0-9]{9}$").WithResourceError(() => ValidatorDictionary.mobile_not_valid);

            RuleFor(x => x.nationalCode)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.nationalcode_is_required)
                .Matches("^[0-9]{10}$").WithResourceError(() => ValidatorDictionary.nationalcode_is_not_valid);
        }
    }
}
