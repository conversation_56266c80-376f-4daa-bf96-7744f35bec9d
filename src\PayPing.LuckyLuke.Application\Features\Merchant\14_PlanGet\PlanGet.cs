﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Merchant.PlanGet
{
    public record PlanGetRequest(Guid code);

    public class PlanGetResponse
    {
        public Guid code { get; set; }
        public DateTimeOffset planCreatedDate { get; set; }
        public bool isActive { get; set; }
        public GuaranteeType guaranteeType { get; set; }
        public decimal totalMaxAmount { get; set; }
        public int installmentsCount { get; set; }
        public decimal interestRate { get; set; }
        public decimal minPrePaymentRate { get; set; }
        public bool consumerContractIsMandatory { get; set; }
    }

    public class PlanGetEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                MerchantRoutes.PlanGet,
                async (
                    [AsParameters] PlanGetRequest request,
                    IPlanGetRequestHandler handler,
                    CancellationToken cancellationToken) =>
                {

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("PlanGet")
            .WithApiVersionSet(builder.NewApiVersionSet("Plan").Build())
            .Produces<PlanGetResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Plan Get")
            .WithDescription("Plan Get")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class PlanGetRequestHandler : IPlanGetRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly BNPLOptions _bnplOptions;

        public PlanGetRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<PlanGetResponse> HandleAsync(PlanGetRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var plan = await _dbContext.Plans.AsNoTracking()
                .Where(p =>
                    p.Code == request.code &&
                    p.MerchantUserId == userId &&
                    p.Credit.IsActive &&
                    p.Credit.Contract.StartDate <= DateTimeOffset.UtcNow &&
                    p.Credit.Contract.ExpireDate > DateTimeOffset.UtcNow)
                .Select(p => new PlanGetResponse()
                {
                    totalMaxAmount = p.MaxCreditAmount,
                    installmentsCount = p.InstallmentCount,
                    guaranteeType = p.GuaranteeType,
                    planCreatedDate = p.CreatedAt,
                    isActive = p.IsActive,
                    code = p.Code,
                    interestRate = p.InterestRate,
                    minPrePaymentRate = p.MinPrePaymentRate,
                    consumerContractIsMandatory = p.ContractIsMandatory,
                })
                .FirstOrDefaultAsync();

            return plan;
        }

    }
    public interface IPlanGetRequestHandler
    {
        ValueTask<PlanGetResponse> HandleAsync(PlanGetRequest request, CancellationToken cancellationToken);
    }
}
