﻿using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;

namespace PayPing.LuckyLuke.Application.Infrastructure.Provider
{
    public interface ISalaryGuaranteeProvider : IGuaranteeProvider
    {
        OrderGuaranteeDto CreateOrderGuarantee(long id, long orderId, int guarantorId, int consumerId, DateTimeOffset dueDate, CancellationToken cancellationToken);
        ValueTask<int> CurrentGuarantorIdAsync(CancellationToken cancellationToken);
        (bool isValid, string error, long? guaranteeId) ValidateIssuance(Order order);
    }
}
