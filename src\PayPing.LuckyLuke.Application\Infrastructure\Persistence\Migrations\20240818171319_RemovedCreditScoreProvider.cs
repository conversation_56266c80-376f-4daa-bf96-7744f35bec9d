﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class RemovedCreditScoreProvider : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "Fk_Ocv_Csp_CreditScoreProviderId",
                table: "OrderCreditValidations");

            migrationBuilder.DropTable(
                name: "CreditScoreProviders");

            migrationBuilder.DropIndex(
                name: "IX_OrderCreditValidations_CreditScoreProviderId",
                table: "OrderCreditValidations");

            migrationBuilder.DropColumn(
                name: "CreditScoreProviderId",
                table: "OrderCreditValidations");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CreditScoreProviderId",
                table: "OrderCreditValidations",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "CreditScoreProviders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false),
                    DeletedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    Domain = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    LastModifiedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<int>(type: "integer", nullable: true),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CreditScoreProviders", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OrderCreditValidations_CreditScoreProviderId",
                table: "OrderCreditValidations",
                column: "CreditScoreProviderId");

            migrationBuilder.CreateIndex(
                name: "IX_CreditScoreProvider_Name",
                table: "CreditScoreProviders",
                column: "Name");

            migrationBuilder.AddForeignKey(
                name: "Fk_Ocv_Csp_CreditScoreProviderId",
                table: "OrderCreditValidations",
                column: "CreditScoreProviderId",
                principalTable: "CreditScoreProviders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
