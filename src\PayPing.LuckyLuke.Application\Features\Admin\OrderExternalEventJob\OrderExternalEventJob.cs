﻿using DNTPersianUtils.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using Quartz;

namespace PayPing.LuckyLuke.Application.Features.Admin.OrderExternalEventJob
{
    [DisallowConcurrentExecution]
    public class OrderExternalEventJob : IJob
    {
        private static readonly HttpClient _httpClient = new HttpClient();
        private readonly BackgroundJobOptions _jobOptions;
        private readonly IGuaranteeService _guaranteeService;
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<OrderExternalEventJob> _logger;
        public OrderExternalEventJob(IOptions<BackgroundJobOptions> jobOptions, IGuaranteeService guaranteeService, ApplicationDbContext dbContext, ILogger<OrderExternalEventJob> logger)
        {
            _jobOptions = jobOptions.Value;
            _guaranteeService = guaranteeService;
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            if (context.RefireCount > 10)
            {
                _logger.LogInformation($"At OrderExternalEventJob Refire Count exceeded 10 times, short-circuiting job");
                return;
            }

            try
            {
                _logger.LogInformation($"At OrderExternalEventJob started at {DateTimeOffset.UtcNow}");

                int contextResult = 0;

                var events = await _dbContext.OrderExternalEventOutboxes.Where(e => e.FailCount <= 10).OrderBy(e => e.CreatedAt).ToListAsync();

                var cancelEvents = events.Where(e => e.EventType == OrderExternalEventType.CancelHook).ToList();
                foreach (var item in cancelEvents)
                {
                    await GetCancelResponsesAsync(item, context.CancellationToken);
                    contextResult += 1;
                }
                
                var revokeGuaranteeEvents = events.Where(e => e.EventType == OrderExternalEventType.RevokeGuarantee).ToList();
                foreach (var item in revokeGuaranteeEvents)
                {
                    await GetRevokeGuaranteeResponsesAsync(item, context.CancellationToken);
                    contextResult += 1;
                }

                context.Result = contextResult;
            }
            catch (Exception ex)
            {
                throw new JobExecutionException(msg: $"At OrderExternalEventJob exception happened at {DateTimeOffset.UtcNow} utc", refireImmediately: false, cause: ex);
            }
        }

        private async Task GetCancelResponsesAsync(OrderExternalEventOutbox e, CancellationToken cancellationToken)
        {
            try
            {
                if (e.HasFailed && e.FailCount > 10)
                {
                    _logger.LogInformation($"At OrderExternalEventJob bypassing event. id: {e.Id} with fail count: {e.FailCount}");
                    return;
                }

                var order = await _dbContext.Orders.AsNoTracking().Where(o => o.Id == e.OrderId).FirstOrDefaultAsync();

                var canceledStatuses = OrderStatusProvider.GetCanceled();

                if (order != null && canceledStatuses.Contains(order.Status))
                {
                    string cancelUrl = order.ClientCancelUrl.BuildCancelUrl(order.TrackingCode, order.ClientRefId, null);

                    if (!string.IsNullOrWhiteSpace(cancelUrl))
                    {

                        HttpResponseMessage response = await _httpClient.GetAsync(cancelUrl, cancellationToken);

                        if (response.IsSuccessStatusCode)
                        {
                            _dbContext.OrderExternalEventOutboxes.Remove(e);
                        }
                        else
                        {
                            e.HasFailed = true;
                            e.FailCount += 1;
                            e.Message = $"At OrderExternalEventJob CancelHook, Failed to get response from {cancelUrl}. Status Code: {response.StatusCode}";
                        }
                    }
                    else
                    {
                        _dbContext.OrderExternalEventOutboxes.Remove(e);
                    }
                }
                else
                {
                    _dbContext.OrderExternalEventOutboxes.Remove(e);
                }

                await _dbContext.SaveChangesAsync();
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, $"At OrderExternalEventJob GetCancelResponses, Request error for order {e.OrderId} at {DateTimeOffset.UtcNow} utc");
            }
            catch (TaskCanceledException ex) when (!cancellationToken.IsCancellationRequested)
            {
                _logger.LogError(ex, $"At OrderExternalEventJob GetCancelResponses, Request to order {e.OrderId} timed out.  at {DateTimeOffset.UtcNow} utc");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"At OrderExternalEventJob GetCancelResponses, Unexpected error for order {e.OrderId} at {DateTimeOffset.UtcNow} utc");
            }
            finally
            {
                e.HasFailed = true;
                e.FailCount += 1;
                e.Message = $"At OrderExternalEventJob GetCancelResponses, exception happened at {DateTimeOffset.UtcNow} utc";

                await _dbContext.SaveChangesAsync();
            }

        }

        private async Task GetRevokeGuaranteeResponsesAsync(OrderExternalEventOutbox e, CancellationToken cancellationToken)
        {

            try
            {
                if (e.HasFailed && e.FailCount > 10)
                {
                    _logger.LogInformation($"At OrderExternalEventJob bypassing event. id: {e.Id} with fail count: {e.FailCount}");
                    return;
                }

                var order = await _dbContext.Orders.Where(o => o.Id == e.OrderId).Include(o => o.OrderGuarantees).FirstOrDefaultAsync();

                var canceledStatuses = OrderStatusProvider.GetCanceled();

                if (order != null &&
                    canceledStatuses.Contains(order.Status) &&
                    !order.GuaranteeRevoked &&
                    order.OrderGuarantees != null &&
                    order.OrderGuarantees.Any())
                {
                    var guaranteeProvider = await _guaranteeService.GetGuaranteeProviderByGuarantorId(order.OrderPlan.GuarantorId);

                    // revoke guarantee
                    var guarantee = order.OrderGuarantees.OrderByDescending(g => g.CreatedAt).First();

                    (bool success, string error) revokeResult = (false, string.Empty);

                    if (guaranteeProvider.GuaranteeType == GuaranteeType.Promissory && guarantee.GuaranteeRefId.HasValue() && guaranteeProvider is IPromissoryGuaranteeProvider pp)
                    {
                        guarantee.SettlementData = pp.RenewSettlementContextIfNeeded(guarantee);

                        var settleResult = await pp.SettleGuaranteeAsync(guarantee.SettlementData, cancellationToken);

                        revokeResult = (settleResult.success, settleResult.error);
                    }
                    else
                    {
                        // this is subject to promissory deletion
                        var deleteResult = await guaranteeProvider.DeleteGuaranteeAsync(guarantee.Data);
                        revokeResult = (deleteResult.success, deleteResult.error);
                    }

                    if (revokeResult.success)
                    {
                        _dbContext.OrderExternalEventOutboxes.Remove(e);
                    }
                    else
                    {
                        e.HasFailed = true;
                        e.FailCount += 1;
                        e.Message = revokeResult.error;
                    }

                    order.GuaranteeRevoked = revokeResult.success;
                }
                else
                {
                    _dbContext.OrderExternalEventOutboxes.Remove(e);
                }

                await _dbContext.SaveChangesAsync();
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, $"At OrderExternalEventJob GetRevokeGuaranteeResponsesAsync, error for order {e.OrderId} at {DateTimeOffset.UtcNow} utc");
                e.HasFailed = true;
                e.FailCount += 1;
                e.Message = ex.Message;

                await _dbContext.SaveChangesAsync();
            }
            catch (TaskCanceledException ex) when (!cancellationToken.IsCancellationRequested)
            {
                _logger.LogError(ex, $"At OrderExternalEventJob GetRevokeGuaranteeResponsesAsync, order {e.OrderId} timed out. at {DateTimeOffset.UtcNow} utc");
                e.HasFailed = true;
                e.FailCount += 1;
                e.Message = ex.Message;

                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"At OrderExternalEventJob GetRevokeGuaranteeResponsesAsync, Unexpected error for order {e.OrderId} at {DateTimeOffset.UtcNow} utc");
                e.HasFailed = true;
                e.FailCount += 1;
                e.Message = ex.Message;

                await _dbContext.SaveChangesAsync();
            }
        }

    }
}
