﻿using FluentValidation;
using Grpc.Core;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Consumer.OrderList
{
    public record OrderListRequest(DateTime? FromDate, DateTime? ToDate, OrderStatusRequest? orderStatusRequest, int? merchantId, int PageSize = 10, int PageNumber = 1);

    public enum OrderStatusRequest
    {
        [Description("جاری")]
        NotPaidOff = 1,
        [Description("تسویه شده")]
        PaidOff = 2,
        [Description("لغو شده")]
        Canceled = 3,
    }

    public class OrderListResponse
    {
        public OrderListResponse(List<OrderInfoVM> orders, long total)
        {
            Orders = orders;
            this.total = total;
        }

        [Required]
        public long total { get; set; }

        [Required]
        public List<OrderInfoVM> Orders { get; set; }
    }
    public class OrderInfo
    {
        public OrderInfo(Guid trackingCode, DateTimeOffset createdDate, decimal totalBasketAmount, OrderStatus status, int totalInstallments, int paidInstallments, int lenderUserId, decimal creditAmount, string merchantName, decimal remainedInstallmentAmount, decimal paidInstallmentAmount)
        {
            TrackingCode = trackingCode;
            CreatedDate = createdDate;
            TotalBasketAmount = totalBasketAmount;
            Status = status;
            TotalInstallments = totalInstallments;
            PaidInstallments = paidInstallments;
            LenderUserId = lenderUserId;
            CreditAmount = creditAmount;
            MerchantName = merchantName;
            RemainedInstallmentAmount = remainedInstallmentAmount;
            PaidInstallmentAmount = paidInstallmentAmount;
        }

        public Guid TrackingCode { get; set; }
        public DateTimeOffset CreatedDate { get; set; }
        public decimal TotalBasketAmount { get; set; }
        public OrderStatus Status { get; set; }
        public int TotalInstallments { get; set; }
        public int PaidInstallments { get; set; }
        public int LenderUserId { get; set; }
        public decimal CreditAmount { get; set; }
        public string MerchantName { get; set; }
        public decimal RemainedInstallmentAmount { get; set; }
        public decimal PaidInstallmentAmount { get; set; }

    }

    public class OrderInfoVM
    {
        public OrderInfoVM(Guid trackingCode, DateTimeOffset createdDate, decimal totalBasketAmount, ConsumerOrderBriefStatus step, string businessName, int totalInstallments, int paidInstallments, decimal creditAmount, decimal remainedInstallmentAmount, decimal paidInstallmentAmount)
        {
            TrackingCode = trackingCode;
            CreatedDate = createdDate;
            TotalBasketAmount = totalBasketAmount;
            Step = step;
            MerchantBusinessName = businessName;
            TotalInstallments = totalInstallments;
            PaidInstallments = paidInstallments;
            CreditAmount = creditAmount;
            RemainedInstallmentAmount = remainedInstallmentAmount;
            PaidInstallmentAmount = paidInstallmentAmount;
        }

        public Guid TrackingCode { get; set; }
        public DateTimeOffset CreatedDate { get; set; }
        public decimal TotalBasketAmount { get; set; }
        public ConsumerOrderBriefStatus Step { get; set; }
        public string MerchantBusinessName { get; set; }
        public int TotalInstallments { get; set; }
        public int PaidInstallments { get; set; }
        public decimal CreditAmount { get; set; }
        public decimal RemainedInstallmentAmount { get; set; }
        public decimal PaidInstallmentAmount { get; set; }
    }

    public class OrderListEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.OrderList,
                async (
                    [AsParameters] OrderListRequest request,
                    IOrderListRequestHandler handler,
                    IValidator<OrderListRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var result = await handler.HandleAsync(request, cancellationToken);
                    return Results.Ok(result);
                })
                .RequireAuthorization("read")
                .WithName("Dashboard OrderList")
                .WithApiVersionSet(builder.NewApiVersionSet("Dashboard").Build())
                .Produces<OrderListResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Dashboard Order List")
                .WithDescription("Dashboard Order List")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OrderListRequestHandler : IOrderListRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly IUserService _userService;

        public OrderListRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IUserService userService)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _userService = userService;
        }

        public async ValueTask<OrderListResponse> HandleAsync(OrderListRequest request, CancellationToken cancellationToken)
        {
            var query = BuildOrderQuery(request);
            long total = await query.LongCountAsync();
            var paginatedOrders = await GetPaginatedOrdersAsync(query, request, cancellationToken);
            var orderInfos = GetOrderInfosVM(paginatedOrders, cancellationToken);

            return new OrderListResponse(orderInfos.ToList(), total);
        }

        private IQueryable<Order> BuildOrderQuery(OrderListRequest request)
        {
            int userId = _userContext.CurrentUserId.Value;
            var validStatuses = OrderStatusProvider.GetPrePaidOrMerchantCanceled();

            var query = _dbContext.Orders.AsNoTracking()
                .Where(o => o.ConsumerUserId == userId && validStatuses.Contains(o.Status));

            if (request.FromDate.HasValue)
            {
                query = query.Where(o => o.CreatedAt >= request.FromDate);
            }

            if (request.ToDate.HasValue)
            {
                query = query.Where(o => o.CreatedAt <= request.ToDate);
            }

            if (request.merchantId.HasValue)
            {
                query = query.Where(o => o.MerchantUserId == request.merchantId.Value);
            }

            if (request.orderStatusRequest.HasValue)
            {
                switch (request.orderStatusRequest.Value)
                {
                    case OrderStatusRequest.NotPaidOff:
                        var nps = OrderStatusProvider.GetNotPaidOff();
                        query = query.Where(o => nps.Contains(o.Status));
                        break;
                    case OrderStatusRequest.PaidOff:
                        var ps = OrderStatusProvider.GetPaidOff();
                        query = query.Where(o => ps.Contains(o.Status));
                        break;
                    //case OrderStatusRequest.Delayed:
                    //    var dl = OrderStatusProvider.GetDelayed();
                    //    query = query.Where(o => dl.Contains(o.Status));
                    //    break;
                    //case OrderStatusRequest.Defaulted:
                    //    var df = OrderStatusProvider.GetDefaulted();
                    //    query = query.Where(o => df.Contains(o.Status));
                    //    break;
                    case OrderStatusRequest.Canceled:
                        var cf = OrderStatusProvider.GetCanceled();
                        query = query.Where(o => cf.Contains(o.Status));
                        break;
                    default:
                        break;
                }
            }

            return query;
        }

        private async Task<List<OrderInfo>> GetPaginatedOrdersAsync(IQueryable<Order> query, OrderListRequest request, CancellationToken cancellationToken)
        {
            return await query
                .OrderByDescending(o => o.CreatedAt)
                .Select(o => new OrderInfo(
                    o.TrackingCode,
                    o.CreatedAt,
                    o.OrderTotalAmount,
                    o.Status,
                    o.Installments.Where(x => true).Count(),
                    o.Installments.Where(x => x.Status == InstallmentStatus.PaidOff || x.Status == InstallmentStatus.SalaryDeductionPaidOff).Count(),
                    o.MerchantUserId,
                    o.CreditedAmount,
                    o.MerchantName,
                    o.Installments.Where(x => x.Status != InstallmentStatus.PaidOff && x.Status != InstallmentStatus.SalaryDeductionPaidOff).Sum(x => x.FinalAmount),
                    o.Installments.Where(x => x.Status == InstallmentStatus.PaidOff || x.Status == InstallmentStatus.SalaryDeductionPaidOff).Sum(x => x.FinalAmount)
                ))
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);
        }

        private IEnumerable<OrderInfoVM> GetOrderInfosVM(IEnumerable<OrderInfo> orders, CancellationToken cancellationToken)
        {
            var tasks = orders.Select(o => new OrderInfoVM(
                o.TrackingCode,
                o.CreatedDate,
                o.TotalBasketAmount,
                o.Status.ToConsumerOrderBriefStatus(),
                o.MerchantName,
                o.TotalInstallments,
                o.PaidInstallments,
                o.CreditAmount,
                o.RemainedInstallmentAmount,
                o.PaidInstallmentAmount));

            return tasks;
        }

    }

    public interface IOrderListRequestHandler
    {
        ValueTask<OrderListResponse> HandleAsync(OrderListRequest request, CancellationToken cancellationToken);
    }

    public class OrderListValidator : AbstractValidator<OrderListRequest>
    {
        public OrderListValidator()
        {
            RuleFor(x => x.PageSize).GreaterThan(0).WithResourceError(() => ValidatorDictionary.page_size_greater_zero);
            RuleFor(x => x.PageNumber).GreaterThan(0).WithResourceError(() => ValidatorDictionary.page_number_greater_zero);
        }
    }
}
