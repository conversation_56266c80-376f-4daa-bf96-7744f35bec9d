﻿using Microsoft.Extensions.DependencyInjection;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class KuknosGuaranteeFactory : IGuaranteeFactory
    {
        private readonly IServiceProvider _serviceProvider;

        public KuknosGuaranteeFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public GuaranteeProvider GuaranteeProvider => GuaranteeProvider.Kuknos;

        public IPromissoryGuaranteeProvider CreatePromissory()
        {
            return _serviceProvider.GetServices<IPromissoryGuaranteeProvider>().Where(s => s.GuaranteeProvider == GuaranteeProvider).First();
        }

        public IChequeGuaranteeProvider CreateCheque()
        {
            throw new NotImplementedException();
        }

        public INoneGuaranteeProvider CreateNoneGuarantor()
        {
            throw new NotImplementedException();
        }

        public ISalaryGuaranteeProvider CreateSalaryGuarantor()
        {
            throw new NotImplementedException();
        }
    }
}
