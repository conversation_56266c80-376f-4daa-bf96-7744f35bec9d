﻿using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Attributes;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Web.OpenApiFilters
{
    public class OpenApiIgnoreSchemaFilter : ISchemaFilter, IDocumentFilter
    {
        private static HashSet<string> ExcludedKeys = new();

        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            if (context.Type.GetCustomAttribute<OpenApiIgnoreAttribute>() != null)
            {
                ExcludedKeys.Add(context.Type.FullName);
                return;
            }

            if (schema.Properties != null)
            {
                var excludedProperties = context.Type.GetProperties().Where(t => t.GetCustomAttribute<OpenApiIgnoreAttribute>() != null);

                foreach (var excludedProperty in excludedProperties)
                {
                    var propertyToRemove = schema.Properties.Keys.SingleOrDefault(x => string.Equals(x, excludedProperty.Name, StringComparison.OrdinalIgnoreCase));

                    if (propertyToRemove != null)
                    {
                        schema.Properties.Remove(propertyToRemove);
                    }
                }
            }
        }

        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            foreach (var key in swaggerDoc.Components.Schemas.Keys)
            {
                if (ExcludedKeys.Any(x => x.EndsWith(key)))
                {
                    swaggerDoc.Components.Schemas.Remove(key);
                }
            }
        }
    }
}
