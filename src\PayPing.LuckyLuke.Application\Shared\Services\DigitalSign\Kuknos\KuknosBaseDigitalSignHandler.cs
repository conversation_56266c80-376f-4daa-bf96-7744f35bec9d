﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Services.DigitalSign.Kuknos
{
    public abstract class KuknosBaseDigitalSignHandler
    {
        protected readonly ApplicationDbContext _dbContext;
        protected readonly IUserContext _userContext;
        protected readonly IKuknosApiHttpClient _kuknosApi;
        protected readonly KuknosOptions _kuknosOptions;
        protected KuknosBaseDigitalSignHandler(ApplicationDbContext dbContext, IUserContext userContext, IKuknosApiHttpClient kuknosApi, IOptions<KuknosOptions> kuknosOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _kuknosApi = kuknosApi;
            _kuknosOptions = kuknosOptions.Value;
        }

        protected string RSASign(RSA rsa, string content)
        {
            Guard.Against.NullOrEmpty(content, "signature content");
            byte[] bytes = Encoding.UTF8.GetBytes(content);
            return Convert.ToBase64String(rsa.SignData(bytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1));
        }

        protected async ValueTask<int> UpdateContextAsync(KuknosDigitalSignContextV1 context)
        {
            var og = await _dbContext.DigitalSigns.Where(x => x.Id == context.DigitalSignId).FirstOrDefaultAsync();

            Guard.Against.Null(og);

            og.Data = JsonSerializer.Serialize(context);

            return await _dbContext.SaveChangesAsync();
        }

        protected virtual void ValidateContext(KuknosDigitalSignContextV1 context)
        {
            Guard.Against.Null(context);

            Guard.Against.NullOrEmpty(context.FullNameFa);
            Guard.Against.NullOrEmpty(context.Address);

            Guard.Against.NullOrEmpty(context.Mobile);
            Guard.Against.NullOrEmpty(context.NationalCode);
            Guard.Against.NullOrEmpty(context.Province);
            Guard.Against.NullOrEmpty(context.City);
            Guard.Against.NullOrEmpty(context.Email);
            Guard.Against.NullOrEmpty(context.BirthDate);
            Guard.Against.NullOrEmpty(context.NationalCardSeries);
            Guard.Against.NullOrEmpty(context.PostalCode);
            Guard.Against.NullOrEmpty(context.FirstNameEn);
            Guard.Against.NullOrEmpty(context.LastNameEn);
            Guard.Against.NullOrEmpty(context.FirstNameFa);
            Guard.Against.NullOrEmpty(context.LastNameFa);
            Guard.Against.NullOrEmpty(context.FatherNameFa);

        }


    }
}
