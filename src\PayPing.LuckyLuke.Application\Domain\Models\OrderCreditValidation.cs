﻿using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class OrderCreditValidation : BaseEntity<long>, IAuditableEntity
    {

        public long OrderId { get; set; }

        // status is dependent on guarantor type
        public int Status { get; set; }

        public int Score { get; set; }
        public bool HasLoans { get; set; }
        public string Description { get; set; }
        public string Risk { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }

        public int? LastModifiedBy { get; set; }

        public Order Order { get; set; }
    }


}
