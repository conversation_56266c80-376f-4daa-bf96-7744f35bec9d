﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kuknos;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kuknos
{
    public class FinalizeHandler : Kuk<PERSON>BaseHandler, IKuknosPromissoryService
    {
        private readonly IKuknosPromissoryService _next;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly BNPLOptions _bnplOptions;

        public FinalizeHandler(
            IKuknosPromissoryService next,
            IOptions<BNPLOptions> bnplOptions,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IUploadGrpcClient uploadGrpcClient,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _next = next;
            _uploadGrpcClient = uploadGrpcClient;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<object> HandleAsync(KuknosPromissoryContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosPromissoryStatusV1.Finalize)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var result = await _kuknosApi.FinalizeAsync(context.PromissoryId, context.OrderTrackingCode, cancellationToken);

            if (result.data == null || string.IsNullOrWhiteSpace(result.data.final_pdf_document))
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "promissory finalize result is empty", false);
            }

            var downloaded = await _kuknosApi.DownloadAsync(result.data.final_pdf_document, context.OrderTrackingCode, cancellationToken);
            if (downloaded == null || downloaded.Length == 0)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not download final promissory from kuknos ipfs download", false);
            }

            var file = await downloaded.ToByteArrayAsync();

            // upload file to storage
            string validName = Path.ChangeExtension(Path.GetRandomFileName(), ".pdf");

            var uresult = await _uploadGrpcClient.UploadUserDocumentAsync(file, Path.GetExtension(validName).TrimStart('.'), context.ConsumerUserId, cancellationToken);

            if (!uresult.Succeeded)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not upload final promissory to storage service", false);
            }


            context.Status = KuknosPromissoryStatusV1.Done;
            context.PromissoryFinalDocumentHash = result.data.final_pdf_document;
            context.PromissoryTreasuryId = result.data.treasury_id;
            context.PromissoryFinalDocumentFileId = uresult.SuccessResult;
            context.PromissoryFinalDocumentFileContentType = "application/pdf";

            //// save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

        protected override void ValidateContext(KuknosPromissoryContextV1 context)
        {
            base.ValidateContext(context);
            Guard.Against.Expression<KuknosPromissoryContextV1>(x => x.PromissoryNotePaymentStatus != PromissoryNotePaymentStatus.Paid, context, "promissory note payment status is incorrect");

        }
    }
}
