﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.Tools.SdkBase.Types;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public interface IInquiryApiClient
    {
        Task<ServiceResult<NationalCodeToOwnerResponseModel>> GetPersonalInfoByNationalCode(string nationalCode, string birthDate, CancellationToken cancellationToken = default);
        Task<ServiceResult<MatchingResponseModel>> IsMatchingMobileWithNationalCode(string mobileNumber, string nationalCode, CancellationToken cancellationToken = default);
        Task<StartCreditScoreResponseModel> StartCreditScore(string nationalCode, string mobileNumber, CancellationToken cancellationToken = default);
        Task<ValidateCreditScoreResponseModel> ValidateCreditScore(string code, string token, CancellationToken cancellationToken = default);
        Task<ReportLinkCreditScoreResultModel> GenerateReportLinkCreditScore(string code, string token, CancellationToken cancellationToken = default);
        Task<CreditScoreReportResponseModel> GetCreditScoreReport(string uniqueCode, CancellationToken cancellationToken = default);
        Task<ShebaToOwnerResponse> GetPersonalInfoByIban(string sheba, CancellationToken cancellationToken = default);
        Task<MatchingResponseModel> IsMatchingShebaWithNationalCode(string sheba, string nationalCode, string birthDate, CancellationToken cancellationToken = default);
        Task<ServiceResult<PostalCodeInfoResponseModel>> GetPostalCodeInfo(string postalCode, CancellationToken cancellationToken = default);
        Task<ServiceResult<MatchingResponseModel>> IsMatchingCardWithNationalCode(string cardNumber, string nationalCode, string birthDate, CancellationToken cancellationToken = default);
        Task<ServiceResult<ConvertCardToShebaResponseModel>> ConvertCardToSheba(string cardNumber, CancellationToken cancellationToken = default);
    }
}
