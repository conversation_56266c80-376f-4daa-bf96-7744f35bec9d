﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedEmployees : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "EmployeeInfos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EmployerUserId = table.Column<int>(type: "integer", nullable: false),
                    MobileNumber = table.Column<string>(type: "character varying(11)", maxLength: 11, nullable: true),
                    NationalCode = table.Column<string>(type: "character varying(32)", maxLength: 32, nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmployeeInfos", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PlanEmployees",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EmployeeInfoId = table.Column<int>(type: "integer", nullable: false),
                    PlanId = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlanEmployees", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlanEmployees_EmployeeInfos_EmployeeInfoId",
                        column: x => x.EmployeeInfoId,
                        principalTable: "EmployeeInfos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PlanEmployees_Plans_PlanId",
                        column: x => x.PlanId,
                        principalTable: "Plans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeInfo_EmployerUserId",
                table: "EmployeeInfos",
                column: "EmployerUserId");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeInfo_MobileNumber",
                table: "EmployeeInfos",
                column: "MobileNumber");

            migrationBuilder.CreateIndex(
                name: "IX_EmployeeInfos_EmployerUserId_MobileNumber_NationalCode",
                table: "EmployeeInfos",
                columns: new[] { "EmployerUserId", "MobileNumber", "NationalCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PlanEmployees_PlanId",
                table: "PlanEmployees",
                column: "PlanId");

            migrationBuilder.CreateIndex(
                name: "IX_PlanEmployees_PlanId_EmployeeInfoId",
                table: "PlanEmployees",
                columns: new[] { "PlanId", "EmployeeInfoId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PlanEmployees");

            migrationBuilder.DropTable(
                name: "EmployeeInfos");
        }
    }
}
