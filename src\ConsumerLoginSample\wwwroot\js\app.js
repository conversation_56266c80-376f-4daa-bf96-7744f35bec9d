﻿function log() {
    document.getElementById("results").innerText = "";

    Array.prototype.forEach.call(arguments, function (msg) {
        if (typeof msg !== "undefined") {
            if (msg instanceof Error) {
                msg = "Error: " + msg.message;
            } else if (typeof msg !== "string") {
                msg = JSON.stringify(msg, null, 2);
            }
            document.getElementById("results").innerText += msg + "\r\n";
        }
    });
}

document.getElementById("login").addEventListener("click", login, false);
document.getElementById("api").addEventListener("click", api, false);
document.getElementById("logout").addEventListener("click", logout, false);

var config = {
    authority: "https://oauth.payping.dev",
    client_id: "payping-BNPLClient",
    redirect_uri: "https://localhost:5016/callback.html",
    response_type: "code",
    scope: "openid profile bnpl:consumerread bnpl:consumerwrite nationalcode birthday",
    post_logout_redirect_uri: "https://localhost:5016/index.html",
};
var mgr = new Oidc.UserManager(config);

mgr.events.addUserSignedOut(function () {
    log("User signed out of IdentityServer");
});

mgr.getUser().then(function (user) {
    if (user) {
        log("User logged in", user);
    } else {
        log("User not logged in");
    }
});

function login() {
    mgr.signinRedirect({
        extraQueryParams: {
            userType: "consumer"
        },
    });
}

function api() {
    mgr.getUser().then(function (user) {
        var url = "https://localhost:6001/identity";

        var xhr = new XMLHttpRequest();
        xhr.open("GET", url);
        xhr.onload = function () {
            log(xhr.status, JSON.parse(xhr.responseText));
        };
        xhr.setRequestHeader("Authorization", "Bearer " + user.access_token);
        xhr.send();
    });
}

function logout() {
    mgr.signoutRedirect();
}

