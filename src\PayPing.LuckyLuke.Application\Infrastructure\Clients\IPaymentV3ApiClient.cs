﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public interface IPaymentV3ApiClient
    {
        ValueTask<PaymentApiPayResponse> PayAsync(PaymentApiPayRequest model, string merchantToken, CancellationToken cancellationToken = default);
        ValueTask<PaymentApiVerifyResponse> VerifyAsync(PaymentApiVerifyRequest model, string merchantToken, CancellationToken cancellationToken = default);
    }
}
