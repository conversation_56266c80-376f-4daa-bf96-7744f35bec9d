﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Shared.Clients.ExternalHttp;
using PayPing.LuckyLuke.Application.Shared.Clients.Http;
using Polly;
using System.Net;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public static partial class ServiceCollectionExtensions
    {
        public static IServiceCollection AddInquiryHttpClient(this IServiceCollection services, ServiceDiscoveryOptions serviceDiscoveryOptions)
        {
            var retryPolicy = Policy
                .Handle<HttpRequestException>(ex =>
                {
                    if (ex.StatusCode == null || (int)ex.StatusCode == 408 || (int)ex.StatusCode >= 500)
                        return true;
                    else
                        return false;
                })
                .OrResult<HttpResponseMessage>(response => false)
                .WaitAndRetryAsync(3, _ => TimeSpan.FromMilliseconds(1000));

            services.AddHttpClient<IInquiryApiClient, InquiryApiClient>(c =>
            {
                c.BaseAddress = new Uri(serviceDiscoveryOptions.InquiryServiceAddress);
            })
            .AddClientAccessTokenHandler(ApplicationConstants.AutomaticAccessTokenManagementName)
            .AddPolicyHandler(retryPolicy)
            .AddTransientHttpErrorPolicy(p => p.CircuitBreakerAsync(5, TimeSpan.FromSeconds(30)))
            .AddPolicyHandler(p => Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(60)));

            return services;
        }

        /// <summary>
        /// Obsolete
        /// </summary>
        /// <param name="services"></param>
        /// <param name="serviceDiscoveryOptions"></param>
        /// <returns></returns>
        //public static IServiceCollection AddFileManagerHttpClient(this IServiceCollection services, ServiceDiscoveryOptions serviceDiscoveryOptions)
        //{
        //    var retryPolicy = Policy
        //        .Handle<HttpRequestException>(ex =>
        //        {
        //            if (ex.StatusCode == null || (int)ex.StatusCode == 408 || (int)ex.StatusCode >= 500)
        //                return true;
        //            else
        //                return false;
        //        })
        //        .OrResult<HttpResponseMessage>(response => false)
        //        .WaitAndRetryAsync(3, _ => TimeSpan.FromMilliseconds(1000));

        //    services.AddHttpClient<IFileManagerApiClient, FileManagerApiClient>(c =>
        //    {
        //        c.BaseAddress = new Uri(serviceDiscoveryOptions.FileManagerServiceAddress);
        //    })
        //    .AddClientAccessTokenHandler(ApplicationConstants.AutomaticAccessTokenManagementName)
        //    .AddPolicyHandler(retryPolicy)
        //    .AddTransientHttpErrorPolicy(p => p.CircuitBreakerAsync(5, TimeSpan.FromSeconds(30)))
        //    .AddPolicyHandler(p => Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(60)));

        //    return services;
        //}

        public static IServiceCollection AddS3ServiceHttpClient(this IServiceCollection services, ServiceDiscoveryOptions serviceDiscoveryOptions)
        {
            var retryPolicy = Policy
                .Handle<HttpRequestException>(ex =>
                {
                    if (ex.StatusCode == null || (int)ex.StatusCode == 408 || (int)ex.StatusCode >= 500)
                        return true;
                    else
                        return false;
                })
                .OrResult<HttpResponseMessage>(response => false)
                .WaitAndRetryAsync(3, _ => TimeSpan.FromMilliseconds(1000));

            services.AddHttpClient<IS3ServiceApiClient, S3ServiceApiClient>(c =>
            {
                c.BaseAddress = new Uri(serviceDiscoveryOptions.S3ServiceAddress);
            })
            .AddPolicyHandler(retryPolicy)
            .AddTransientHttpErrorPolicy(p => p.CircuitBreakerAsync(5, TimeSpan.FromSeconds(30)))
            .AddPolicyHandler(p => Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(60)));

            return services;
        }

        public static IServiceCollection AddPaymentIpgHttpClient(this IServiceCollection services, ServiceDiscoveryOptions serviceDiscoveryOptions)
        {
            var retryPolicy = Policy
                .Handle<HttpRequestException>(ex =>
                {
                    if (ex.StatusCode == null || (int)ex.StatusCode == 408 || (int)ex.StatusCode >= 500)
                        return true;
                    else
                        return false;
                })
                .OrResult<HttpResponseMessage>(response => false)
                .WaitAndRetryAsync(3, _ => TimeSpan.FromMilliseconds(1000));

            services.AddHttpClient<IPaymentIpgApiClient, PaymentIpgApiClient>(c =>
            {
                c.BaseAddress = new Uri(serviceDiscoveryOptions.IpgServiceAddress);
            })
            .AddClientAccessTokenHandler(ApplicationConstants.AutomaticAccessTokenManagementName)
            .AddPolicyHandler(retryPolicy)
            .AddTransientHttpErrorPolicy(p => p.CircuitBreakerAsync(5, TimeSpan.FromSeconds(30)))
            .AddPolicyHandler(p => Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(60)));

            return services;
        }

        public static IServiceCollection AddPaymentV3HttpClient(this IServiceCollection services, ServiceDiscoveryOptions serviceDiscoveryOptions)
        {
            var retryPolicy = Policy
                .Handle<HttpRequestException>(ex =>
                {
                    if (ex.StatusCode == null || (int)ex.StatusCode == 408 || (int)ex.StatusCode >= 500)
                        return true;
                    else
                        return false;
                })
                .OrResult<HttpResponseMessage>(response => false)
                .WaitAndRetryAsync(3, _ => TimeSpan.FromMilliseconds(1000));

            services.AddHttpClient<IPaymentV3ApiClient, PaymentV3ApiClient>(c =>
            {
                c.BaseAddress = new Uri(serviceDiscoveryOptions.PaymentServiceAddress);
            })
            .AddPolicyHandler(retryPolicy)
            .AddTransientHttpErrorPolicy(p => p.CircuitBreakerAsync(5, TimeSpan.FromSeconds(30)))
            .AddPolicyHandler(p => Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(60)));

            return services;
        }

        public static IServiceCollection AddUserHttpClient(this IServiceCollection services, ServiceDiscoveryOptions serviceDiscoveryOptions)
        {
            var retryPolicy = Policy
                .Handle<HttpRequestException>(ex =>
                {
                    if (ex.StatusCode == null || (int)ex.StatusCode == 408 || (int)ex.StatusCode >= 500)
                        return true;
                    else
                        return false;
                })
                .OrResult<HttpResponseMessage>(response => false)
                .WaitAndRetryAsync(3, _ => TimeSpan.FromMilliseconds(1000));

            services.AddHttpClient<IUserApiClient, UserApiClient>(c =>
            {
                c.BaseAddress = new Uri(serviceDiscoveryOptions.UserServiceAddress);
            })
            .AddPolicyHandler(retryPolicy)
            .AddTransientHttpErrorPolicy(p => p.CircuitBreakerAsync(5, TimeSpan.FromSeconds(30)))
            .AddPolicyHandler(p => Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(60)));

            return services;
        }


        public static IServiceCollection AddKuknosHttpClient(this IServiceCollection services, KuknosOptions kuknosOptions, ProxyOptions proxyOptions, IWebHostEnvironment environment)
        {
            var retryPolicy = Policy
                .Handle<HttpRequestException>(ex =>
                {
                    if (ex.StatusCode == null || (int)ex.StatusCode == 408 || (int)ex.StatusCode >= 500)
                        return true;
                    else
                        return false;
                })
                .OrResult<HttpResponseMessage>(response => false)
                .WaitAndRetryAsync(3, _ => TimeSpan.FromMilliseconds(1000));

            services.AddHttpClient<IKuknosApiHttpClient, KuknosApiHttpClient>(c =>
            {
                c.BaseAddress = new Uri(kuknosOptions.BaseUrl);
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                Proxy = new WebProxy(proxyOptions.Address)
                {
                    BypassProxyOnLocal = false,
                    UseDefaultCredentials = false,
                    Credentials = new NetworkCredential()
                    {
                        UserName = proxyOptions.UserName,
                        Password = proxyOptions.Password
                    }
                },
                UseProxy = !environment.IsProduction(),
            })
            .AddPolicyHandler(retryPolicy)
            .AddTransientHttpErrorPolicy(p => p.CircuitBreakerAsync(5, TimeSpan.FromSeconds(30)))
            .AddPolicyHandler(p => Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(60)));

            return services;
        }

        public static IServiceCollection AddKiahooshanHttpClient(this IServiceCollection services, KiahooshanOptions kihooOptions, ProxyOptions proxyOptions, IWebHostEnvironment environment)
        {
            var retryPolicy = Policy
                .Handle<HttpRequestException>(ex =>
                {
                    if (ex.StatusCode == null || (int)ex.StatusCode == 408 || (int)ex.StatusCode >= 500)
                        return true;
                    else
                        return false;
                })
                .OrResult<HttpResponseMessage>(response => false)
                .WaitAndRetryAsync(4, _ => TimeSpan.FromMilliseconds(2000));

            services.AddHttpClient<IKiahooshanApiHttpClient, KiahooshanApiHttpClient>(c =>
            {
                c.BaseAddress = new Uri(kihooOptions.BaseUrl);
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                Proxy = new WebProxy(proxyOptions.Address)
                {
                    BypassProxyOnLocal = false,
                    UseDefaultCredentials = false,
                    Credentials = new NetworkCredential()
                    {
                        UserName = proxyOptions.UserName,
                        Password = proxyOptions.Password
                    }
                },
                UseProxy = !environment.IsProduction(),
            })
            .AddPolicyHandler(retryPolicy)
            .AddTransientHttpErrorPolicy(p => p.CircuitBreakerAsync(6, TimeSpan.FromSeconds(30)))
            .AddPolicyHandler(p => Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(120)));

            return services;
        }

    }
}
