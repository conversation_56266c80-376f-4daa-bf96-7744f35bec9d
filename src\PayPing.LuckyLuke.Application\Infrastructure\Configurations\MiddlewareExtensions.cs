﻿using Elastic.Apm.Api;
using Elastic.Apm.NetCoreAll;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Builder.Extensions;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using Swashbuckle.AspNetCore.Swagger;
using System;

namespace PayPing.BNPL.Application.Infrastructure.Configurations
{
    public static class MiddlewareExtensions
    {
        public static IApplicationBuilder UseConsumerApplication(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            var configuration = app.ApplicationServices.GetRequiredService<IConfiguration>();
            var bnplOptions = configuration.GetSection(BNPLOptions.SectionName).Get<BNPLOptions>();

            app.UseApm(configuration);
            app.UseRouting();

            app.UseExceptionHandler(_ => { });
            app.UseCors(ApplicationConstants.CorsPolicy);
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpointBuilder =>
            {
                endpointBuilder.MapConsumerEndpoints();
                //endpointBuilder.MapGet("/health", x =>
                //{
                //    using (var scope = app.ApplicationServices.CreateScope())
                //    {
                //        var aa = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>().ContractTemplates.FirstOrDefault().DurationInDays.ToString();
                //        return x.Response.WriteAsync(aa);
                //    }
                //}).AllowAnonymous();
                endpointBuilder.UseCustomHealthCheck();

                if (env.IsDevelopment())
                {
                    app.UseCustomSwagger(endpointBuilder, bnplOptions.BaseUrl, bnplOptions.BasePath);
                }

                endpointBuilder.MapPrometheusScrapingEndpoint();
            });




            return app;
        }

        private static IEndpointRouteBuilder MapConsumerEndpoints(this IEndpointRouteBuilder eb)
        {
            var scope = eb.ServiceProvider.CreateScope();

            var endpoints = scope.ServiceProvider.GetServices<IMinimalEndpoint>();

            foreach (var endpoint in endpoints)
            {
                endpoint.MapEndpoint(eb);
            }

            return eb;
        }

        public static IApplicationBuilder UseMerchantApplication(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            var configuration = app.ApplicationServices.GetRequiredService<IConfiguration>();
            var bnplOptions = configuration.GetSection(BNPLOptions.SectionName).Get<BNPLOptions>();


            app.UseApm(configuration);
            app.UseRouting();

            app.UseExceptionHandler(_ => { });
            app.UseCors(ApplicationConstants.CorsPolicy);
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpointBuilder =>
            {
                endpointBuilder.MapMerchantEndpoints();
                //endpointBuilder.MapGet("/health", x =>
                //{
                //    using (var scope = app.ApplicationServices.CreateScope())
                //    {
                //        var aa = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>().ContractTemplates.FirstOrDefault().DurationInDays.ToString();
                //        return x.Response.WriteAsync(aa);
                //    }
                //}).AllowAnonymous();
                endpointBuilder.UseCustomHealthCheck();

                if (env.IsDevelopment())
                {
                    app.UseCustomSwagger(endpointBuilder, bnplOptions.BaseUrl, bnplOptions.BasePath);
                }

                endpointBuilder.MapPrometheusScrapingEndpoint();
            });

            return app;
        }

        private static IEndpointRouteBuilder MapMerchantEndpoints(this IEndpointRouteBuilder eb)
        {
            var scope = eb.ServiceProvider.CreateScope();

            var endpoints = scope.ServiceProvider.GetServices<IMerchantMinimalEndpoint>();

            foreach (var endpoint in endpoints)
            {
                endpoint.MapEndpoint(eb);
            }

            return eb;
        }

        public static IApplicationBuilder UseAdminApplication(this IApplicationBuilder app, IWebHostEnvironment env)
        {
            var configuration = app.ApplicationServices.GetRequiredService<IConfiguration>();
            var bnplOptions = configuration.GetSection(BNPLOptions.SectionName).Get<BNPLOptions>();


            app.UseApm(configuration);
            app.UseRouting();

            app.UseExceptionHandler(_ => { });
            app.UseCors(ApplicationConstants.CorsPolicy);
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpointBuilder =>
            {
                endpointBuilder.MapAdminEndpoints();
                //endpointBuilder.MapGet("/health", x =>
                //{
                //    using (var scope = app.ApplicationServices.CreateScope())
                //    {
                //        var aa = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>().ContractTemplates.FirstOrDefault().DurationInDays.ToString();
                //        return x.Response.WriteAsync(aa);
                //    }
                //}).AllowAnonymous();
                endpointBuilder.UseCustomHealthCheck();

                if (env.IsDevelopment())
                {
                    app.UseCustomSwagger(endpointBuilder, bnplOptions.BaseUrl, bnplOptions.BasePath);
                }

                endpointBuilder.MapPrometheusScrapingEndpoint();
            });

            return app;
        }
        private static IEndpointRouteBuilder MapAdminEndpoints(this IEndpointRouteBuilder eb)
        {
            var scope = eb.ServiceProvider.CreateScope();

            var endpoints = scope.ServiceProvider.GetServices<IAdminMinimalEndpoint>();

            foreach (var endpoint in endpoints)
            {
                endpoint.MapEndpoint(eb);
            }

            return eb;
        }


        public static IApplicationBuilder UseApm(this IApplicationBuilder app, IConfiguration configuration)
        {
            app.UseAllElasticApm(configuration);

            Elastic.Apm.Agent.AddFilter((ITransaction transaction) =>
            {
                // exclude Http Options method
                if (transaction.Context?.Request?.Method == "OPTIONS") return null;

                return transaction;
            });

            Elastic.Apm.Agent.AddFilter((ITransaction transaction) =>
            {
                // exclude Http /health endpoint
                if (transaction.Context?.Request?.Url?.PathName is string path && path.StartsWith("/health"))
                {
                    return null;
                }

                return transaction;
            });

            Elastic.Apm.Agent.AddFilter((ITransaction transaction) =>
            {
                // exclude Http /metrics endpoint
                if (transaction.Context?.Request?.Url?.PathName is string path && path.StartsWith("/metrics"))
                {
                    return null;
                }

                return transaction;
            });

            return app;
        }
        public static IApplicationBuilder UseCustomSwagger(this IApplicationBuilder app, IEndpointRouteBuilder eb, string baseUrl, string basePath)
        {
            app.UseSwagger(options =>
            {
                //if (!string.IsNullOrWhiteSpace(basePath))
                //{
                //    options.PreSerializeFilters.Add((swaggerDoc, httpReq) =>
                //    {
                //        swaggerDoc.Servers = new List<OpenApiServer> { new OpenApiServer { Url = $"{baseUrl}/{basePath}" } };
                //    });
                //}
            });
            app.UseSwaggerUI(
            options =>
            {
                string swaggerJsonBasePath = string.IsNullOrWhiteSpace(basePath) ? $"/swagger" : $"/{basePath}/swagger";

                var descriptions = eb.DescribeApiVersions();
                // build a swagger endpoint for each discovered API version
                foreach (var description in descriptions)
                {
                    var url = $"{swaggerJsonBasePath}/{description.GroupName}/swagger.json";
                    var name = description.GroupName.ToUpperInvariant();
                    options.SwaggerEndpoint(url, name);
                }
            });

            return app;
        }

        public static IEndpointRouteBuilder UseCustomHealthCheck(this IEndpointRouteBuilder app)
        {
            // Map Liveness Check
            app.MapHealthChecks("/health/liveness", new HealthCheckOptions()
            {
                Predicate = check => check.Tags.Contains("liveness"),
                //ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse,
                ResultStatusCodes =
            {
                [HealthStatus.Healthy] = StatusCodes.Status200OK,
                [HealthStatus.Degraded] = StatusCodes.Status500InternalServerError,
                [HealthStatus.Unhealthy] = StatusCodes.Status503ServiceUnavailable
            }
            });

            // Map Readiness Check
            app.MapHealthChecks("/health/readiness", new HealthCheckOptions()
            {
                Predicate = check => check.Tags.Contains("readiness"),
                //ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse,
                ResultStatusCodes =
            {
                [HealthStatus.Healthy] = StatusCodes.Status200OK,
                [HealthStatus.Degraded] = StatusCodes.Status500InternalServerError,
                [HealthStatus.Unhealthy] = StatusCodes.Status503ServiceUnavailable
            }
            });

            // Map Health Checks UI Dashboard
            //app.MapHealthChecksUI(options =>
            //{
            //    options.UIPath = "/health-ui"; // This will make the UI accessible at /health-ui
            //});

            return app;
        }
    }
}
