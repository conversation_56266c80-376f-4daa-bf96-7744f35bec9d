﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public interface IKuknosApiHttpClient
    {
        ValueTask<KuknosApiResponse<bool>> CreateAccountAsync(
            string publicKey,
            string mobile,
            string nationalCode,
            string userSignature,
            Guid? orderTrackingCode, 
            CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<KuknosApiCreateCertificateDataResponse>> CreateCertificateAsync(
            string publicKey,
            string birthDate,
            string certificateSigningRequest,
            string nationalCode,
            string nationalCardSeries,
            string postalCode,
            string firstnameEn,
            string lastnameEn,
            string firstNameFa,
            string lastNameFa,
            string fathernameFa,
            bool isMale,
            Stream signatureImage,
            string signatureImageName,
            Guid? orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<bool>> DeleteAsync(
            string promissoryId,
            Guid orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<Stream> DownloadAsync(
            string fileHash,
            Guid orderTrackingCode,
            CancellationToken cancellationToken);
        ValueTask<KuknosApiFileDownloadResponse> DownloadRawDocumentAsync(
            string docHash,
            Guid orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<KuknosApiFinalizeResponse>> FinalizeAsync(
            string promissoryId,
            Guid orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<KuknosApiFinalizeSettlementResponse>> FinalizeSettlementAsync(string promissoryId, Guid orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<string>> GetCertificateAsync(
            string publicKey,
            string trackingCode,
            Guid? orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<string> GetTokenAsync(
            Guid? orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<KuknosApiIssuePromissoryDataResponse>> IssuePromissoryAsync(
            KuknosApiIssuePromissoryRequest model,
            Guid orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<KuknosApiIssueSettlementDataResponse>> IssueSettlementAsync(string promissoryId, Guid orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<string>> PayAsync(
            KuknosApiPayRequest model,
            string promissoryId,
            Guid orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<KuknosApiFileDownloadResponse> SignDocumentWithSignerPdfAsync(
            string certificate,
            string privateKey,
            byte[] rawDoc,
            Guid orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<bool>> SignedAsync(
            KuknosApiSignedRequest model,
            string promissoryId,
            Guid orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<bool>> SignSettlementAsync(string promissoryId, string signedPdfHash, Guid orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<KuknosApiResponse<string>> UploadAsync(
            byte[] file,
            string name,
            Guid orderTrackingCode,
            CancellationToken cancellationToken = default);
    }
}
