﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Services.User;

namespace PayPing.LuckyLuke.Application.Features.Admin.GuarantorCreate
{
    public record GuarantorCreateRequest(GuaranteeType GuaranteeType, GuaranteeProvider GuaranteeProvider, string Name, string Domain);

    public class GuarantorCreateResponse
    {
        public GuaranteeType GuaranteeType { get; set; }
        public GuaranteeProvider GuaranteeProvider { get; set; }
        public Guid Code { get; set; }
        public string Name { get; set; }
        public string Domain { get; set; }
    }

    public class GuarantorCreateEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                AdminRoutes.GuarantorCreate,
                async (
                    [FromBody] GuarantorCreateRequest request,
                    IGuarantorCreateRequestHandler handler,
                    IValidator<GuarantorCreateRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
                .RequireAuthorization("admin")
                .WithName("GuarantorCreate")
                .WithApiVersionSet(builder.NewApiVersionSet("Guarantor").Build())
                .Produces<GuarantorCreateResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Guarantor Create")
                .WithDescription("Guarantor Create")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GuarantorCreateRequestHandler : IGuarantorCreateRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;

        public GuarantorCreateRequestHandler(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async ValueTask<GuarantorCreateResponse> HandleAsync(GuarantorCreateRequest request, CancellationToken cancellationToken)
        {
            Guarantor newGuarantor = new Guarantor
            {
                Code = Guid.NewGuid(),
                Domain = request.Domain,
                GuaranteeProvider = request.GuaranteeProvider,
                GuaranteeType = request.GuaranteeType,
                Name = request.Name,
            };
           
            await _dbContext.Guarantors.AddAsync(newGuarantor);
            await _dbContext.SaveChangesAsync();

            return new GuarantorCreateResponse
            {
                Code = newGuarantor.Code,
                Domain = newGuarantor.Domain,
                GuaranteeProvider = newGuarantor.GuaranteeProvider,
                GuaranteeType = newGuarantor.GuaranteeType,
                Name = newGuarantor.Name
            };
        }

    }

    public interface IGuarantorCreateRequestHandler
    {
        ValueTask<GuarantorCreateResponse> HandleAsync(GuarantorCreateRequest request, CancellationToken cancellationToken);
    }

    public class GuarantorCreateValidator : AbstractValidator<GuarantorCreateRequest>
    {
        public GuarantorCreateValidator()
        {
            RuleFor(x => x.GuaranteeProvider).NotEmpty();
            RuleFor(x => x.GuaranteeType).NotEmpty();
            RuleFor(x => x.Name).NotEmpty();
        }
    }
}

