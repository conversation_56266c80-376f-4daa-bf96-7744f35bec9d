﻿using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Web;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class SalaryGuaranteeContractLessStepsProvider : IContractLessStepsProvider
    {
        public GuaranteeType GuaranteeType => GuaranteeType.Salary;

        public async ValueTask<OrderStepsDto> CreateContractLessSteps(Order order, int userId)
        {
            OrderSteps nextCode = order.Status.ToOrderStep();

            var result = new OrderStepsDto(nextCode, PreGuaranteeSteps);

            // ConsumerUserId, Which is set on Order, related dynamic steps 
            if (order.PlanIsLocked)
            {
                if (order.Status == OrderStatus.CreditValidationSucceeded || order.Status == OrderStatus.ProfileFilled)
                {
                    result.Next = OrderSteps.Quote;
                }
                else if (order.Status == OrderStatus.DigitalSignatureCreated || order.Status == OrderStatus.GuaranteeFailed)
                {
                    throw new InvalidOperationException("salary guarantee contract less type of order should not be in DigitalSignatureCreated or GuaranteeFailed step");
                }
                else if (order.Status == OrderStatus.GuaranteeInProgress)
                {
                    throw new InvalidOperationException("salary guarantee contract less type of order should not be in GuaranteeInProgress step");
                }
            }

            if (order.Status == OrderStatus.GuaranteeSucceeded)
            {
                result.Next = OrderSteps.Quote;
            }


            result.Steps.AddRange(PostContractSteps);

            foreach (var step in result.Steps)
            {
                if ((int)result.Next > (int)step.Code)
                {
                    step.IsPassed = true;
                }
            }

            return result;
        }

        private List<OrderStepDto> PreGuaranteeSteps => new List<OrderStepDto>()
        {
            new ("انتخاب طرح اعتباری", OrderSteps.PlanList, false),
            new ("رتبه سنجی اعتباری", OrderSteps.CreditScore, true),
        };

        private List<OrderStepDto> PostContractSteps => new List<OrderStepDto>()
        {
            new ("پیش فاکتور", OrderSteps.Quote, true),
            new ("اقساط", OrderSteps.GetInstallments, false),
            new ("تسویه شده", OrderSteps.PaidOff, false),
            new ("لغو شده", OrderSteps.Canceled, false)
        };
    }
}
