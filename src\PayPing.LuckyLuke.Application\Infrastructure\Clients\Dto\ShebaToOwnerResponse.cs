﻿using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public class ShebaToOwnerResponse
    {
        public string ShebaNumber { get; set; }

        public BankEnum BankName { get; set; }

        public BankAccountStatusEnum AccountStatus { get; set; }

        public string DepositNumber { get; set; }

        public List<AccountOwnerInfo> AccountOwnerInfos { get; set; }
    }

    public class AccountOwnerInfo
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
    }

    public enum BankEnum
    {


        Unknown = 0,

        CentralBank,


        Melli,


        Mellat,


        Saman,


        Ayandeh,


        EghtesadNovin,


        Sepah,


        Tejarat,


        Pasargad,


        Postbank,


        Parsian,


        IranZamin,


        Keshvarzi,


        ToseeTaavon,


        ToseeSaderat,


        Dey,


        KhavarMianeh,


        Resalat,


        Sarmayeh,


        Sina,


        Shahr,


        Saderat,


        SanatVaMadan,


        <PERSON><PERSON><PERSON><PERSON>,


        <PERSON><PERSON><PERSON>,


        <PERSON><PERSON><PERSON>,


        <PERSON><PERSON>,


        <PERSON>,


        <PERSON>,


        <PERSON>,


        KarAfarin,


        Noor,


        IranVenezuela,
    }
    public enum BankAccountStatusEnum
    {
        Unknown = 0,
        Active,
        BlockWithDeposit,
        BlockWithoutDeposit,
        Idle
    }
}
