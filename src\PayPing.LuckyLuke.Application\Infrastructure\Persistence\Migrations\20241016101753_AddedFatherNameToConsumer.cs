﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedFatherNameToConsumer : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Father<PERSON><PERSON>",
                table: "ConsumerInfos",
                type: "character varying(128)",
                maxLength: 128,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON>",
                table: "ConsumerInfos");
        }
    }
}
