﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer._14_PrePaymentRequest
{
    public record PrePaymentRequest(Guid trackingCode);

    public record PrePaymentResponse(string url);

    public class PrePaymentEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.PayPrePayment,
                async (
                    PrePaymentRequest request,
                    IPrePaymentRequestHandler handler,
                    IValidator<PrePaymentRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("PrePaymentPay")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<PrePaymentResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("PrePayment")
            .WithDescription("PrePayment")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class PrePaymentRequestHandler : IPrePaymentRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IUserContext _userContext;
        private readonly IUserService _userService;
        private readonly IPaymentGrpcClient _paymentGrpcClient;
        private readonly ILogger<PrePaymentRequestHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public PrePaymentRequestHandler(ApplicationDbContext dbContext, IGuaranteeService guaranteeService, IUserContext userContext, IUserService userService, IPaymentGrpcClient paymentGrpcClient, IOptions<BNPLOptions> bnplOptions, ILogger<PrePaymentRequestHandler> logger)
        {
            _dbContext = dbContext;
            _guaranteeService = guaranteeService;
            _userContext = userContext;
            _userService = userService;
            _paymentGrpcClient = paymentGrpcClient;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<PrePaymentResponse> HandleAsync(PrePaymentRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.Where(o => o.TrackingCode == request.trackingCode)
                .Include(o => o.OrderPayments)
                .FirstOrDefaultAsync();

            await ValidateOrder(userId, request, order);

            var consumerInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);

            try
            {
                var payResp = await _paymentGrpcClient.CreatePaymentAsync(
                new PaymentGrpcRequest(
                    order.MerchantUserId,
                    (int)order.PrePaymentAmount,
                    (int)order.ConsumerOperationCostAmount,
                    (int)order.MerchantOperationCostAmount,
                    order.ClientCallbackUrl,
                    consumerInfo.FullName,
                    order.Description ?? string.Empty,
                    order.ClientRefId,
                    consumerInfo.UserName));

                if (payResp != null && !string.IsNullOrWhiteSpace(payResp.paymentCode))
                {
                    order.OrderPayments.Add(new OrderPayment()
                    {
                        Amount = decimal.Add(order.PrePaymentAmount, order.ConsumerOperationCostAmount),
                        PaymentStatus = OrderPaymentStatus.Init,
                        PaymentCode = payResp.paymentCode,
                        PaymentType = OrderPaymentType.PrePayment,
                        WalletId = order.OrderPlan.WalletId,
                        CreditId = order.OrderPlan.CreditId
                    });

                    order.LatestPrePaymentTryDate = DateTimeOffset.UtcNow;
                    order.Status = OrderStatus.PrePaymentInProgress;

                    await _dbContext.SaveChangesAsync();

                    return new PrePaymentResponse(payResp.url);
                }
                else
                {
                    _logger.LogWarning($"pre payment creation failed for order: {order.TrackingCode}");

                    order.Status = OrderStatus.PrePaymentFailed;

                    await _dbContext.SaveChangesAsync();

                    throw new PaymentException(request.trackingCode.ToString(), $"pre payment creation failed for order: {order.TrackingCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.InnerException?.Message ?? ex.Message);

                order.Status = OrderStatus.PrePaymentFailed;

                await _dbContext.SaveChangesAsync();

                throw new PaymentException(request.trackingCode.ToString(), ex.Message);

            }
        }

        private async ValueTask ValidateOrder(int userId, PrePaymentRequest request, Order order)
        {
            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            {
                throw new OrderExpiredException(request.trackingCode.ToString(), "سفارش منقضی شده است");
            }

            if (order.ConsumerUserId != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }

            var gtypes = await _guaranteeService.GetGuaranteeTypesByGuarantorId(order.OrderPlan.GuarantorId);

            bool nonExpirableAcceptable = OrderStatusProvider.GetPrePaymentNonExpirableAcceptable().Contains(order.Status);
            switch (gtypes.guaranteeType)
            {
                case GuaranteeType.None:
                case GuaranteeType.Salary:
                    if (!order.OrderPlan.ContractIsMandatory)
                    {
                        nonExpirableAcceptable = OrderStatusProvider.GetNoneGuaranteeContractLessPrePaymentNonExpirableAcceptable().Contains(order.Status);
                    }
                    break;
                case GuaranteeType.Promissory:
                case GuaranteeType.Cheque:
                default:
                    break;
            }

            var expirableAcceptableStatuses = OrderStatusProvider.GetPrePaymentExpirableAcceptable();

            if (!nonExpirableAcceptable &&
                !(expirableAcceptableStatuses.Contains(order.Status) &&
                    order.LatestPrePaymentTryDate.HasValue &&
                    order.LatestPrePaymentTryDate.Value.AddMinutes(10) < DateTimeOffset.UtcNow
                 )
            )
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش در مرحله پیش پرداخت نیست", string.Empty);
            }
        }
    }

    public interface IPrePaymentRequestHandler
    {
        ValueTask<PrePaymentResponse> HandleAsync(PrePaymentRequest request, CancellationToken cancellationToken);
    }

    public class PrePaymentValidator : AbstractValidator<PrePaymentRequest>
    {
        public PrePaymentValidator()
        {
            RuleFor(x => x.trackingCode).NotEqual(Guid.Empty).WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}
