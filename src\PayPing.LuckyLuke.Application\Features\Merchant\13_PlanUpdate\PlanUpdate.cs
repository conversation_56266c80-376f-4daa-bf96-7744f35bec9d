﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Features.Consumer._085_UploadSignature;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Services.Excel;
using System.Data;
using System.Text.RegularExpressions;

namespace PayPing.LuckyLuke.Application.Features.Merchant._12_PlanCreate
{
    public class UpdatePlanRequest
    {
        public Guid planCode { get; set; }
        public List<CreatePlanEmployeeRequest> employees { get; set; }
        public IFormFile employeesFile { get; set; }
    }

    public record UpdatePlanResponse(Guid planCode);

    public class UpdatePlanEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPut(
                MerchantRoutes.UpdatePlan,
                async ([FromForm] UpdatePlanRequest request, IUpdatePlanRequestHandler handler, IValidator<UpdatePlanRequest> validator, CancellationToken cancellationToken) =>
            {

                var validationResult = await validator.ValidateAsync(request);
                if (!validationResult.IsValid)
                {
                    throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                }

                var result = await handler.HandleAsync(request, cancellationToken);

                return Results.Ok(result);
            })
            .RequireAuthorization("write")
            .DisableAntiforgery()
            .Accepts<UploadSignatureRequest>("multipart/form-data")
            .WithName("UpdatePlan")
            .WithApiVersionSet(builder.NewApiVersionSet("Plan").Build())
            .Produces<UpdatePlanResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Update Plan")
            .WithDescription("Update Plan")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class UpdatePlanRequestHandler : IUpdatePlanRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;
        private readonly IExcelService _excelService;

        public UpdatePlanRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IExcelService excelService, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
            _excelService = excelService;
        }

        public async ValueTask<UpdatePlanResponse> HandleAsync(UpdatePlanRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var plan = await _dbContext.Plans.AsNoTracking()
                .Where(x => x.Code == request.planCode && x.MerchantUserId == userId)
                .FirstOrDefaultAsync(cancellationToken);

            if (plan == null)
            {
                throw new ArgumentException("طرح اقساطی یافت نشد");
            }

            if (plan.GuaranteeType == GuaranteeType.Salary)
            {
                List<EmployeeExcelReaderModel> excelList = new List<EmployeeExcelReaderModel>();

                if (request.employeesFile != null && request.employeesFile.Length > 0)
                {
                    ModelStateDictionary fileValidationDic = new ModelStateDictionary();
                    var validExcel = await FileHelpers.ProcessFormFile(request.employeesFile, fileValidationDic, ApplicationConstants.EmployeesEXcelValidExtensions, ApplicationConstants.EmployeesEXcelMaxSizeInBytes);

                    if (validExcel.Length == 0 || !fileValidationDic.IsValid)
                    {
                        throw new ArgumentException($"اکسل مشتریان معتبر نیست. {string.Join(", ", fileValidationDic.Values.SelectMany(v => v.Errors).Select(x => x.ErrorMessage))}");
                    }

                    (bool hasError, List<EmployeeExcelReaderModel> list) datasetModel = ExtractEmployeeInfoFromExcel(request.employeesFile);
                    if (datasetModel.hasError)
                    {
                        throw new ArgumentException($"فایل اکسل مشتریان معتبر نیست");
                    }
                    else
                    {
                        excelList = datasetModel.list;
                    }
                }


                if (request.employees != null && request.employees.Count > 0)
                {
                    foreach (var item in request.employees)
                    {
                        if (!excelList.Any(x => x.mobile == item.mobile && x.nationalCode == item.nationalCode))
                        {
                            var employeeInfoId = await InsertIfNotDuplicateOrGetEmployeeInfoAsync(plan.MerchantUserId, item.mobile, item.nationalCode, userId);
                            if (employeeInfoId != 0)
                            {
                                await InsertPlanEmployeeIfNotDuplicateAsync(plan.Id, employeeInfoId, userId);
                            }
                        }
                    }
                }

                foreach (var item in excelList)
                {
                    var employeeInfoId = await InsertIfNotDuplicateOrGetEmployeeInfoAsync(plan.MerchantUserId, item.mobile, item.nationalCode, userId);
                    if (employeeInfoId != 0)
                    {
                        await InsertPlanEmployeeIfNotDuplicateAsync(plan.Id, employeeInfoId, userId);
                    }
                }
            }

            return new UpdatePlanResponse(plan.Code);
        }

        public async ValueTask<int> InsertIfNotDuplicateOrGetEmployeeInfoAsync(int employerUserId, string mobileNumber, string nationalCode, int currentUserId)
        {
            string employerUserIdColumn = nameof(EmployeeInfo.EmployerUserId);
            string mobileColumn = nameof(EmployeeInfo.MobileNumber);
            string nationalcodeColumn = nameof(EmployeeInfo.NationalCode);

            var sql = @$"
                INSERT INTO public.""EmployeeInfos"" (""{employerUserIdColumn}"", ""{mobileColumn}"", ""{nationalcodeColumn}"", ""CreatedAt"", ""CreatedBy"")
                VALUES (@p0, @p1, @p2, @p3, @p4)
                ON CONFLICT (""{employerUserIdColumn}"", ""{mobileColumn}"", ""{nationalcodeColumn}"") DO NOTHING
                RETURNING public.""EmployeeInfos"".""Id"", public.""EmployeeInfos"".""EmployerUserId"", public.""EmployeeInfos"".""MobileNumber"", public.""EmployeeInfos"".""NationalCode"", public.""EmployeeInfos"".""CreatedAt"", public.""EmployeeInfos"".""CreatedBy"";";

            var res = await _dbContext.EmployeeInfos
                .FromSqlRaw(sql, employerUserId, mobileNumber, nationalCode, DateTimeOffset.UtcNow, currentUserId)
                .IgnoreQueryFilters()
                .ToListAsync();

            var addedId = res?.FirstOrDefault()?.Id;

            if (addedId.HasValue && addedId.Value != default)
            {
                return addedId.Value;
            }

            var existingId = await _dbContext.EmployeeInfos
                .Where(x => x.EmployerUserId == employerUserId && x.MobileNumber == mobileNumber && x.NationalCode == nationalCode)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            if (existingId == default)
            {
                throw new Exception($"couldnt find duplicate employee with info; employer {employerUserId}, mobile {mobileNumber}, nc {nationalCode}");
            }

            return existingId;
        }

        public async ValueTask InsertPlanEmployeeIfNotDuplicateAsync(int planId, int employeeId, int currentUserId)
        {
            string planIdColumn = nameof(PlanEmployee.PlanId);
            string employeeInfoIdColumn = nameof(PlanEmployee.EmployeeInfoId);

            var sql = @$"
                INSERT INTO public.""PlanEmployees"" (""{planIdColumn}"", ""{employeeInfoIdColumn}"", ""CreatedAt"", ""CreatedBy"")
                VALUES (@p0, @p1, @p2, @p3)
                ON CONFLICT (""{planIdColumn}"", ""{employeeInfoIdColumn}"") DO NOTHING;";

            var res = await _dbContext.Database
                .ExecuteSqlRawAsync(sql, planId, employeeId, DateTimeOffset.UtcNow, currentUserId);
        }

        private (bool hasError, List<EmployeeExcelReaderModel> list) ExtractEmployeeInfoFromExcel(IFormFile excel)
        {
            var result = new List<EmployeeExcelReaderModel>();
            try
            {
                using Stream es = excel.OpenReadStream();

                var dataset = _excelService.ReadExcelFileIntoDataSet(es);

                var rows = dataset.Tables[0].Rows.Cast<DataRow>().Skip(1);

                foreach (var row in rows)
                {
                    string mobile = row[0].ToString().Trim();
                    string nc = row[1].ToString().Trim();

                    if (Regex.IsMatch(mobile, "^09[0-9]{9}$") && Regex.IsMatch(nc, "^[0-9]{10}$"))
                    {
                        result.Add(new EmployeeExcelReaderModel(mobile, nc));
                    }
                }

                return (false, result);
            }
            catch
            {
                return (true, default);
            }
        }

    }
    public interface IUpdatePlanRequestHandler
    {
        ValueTask<UpdatePlanResponse> HandleAsync(UpdatePlanRequest request, CancellationToken cancellationToken);
    }

    public class UpdatePlanValidator : AbstractValidator<UpdatePlanRequest>
    {
        public UpdatePlanValidator()
        {
            RuleFor(x => x.planCode).NotEmpty().WithResourceError(() => ValidatorDictionary.plan_code_is_required);

            RuleFor(x => x.employees)
                .ForEach(item => item.SetValidator(new CreatePlanEmployeeRequestValidator()));
        }
    }

}
