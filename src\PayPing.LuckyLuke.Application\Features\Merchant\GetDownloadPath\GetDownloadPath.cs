﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant.GetDownloadPath
{
    public record GetDownloadPathRequest(string fileName, Guid? orderTrackingCode);
    public record GetDownloadPathResponse(string url);

    public class GetDownloadPathEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                MerchantRoutes.GetDownloadPath,
                async (
                    [AsParameters] GetDownloadPathRequest request,
                    IGetDownloadPathRequestHandler handler,
                    IValidator<GetDownloadPathRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetDownloadPath")
            .WithApiVersionSet(builder.NewApiVersionSet("File").Build())
            .Produces<GetDownloadPathResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Download Path")
            .WithDescription("Get Download Path")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetDownloadPathRequestHandler : IGetDownloadPathRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly BNPLOptions _bnplOptions;

        public GetDownloadPathRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IUploadGrpcClient uploadGrpcClient,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _uploadGrpcClient = uploadGrpcClient;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<GetDownloadPathResponse> HandleAsync(GetDownloadPathRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            Order order = null;
            if (request.orderTrackingCode.HasValue)
            {
                order = await _dbContext.Orders.AsNoTracking().Where(o => o.TrackingCode == request.orderTrackingCode.Value && o.MerchantUserId == userId).FirstOrDefaultAsync();
                if (order == null || !order.ConsumerUserId.HasValue)
                {
                    throw new ArgumentException("order not found");
                }
            }

            int docUserId = order != null && order.ConsumerUserId.HasValue ? order.ConsumerUserId.Value : userId;

            var presignedUrl = await _uploadGrpcClient.GetPresignedUrlAsync(request.fileName, docUserId, FileManager.Grpc.UploadTypeEnum.Misc, cancellationToken);

            return new(presignedUrl.SuccessResult);
        }
    }
    public interface IGetDownloadPathRequestHandler
    {
        ValueTask<GetDownloadPathResponse> HandleAsync(GetDownloadPathRequest request, CancellationToken cancellationToken);
    }

    public class GetOrderValidator : AbstractValidator<GetDownloadPathRequest>
    {
        public GetOrderValidator()
        {
            RuleFor(x => x.fileName).NotEmpty().WithResourceError(() => ValidatorDictionary.file_name_is_required);
        }
    }
}
