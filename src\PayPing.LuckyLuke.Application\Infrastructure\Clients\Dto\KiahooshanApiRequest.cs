﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public record KiahooshanApiIssuePromissoryRequest(
        int amount,
        string dueDate,
        string description,
        KiahooshanApiIssuePromissoryIssuerRequest issuer,
        KiahooshanApiIssuePromissoryRecipientRequest recipient,
        string paymentPlace
    );

    public record KiahooshanApiIssuePromissoryIssuerRequest(
        string nationalCode,
        string mobileNumber,
        string fullName,
        string iban,
        string address,
        string postalCode,
        bool isLegal
    );

    public record KiahooshanApiIssuePromissoryRecipientRequest(
        string nationalCode,
        string mobileNumber,
        string fullName,
        bool isLegal
    );

    public record KiahooshanApiFinalizePromissoryRequest
    {
        public string uniqueId { get; init; }
        public string signedPdf { get; init; }

        [JsonPropertyName("dsig-uniqueId")]
        public string dsigUniqueId { get; init; }
    }

    public record KiahooshanApiInitialSettlementPromissoryRequest(string uniqueId, int settlementAmount, string treasuryId);
    public record KiahooshanApiFinalizeSettlementPromissoryRequest
    {
        public string signedPdf { get; init; }

        [JsonPropertyName("settle-UniqueId")]
        public string settleUniqueId { get; set; }

        [JsonPropertyName("dsig-uniqueId")]
        public string dsigUniqueId { get; init; }
    }

    public record KiahooshanApiDeletePromissoryRequest(string uniqueId);

    public record KiahooshanApiLoginRequest(
        string username,
        string password,    
        string orgName,
        string orgNationalCode);

}
