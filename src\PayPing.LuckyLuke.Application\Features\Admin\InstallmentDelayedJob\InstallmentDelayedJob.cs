﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Features.Admin.InstallmentProcessJob
{
    [DisallowConcurrentExecution]
    public class InstallmentDelayedJob : IJob
    {
        private readonly BackgroundJobOptions _jobOptions;
        private readonly INotifyService _notifyService;
        private readonly IUserService _userService;
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<InstallmentDelayedJob> _logger;
        public InstallmentDelayedJob(INotifyService notifyService, IUserService userService, IOptions<BackgroundJobOptions> jobOptions, ApplicationDbContext dbContext, ILogger<InstallmentDelayedJob> logger)
        {
            _jobOptions = jobOptions.Value;
            _notifyService = notifyService;
            _userService = userService;
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            if (context.RefireCount > 10)
            {
                _logger.LogInformation($"At InstallmentDelayedJob Refire Count exceeded 10 times, short-circuiting job");
                return;
            }

            try
            {
                // notify delayed installment to consumer
                _logger.LogInformation($"At InstallmentDelayedJob started at {DateTimeOffset.UtcNow}");

                // since installments duedates had safe-scaped irantimezone
                // no duedate is set between 23:30 and 3:29 irantime
                // comparing dates makes no problem
                DateTimeOffset margin = DateTimeOffset.UtcNow;

                var contextResult = new List<long>();

                var stats = OrderStatusProvider.GetInstallmentDelayedJobAcceptable();

                var delayeds = await _dbContext.Installments
                    .Where(i =>
                        i.Status != InstallmentStatus.PaidOff && i.Status != InstallmentStatus.SalaryDeductionPaidOff &&
                        stats.Contains(i.Order.Status) &&
                        i.PaymentStatus != PaymentStatus.PaymentSucceeded &&
                        i.DueDate.Date < margin.Date)
                    .Select(i => new DelayedInstallmentDto()
                    {
                        InstallmentId = i.Id,
                        OrderId = i.OrderId,
                        DueDate = i.DueDate,
                        InstallmentCode = i.Code,
                        InstallmentStatus = i.Status,
                        TotalAmount = i.TotalAmount,
                        FinalAmount = i.FinalAmount,
                        HasInstallmentDelayPenalty = i.Order.OrderPlan.HasInstallmentDelayPenalty,
                        InstallmentDelayPenaltyFreeInDays = i.Order.OrderPlan.InstallmentDelayPenaltyFreeInDays,
                        InstallmentDelayPenaltyRatePerDay = i.Order.OrderPlan.InstallmentDelayPenaltyRatePerDay,
                        ConsumerUserId = i.Order.ConsumerUserId,
                        MerchantUserId = i.Order.MerchantUserId,
                        InterestRate = i.Order.OrderPlan.InterestRate,
                        ConsumerUserName = i.Order.ConsumerUserName,
                        GuarantorId = i.Order.OrderPlan.GuarantorId
                        
                    })
                    .ToListAsync();

                if (delayeds != null && delayeds.Count > 0)
                {
                    foreach (var item in delayeds)
                    {
                        await ProcessDelayedInstallmentAsync(context, margin, item);

                        contextResult.Add(item.InstallmentId);
                    }
                }
                else
                {
                    _logger.LogInformation($"At InstallmentDelayedJob no delayed installments found");
                }

                context.Result = contextResult;
            }
            catch (Exception ex)
            {
                throw new JobExecutionException(msg: "At InstallmentDelayedJob exception happened", refireImmediately: false, cause: ex);
            }

        }

        private async Task ProcessDelayedInstallmentAsync(IJobExecutionContext context, DateTimeOffset margin, DelayedInstallmentDto item)
        {
            try
            {
                var gapDays = (margin - item.DueDate).Days;

                Installment installment = null;
                decimal finalAmount = item.FinalAmount;

                if (item.HasInstallmentDelayPenalty &&
                    item.InstallmentDelayPenaltyRatePerDay > decimal.Zero &&
                    gapDays > item.InstallmentDelayPenaltyFreeInDays)
                {
                    // calculate new DelayPenaltyAmount and FinalAmount of installment based on gapdays
                    installment = await GetInstallmentWithOrderAsync(item.InstallmentId, installment, context.CancellationToken);

                    if (installment != null && installment.Status != InstallmentStatus.SalaryDeductionWaiting)
                    {
                        var penaltyPack = CalculateDelayPenalty(
                            item.InstallmentDelayPenaltyRatePerDay,
                            item.InterestRate,
                            item.InstallmentDelayPenaltyFreeInDays,
                            gapDays,
                            item.TotalAmount);

                        installment.DelayPenaltyAmount = penaltyPack.delayPenaltyAmount;
                        installment.FinalAmount = penaltyPack.finalAmount;

                        finalAmount = penaltyPack.finalAmount;
                    }
                }

                if (gapDays is 1 or 7 &&
                    item.ConsumerUserId.HasValue &&
                    !string.IsNullOrWhiteSpace(item.ConsumerUserName))
                {
                    // notify consumer
                    await _notifyService.SendInstallmentDelayedMessage(
                        item.ConsumerUserName,
                        item.MerchantUserId,
                        $"{finalAmount:N0} تومان",
                        item.InstallmentCode,
                        context.CancellationToken);

                    _logger.LogInformation(
                        "At InstallmentDelayedJob, InstallmentDelayedMessage sent for consumer: {ItemConsumerUserId}, merchant: {ItemMerchantUserId}, installment code: {ItemInstallmentCode}",
                        item.ConsumerUserId.Value,
                        item.MerchantUserId,
                        item.InstallmentCode);
                }

                if (item.InstallmentStatus == InstallmentStatus.Waiting)
                {
                    // set installment to Delayed
                    // set order to InstallmentsPaymentDelayed
                    installment = await GetInstallmentWithOrderAsync(item.InstallmentId, installment, context.CancellationToken);

                    if (installment != null)
                    {
                        installment.Status = InstallmentStatus.Delayed;
                        installment.Order.Status = OrderStatus.InstallmentsPaymentDelayed;
                    }
                }
                else if (item.InstallmentStatus == InstallmentStatus.Delayed && gapDays >= ApplicationConstants.InstallmentSalaryDeductionDays)
                {
                    var guaranteeType = await _dbContext.Guarantors.AsNoTracking().Where(x => x.Id == item.GuarantorId).Select(x => x.GuaranteeType).FirstOrDefaultAsync();
                    if (guaranteeType == GuaranteeType.Salary)
                    {
                        // set installment to SalaryDeduction
                        // set order to SalaryDeduction
                        installment = await GetInstallmentWithOrderAsync(item.InstallmentId, installment, context.CancellationToken);

                        if (installment != null)
                        {
                            installment.Status = InstallmentStatus.SalaryDeductionWaiting;
                            installment.Order.Status = OrderStatus.InstallmentsWaitingForSalaryDeduction;
                        }
                    }
                }

                if (installment != null)
                {
                    await _dbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "At InstallmentDelayedJob exception happened processing installment {InstallmentId} of order {OrderId}", item.InstallmentId, item.OrderId);
            }
        }

        private async Task<Installment> GetInstallmentWithOrderAsync(long instId, Installment installment, CancellationToken cancellationToken)
        {
            if (installment != null)
            {
                return installment;
            }

            return await _dbContext.Installments.Where(i => i.Id == instId).Include(i => i.Order).SingleOrDefaultAsync(cancellationToken);
        }

        private (decimal delayPenaltyAmount, decimal finalAmount) CalculateDelayPenalty(
            decimal rateBias,
            decimal interestRate,
            int freeDays,
            int gapDays,
            decimal total)
        {
            int applicabaleDays = gapDays - freeDays;
            decimal final = total;
            decimal penalty = decimal.Zero;

            if (applicabaleDays > 0)
            {
                var biasPercent = decimal.Divide(rateBias, 100m);
                var interestPercent = decimal.Divide(interestRate, 100m);
                decimal absRate = decimal.Divide(decimal.Add(biasPercent, decimal.Multiply(interestPercent, 12)), 365m);
                decimal absAmount = decimal.Multiply(applicabaleDays, total);

                penalty = Math.Ceiling(decimal.Multiply(absAmount, absRate));
                final = decimal.Add(total, penalty);
            }

            return (penalty, final);
        }
    }

    public class DelayedInstallmentDto
    {
        public long InstallmentId { get; set; }
        public long OrderId { get; set; }
        public InstallmentStatus InstallmentStatus { get; set; }
        public DateTimeOffset DueDate { get; set; }
        public Guid InstallmentCode { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal FinalAmount { get; set; }
        public int? ConsumerUserId { get; set; }
        public string ConsumerUserName { get; set; }

        public int MerchantUserId { get; set; }

        // order plan
        public bool HasInstallmentDelayPenalty { get; init; }
        public decimal InstallmentDelayPenaltyRatePerDay { get; init; }
        public int InstallmentDelayPenaltyFreeInDays { get; init; }

        public decimal InterestRate { get; set; }

        public int GuarantorId { get; set; }
    }
}
