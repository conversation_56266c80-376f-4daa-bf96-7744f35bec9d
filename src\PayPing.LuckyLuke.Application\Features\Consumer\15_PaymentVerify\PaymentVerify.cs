﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using StackExchange.Redis;

namespace PayPing.LuckyLuke.Application.Features.Consumer._15_PaymentVerify
{
    public record PaymentVerify(string paymentCode, long paymentRefId, decimal amount);

    public record PaymentVerifyResponse(decimal amount, string cardNumber, long paymentRefId);

    public class PaymentVerifyEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.VerifyPayment,
                async (
                    PaymentVerify request,
                    IPaymentVerifyHandler handler,
                    IValidator<PaymentVerify> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(string.Empty, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("merchantWrite")
            .RequireCors(c => c.AllowAnyOrigin())
            .WithName("PrePaymentVerify")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<PaymentVerifyResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("PrePayment Verify")
            .WithDescription("PrePayment Verify")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class PaymentVerifyHandler : IPaymentVerifyHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly IPaymentGrpcClient _paymentGrpcClient;
        private readonly IWalletService _walletService;
        private readonly INotifyService _notifyService;
        private readonly ICreditService _creditService;
        private readonly ILogger<PaymentVerifyHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public PaymentVerifyHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IPaymentGrpcClient paymentGrpcClient,
            IWalletService walletService,
            INotifyService notifyService,
            ICreditService creditService,
            IOptions<BNPLOptions> bnplOptions,
            ILogger<PaymentVerifyHandler> logger)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _paymentGrpcClient = paymentGrpcClient;
            _walletService = walletService;
            _notifyService = notifyService;
            _creditService = creditService;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<PaymentVerifyResponse> HandleAsync(PaymentVerify request, CancellationToken cancellationToken)
        {
            int merchantUserId = _userContext.CurrentUserId.Value;

            var orderPayment = await _dbContext.OrderPayments
                .Where(x => x.PaymentCode == request.paymentCode && x.Amount == request.amount)
                .Include(x => x.Order)
                .FirstOrDefaultAsync();

            if (orderPayment == null)
            {
                throw new NotFoundException(nameof(request.paymentCode), "اطلاعات ارسالی نامعتبر است");
            }

            if (orderPayment.PaymentStatus != OrderPaymentStatus.Init)
            {
                throw new CustomValidationException(orderPayment.Order.TrackingCode.ToString(), "اطلاعات ارسالی تکراریست", string.Empty);
            }

            if (orderPayment.Order.Status != OrderStatus.PrePaymentInProgress)
            {
                throw new CustomValidationException(orderPayment.Order.TrackingCode.ToString(), "اطلاعات ارسالی تکراریست", string.Empty);
            }

            if (orderPayment.Order.MerchantUserId != merchantUserId)
            {
                throw new CustomValidationException(orderPayment.Order.TrackingCode.ToString(), "توکن احراز هویت پذیرنده تایید نشده است", string.Empty);
            }

            if (orderPayment.PaymentType != OrderPaymentType.PrePayment)
            {
                throw new CustomValidationException(orderPayment.Order.TrackingCode.ToString(), "پرداخت باید از نوع پیش پرداخت باشد", string.Empty);
            }

            var verifyResult = await _paymentGrpcClient
                .VerifyPaymentAsync(new PaymentVerifyGrpcRequest(orderPayment.Order.MerchantUserId, request.paymentRefId, orderPayment.PaymentCode));

            if (verifyResult != null)
            {
                // credit (unfreeze + withdraw)
                decimal withdrawAmount = orderPayment.Order.CreditedAmount;
                
                var withdrawResult = await _walletService.WithdrawCreditAndSaveAsync(
                    orderPayment.WalletId,
                    orderPayment.Order.CreditLockId,
                    withdrawAmount,
                    cancellationToken);

                // add a suspecious credit on failure
                if (!withdrawResult.Success)
                {
                    await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                        orderPayment.CreditId,
                        orderPayment.WalletId,
                        withdrawAmount,
                        CreditTransactionType.Withdraw,
                        orderPayment.Order.CreditLockId,
                        withdrawResult.Error);
                }

                var ppRunningSumResult = await _creditService.IncreasePrePaymentSumOnCreditAndSaveAsync(
                    orderPayment.CreditId,
                    true,
                    orderPayment.Order.PrePaymentAmount,
                    orderPayment.Order.MerchantOperationCostAmount,
                    orderPayment.Order.ConsumerOperationCostAmount,
                    cancellationToken);

                if (!ppRunningSumResult.Success)
                {
                    await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                        orderPayment.CreditId,
                        orderPayment.WalletId,
                        orderPayment.Order.PrePaymentAmount,
                        CreditTransactionType.PrepaymentRS,
                        null,
                        ppRunningSumResult.Error);

                    await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                        orderPayment.CreditId,
                        orderPayment.WalletId,
                        orderPayment.Order.MerchantOperationCostAmount,
                        CreditTransactionType.MerchantWageRS,
                        null,
                        ppRunningSumResult.Error);

                    await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                        orderPayment.CreditId,
                        orderPayment.WalletId,
                        orderPayment.Order.ConsumerOperationCostAmount,
                        CreditTransactionType.ConsumerWageRS,
                        null,
                        ppRunningSumResult.Error);
                }

                orderPayment.PaymentStatus = OrderPaymentStatus.Paid;
                orderPayment.PayDate = DateTimeOffset.UtcNow;
                orderPayment.CardNumber = verifyResult.cardNumber;

                orderPayment.Order.Status = OrderStatus.PrePaymentSucceeded;

                await _dbContext.SaveChangesAsync();

                await _notifyService.SendOrderRegisterMessage(orderPayment.Order.ConsumerUserName, merchantUserId, cancellationToken);

                return new PaymentVerifyResponse(
                    verifyResult.amount,
                    verifyResult.cardNumber,
                    request.paymentRefId);
            }
            else
            {
                _logger.LogWarning($"At PaymentVerify; verify result is null. merchant user id: {merchantUserId}, paymentcode: {request.paymentCode}, paymentrefid: {request.paymentRefId}");

                orderPayment.PaymentStatus = OrderPaymentStatus.Failed;

                orderPayment.Order.Status = OrderStatus.PrePaymentVerifyFailed;

                await _dbContext.SaveChangesAsync();

                throw new PaymentException(orderPayment.Order.TrackingCode.ToString(), $"At PaymentVerify; payment verify failed for order: {orderPayment?.OrderId}");
            }
        }
    }

    public interface IPaymentVerifyHandler
    {
        ValueTask<PaymentVerifyResponse> HandleAsync(PaymentVerify request, CancellationToken cancellationToken);
    }

    public class PaymentVerifyValidator : AbstractValidator<PaymentVerify>
    {
        public PaymentVerifyValidator()
        {
            RuleFor(x => x.paymentCode).NotEmpty().WithResourceError(() => ValidatorDictionary.payment_code_is_required);
            RuleFor(x => x.paymentRefId).GreaterThan(0).WithResourceError(() => ValidatorDictionary.payment_ref_id_greater_zero);
            RuleFor(x => x.amount).GreaterThan(0).WithResourceError(() => ValidatorDictionary.amount_greater_zero);
        }
    }

}
