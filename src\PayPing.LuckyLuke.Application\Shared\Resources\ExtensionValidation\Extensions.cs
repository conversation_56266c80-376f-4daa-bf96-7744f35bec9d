﻿using FluentValidation;
using System.Linq.Expressions;

namespace PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation
{
    public static class Extensions
    {
        public static IRuleBuilderOptions<T, TProperty> WithResourceError<T, TProperty>(this IRuleBuilderOptions<T, TProperty> rule, Expression<Func<string>> expression)
        {
            var name = ((MemberExpression)expression.Body).Member.Name;
            var value = expression.Compile()();

            rule.WithErrorCode(name);
            rule.WithMessage(value);
            return rule;

        }
    }
}
