﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedPaymentGroup : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "LenderUserId",
                table: "Orders",
                newName: "MerchantUserId");

            migrationBuilder.RenameColumn(
                name: "ClientToken",
                table: "Orders",
                newName: "MerchantName");

            migrationBuilder.RenameIndex(
                name: "IX_Order_LenderUserId",
                table: "Orders",
                newName: "IX_Order_MerchantUserId");

            migrationBuilder.AddColumn<decimal>(
                name: "DiscountAmount",
                table: "OrderPayments",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<long>(
                name: "PaymentGroupId",
                table: "OrderPayments",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "DiscountAmount",
                table: "Installments",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.CreateTable(
                name: "PaymentGroups",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ConsumerUserId = table.Column<int>(type: "integer", nullable: false),
                    TotalAmount = table.Column<decimal>(type: "numeric", nullable: false),
                    PaymentCode = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    LastModifiedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PaymentGroups", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OrderPayments_PaymentGroupId",
                table: "OrderPayments",
                column: "PaymentGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_PaymentGroup_PaymentCode",
                table: "PaymentGroups",
                column: "PaymentCode");

            migrationBuilder.AddForeignKey(
                name: "FK_OrderPayments_PaymentGroups_PaymentGroupId",
                table: "OrderPayments",
                column: "PaymentGroupId",
                principalTable: "PaymentGroups",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderPayments_PaymentGroups_PaymentGroupId",
                table: "OrderPayments");

            migrationBuilder.DropTable(
                name: "PaymentGroups");

            migrationBuilder.DropIndex(
                name: "IX_OrderPayments_PaymentGroupId",
                table: "OrderPayments");

            migrationBuilder.DropColumn(
                name: "DiscountAmount",
                table: "OrderPayments");

            migrationBuilder.DropColumn(
                name: "PaymentGroupId",
                table: "OrderPayments");

            migrationBuilder.DropColumn(
                name: "DiscountAmount",
                table: "Installments");

            migrationBuilder.RenameColumn(
                name: "MerchantUserId",
                table: "Orders",
                newName: "LenderUserId");

            migrationBuilder.RenameColumn(
                name: "MerchantName",
                table: "Orders",
                newName: "ClientToken");

            migrationBuilder.RenameIndex(
                name: "IX_Order_MerchantUserId",
                table: "Orders",
                newName: "IX_Order_LenderUserId");
        }
    }
}
