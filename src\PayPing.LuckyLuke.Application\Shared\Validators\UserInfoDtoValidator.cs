﻿using FluentValidation;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;

namespace PayPing.LuckyLuke.Application.Shared.Validators
{
    public class UserInfoDtoValidator : AbstractValidator<UserInfoDto>
    {
        public UserInfoDtoValidator()
        {
            RuleFor(c => c.UserName).NotEmpty();
            RuleFor(c => c.FirstName).NotEmpty();
            RuleFor(c => c.LastName).NotEmpty();
            RuleFor(c => c.FatherName).NotEmpty();
            RuleFor(c => c.FullName).NotEmpty();
            RuleFor(c => c.NationalCode).NotEmpty();
            RuleFor(c => c.BirthDate).NotEmpty();
            RuleFor(c => c.PostalCode).NotEmpty();
            RuleFor(c => c.Address).NotEmpty();
            RuleFor(c => c.FirstNameEnglish).NotEmpty();
            RuleFor(c => c.LastNameEnglish).NotEmpty();
            RuleFor(c => c.NationalIdSeries).NotEmpty();
            RuleFor(c => c.IBan).NotEmpty();
            RuleFor(c => c.Email).NotEmpty();
            RuleFor(c => c.ProvinceEnglish).NotEmpty();
            RuleFor(c => c.CityEnglish).NotEmpty();
        }
    }
}
