﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using RedLockNet;
using Wallet;
using CreateWalletResponse = PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto.CreateWalletResponse;

namespace PayPing.LuckyLuke.Application.Shared.Services.Credit;

public class WalletService(
    ApplicationDbContext dbContext,
    IWalletGrpcClient walletGrpcClient,
    IDistributedLockFactory distributedLock,
    ILogger<WalletService> logger) : IWalletService
{
    private readonly TimeSpan _expirationDuration = TimeSpan.FromSeconds(30);
    private readonly TimeSpan _waitDuration = TimeSpan.FromSeconds(10);
    private readonly TimeSpan _retryInterval = TimeSpan.FromSeconds(1);

    // // Retry policy for external service calls
    // private readonly AsyncRetryPolicy _retryPolicy = Policy
    //     .Handle<Exception>(ex => ex is not ArgumentException) // Don't retry on validation errors
    //     .WaitAndRetryAsync(
    //         3, // Retry 3 times
    //         retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // Exponential backoff
    //         (exception, timeSpan, retryCount, _) =>
    //         {
    //             // Log the retry
    //             logger.LogWarning(exception,
    //                 "Retry {RetryCount} after {RetryTimeSpan}s delay due to: {ExceptionMessage}",
    //                 retryCount, timeSpan.TotalSeconds, exception.Message);
    //         });

    /// <summary>
    /// Helper method to execute operations with distributed lock
    /// </summary>
    private async Task<TResult> ExecuteWithLockAsync<TResult>(
        Guid walletId,
        Func<Task<TResult>> operation,
        string operationName,
        CancellationToken cancellationToken)
    {
        // Validate input
        ValidateWalletId(walletId);

        // Acquire distributed lock
        await using var redLock = await distributedLock.CreateLockAsync(
            GetWalletLockKey(walletId),
            _expirationDuration,
            _waitDuration,
            _retryInterval,
            cancellationToken);

        // Check if lock was acquired
        if (redLock.IsAcquired)
        {
            return await operation();
        }

        logger.LogWarning("Distributed lock not acquired for wallet: {WalletId} on {OperationName}", walletId, operationName);

        throw new WalletLockAcquisitionException(walletId, operationName);
    }

    /// <summary>
    /// Validates that the amount is not negative
    /// </summary>
    private static void ValidateAmount(decimal amount)
    {
        Guard.Against.Negative(amount, nameof(amount), "Amount cannot be negative");
    }

    /// <summary>
    /// Validates that the wallet ID is not empty
    /// </summary>
    private static void ValidateWalletId(Guid walletId)
    {
        Guard.Against.Default(walletId, nameof(walletId), "Wallet ID cannot be empty");
    }

    public async ValueTask<int> AddFailedCreditTransactionOutboxAndSaveAsync(int creditId, Guid walletId, decimal amount, CreditTransactionType transactionType, string lockId, string message)
    {
        dbContext.CreditTransactionOutboxes.Add(new CreditTransactionOutbox()
        {
            Amount = amount,
            CreditId = creditId,
            TransactionType = transactionType,
            LockId = lockId,
            Message = message,
            WalletId = walletId,
            HasFailed = true
        });

        return await dbContext.SaveChangesAsync();
    }

    public async ValueTask<int> AddCreditTransactionOutboxAndSaveAsync(int creditId, Guid walletId, decimal amount, CreditTransactionType transactionType, string lockId, string message)
    {
        dbContext.CreditTransactionOutboxes.Add(new CreditTransactionOutbox()
        {
            Amount = amount,
            CreditId = creditId,
            TransactionType = transactionType,
            LockId = lockId,
            Message = message,
            WalletId = walletId,
            HasFailed = false
        });

        return await dbContext.SaveChangesAsync();
    }

    public async Task<CreateWalletResponse> CreateWallet(int userId, Guid creditCode, CancellationToken cancellationToken = default)
    {
        var result = await walletGrpcClient.CreateWallet(userId, creditCode, cancellationToken);
        if (result.Succeeded)
        {
            return new CreateWalletResponse(true, null, Guid.Parse(result.SuccessResult.WalletId));
        }

        logger.LogError("could not create wallet for user: {UserId}, creditCode: {CreditCode}. Error: {Error}", userId, creditCode, result.Message);
        return new CreateWalletResponse(false, result.Message, Guid.Empty);
    }

    public async ValueTask<GetWalletCreditInfoResponse> GetWalletCreditInfoAsync(Guid walletId, CancellationToken cancellationToken)
    {
        var credit = await dbContext.Credits.AsNoTracking()
            .Where(c => c.WalletId == walletId && c.IsActive)
            .FirstOrDefaultAsync(cancellationToken);

        if (credit == null)
        {
            return new(false, "wallet not found", 0, 0, 0, 0, 0, 0, 0, 0, 0);
        }

        var wallet = await walletGrpcClient.GetWalletDetailsById(credit.WalletId, cancellationToken);
        if (!wallet.Succeeded)
        {
            return new GetWalletCreditInfoResponse(false, wallet.Message, 0, 0, 0, 0, 0, 0, 0, 0, 0);
        }
        
        var totalDeposit = await walletGrpcClient.GetWalletTransactionTotalAmount(credit.WalletId,
            TransactionType.TransactionDeposit,
            cancellationToken);
        
        return new(true,
            null,
            totalDeposit.Succeeded ? totalDeposit.SuccessResult.TotalAmount.ToDecimal() : 0,
            wallet.SuccessResult.AccessBalance.ToDecimal(),
            wallet.SuccessResult.BlockBalance.ToDecimal(),
            credit.TotalRawPrePaymentAmount, credit.TotalRawPrePaymentCount,
            credit.TotalMerchantWageAmount, credit.TotalConsumerWageAmount,
            credit.TotalInstallmentPaymentAmount, credit.TotalInstallmentPaymentCount);
    }

    public async Task<IncreaseWalletCreditResponse> ResetWalletCreditAmountAndSaveAsync(Guid walletId, decimal amount, int walletOwnerUserId, CancellationToken cancellationToken)
    {
        // Validate inputs
        ValidateWalletId(walletId);
        ValidateAmount(amount);

        try
        {
            return await ExecuteWithLockAsync(
                walletId,
                async () =>
                {
                    var credit = await dbContext.Credits
                        .Where(c => c.WalletId == walletId && c.IsActive)
                        .FirstOrDefaultAsync(cancellationToken);

                    if (credit == null)
                    {
                        logger.LogWarning("Could not find active credit for wallet: {WalletId} on ResetWalletCreditAmount", walletId);
                        return new IncreaseWalletCreditResponse(false, "Could not find active credit on ResetWalletCreditAmount", 0, 0);
                    }

                    var walletBalanceResult = await walletGrpcClient.GetWalletBalanceById(walletId, cancellationToken);

                    if (!walletBalanceResult.Succeeded)
                    {
                        logger.LogWarning("Could not get wallet balance for wallet: {WalletId} on ResetWalletCreditAmount", walletId);
                        return new IncreaseWalletCreditResponse(walletBalanceResult.Succeeded, walletBalanceResult.Message, 0, 0);
                    }

                    var currentBalance = walletBalanceResult.SuccessResult.Balance.ToDecimal();

                    var resetTransaction = await HandleResetWalletTransaction(walletId, currentBalance, amount, credit, cancellationToken);
                    if (!resetTransaction)
                    {
                        logger.LogError("Could not handle wallet transaction for wallet: {WalletId}", walletId);
                        return new IncreaseWalletCreditResponse(false, "Could not handle wallet transaction", 0, 0);
                    }

                    await dbContext.SaveChangesAsync(cancellationToken);
                    return new IncreaseWalletCreditResponse(true, null, amount, amount);
                },
                "ResetWalletCreditAmount",
                cancellationToken);
        }
        catch (WalletLockAcquisitionException ex)
        {
            return new IncreaseWalletCreditResponse(false, $"Could not acquire lock on {ex.OperationName}", 0, 0);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in ResetWalletCreditAmountAndSaveAsync for wallet: {WalletId}", walletId);
            return new IncreaseWalletCreditResponse(false, $"Unexpected error: {ex.Message}", 0, 0);
        }
    }

    public async Task<IncreaseWalletCreditResponse> IncreaseWalletCreditAndSaveAsync(Guid walletId, decimal amount, CancellationToken cancellationToken)
    {
        // Validate inputs
        ValidateWalletId(walletId);
        ValidateAmount(amount);

        try
        {
            return await ExecuteWithLockAsync(
                walletId,
                async () =>
                {
                    var credit = await dbContext.Credits
                        .Where(c => c.WalletId == walletId && c.IsActive)
                        .FirstOrDefaultAsync(cancellationToken);

                    if (credit == null)
                    {
                        logger.LogWarning("Could not find active credit for wallet: {WalletId} on increase credit", walletId);
                        return new IncreaseWalletCreditResponse(false, "Could not find active credit on increase credit", 0, 0);
                    }

                    var walletResult = await walletGrpcClient.GetWalletDetailsById(walletId, cancellationToken);

                    if (!walletResult.Succeeded)
                    {
                        logger.LogWarning("Could not get wallet for wallet: {WalletId} on increase credit", walletId);
                        return new IncreaseWalletCreditResponse(walletResult.Succeeded, "Could not get wallet on increase credit", 0, 0);
                    }

                    var creditTransaction = new CreditTransaction
                    {
                        Amount = amount,
                        CorrelationId = Guid.NewGuid(),
                        TransactionType = CreditTransactionType.Deposit,
                        CreditId = credit.Id,
                        CreditCode = credit.Code,
                        WalletId = walletId,
                        Description = $"تراکنش افزایش موجودی اعتباری مبلغ: {amount} تومان"
                    };
                    dbContext.CreditTransactions.Add(creditTransaction);

                    var depositResult = await walletGrpcClient.Deposit(
                            walletId,
                            creditTransaction.CorrelationId,
                            amount,
                            creditTransaction.Description,
                            cancellationToken);

                    if (!depositResult.Succeeded || !depositResult.SuccessResult.Success)
                    {
                        logger.LogError("Could not deposit amount for wallet: {WalletId}. Error: {error}", walletId, depositResult.Message);
                        return new IncreaseWalletCreditResponse(false, depositResult.Message, 0, 0);
                    }

                    await dbContext.SaveChangesAsync(cancellationToken);

                    return new IncreaseWalletCreditResponse(true,
                        null,
                        depositResult.SuccessResult.AccessBalance.ToDecimal(),
                        walletResult.SuccessResult.TotalBalance.ToDecimal() + amount);
                },
                "IncreaseWalletCredit",
                cancellationToken);
        }
        catch (WalletLockAcquisitionException ex)
        {
            return new IncreaseWalletCreditResponse(false, $"Could not acquire lock on {ex.OperationName}", 0, 0);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in IncreaseWalletCreditAndSaveAsync for wallet: {WalletId}", walletId);
            return new IncreaseWalletCreditResponse(false, $"Unexpected error: {ex.Message}", 0, 0);
        }
    }

    public async Task<LockWalletCreditResponse> LockCreditAndSaveAsync(Guid walletId, decimal amount, CancellationToken cancellationToken)
    {
        // Validate inputs
        ValidateWalletId(walletId);
        ValidateAmount(amount);

        try
        {
            return await ExecuteWithLockAsync(
                walletId,
                async () =>
                {
                    var credit = await dbContext.Credits
                        .Where(c => c.WalletId == walletId && c.IsActive)
                        .FirstOrDefaultAsync(cancellationToken);

                    if (credit == null)
                    {
                        logger.LogWarning("Could not find active credit for wallet: {WalletId} on lock credit", walletId);
                        return new LockWalletCreditResponse(false, false, "Could not find active credit on lock credit", null, 0, 0);
                    }

                    var walletResult = await walletGrpcClient.GetWalletBalanceById(walletId, cancellationToken);

                    if (!walletResult.Succeeded)
                    {
                        logger.LogWarning("Could not get wallet balance for wallet: {WalletId} on lock credit", walletId);
                        return new LockWalletCreditResponse(false, false, "Could not get wallet balance on lock credit", null, 0, 0);
                    }

                    if (walletResult.SuccessResult.Balance.ToDecimal() < amount)
                    {
                        logger.LogWarning("Wallet does not have enough balance for wallet: {WalletId}, current balance: {WalletBalance}, requested amount: {Amount} on lock credit",
                            walletId, walletResult.SuccessResult.Balance, amount);
                        return new LockWalletCreditResponse(false, true, "Wallet does not have enough balance on lock credit", null, 0, 0);
                    }

                    var trx = new CreditTransaction
                    {
                        Amount = amount,
                        CorrelationId = Guid.NewGuid(),
                        TransactionType = CreditTransactionType.Freeze,
                        CreditId = credit.Id,
                        CreditCode = credit.Code,
                        WalletId = walletId,
                        Description = $"تراکنش بلاک موجودی اعتباری مبلغ: {amount} تومان"
                    };

                    dbContext.CreditTransactions.Add(trx);

                    var freezeResult = await walletGrpcClient.Freeze(walletId, trx.CorrelationId, amount, cancellationToken);

                    if (!freezeResult.Succeeded || !freezeResult.SuccessResult.Success)
                    {
                        logger.LogError("Could not lock amount for wallet: {WalletId}. Error: {error}", walletId, freezeResult.Message);
                        return new LockWalletCreditResponse(false, false, freezeResult.Message, null, 0, 0);
                    }

                    await dbContext.SaveChangesAsync(cancellationToken);

                    return new LockWalletCreditResponse(true,
                        false,
                        null,
                        trx.CorrelationId.ToString(),
                        freezeResult.SuccessResult.AccessBalance.ToDecimal(),
                        freezeResult.SuccessResult.BlockBalance.ToDecimal());
                },
                "LockWalletCredit",
                cancellationToken);
        }
        catch (WalletLockAcquisitionException ex)
        {
            return new LockWalletCreditResponse(false, false, $"Could not acquire lock on {ex.OperationName}", null, 0, 0);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in LockCreditAndSaveAsync for wallet: {WalletId}", walletId);
            return new LockWalletCreditResponse(false, false, $"Unexpected error: {ex.Message}", null, 0, 0);
        }
    }

    public async Task<PartialWithdrawWalletCreditResponse> WithdrawCreditAndSaveAsync(Guid walletId, string lockId, decimal amount, CancellationToken cancellationToken)
    {
        // Validate inputs
        Guard.Against.NullOrWhiteSpace(lockId, nameof(lockId), "Lock ID cannot be empty");
        ValidateWalletId(walletId);
        ValidateAmount(amount);

        try
        {
            return await ExecuteWithLockAsync(
                walletId,
                async () =>
                {
                    var credit = await dbContext.Credits
                        .Where(c => c.WalletId == walletId && c.IsActive)
                        .FirstOrDefaultAsync(cancellationToken);

                    if (credit == null)
                    {
                        logger.LogWarning("Could not find active credit for wallet: {WalletId} on WithdrawCreditAndSaveAsync", walletId);
                        return new PartialWithdrawWalletCreditResponse(false, "Could not find active credit on WithdrawCreditAndSaveAsync", null, 0);
                    }

                    var unfreezeTransaction = new CreditTransaction()
                    {
                        Amount = amount,
                        CorrelationId = Guid.Parse(lockId),
                        TransactionType = CreditTransactionType.UnFreeze,
                        CreditId = credit.Id,
                        CreditCode = credit.Code,
                        WalletId = walletId,
                        Description = $"تراکنش آنبلاک موجودی اعتباری مبلغ: {amount} تومان"
                    };
                    dbContext.CreditTransactions.Add(unfreezeTransaction);

                    var withdrawTransaction = new CreditTransaction()
                    {
                        Amount = amount,
                        CorrelationId = Guid.Parse(lockId),
                        TransactionType = CreditTransactionType.Withdraw,
                        CreditId = credit.Id,
                        CreditCode = credit.Code,
                        WalletId = walletId,
                        Description = $"تراکنش برداشت موجودی اعتباری مبلغ: {amount} تومان"
                    };
                    dbContext.CreditTransactions.Add(withdrawTransaction);

                    var walletTransactionResult = await walletGrpcClient.UnfreezeAndWithdraw(
                            walletId,
                            Guid.Parse(lockId),
                            amount,
                            cancellationToken);

                    if (!walletTransactionResult.Succeeded || !walletTransactionResult.SuccessResult.Success)
                    {
                        logger.LogError("Could not unfreeze and withdraw amount for wallet: {WalletId}. Error: {error}", walletId, walletTransactionResult.Message);
                        return new PartialWithdrawWalletCreditResponse(false, walletTransactionResult.Message, null, 0);
                    }

                    await dbContext.SaveChangesAsync(cancellationToken);

                    return new PartialWithdrawWalletCreditResponse(true,
                        null,
                        withdrawTransaction.Id.ToString(),
                        walletTransactionResult.SuccessResult.BlockBalance.ToDecimal());
                },
                "WithdrawCredit",
                cancellationToken);
        }
        catch (WalletLockAcquisitionException ex)
        {
            return new PartialWithdrawWalletCreditResponse(false, $"Could not acquire lock on {ex.OperationName}", null, 0);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in WithdrawCreditAndSaveAsync for wallet: {WalletId}", walletId);
            return new PartialWithdrawWalletCreditResponse(false, $"Unexpected error: {ex.Message}", null, 0);
        }
    }

    public async Task<UnBlockWalletCreditResponse> UnLockCreditAndSaveAsync(Guid walletId, string lockId, decimal amount, CancellationToken cancellationToken)
    {
        // Validate inputs
        Guard.Against.NullOrWhiteSpace(lockId, nameof(lockId), "Lock ID cannot be empty");
        ValidateWalletId(walletId);
        ValidateAmount(amount);

        try
        {
            return await ExecuteWithLockAsync(
                walletId,
                async () =>
                {
                    var credit = await dbContext.Credits
                        .Where(c => c.WalletId == walletId && c.IsActive)
                        .FirstOrDefaultAsync(cancellationToken);

                    if (credit == null)
                    {
                        logger.LogWarning("Could not find active credit for wallet: {WalletId} on UnLockCreditAndSaveAsync", walletId);
                        return new UnBlockWalletCreditResponse(false, "Could not find active credit on UnLockCreditAndSaveAsync", null, 0, 0);
                    }
                    
                    var unfreeze = new CreditTransaction()
                    {
                        Amount = amount,
                        CorrelationId = Guid.Parse(lockId),
                        TransactionType = CreditTransactionType.UnFreeze,
                        CreditId = credit.Id,
                        CreditCode = credit.Code,
                        WalletId = walletId,
                        Description = $"تراکنش آنبلاک موجودی اعتباری مبلغ: {amount} تومان"
                    };
                    dbContext.CreditTransactions.Add(unfreeze);

                    var unfreezeResult = await walletGrpcClient.Unfreeze(walletId, Guid.Parse(lockId), cancellationToken);

                    if (!unfreezeResult.Succeeded || !unfreezeResult.SuccessResult.Success)
                    {
                        logger.LogError("Could not unlock amount for wallet: {WalletId}. Error: {error}", walletId, unfreezeResult.Message);
                        return new UnBlockWalletCreditResponse(false, unfreezeResult.Message, null, 0, 0);
                    }

                    await dbContext.SaveChangesAsync(cancellationToken);

                    return new UnBlockWalletCreditResponse(true,
                        null,
                        unfreeze.CorrelationId.ToString(),
                        unfreezeResult.SuccessResult.AccessBalance.ToDecimal(),
                        unfreezeResult.SuccessResult.BlockBalance.ToDecimal());
                },
                "UnlockWalletCredit",
                cancellationToken);
        }
        catch (WalletLockAcquisitionException ex)
        {
            return new UnBlockWalletCreditResponse(false, $"Could not acquire lock on {ex.OperationName}", null, 0, 0);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in UnLockCreditAndSaveAsync for wallet: {WalletId}", walletId);
            return new UnBlockWalletCreditResponse(false, $"Unexpected error: {ex.Message}", null, 0, 0);
        }
    }

    private async Task<bool> HandleResetWalletTransaction(Guid walletId,
        decimal currentBalance,
        decimal amount,
        BNPL.Domain.Models.Credit credit,
        CancellationToken cancellationToken = default)
    {
        // Validate inputs
        ValidateWalletId(walletId);
        ValidateAmount(amount);

        // If current balance is equal to amount, we don't need to do anything
        if (currentBalance == amount)
        {
            return true;
        }

        try
        {
            // If current balance is greater than amount, we need to withdraw
            if (currentBalance > amount)
            {
                var withdrawAmount = currentBalance - amount;
                var withdraw = new CreditTransaction
                {
                    Amount = withdrawAmount,
                    CorrelationId = Guid.NewGuid(),
                    TransactionType = CreditTransactionType.Withdraw,
                    CreditId = credit.Id,
                    CreditCode = credit.Code,
                    WalletId = walletId,
                    Description = $"تراکنش برداشت موجودی اعتباری جهت تصحیح موجودی در فرایند فعالسازی فروش اقساطی پذیرنده مبلغ: {withdrawAmount} تومان"
                };

                var withdrawResult = await walletGrpcClient.Withdraw(
                        walletId,
                        withdraw.CorrelationId,
                        withdrawAmount,
                        withdraw.Description,
                        cancellationToken);

                if (!withdrawResult.Succeeded || !withdrawResult.SuccessResult.Success)
                {
                    logger.LogError("Could not withdraw amount for wallet: {WalletId}. Error: {error}", walletId, withdrawResult.Message);
                    return false;
                }

                dbContext.CreditTransactions.Add(withdraw);
                return true;
            }

            // If current balance is less than amount, we need to deposit
            var depositAmount = amount - currentBalance;
            var deposit = new CreditTransaction
            {
                Amount = depositAmount,
                CorrelationId = Guid.NewGuid(),
                TransactionType = CreditTransactionType.Deposit,
                CreditId = credit.Id,
                CreditCode = credit.Code,
                WalletId = walletId,
                Description = $"تراکنش افزایش موجودی اعتباری جهت تنظیم موجودی در فرایند فعالسازی فروش اقساطی پذیرنده مبلغ: {depositAmount} تومان"
            };

            var depositResult = await walletGrpcClient.Deposit(
                    walletId,
                    deposit.CorrelationId,
                    depositAmount,
                    deposit.Description,
                    cancellationToken);

            if (!depositResult.Succeeded || !depositResult.SuccessResult.Success)
            {
                logger.LogError("Could not deposit amount for wallet: {WalletId}. Error: {error}", walletId, depositResult.Message);
                return false;
            }

            dbContext.CreditTransactions.Add(deposit);
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in HandleResetWalletTransaction for wallet: {WalletId}", walletId);
            return false;
        }
    }

    private static string GetWalletLockKey(Guid walletId) => $"{ApplicationConstants.WalletDistLockKeyPrefix}-{walletId}";
}