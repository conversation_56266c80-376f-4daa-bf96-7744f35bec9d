﻿using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Features.Merchant.OrderRefundInfo;
using PayPing.LuckyLuke.Application.Shared.Services.User;
using Polly;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public interface IMerchantCanceler
    {
        ValueTask CancelAsync(Order order, string reason, string description, CancellationToken cancellationToken);
        ValueTask PostCancelAsync(Order order, CancellationToken cancellationToken);
    }
}
