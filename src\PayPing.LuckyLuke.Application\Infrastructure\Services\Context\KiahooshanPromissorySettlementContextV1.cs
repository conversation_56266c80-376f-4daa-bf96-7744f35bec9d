﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Services.Context
{
    public struct KiahooshanPromissorySettlementContextV1
    {
        public long OrderId { get; set; }
        public Guid OrderTrackingCode { get; set; }
        public long OrderGuaranteeId { get; set; }
        public KiahooshanPromissorySettlementStatusV1 Status { get; set; }
        public int ConsumerUserId { get; set; }
        public int RecipientUserId { get; set; }
        public int Amount { get; set; }
        
        public string PromissoryUniqueId { get; set; }


        public string SettlementSignedDocumentFileId { get; set; }
        public string SettlementSignedDocumentFileContentType { get; set; }

        public string PromissoryTreasuryId { get; set; }

        public string InitialSettleUniqueId { get; set; }
        public string FinalizedSettleUniqueId { get; set; }
    }

    public enum KiahooshanPromissorySettlementStatusV1
    {
        Issue = 0,
        Finalize = 50,
        Done = 100,
    }
}
