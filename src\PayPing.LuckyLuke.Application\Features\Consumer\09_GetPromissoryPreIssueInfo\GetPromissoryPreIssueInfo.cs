﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Builder;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Services.Guarantee;

namespace PayPing.LuckyLuke.Application.Features.Consumer._09_GetPromissoryPreIssueInfo
{
    public record GetPromissoryPreIssueInfoRequest(Guid trackingCode);

    public record GetPromissoryPreIssueInfoResponse(
        Guid trackingCode,
        decimal amount,
        DateTimeOffset dueDate, 
        string consumerFullName,
        string consumerNationalCode,
        string consumerMobile,
        string consumerAddress,
        string consumerPostalCode,
        string consumerIBan,
        string merchantFullName,
        string merchantNationalCode,
        string merchantMobile,
        string merchantAddress,
        string merchantPostalCode,
        bool hasPromissoryPayment,
        int promissoryAmount
        );

    public class GetPromissoryPreIssueInfoEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.GetPromissoryPreIssueInfo,
                async (
                    [AsParameters] GetPromissoryPreIssueInfoRequest request,
                    IGetPromissoryPreIssueInfoRequestHandler handler,
                    IValidator<GetPromissoryPreIssueInfoRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetPromissoryPreIssueInfo")
            .WithApiVersionSet(builder.NewApiVersionSet("Promissory").Build())
            .Produces<GetPromissoryPreIssueInfoResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Promissory Pre Issue Info")
            .WithDescription("Get Promissory Pre Issue Info")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetPromissoryPreIssueInfoRequestHandler : IGetPromissoryPreIssueInfoRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly IUserService _userService;
        private readonly IGuaranteeService _guaranteeService;
        private readonly BNPLOptions _bnplOptions;

        public GetPromissoryPreIssueInfoRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IUserService userService,
            IGuaranteeService guaranteeService,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _userService = userService;
            _guaranteeService = guaranteeService;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<GetPromissoryPreIssueInfoResponse> HandleAsync(GetPromissoryPreIssueInfoRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.AsNoTracking()
                .Where(o => o.TrackingCode == request.trackingCode)
                .Include(o => o.Installments)
                .FirstOrDefaultAsync();

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            {
                throw new OrderExpiredException(request.trackingCode.ToString(), "سفارش منقضی شده است");
            }

            if (order.ConsumerUserId != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }

            if (order.Status != OrderStatus.ProfileFilled && order.Status != OrderStatus.DigitalSignatureCreated && order.Status != OrderStatus.GuaranteeFailed)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "اطلاعات پروفایل تکمیل نشده یا سفته در جریان است", string.Empty);
            }

            var ci = await _dbContext.ConsumerInfos.AsNoTracking()
                .Where(o => o.ConsumerUserId == userId)
                .FirstOrDefaultAsync();

            if (ci == null)
            {
                throw new NotFoundException(userId.ToString(), "consumerinfo");
            }

            var merchantInfo = await _userService.GetMerchantExtraInfoAsync(order.MerchantUserId, cancellationToken);
            var consumerInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);

            IPromissoryGuaranteeProvider provider = await _guaranteeService.GetPromissoryGuaranteeProviderByGuarantorId(order.OrderPlan.GuarantorId);

            var guaranteePaymentModel = provider.GetPromissoryPayment(order.GuaranteeAmount);

            return new GetPromissoryPreIssueInfoResponse(
                order.TrackingCode,
                order.GuaranteeAmount,
                order.DueDate.Value,
                consumerInfo.FullName,
                consumerInfo.NationalCode,
                consumerInfo.UserName,
                consumerInfo.Address,
                consumerInfo.PostalCode,
                consumerInfo.IBan,
                merchantInfo.DisplayName,
                merchantInfo.DisplayNationalCode,
                merchantInfo.PhoneNumber,
                merchantInfo.Address,
                merchantInfo.PostalCode,
                guaranteePaymentModel.hasPayment,
                guaranteePaymentModel.amountToman);
        }
    }
    public interface IGetPromissoryPreIssueInfoRequestHandler
    {
        ValueTask<GetPromissoryPreIssueInfoResponse> HandleAsync(GetPromissoryPreIssueInfoRequest request, CancellationToken cancellationToken);
    }

    public class GetPromissoryPreIssueInfoValidator : AbstractValidator<GetPromissoryPreIssueInfoRequest>
    {
        public GetPromissoryPreIssueInfoValidator()
        {
            RuleFor(x => x.trackingCode).NotEqual(Guid.Empty).WithResourceError(() => ValidatorDictionary.tracking_code_is_required); 
        }
    }
}
