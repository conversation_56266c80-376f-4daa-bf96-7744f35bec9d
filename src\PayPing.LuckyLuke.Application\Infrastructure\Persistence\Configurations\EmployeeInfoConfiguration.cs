﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Models;
using System.Reflection.Emit;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class EmployeeInfoConfiguration : IEntityTypeConfiguration<EmployeeInfo>
{
    public void Configure(EntityTypeBuilder<EmployeeInfo> builder)
    {
        builder.HasIndex(x => x.EmployerUserId).HasDatabaseName("IX_EmployeeInfo_EmployerUserId");

        builder.Property(nameof(EmployeeInfo.MobileNumber)).HasMaxLength(11);
        builder.HasIndex(x => x.MobileNumber).HasDatabaseName("IX_EmployeeInfo_MobileNumber");

        builder.Property(nameof(EmployeeInfo.NationalCode)).HasMaxLength(32);

        builder
            .HasIndex(i => new { i.EmployerUserId, i.MobileNumber, i.NationalCode })
            .IsUnique();

        builder
            .HasMany(e => e.PlanEmployees)
            .WithOne(e => e.EmployeeInfo)
            .HasForeignKey(e => e.EmployeeInfoId)
            .IsRequired();
    }
}
