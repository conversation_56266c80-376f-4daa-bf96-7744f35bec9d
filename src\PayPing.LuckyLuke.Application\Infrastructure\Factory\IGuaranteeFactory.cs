﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public interface IGuaranteeFactory
    {
        GuaranteeProvider GuaranteeProvider { get; }

        IPromissoryGuaranteeProvider CreatePromissory();

        IChequeGuaranteeProvider CreateCheque();

        INoneGuaranteeProvider CreateNoneGuarantor();
        ISalaryGuaranteeProvider CreateSalaryGuarantor();
    }
}
