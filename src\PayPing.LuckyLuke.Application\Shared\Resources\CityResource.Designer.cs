﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PayPing.LuckyLuke.Application.Shared.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class CityResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal CityResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PayPing.LuckyLuke.Application.Shared.Resources.CityResource", typeof(CityResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abbar.
        /// </summary>
        internal static string آب_بر {
            get {
                return ResourceManager.GetString("آب بر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abpakhsh.
        /// </summary>
        internal static string آب_پخش {
            get {
                return ResourceManager.GetString("آب پخش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abadan.
        /// </summary>
        internal static string آبادان {
            get {
                return ResourceManager.GetString("آبادان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abadeh.
        /// </summary>
        internal static string آباده {
            get {
                return ResourceManager.GetString("آباده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abadehtashk.
        /// </summary>
        internal static string آباده_طشک {
            get {
                return ResourceManager.GetString("آباده طشک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abdan.
        /// </summary>
        internal static string آبدان {
            get {
                return ResourceManager.GetString("آبدان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abdanan.
        /// </summary>
        internal static string آبدانان {
            get {
                return ResourceManager.GetString("آبدانان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abarkooh.
        /// </summary>
        internal static string ابرکوه {
            get {
                return ResourceManager.GetString("ابرکوه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abrisham.
        /// </summary>
        internal static string ابریشم {
            get {
                return ResourceManager.GetString("ابریشم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abesard.
        /// </summary>
        internal static string آبسرد {
            get {
                return ResourceManager.GetString("آبسرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AbeshAhmad.
        /// </summary>
        internal static string آبش_احمد {
            get {
                return ResourceManager.GetString("آبش احمد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AbAli.
        /// </summary>
        internal static string آبعلی {
            get {
                return ResourceManager.GetString("آبعلی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abgarm.
        /// </summary>
        internal static string آبگرم {
            get {
                return ResourceManager.GetString("آبگرم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abhar.
        /// </summary>
        internal static string ابهر {
            get {
                return ResourceManager.GetString("ابهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AboozeidAbad.
        /// </summary>
        internal static string ابوزیدآباد {
            get {
                return ResourceManager.GetString("ابوزیدآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aboomoosa.
        /// </summary>
        internal static string ابوموسی {
            get {
                return ResourceManager.GetString("ابوموسی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AbiBigloo.
        /// </summary>
        internal static string آبی_بیگلو {
            get {
                return ResourceManager.GetString("آبی بیگلو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abeyek.
        /// </summary>
        internal static string آبیک {
            get {
                return ResourceManager.GetString("آبیک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AhmadAbad.
        /// </summary>
        internal static string احمدآباد {
            get {
                return ResourceManager.GetString("احمدآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ahmadabadesolat.
        /// </summary>
        internal static string احمدابادصولت {
            get {
                return ResourceManager.GetString("احمدابادصولت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AhmadSarGoorab.
        /// </summary>
        internal static string احمدسرگوراب {
            get {
                return ResourceManager.GetString("احمدسرگوراب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EkhtiarAbad.
        /// </summary>
        internal static string اختیارآباد {
            get {
                return ResourceManager.GetString("اختیارآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adimi.
        /// </summary>
        internal static string ادیمی {
            get {
                return ResourceManager.GetString("ادیمی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AzarShahr.
        /// </summary>
        internal static string آذرشهر {
            get {
                return ResourceManager.GetString("آذرشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aradan.
        /// </summary>
        internal static string آرادان {
            get {
                return ResourceManager.GetString("آرادان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arak.
        /// </summary>
        internal static string اراک {
            get {
                return ResourceManager.GetString("اراک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AranBidgol.
        /// </summary>
        internal static string آران_وبیدگل {
            get {
                return ResourceManager.GetString("آران وبیدگل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arjmand.
        /// </summary>
        internal static string ارجمند {
            get {
                return ResourceManager.GetString("ارجمند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ardagh.
        /// </summary>
        internal static string ارداق {
            get {
                return ResourceManager.GetString("ارداق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ardebil.
        /// </summary>
        internal static string اردبیل {
            get {
                return ResourceManager.GetString("اردبیل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ardestan.
        /// </summary>
        internal static string اردستان {
            get {
                return ResourceManager.GetString("اردستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ardakan.
        /// </summary>
        internal static string اردکان {
            get {
                return ResourceManager.GetString("اردکان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ardal.
        /// </summary>
        internal static string اردل {
            get {
                return ResourceManager.GetString("اردل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orzooeeyeh.
        /// </summary>
        internal static string ارزوییه {
            get {
                return ResourceManager.GetString("ارزوییه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eresk.
        /// </summary>
        internal static string ارسک {
            get {
                return ResourceManager.GetString("ارسک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arsanjan.
        /// </summary>
        internal static string ارسنجان {
            get {
                return ResourceManager.GetString("ارسنجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arkavaz.
        /// </summary>
        internal static string ارکواز {
            get {
                return ResourceManager.GetString("ارکواز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Armardeh.
        /// </summary>
        internal static string آرمرده {
            get {
                return ResourceManager.GetString("آرمرده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Armaghankhaneh.
        /// </summary>
        internal static string ارمغانخانه {
            get {
                return ResourceManager.GetString("ارمغانخانه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Urumieh.
        /// </summary>
        internal static string ارومیه {
            get {
                return ResourceManager.GetString("ارومیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arvandkenar.
        /// </summary>
        internal static string اروندکنار {
            get {
                return ResourceManager.GetString("اروندکنار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ArianShahr.
        /// </summary>
        internal static string آرین_شهر {
            get {
                return ResourceManager.GetString("آرین شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AzadShahr.
        /// </summary>
        internal static string آزادشهر {
            get {
                return ResourceManager.GetString("آزادشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azgale.
        /// </summary>
        internal static string ازگله {
            get {
                return ResourceManager.GetString("ازگله", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azna.
        /// </summary>
        internal static string ازنا {
            get {
                return ResourceManager.GetString("ازنا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azandaryan.
        /// </summary>
        internal static string ازندریان {
            get {
                return ResourceManager.GetString("ازندریان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azhiyeh.
        /// </summary>
        internal static string اژیه {
            get {
                return ResourceManager.GetString("اژیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Asara.
        /// </summary>
        internal static string آسارا {
            get {
                return ResourceManager.GetString("آسارا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Asalem.
        /// </summary>
        internal static string اسالم {
            get {
                return ResourceManager.GetString("اسالم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Espakeh.
        /// </summary>
        internal static string اسپکه {
            get {
                return ResourceManager.GetString("اسپکه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Astara.
        /// </summary>
        internal static string آستارا {
            get {
                return ResourceManager.GetString("آستارا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AstanehAshrafieh.
        /// </summary>
        internal static string آستانه_اشرفیه {
            get {
                return ResourceManager.GetString("آستانه اشرفیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Astanehsarband.
        /// </summary>
        internal static string آستانه_سربند {
            get {
                return ResourceManager.GetString("آستانه سربند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estahban.
        /// </summary>
        internal static string استهبان {
            get {
                return ResourceManager.GetString("استهبان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AsadAbad.
        /// </summary>
        internal static string اسدآباد {
            get {
                return ResourceManager.GetString("اسدآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Asadyeh.
        /// </summary>
        internal static string اسدیه {
            get {
                return ResourceManager.GetString("اسدیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Esfaden.
        /// </summary>
        internal static string اسفدن {
            get {
                return ResourceManager.GetString("اسفدن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Esfarayen.
        /// </summary>
        internal static string اسفراین {
            get {
                return ResourceManager.GetString("اسفراین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Esfarvarin.
        /// </summary>
        internal static string اسفرورین {
            get {
                return ResourceManager.GetString("اسفرورین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oskoo.
        /// </summary>
        internal static string اسکو {
            get {
                return ResourceManager.GetString("اسکو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eslamabadgharb.
        /// </summary>
        internal static string اسلام_آبادغرب {
            get {
                return ResourceManager.GetString("اسلام آبادغرب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EslamShahr.
        /// </summary>
        internal static string اسلامشهر {
            get {
                return ResourceManager.GetString("اسلامشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eslamyeh.
        /// </summary>
        internal static string اسلامیه {
            get {
                return ResourceManager.GetString("اسلامیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AsemanAbad.
        /// </summary>
        internal static string آسمان_آباد {
            get {
                return ResourceManager.GetString("آسمان آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Asir.
        /// </summary>
        internal static string اسیر {
            get {
                return ResourceManager.GetString("اسیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ashtarinan.
        /// </summary>
        internal static string اشترینان {
            get {
                return ResourceManager.GetString("اشترینان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eshtehard.
        /// </summary>
        internal static string اشتهارد {
            get {
                return ResourceManager.GetString("اشتهارد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ashtian.
        /// </summary>
        internal static string آشتیان {
            get {
                return ResourceManager.GetString("آشتیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ashkhaneh.
        /// </summary>
        internal static string آشخانه {
            get {
                return ResourceManager.GetString("آشخانه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ashkezar.
        /// </summary>
        internal static string اشکذر {
            get {
                return ResourceManager.GetString("اشکذر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ashknan.
        /// </summary>
        internal static string اشکنان {
            get {
                return ResourceManager.GetString("اشکنان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oshnavieh.
        /// </summary>
        internal static string اشنویه {
            get {
                return ResourceManager.GetString("اشنویه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Isfahan.
        /// </summary>
        internal static string اصفهان {
            get {
                return ResourceManager.GetString("اصفهان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aslandooz.
        /// </summary>
        internal static string اصلاندوز {
            get {
                return ResourceManager.GetString("اصلاندوز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ataghor.
        /// </summary>
        internal static string اطاقور {
            get {
                return ResourceManager.GetString("اطاقور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aghajari.
        /// </summary>
        internal static string آغاجاری {
            get {
                return ResourceManager.GetString("آغاجاری", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Afzar.
        /// </summary>
        internal static string افزر {
            get {
                return ResourceManager.GetString("افزر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Afoos.
        /// </summary>
        internal static string افوس {
            get {
                return ResourceManager.GetString("افوس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AghGhola.
        /// </summary>
        internal static string آق_قلا {
            get {
                return ResourceManager.GetString("آق قلا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eghbalyeh.
        /// </summary>
        internal static string اقبالیه {
            get {
                return ResourceManager.GetString("اقبالیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aghkand.
        /// </summary>
        internal static string آقکند {
            get {
                return ResourceManager.GetString("آقکند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eghlid.
        /// </summary>
        internal static string اقلید {
            get {
                return ResourceManager.GetString("اقلید", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alasht.
        /// </summary>
        internal static string آلاشت {
            get {
                return ResourceManager.GetString("آلاشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alashtar.
        /// </summary>
        internal static string الشتر {
            get {
                return ResourceManager.GetString("الشتر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alvan.
        /// </summary>
        internal static string الوان {
            get {
                return ResourceManager.GetString("الوان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alvand.
        /// </summary>
        internal static string الوند {
            get {
                return ResourceManager.GetString("الوند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aloni.
        /// </summary>
        internal static string آلونی {
            get {
                return ResourceManager.GetString("آلونی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aligoodarz.
        /// </summary>
        internal static string الیگودرز {
            get {
                return ResourceManager.GetString("الیگودرز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EmamHasan.
        /// </summary>
        internal static string امام_حسن {
            get {
                return ResourceManager.GetString("امام حسن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EmamShahr.
        /// </summary>
        internal static string امام_شهر {
            get {
                return ResourceManager.GetString("امام شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amol.
        /// </summary>
        internal static string آمل {
            get {
                return ResourceManager.GetString("آمل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amlash.
        /// </summary>
        internal static string املش {
            get {
                return ResourceManager.GetString("املش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Omidieh.
        /// </summary>
        internal static string امیدیه {
            get {
                return ResourceManager.GetString("امیدیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amirkala.
        /// </summary>
        internal static string امیرکلا {
            get {
                return ResourceManager.GetString("امیرکلا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amiriyeh.
        /// </summary>
        internal static string امیریه {
            get {
                return ResourceManager.GetString("امیریه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AminShahr.
        /// </summary>
        internal static string امین_شهر {
            get {
                return ResourceManager.GetString("امین شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anabad.
        /// </summary>
        internal static string انابد {
            get {
                return ResourceManager.GetString("انابد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anar.
        /// </summary>
        internal static string انار {
            get {
                return ResourceManager.GetString("انار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anarestan.
        /// </summary>
        internal static string انارستان {
            get {
                return ResourceManager.GetString("انارستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anarak.
        /// </summary>
        internal static string انارک {
            get {
                return ResourceManager.GetString("انارک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anbaralom.
        /// </summary>
        internal static string انبارآلوم {
            get {
                return ResourceManager.GetString("انبارآلوم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Andoohjerd.
        /// </summary>
        internal static string اندوهجرد {
            get {
                return ResourceManager.GetString("اندوهجرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Andisheh.
        /// </summary>
        internal static string اندیشه {
            get {
                return ResourceManager.GetString("اندیشه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Andimeshk.
        /// </summary>
        internal static string اندیمشک {
            get {
                return ResourceManager.GetString("اندیمشک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ahar.
        /// </summary>
        internal static string اهر {
            get {
                return ResourceManager.GetString("اهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ahrom.
        /// </summary>
        internal static string اهرم {
            get {
                return ResourceManager.GetString("اهرم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ahl.
        /// </summary>
        internal static string اهل {
            get {
                return ResourceManager.GetString("اهل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ahvaz.
        /// </summary>
        internal static string اهواز {
            get {
                return ResourceManager.GetString("اهواز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avajigh.
        /// </summary>
        internal static string آواجیق {
            get {
                return ResourceManager.GetString("آواجیق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avaj.
        /// </summary>
        internal static string آوج {
            get {
                return ResourceManager.GetString("آوج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Evaz.
        /// </summary>
        internal static string اوز {
            get {
                return ResourceManager.GetString("اوز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edge.
        /// </summary>
        internal static string ایج {
            get {
                return ResourceManager.GetString("ایج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Izeh.
        /// </summary>
        internal static string ایذه {
            get {
                return ResourceManager.GetString("ایذه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IranShahr.
        /// </summary>
        internal static string ایرانشهر {
            get {
                return ResourceManager.GetString("ایرانشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Izadkhast.
        /// </summary>
        internal static string ایزدخواست {
            get {
                return ResourceManager.GetString("ایزدخواست", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IzadShahr.
        /// </summary>
        internal static string ایزدشهر {
            get {
                return ResourceManager.GetString("ایزدشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aysek.
        /// </summary>
        internal static string آیسک {
            get {
                return ResourceManager.GetString("آیسک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ilam.
        /// </summary>
        internal static string ایلام {
            get {
                return ResourceManager.GetString("ایلام", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ilikhchi.
        /// </summary>
        internal static string ایلخچی {
            get {
                return ResourceManager.GetString("ایلخچی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ImanShahr.
        /// </summary>
        internal static string ایمانشهر {
            get {
                return ResourceManager.GetString("ایمانشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incheboroon.
        /// </summary>
        internal static string اینچه_برون {
            get {
                return ResourceManager.GetString("اینچه برون", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ivan.
        /// </summary>
        internal static string ایوان {
            get {
                return ResourceManager.GetString("ایوان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ivanaki.
        /// </summary>
        internal static string ایوانکی {
            get {
                return ResourceManager.GetString("ایوانکی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ivooghli.
        /// </summary>
        internal static string ایواوغلی {
            get {
                return ResourceManager.GetString("ایواوغلی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ivar.
        /// </summary>
        internal static string ایور {
            get {
                return ResourceManager.GetString("ایور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Babanar.
        /// </summary>
        internal static string باب_انار {
            get {
                return ResourceManager.GetString("باب انار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Babaheidar.
        /// </summary>
        internal static string باباحیدر {
            get {
                return ResourceManager.GetString("باباحیدر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Babarashani.
        /// </summary>
        internal static string بابارشانی {
            get {
                return ResourceManager.GetString("بابارشانی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Babol.
        /// </summary>
        internal static string بابل {
            get {
                return ResourceManager.GetString("بابل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Babolsar.
        /// </summary>
        internal static string بابلسر {
            get {
                return ResourceManager.GetString("بابلسر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bajgiran.
        /// </summary>
        internal static string باجگیران {
            get {
                return ResourceManager.GetString("باجگیران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bakharz.
        /// </summary>
        internal static string باخرز {
            get {
                return ResourceManager.GetString("باخرز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Badrood.
        /// </summary>
        internal static string بادرود {
            get {
                return ResourceManager.GetString("بادرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baar.
        /// </summary>
        internal static string بار {
            get {
                return ResourceManager.GetString("بار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barogh.
        /// </summary>
        internal static string باروق {
            get {
                return ResourceManager.GetString("باروق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BazarJome.
        /// </summary>
        internal static string بازارجمعه {
            get {
                return ResourceManager.GetString("بازارجمعه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bazargan.
        /// </summary>
        internal static string بازرگان {
            get {
                return ResourceManager.GetString("بازرگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bazoft.
        /// </summary>
        internal static string بازفت {
            get {
                return ResourceManager.GetString("بازفت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basmenj.
        /// </summary>
        internal static string باسمنج {
            get {
                return ResourceManager.GetString("باسمنج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basht.
        /// </summary>
        internal static string باشت {
            get {
                return ResourceManager.GetString("باشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baghbahadoran.
        /// </summary>
        internal static string باغ_بهادران {
            get {
                return ResourceManager.GetString("باغ بهادران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baghmalek.
        /// </summary>
        internal static string باغ_ملک {
            get {
                return ResourceManager.GetString("باغ ملک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baghestan.
        /// </summary>
        internal static string باغستان {
            get {
                return ResourceManager.GetString("باغستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baghin.
        /// </summary>
        internal static string باغین {
            get {
                return ResourceManager.GetString("باغین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baft.
        /// </summary>
        internal static string بافت {
            get {
                return ResourceManager.GetString("بافت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bafran.
        /// </summary>
        internal static string بافران {
            get {
                return ResourceManager.GetString("بافران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bafgh.
        /// </summary>
        internal static string بافق {
            get {
                return ResourceManager.GetString("بافق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BagherShahr.
        /// </summary>
        internal static string باقرشهر {
            get {
                return ResourceManager.GetString("باقرشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baladeh.
        /// </summary>
        internal static string بالاده {
            get {
                return ResourceManager.GetString("بالاده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baneh.
        /// </summary>
        internal static string بانه {
            get {
                return ResourceManager.GetString("بانه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baek.
        /// </summary>
        internal static string بایک {
            get {
                return ResourceManager.GetString("بایک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bayangan.
        /// </summary>
        internal static string باینگان {
            get {
                return ResourceManager.GetString("باینگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bejestan.
        /// </summary>
        internal static string بجستان {
            get {
                return ResourceManager.GetString("بجستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bojnoord.
        /// </summary>
        internal static string بجنورد {
            get {
                return ResourceManager.GetString("بجنورد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bakhshayesh.
        /// </summary>
        internal static string بخشایش {
            get {
                return ResourceManager.GetString("بخشایش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Badreh.
        /// </summary>
        internal static string بدره {
            get {
                return ResourceManager.GetString("بدره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Borazjan.
        /// </summary>
        internal static string برازجان {
            get {
                return ResourceManager.GetString("برازجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bordkhun.
        /// </summary>
        internal static string بردخون {
            get {
                return ResourceManager.GetString("بردخون", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bardestan.
        /// </summary>
        internal static string بردستان {
            get {
                return ResourceManager.GetString("بردستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bardeskan.
        /// </summary>
        internal static string بردسکن {
            get {
                return ResourceManager.GetString("بردسکن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bardsir.
        /// </summary>
        internal static string بردسیر {
            get {
                return ResourceManager.GetString("بردسیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barzok.
        /// </summary>
        internal static string برزک {
            get {
                return ResourceManager.GetString("برزک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barzool.
        /// </summary>
        internal static string برزول {
            get {
                return ResourceManager.GetString("برزول", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barfanbar.
        /// </summary>
        internal static string برف_انبار {
            get {
                return ResourceManager.GetString("برف انبار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barehsar.
        /// </summary>
        internal static string بره_سر {
            get {
                return ResourceManager.GetString("بره سر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baravat.
        /// </summary>
        internal static string بروات {
            get {
                return ResourceManager.GetString("بروات", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boroojerd.
        /// </summary>
        internal static string بروجرد {
            get {
                return ResourceManager.GetString("بروجرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boroojen.
        /// </summary>
        internal static string بروجن {
            get {
                return ResourceManager.GetString("بروجن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bazman.
        /// </summary>
        internal static string بزمان {
            get {
                return ResourceManager.GetString("بزمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bezanjan.
        /// </summary>
        internal static string بزنجان {
            get {
                return ResourceManager.GetString("بزنجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bostan.
        /// </summary>
        internal static string بستان {
            get {
                return ResourceManager.GetString("بستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BostanAbad.
        /// </summary>
        internal static string بستان_آباد {
            get {
                return ResourceManager.GetString("بستان آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bastak.
        /// </summary>
        internal static string بستک {
            get {
                return ResourceManager.GetString("بستک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bastam.
        /// </summary>
        internal static string بسطام {
            get {
                return ResourceManager.GetString("بسطام", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boshrooyeh.
        /// </summary>
        internal static string بشرویه {
            get {
                return ResourceManager.GetString("بشرویه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bafrooeeyeh.
        /// </summary>
        internal static string بفروییه {
            get {
                return ResourceManager.GetString("بفروییه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BolbanAbad.
        /// </summary>
        internal static string بلبان_آباد {
            get {
                return ResourceManager.GetString("بلبان آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beldaji.
        /// </summary>
        internal static string بلداجی {
            get {
                return ResourceManager.GetString("بلداجی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baladeh.
        /// </summary>
        internal static string بلده {
            get {
                return ResourceManager.GetString("بلده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bam.
        /// </summary>
        internal static string بم {
            get {
                return ResourceManager.GetString("بم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bampoor.
        /// </summary>
        internal static string بمپور {
            get {
                return ResourceManager.GetString("بمپور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bon.
        /// </summary>
        internal static string بن {
            get {
                return ResourceManager.GetString("بن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bonab.
        /// </summary>
        internal static string بناب {
            get {
                return ResourceManager.GetString("بناب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BonabJadid.
        /// </summary>
        internal static string بناب_جدید {
            get {
                return ResourceManager.GetString("بناب جدید", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Banarooyeh.
        /// </summary>
        internal static string بنارویه {
            get {
                return ResourceManager.GetString("بنارویه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bent.
        /// </summary>
        internal static string بنت {
            get {
                return ResourceManager.GetString("بنت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bonjar.
        /// </summary>
        internal static string بنجار {
            get {
                return ResourceManager.GetString("بنجار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarEmamKhomeini.
        /// </summary>
        internal static string بندرامام_خمینی {
            get {
                return ResourceManager.GetString("بندرامام خمینی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarAnzali.
        /// </summary>
        internal static string بندرانزلی {
            get {
                return ResourceManager.GetString("بندرانزلی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarTorkaman.
        /// </summary>
        internal static string بندرترکمن {
            get {
                return ResourceManager.GetString("بندرترکمن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarJask.
        /// </summary>
        internal static string بندرجاسک {
            get {
                return ResourceManager.GetString("بندرجاسک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarDayyer.
        /// </summary>
        internal static string بندردیر {
            get {
                return ResourceManager.GetString("بندردیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarDeylam.
        /// </summary>
        internal static string بندردیلم {
            get {
                return ResourceManager.GetString("بندردیلم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarRig.
        /// </summary>
        internal static string بندرریگ {
            get {
                return ResourceManager.GetString("بندرریگ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarAbas.
        /// </summary>
        internal static string بندرعباس {
            get {
                return ResourceManager.GetString("بندرعباس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarKangan.
        /// </summary>
        internal static string بندرکنگان {
            get {
                return ResourceManager.GetString("بندرکنگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarGaz.
        /// </summary>
        internal static string بندرگز {
            get {
                return ResourceManager.GetString("بندرگز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarGenaveh.
        /// </summary>
        internal static string بندرگناوه {
            get {
                return ResourceManager.GetString("بندرگناوه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarLengeh.
        /// </summary>
        internal static string بندرلنگه {
            get {
                return ResourceManager.GetString("بندرلنگه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BandarMahshahr.
        /// </summary>
        internal static string بندرماهشهر {
            get {
                return ResourceManager.GetString("بندرماهشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Banak.
        /// </summary>
        internal static string بنک {
            get {
                return ResourceManager.GetString("بنک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BehAbad.
        /// </summary>
        internal static string بهاباد {
            get {
                return ResourceManager.GetString("بهاباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bahar.
        /// </summary>
        internal static string بهار {
            get {
                return ResourceManager.GetString("بهار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BaharanShahr.
        /// </summary>
        internal static string بهاران_شهر {
            get {
                return ResourceManager.GetString("بهاران شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baharestan.
        /// </summary>
        internal static string بهارستان {
            get {
                return ResourceManager.GetString("بهارستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Behbahan.
        /// </summary>
        internal static string بهبهان {
            get {
                return ResourceManager.GetString("بهبهان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bahreman.
        /// </summary>
        internal static string بهرمان {
            get {
                return ResourceManager.GetString("بهرمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Behshahr.
        /// </summary>
        internal static string بهشهر {
            get {
                return ResourceManager.GetString("بهشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bahman.
        /// </summary>
        internal static string بهمن {
            get {
                return ResourceManager.GetString("بهمن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Behnamir.
        /// </summary>
        internal static string بهنمیر {
            get {
                return ResourceManager.GetString("بهنمیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bovanat.
        /// </summary>
        internal static string بوانات {
            get {
                return ResourceManager.GetString("بوانات", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boshehr.
        /// </summary>
        internal static string بوشهر {
            get {
                return ResourceManager.GetString("بوشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bookan.
        /// </summary>
        internal static string بوکان {
            get {
                return ResourceManager.GetString("بوکان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boomehen.
        /// </summary>
        internal static string بومهن {
            get {
                return ResourceManager.GetString("بومهن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booeenzahra.
        /// </summary>
        internal static string بویین_زهرا {
            get {
                return ResourceManager.GetString("بویین زهرا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booeensofla.
        /// </summary>
        internal static string بویین_سفلی {
            get {
                return ResourceManager.GetString("بویین سفلی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BooeenMiandasht.
        /// </summary>
        internal static string بویین_ومیاندشت {
            get {
                return ResourceManager.GetString("بویین ومیاندشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Biarjmand.
        /// </summary>
        internal static string بیارجمند {
            get {
                return ResourceManager.GetString("بیارجمند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bijar.
        /// </summary>
        internal static string بیجار {
            get {
                return ResourceManager.GetString("بیجار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bidokht.
        /// </summary>
        internal static string بیدخت {
            get {
                return ResourceManager.GetString("بیدخت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bidestan.
        /// </summary>
        internal static string بیدستان {
            get {
                return ResourceManager.GetString("بیدستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birjand.
        /// </summary>
        internal static string بیرجند {
            get {
                return ResourceManager.GetString("بیرجند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beiram.
        /// </summary>
        internal static string بیرم {
            get {
                return ResourceManager.GetString("بیرم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bistoon.
        /// </summary>
        internal static string بیستون {
            get {
                return ResourceManager.GetString("بیستون", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beiza.
        /// </summary>
        internal static string بیضا {
            get {
                return ResourceManager.GetString("بیضا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bika.
        /// </summary>
        internal static string بیکا {
            get {
                return ResourceManager.GetString("بیکا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bilesavar.
        /// </summary>
        internal static string بیله_سوار {
            get {
                return ResourceManager.GetString("بیله سوار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pataveh.
        /// </summary>
        internal static string پاتاوه {
            get {
                return ResourceManager.GetString("پاتاوه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ParsAbad.
        /// </summary>
        internal static string پارس_آباد {
            get {
                return ResourceManager.GetString("پارس آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parsian.
        /// </summary>
        internal static string پارسیان {
            get {
                return ResourceManager.GetString("پارسیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pariz.
        /// </summary>
        internal static string پاریز {
            get {
                return ResourceManager.GetString("پاریز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pakdasht.
        /// </summary>
        internal static string پاکدشت {
            get {
                return ResourceManager.GetString("پاکدشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paveh.
        /// </summary>
        internal static string پاوه {
            get {
                return ResourceManager.GetString("پاوه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pordanjan.
        /// </summary>
        internal static string پردنجان {
            get {
                return ResourceManager.GetString("پردنجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pardis.
        /// </summary>
        internal static string پردیس {
            get {
                return ResourceManager.GetString("پردیس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parandak.
        /// </summary>
        internal static string پرندک_3_ {
            get {
                return ResourceManager.GetString("پرندک[3]", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parehsar.
        /// </summary>
        internal static string پره_سر {
            get {
                return ResourceManager.GetString("پره سر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Polsefid.
        /// </summary>
        internal static string پل_سفید {
            get {
                return ResourceManager.GetString("پل سفید", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Poldokhtar.
        /// </summary>
        internal static string پلدختر {
            get {
                return ResourceManager.GetString("پلدختر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Poldasht.
        /// </summary>
        internal static string پلدشت {
            get {
                return ResourceManager.GetString("پلدشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pahle.
        /// </summary>
        internal static string پهله {
            get {
                return ResourceManager.GetString("پهله", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pul.
        /// </summary>
        internal static string پول {
            get {
                return ResourceManager.GetString("پول", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Piranshahr.
        /// </summary>
        internal static string پیرانشهر {
            get {
                return ResourceManager.GetString("پیرانشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pirbakran.
        /// </summary>
        internal static string پیربکران {
            get {
                return ResourceManager.GetString("پیربکران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pishghale.
        /// </summary>
        internal static string پیش_قلعه {
            get {
                return ResourceManager.GetString("پیش قلعه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pishva.
        /// </summary>
        internal static string پیشوا {
            get {
                return ResourceManager.GetString("پیشوا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pishin.
        /// </summary>
        internal static string پیشین {
            get {
                return ResourceManager.GetString("پیشین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TazehAbad.
        /// </summary>
        internal static string تازه_آباد {
            get {
                return ResourceManager.GetString("تازه آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TazehShahr.
        /// </summary>
        internal static string تازه_شهر {
            get {
                return ResourceManager.GetString("تازه شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TazeKand.
        /// </summary>
        internal static string تازه_کند {
            get {
                return ResourceManager.GetString("تازه کند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TazeKandeAngoot.
        /// </summary>
        internal static string تازه_کندانگوت {
            get {
                return ResourceManager.GetString("تازه کندانگوت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Takestan.
        /// </summary>
        internal static string تاکستان {
            get {
                return ResourceManager.GetString("تاکستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talesh.
        /// </summary>
        internal static string تالش {
            get {
                return ResourceManager.GetString("تالش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taibad.
        /// </summary>
        internal static string تایباد {
            get {
                return ResourceManager.GetString("تایباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tabriz.
        /// </summary>
        internal static string تبریز {
            get {
                return ResourceManager.GetString("تبریز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Takht.
        /// </summary>
        internal static string تخت {
            get {
                return ResourceManager.GetString("تخت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Torbatjam.
        /// </summary>
        internal static string تربت_جام {
            get {
                return ResourceManager.GetString("تربت جام", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Torbatheydariyeh.
        /// </summary>
        internal static string تربت_حیدریه {
            get {
                return ResourceManager.GetString("تربت حیدریه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tork.
        /// </summary>
        internal static string ترک {
            get {
                return ResourceManager.GetString("ترک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Torkalaki.
        /// </summary>
        internal static string ترکالکی {
            get {
                return ResourceManager.GetString("ترکالکی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Torkamanchai.
        /// </summary>
        internal static string ترکمانچای {
            get {
                return ResourceManager.GetString("ترکمانچای", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tasoj.
        /// </summary>
        internal static string تسوج {
            get {
                return ResourceManager.GetString("تسوج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taft.
        /// </summary>
        internal static string تفت {
            get {
                return ResourceManager.GetString("تفت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tafresh.
        /// </summary>
        internal static string تفرش {
            get {
                return ResourceManager.GetString("تفرش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Takab.
        /// </summary>
        internal static string تکاب {
            get {
                return ResourceManager.GetString("تکاب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tonekabon.
        /// </summary>
        internal static string تنکابن {
            get {
                return ResourceManager.GetString("تنکابن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tonkaman.
        /// </summary>
        internal static string تنکمان {
            get {
                return ResourceManager.GetString("تنکمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tang eram.
        /// </summary>
        internal static string تنگ_ارم {
            get {
                return ResourceManager.GetString("تنگ ارم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tehran.
        /// </summary>
        internal static string تهران {
            get {
                return ResourceManager.GetString("تهران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tootekabon.
        /// </summary>
        internal static string توتکابن {
            get {
                return ResourceManager.GetString("توتکابن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tohid.
        /// </summary>
        internal static string توحید {
            get {
                return ResourceManager.GetString("توحید", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Toodeshk.
        /// </summary>
        internal static string تودشک {
            get {
                return ResourceManager.GetString("تودشک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Toreh.
        /// </summary>
        internal static string توره {
            get {
                return ResourceManager.GetString("توره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tooyserkan.
        /// </summary>
        internal static string تویسرکان {
            get {
                return ResourceManager.GetString("تویسرکان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Titkanlo.
        /// </summary>
        internal static string تیتکانلو {
            get {
                return ResourceManager.GetString("تیتکانلو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tiran.
        /// </summary>
        internal static string تیران {
            get {
                return ResourceManager.GetString("تیران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tikmehdash.
        /// </summary>
        internal static string تیکمه_داش {
            get {
                return ResourceManager.GetString("تیکمه داش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jajarm.
        /// </summary>
        internal static string جاجرم {
            get {
                return ResourceManager.GetString("جاجرم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jalgh.
        /// </summary>
        internal static string جالق {
            get {
                return ResourceManager.GetString("جالق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Javersian.
        /// </summary>
        internal static string جاورسیان {
            get {
                return ResourceManager.GetString("جاورسیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jayzan.
        /// </summary>
        internal static string جایزان {
            get {
                return ResourceManager.GetString("جایزان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jebalbarez.
        /// </summary>
        internal static string جبالبارز {
            get {
                return ResourceManager.GetString("جبالبارز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JafarAbad.
        /// </summary>
        internal static string جعفرآباد {
            get {
                return ResourceManager.GetString("جعفرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jafarieh.
        /// </summary>
        internal static string جعفریه {
            get {
                return ResourceManager.GetString("جعفریه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Joghatay.
        /// </summary>
        internal static string جغتای {
            get {
                return ResourceManager.GetString("جغتای", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jolfa.
        /// </summary>
        internal static string جلفا {
            get {
                return ResourceManager.GetString("جلفا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jelin.
        /// </summary>
        internal static string جلین {
            get {
                return ResourceManager.GetString("جلین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jam.
        /// </summary>
        internal static string جم {
            get {
                return ResourceManager.GetString("جم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jenah.
        /// </summary>
        internal static string جناح {
            get {
                return ResourceManager.GetString("جناح", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JanatShahr.
        /// </summary>
        internal static string جنت_شهر {
            get {
                return ResourceManager.GetString("جنت شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jannatmakan.
        /// </summary>
        internal static string جنت_مکان {
            get {
                return ResourceManager.GetString("جنت مکان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jandagh.
        /// </summary>
        internal static string جندق {
            get {
                return ResourceManager.GetString("جندق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jangal.
        /// </summary>
        internal static string جنگل {
            get {
                return ResourceManager.GetString("جنگل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jahrom.
        /// </summary>
        internal static string جهرم {
            get {
                return ResourceManager.GetString("جهرم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JavadAbad.
        /// </summary>
        internal static string جوادآباد {
            get {
                return ResourceManager.GetString("جوادآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Javanrood.
        /// </summary>
        internal static string جوانرود {
            get {
                return ResourceManager.GetString("جوانرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Joopar.
        /// </summary>
        internal static string جوپار {
            get {
                return ResourceManager.GetString("جوپار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jorghan.
        /// </summary>
        internal static string جورقان {
            get {
                return ResourceManager.GetString("جورقان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jowzdan.
        /// </summary>
        internal static string جوزدان {
            get {
                return ResourceManager.GetString("جوزدان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Javazm.
        /// </summary>
        internal static string جوزم {
            get {
                return ResourceManager.GetString("جوزم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JosheghanKamo.
        /// </summary>
        internal static string جوشقان_وکامو {
            get {
                return ResourceManager.GetString("جوشقان وکامو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jokar.
        /// </summary>
        internal static string جوکار {
            get {
                return ResourceManager.GetString("جوکار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Joonghan.
        /// </summary>
        internal static string جونقان {
            get {
                return ResourceManager.GetString("جونقان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jooybar.
        /// </summary>
        internal static string جویبار {
            get {
                return ResourceManager.GetString("جویبار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jooyam.
        /// </summary>
        internal static string جویم {
            get {
                return ResourceManager.GetString("جویم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jiroft.
        /// </summary>
        internal static string جیرفت {
            get {
                return ResourceManager.GetString("جیرفت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jirandeh.
        /// </summary>
        internal static string جیرنده {
            get {
                return ResourceManager.GetString("جیرنده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chaboksar.
        /// </summary>
        internal static string چابکسر {
            get {
                return ResourceManager.GetString("چابکسر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chabahar.
        /// </summary>
        internal static string چابهار {
            get {
                return ResourceManager.GetString("چابهار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chapeshloo.
        /// </summary>
        internal static string چاپشلو {
            get {
                return ResourceManager.GetString("چاپشلو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chadegan.
        /// </summary>
        internal static string چادگان {
            get {
                return ResourceManager.GetString("چادگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Charak.
        /// </summary>
        internal static string چارک {
            get {
                return ResourceManager.GetString("چارک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chaafchamkhale.
        /// </summary>
        internal static string چاف_و_چمخاله {
            get {
                return ResourceManager.GetString("چاف و چمخاله", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chalancholan.
        /// </summary>
        internal static string چالانچولان {
            get {
                return ResourceManager.GetString("چالانچولان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chaloos.
        /// </summary>
        internal static string چالوس {
            get {
                return ResourceManager.GetString("چالوس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chatrood.
        /// </summary>
        internal static string چترود {
            get {
                return ResourceManager.GetString("چترود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choram.
        /// </summary>
        internal static string چرام {
            get {
                return ResourceManager.GetString("چرام", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Charmahin.
        /// </summary>
        internal static string چرمهین {
            get {
                return ResourceManager.GetString("چرمهین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choghadak.
        /// </summary>
        internal static string چغادک {
            get {
                return ResourceManager.GetString("چغادک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choghamish.
        /// </summary>
        internal static string چغامیش {
            get {
                return ResourceManager.GetString("چغامیش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chaghlondi.
        /// </summary>
        internal static string چغلوندی {
            get {
                return ResourceManager.GetString("چغلوندی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choghabel.
        /// </summary>
        internal static string چقابل {
            get {
                return ResourceManager.GetString("چقابل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chakaneh.
        /// </summary>
        internal static string چکنه {
            get {
                return ResourceManager.GetString("چکنه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chelgerd.
        /// </summary>
        internal static string چلگرد {
            get {
                return ResourceManager.GetString("چلگرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cholicheh.
        /// </summary>
        internal static string چلیچه {
            get {
                return ResourceManager.GetString("چلیچه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chamran.
        /// </summary>
        internal static string چمران {
            get {
                return ResourceManager.GetString("چمران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chamestan.
        /// </summary>
        internal static string چمستان {
            get {
                return ResourceManager.GetString("چمستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chamgordan.
        /// </summary>
        internal static string چمگردان {
            get {
                return ResourceManager.GetString("چمگردان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chenaran.
        /// </summary>
        internal static string چناران {
            get {
                return ResourceManager.GetString("چناران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chenareh.
        /// </summary>
        internal static string چناره {
            get {
                return ResourceManager.GetString("چناره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ChaharBagh.
        /// </summary>
        internal static string چهارباغ {
            get {
                return ResourceManager.GetString("چهارباغ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ChaharBorj.
        /// </summary>
        internal static string چهاربرج {
            get {
                return ResourceManager.GetString("چهاربرج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chahardangeh.
        /// </summary>
        internal static string چهاردانگه {
            get {
                return ResourceManager.GetString("چهاردانگه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chovar.
        /// </summary>
        internal static string چوار {
            get {
                return ResourceManager.GetString("چوار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choobar.
        /// </summary>
        internal static string چوبر {
            get {
                return ResourceManager.GetString("چوبر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chavarzagh.
        /// </summary>
        internal static string چورزق {
            get {
                return ResourceManager.GetString("چورزق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choebdeh.
        /// </summary>
        internal static string چویبده {
            get {
                return ResourceManager.GetString("چویبده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chitab.
        /// </summary>
        internal static string چیتاب {
            get {
                return ResourceManager.GetString("چیتاب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HajiAbad.
        /// </summary>
        internal static string حاجی_آباد {
            get {
                return ResourceManager.GetString("حاجی آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HabibAbad.
        /// </summary>
        internal static string حبیب_آباد {
            get {
                return ResourceManager.GetString("حبیب آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hor.
        /// </summary>
        internal static string حر {
            get {
                return ResourceManager.GetString("حر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hesami.
        /// </summary>
        internal static string حسامی {
            get {
                return ResourceManager.GetString("حسامی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HasanAbad.
        /// </summary>
        internal static string حسن_اباد {
            get {
                return ResourceManager.GetString("حسن اباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HasanAbad.
        /// </summary>
        internal static string حسن_آباد {
            get {
                return ResourceManager.GetString("حسن آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hoseyniyeh.
        /// </summary>
        internal static string حسینیه {
            get {
                return ResourceManager.GetString("حسینیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hesargarmkhan.
        /// </summary>
        internal static string حصارگرمخان {
            get {
                return ResourceManager.GetString("حصارگرمخان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Halab.
        /// </summary>
        internal static string حلب {
            get {
                return ResourceManager.GetString("حلب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hamzeh.
        /// </summary>
        internal static string حمزه {
            get {
                return ResourceManager.GetString("حمزه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hamidia.
        /// </summary>
        internal static string حمیدیا {
            get {
                return ResourceManager.GetString("حمیدیا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hamidieh.
        /// </summary>
        internal static string حمیدیه {
            get {
                return ResourceManager.GetString("حمیدیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hamil.
        /// </summary>
        internal static string حمیل {
            get {
                return ResourceManager.GetString("حمیل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hana.
        /// </summary>
        internal static string حنا {
            get {
                return ResourceManager.GetString("حنا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Havigh.
        /// </summary>
        internal static string حویق {
            get {
                return ResourceManager.GetString("حویق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KhatoonAbad.
        /// </summary>
        internal static string خاتون_آباد {
            get {
                return ResourceManager.GetString("خاتون آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khark.
        /// </summary>
        internal static string خارک {
            get {
                return ResourceManager.GetString("خارک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kharvana.
        /// </summary>
        internal static string خاروانا {
            get {
                return ResourceManager.GetString("خاروانا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khash.
        /// </summary>
        internal static string خاش {
            get {
                return ResourceManager.GetString("خاش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khakali.
        /// </summary>
        internal static string خاکعلی {
            get {
                return ResourceManager.GetString("خاکعلی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KhaledAbad.
        /// </summary>
        internal static string خالدآباد {
            get {
                return ResourceManager.GetString("خالدآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khamene.
        /// </summary>
        internal static string خامنه {
            get {
                return ResourceManager.GetString("خامنه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khanbebin.
        /// </summary>
        internal static string خان_ببین {
            get {
                return ResourceManager.GetString("خان ببین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khanezenian.
        /// </summary>
        internal static string خانه_زنیان {
            get {
                return ResourceManager.GetString("خانه زنیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khanook.
        /// </summary>
        internal static string خانوک {
            get {
                return ResourceManager.GetString("خانوک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khavaran.
        /// </summary>
        internal static string خاوران {
            get {
                return ResourceManager.GetString("خاوران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to khodajoo.
        /// </summary>
        internal static string خداجو_1_ {
            get {
                return ResourceManager.GetString("خداجو[1]", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khorameh.
        /// </summary>
        internal static string خرامه {
            get {
                return ResourceManager.GetString("خرامه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KhoramAbad.
        /// </summary>
        internal static string خرم_آباد {
            get {
                return ResourceManager.GetString("خرم آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khoramdareh.
        /// </summary>
        internal static string خرمدره {
            get {
                return ResourceManager.GetString("خرمدره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khoramdasht.
        /// </summary>
        internal static string خرمدشت {
            get {
                return ResourceManager.GetString("خرمدشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KhoramShahr.
        /// </summary>
        internal static string خرمشهر {
            get {
                return ResourceManager.GetString("خرمشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kharv.
        /// </summary>
        internal static string خرو {
            get {
                return ResourceManager.GetString("خرو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KhosroShahr.
        /// </summary>
        internal static string خسروشهر {
            get {
                return ResourceManager.GetString("خسروشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khesht.
        /// </summary>
        internal static string خشت {
            get {
                return ResourceManager.GetString("خشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khoshkebijar.
        /// </summary>
        internal static string خشکبیجار {
            get {
                return ResourceManager.GetString("خشکبیجار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khoshkrood.
        /// </summary>
        internal static string خشکرود {
            get {
                return ResourceManager.GetString("خشکرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KhezrAbad.
        /// </summary>
        internal static string خضرآباد {
            get {
                return ResourceManager.GetString("خضرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khazridashtbayaz.
        /// </summary>
        internal static string خضری_دشت_بیاض {
            get {
                return ResourceManager.GetString("خضری دشت بیاض", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khalkhal.
        /// </summary>
        internal static string خلخال {
            get {
                return ResourceManager.GetString("خلخال", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khalifan.
        /// </summary>
        internal static string خلیفان {
            get {
                return ResourceManager.GetString("خلیفان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KhalilAbad.
        /// </summary>
        internal static string خلیل_آباد {
            get {
                return ResourceManager.GetString("خلیل آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KhalilShahr.
        /// </summary>
        internal static string خلیل_شهر {
            get {
                return ResourceManager.GetString("خلیل شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khomarlo.
        /// </summary>
        internal static string خمارلو {
            get {
                return ResourceManager.GetString("خمارلو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khomam.
        /// </summary>
        internal static string خمام {
            get {
                return ResourceManager.GetString("خمام", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khomeyr.
        /// </summary>
        internal static string خمیر {
            get {
                return ResourceManager.GetString("خمیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khomein.
        /// </summary>
        internal static string خمین {
            get {
                return ResourceManager.GetString("خمین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KhomeiniShahr.
        /// </summary>
        internal static string خمینی_شهر {
            get {
                return ResourceManager.GetString("خمینی شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khonj.
        /// </summary>
        internal static string خنج {
            get {
                return ResourceManager.GetString("خنج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khenejin.
        /// </summary>
        internal static string خنجین {
            get {
                return ResourceManager.GetString("خنجین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khandab.
        /// </summary>
        internal static string خنداب {
            get {
                return ResourceManager.GetString("خنداب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khaje.
        /// </summary>
        internal static string خواجه {
            get {
                return ResourceManager.GetString("خواجه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khaf.
        /// </summary>
        internal static string خواف {
            get {
                return ResourceManager.GetString("خواف", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khansar.
        /// </summary>
        internal static string خوانسار {
            get {
                return ResourceManager.GetString("خوانسار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khoor.
        /// </summary>
        internal static string خور {
            get {
                return ResourceManager.GetString("خور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khoorasgan.
        /// </summary>
        internal static string خوراسگان {
            get {
                return ResourceManager.GetString("خوراسگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khorzough.
        /// </summary>
        internal static string خورزوق {
            get {
                return ResourceManager.GetString("خورزوق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khorsand.
        /// </summary>
        internal static string خورسند {
            get {
                return ResourceManager.GetString("خورسند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khormoj.
        /// </summary>
        internal static string خورموج {
            get {
                return ResourceManager.GetString("خورموج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khoosf.
        /// </summary>
        internal static string خوسف {
            get {
                return ResourceManager.GetString("خوسف", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khoshrodpey.
        /// </summary>
        internal static string خوش_رودپی {
            get {
                return ResourceManager.GetString("خوش رودپی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khumehzar.
        /// </summary>
        internal static string خومه_زار {
            get {
                return ResourceManager.GetString("خومه زار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Khoy.
        /// </summary>
        internal static string خوی {
            get {
                return ResourceManager.GetString("خوی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dabodasht.
        /// </summary>
        internal static string دابودشت {
            get {
                return ResourceManager.GetString("دابودشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Darab.
        /// </summary>
        internal static string داراب {
            get {
                return ResourceManager.GetString("داراب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daran.
        /// </summary>
        internal static string داران {
            get {
                return ResourceManager.GetString("داران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Darkhovein.
        /// </summary>
        internal static string دارخوین {
            get {
                return ResourceManager.GetString("دارخوین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Darian.
        /// </summary>
        internal static string داریان {
            get {
                return ResourceManager.GetString("داریان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dalaki.
        /// </summary>
        internal static string دالکی {
            get {
                return ResourceManager.GetString("دالکی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Damghan.
        /// </summary>
        internal static string دامغان {
            get {
                return ResourceManager.GetString("دامغان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Damane.
        /// </summary>
        internal static string دامنه {
            get {
                return ResourceManager.GetString("دامنه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Danesfehan.
        /// </summary>
        internal static string دانسفهان {
            get {
                return ResourceManager.GetString("دانسفهان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DavoodAbad.
        /// </summary>
        internal static string داودآباد {
            get {
                return ResourceManager.GetString("داودآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Davarzan.
        /// </summary>
        internal static string داورزن {
            get {
                return ResourceManager.GetString("داورزن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dabiran.
        /// </summary>
        internal static string دبیران {
            get {
                return ResourceManager.GetString("دبیران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Darbbehesht.
        /// </summary>
        internal static string درب_بهشت {
            get {
                return ResourceManager.GetString("درب بهشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Darbgonbad.
        /// </summary>
        internal static string درب_گنبد {
            get {
                return ResourceManager.GetString("درب گنبد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Darjazin.
        /// </summary>
        internal static string درجزین {
            get {
                return ResourceManager.GetString("درجزین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dorche.
        /// </summary>
        internal static string درچه {
            get {
                return ResourceManager.GetString("درچه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daragh.
        /// </summary>
        internal static string درق {
            get {
                return ResourceManager.GetString("درق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daregaz.
        /// </summary>
        internal static string درگز {
            get {
                return ResourceManager.GetString("درگز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dargahan.
        /// </summary>
        internal static string درگهان {
            get {
                return ResourceManager.GetString("درگهان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DarrehShahr.
        /// </summary>
        internal static string دره_شهر {
            get {
                return ResourceManager.GetString("دره شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Darrood.
        /// </summary>
        internal static string درود {
            get {
                return ResourceManager.GetString("درود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dezab.
        /// </summary>
        internal static string دزآب {
            get {
                return ResourceManager.GetString("دزآب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dazj.
        /// </summary>
        internal static string دزج {
            get {
                return ResourceManager.GetString("دزج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dezfool.
        /// </summary>
        internal static string دزفول {
            get {
                return ResourceManager.GetString("دزفول", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dojkord.
        /// </summary>
        internal static string دژکرد {
            get {
                return ResourceManager.GetString("دژکرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dastjerd.
        /// </summary>
        internal static string دستجرد {
            get {
                return ResourceManager.GetString("دستجرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dastgerd.
        /// </summary>
        internal static string دستگرد {
            get {
                return ResourceManager.GetString("دستگرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dastena.
        /// </summary>
        internal static string دستنا {
            get {
                return ResourceManager.GetString("دستنا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dashtak.
        /// </summary>
        internal static string دشتک {
            get {
                return ResourceManager.GetString("دشتک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delbaran.
        /// </summary>
        internal static string دلبران {
            get {
                return ResourceManager.GetString("دلبران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delgosha.
        /// </summary>
        internal static string دلگشا {
            get {
                return ResourceManager.GetString("دلگشا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deland.
        /// </summary>
        internal static string دلند {
            get {
                return ResourceManager.GetString("دلند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delvar.
        /// </summary>
        internal static string دلوار {
            get {
                return ResourceManager.GetString("دلوار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delijan.
        /// </summary>
        internal static string دلیجان {
            get {
                return ResourceManager.GetString("دلیجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Damavand.
        /// </summary>
        internal static string دماوند {
            get {
                return ResourceManager.GetString("دماوند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Damagh.
        /// </summary>
        internal static string دمق {
            get {
                return ResourceManager.GetString("دمق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dandi.
        /// </summary>
        internal static string دندی {
            get {
                return ResourceManager.GetString("دندی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dehaghan.
        /// </summary>
        internal static string دهاقان {
            get {
                return ResourceManager.GetString("دهاقان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dehbarez.
        /// </summary>
        internal static string دهبارز {
            get {
                return ResourceManager.GetString("دهبارز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dahej.
        /// </summary>
        internal static string دهج {
            get {
                return ResourceManager.GetString("دهج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dehdez.
        /// </summary>
        internal static string دهدز {
            get {
                return ResourceManager.GetString("دهدز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dehdasht.
        /// </summary>
        internal static string دهدشت {
            get {
                return ResourceManager.GetString("دهدشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dehram.
        /// </summary>
        internal static string دهرم {
            get {
                return ResourceManager.GetString("دهرم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dehagh.
        /// </summary>
        internal static string دهق {
            get {
                return ResourceManager.GetString("دهق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dehgolan.
        /// </summary>
        internal static string دهگلان {
            get {
                return ResourceManager.GetString("دهگلان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dehloran.
        /// </summary>
        internal static string دهلران {
            get {
                return ResourceManager.GetString("دهلران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Doborji.
        /// </summary>
        internal static string دوبرجی {
            get {
                return ResourceManager.GetString("دوبرجی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dorood.
        /// </summary>
        internal static string دورود {
            get {
                return ResourceManager.GetString("دورود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Doozdoozan.
        /// </summary>
        internal static string دوزدوزان {
            get {
                return ResourceManager.GetString("دوزدوزان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dozeh.
        /// </summary>
        internal static string دوزه {
            get {
                return ResourceManager.GetString("دوزه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dosari.
        /// </summary>
        internal static string دوساری {
            get {
                return ResourceManager.GetString("دوساری", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dustmohammad.
        /// </summary>
        internal static string دوست_محمد {
            get {
                return ResourceManager.GetString("دوست محمد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dogonbadan.
        /// </summary>
        internal static string دوگنبدان {
            get {
                return ResourceManager.GetString("دوگنبدان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DolatAbad.
        /// </summary>
        internal static string دولت_آباد {
            get {
                return ResourceManager.GetString("دولت آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dibaj.
        /// </summary>
        internal static string دیباج {
            get {
                return ResourceManager.GetString("دیباج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DizajDiz.
        /// </summary>
        internal static string دیزج_دیز {
            get {
                return ResourceManager.GetString("دیزج دیز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dizicheh.
        /// </summary>
        internal static string دیزیچه {
            get {
                return ResourceManager.GetString("دیزیچه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dishmok.
        /// </summary>
        internal static string دیشموک {
            get {
                return ResourceManager.GetString("دیشموک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deylaman.
        /// </summary>
        internal static string دیلمان {
            get {
                return ResourceManager.GetString("دیلمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deyhook.
        /// </summary>
        internal static string دیهوک {
            get {
                return ResourceManager.GetString("دیهوک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Divandareh.
        /// </summary>
        internal static string دیواندره {
            get {
                return ResourceManager.GetString("دیواندره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rabor.
        /// </summary>
        internal static string رابر {
            get {
                return ResourceManager.GetString("رابر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raz.
        /// </summary>
        internal static string راز {
            get {
                return ResourceManager.GetString("راز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Razeghan.
        /// </summary>
        internal static string رازقان {
            get {
                return ResourceManager.GetString("رازقان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Razmiyan.
        /// </summary>
        internal static string رازمیان {
            get {
                return ResourceManager.GetString("رازمیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rask.
        /// </summary>
        internal static string راسک {
            get {
                return ResourceManager.GetString("راسک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ramjerd.
        /// </summary>
        internal static string رامجرد {
            get {
                return ResourceManager.GetString("رامجرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ramsar.
        /// </summary>
        internal static string رامسر {
            get {
                return ResourceManager.GetString("رامسر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ramshir.
        /// </summary>
        internal static string رامشیر {
            get {
                return ResourceManager.GetString("رامشیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ramhormoz.
        /// </summary>
        internal static string رامهرمز {
            get {
                return ResourceManager.GetString("رامهرمز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ramyan.
        /// </summary>
        internal static string رامیان {
            get {
                return ResourceManager.GetString("رامیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rankoh.
        /// </summary>
        internal static string رانکوه {
            get {
                return ResourceManager.GetString("رانکوه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ravar.
        /// </summary>
        internal static string راور {
            get {
                return ResourceManager.GetString("راور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rayen.
        /// </summary>
        internal static string راین {
            get {
                return ResourceManager.GetString("راین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Robat.
        /// </summary>
        internal static string رباط {
            get {
                return ResourceManager.GetString("رباط", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Robatsang.
        /// </summary>
        internal static string رباط_سنگ {
            get {
                return ResourceManager.GetString("رباط سنگ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Robatkarim.
        /// </summary>
        internal static string رباط_کریم {
            get {
                return ResourceManager.GetString("رباط کریم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rabt.
        /// </summary>
        internal static string ربط {
            get {
                return ResourceManager.GetString("ربط", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RahimAbad.
        /// </summary>
        internal static string رحیم_آباد {
            get {
                return ResourceManager.GetString("رحیم آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Razan.
        /// </summary>
        internal static string رزن {
            get {
                return ResourceManager.GetString("رزن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rozveh.
        /// </summary>
        internal static string رزوه {
            get {
                return ResourceManager.GetString("رزوه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RostamAbad.
        /// </summary>
        internal static string رستم_آباد {
            get {
                return ResourceManager.GetString("رستم آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rostamkela.
        /// </summary>
        internal static string رستمکلا {
            get {
                return ResourceManager.GetString("رستمکلا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rasht.
        /// </summary>
        internal static string رشت {
            get {
                return ResourceManager.GetString("رشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rashtkhar.
        /// </summary>
        internal static string رشتخوار {
            get {
                return ResourceManager.GetString("رشتخوار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rezvanshahr.
        /// </summary>
        internal static string رضوانشهر {
            get {
                return ResourceManager.GetString("رضوانشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Razaviyeh.
        /// </summary>
        internal static string رضویه {
            get {
                return ResourceManager.GetString("رضویه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Razi.
        /// </summary>
        internal static string رضی {
            get {
                return ResourceManager.GetString("رضی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rafsanjan.
        /// </summary>
        internal static string رفسنجان {
            get {
                return ResourceManager.GetString("رفسنجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rafi.
        /// </summary>
        internal static string رفیع {
            get {
                return ResourceManager.GetString("رفیع", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ravansar.
        /// </summary>
        internal static string روانسر {
            get {
                return ResourceManager.GetString("روانسر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ruodab.
        /// </summary>
        internal static string روداب {
            get {
                return ResourceManager.GetString("روداب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roodbar.
        /// </summary>
        internal static string رودبار {
            get {
                return ResourceManager.GetString("رودبار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rodboneh.
        /// </summary>
        internal static string رودبنه {
            get {
                return ResourceManager.GetString("رودبنه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rodsar.
        /// </summary>
        internal static string رودسر {
            get {
                return ResourceManager.GetString("رودسر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roodehen.
        /// </summary>
        internal static string رودهن {
            get {
                return ResourceManager.GetString("رودهن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roniz.
        /// </summary>
        internal static string رونیز {
            get {
                return ResourceManager.GetString("رونیز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Royan.
        /// </summary>
        internal static string رویان {
            get {
                return ResourceManager.GetString("رویان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rooydar.
        /// </summary>
        internal static string رویدر {
            get {
                return ResourceManager.GetString("رویدر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rey.
        /// </summary>
        internal static string ری {
            get {
                return ResourceManager.GetString("ری", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reyhan.
        /// </summary>
        internal static string ریحان {
            get {
                return ResourceManager.GetString("ریحان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Riz.
        /// </summary>
        internal static string ریز {
            get {
                return ResourceManager.GetString("ریز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rineh.
        /// </summary>
        internal static string رینه {
            get {
                return ResourceManager.GetString("رینه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rivash.
        /// </summary>
        internal static string ریوش {
            get {
                return ResourceManager.GetString("ریوش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zabol.
        /// </summary>
        internal static string زابل {
            get {
                return ResourceManager.GetString("زابل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zaboli.
        /// </summary>
        internal static string زابلی {
            get {
                return ResourceManager.GetString("زابلی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zarch.
        /// </summary>
        internal static string زارچ {
            get {
                return ResourceManager.GetString("زارچ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zagheh.
        /// </summary>
        internal static string زاغه {
            get {
                return ResourceManager.GetString("زاغه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zahedan.
        /// </summary>
        internal static string زاهدان {
            get {
                return ResourceManager.GetString("زاهدان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZahedShahr.
        /// </summary>
        internal static string زاهدشهر {
            get {
                return ResourceManager.GetString("زاهدشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zavieh.
        /// </summary>
        internal static string زاویه {
            get {
                return ResourceManager.GetString("زاویه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zayanderood.
        /// </summary>
        internal static string زاینده_رود {
            get {
                return ResourceManager.GetString("زاینده رود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZarAbad.
        /// </summary>
        internal static string زرآباد {
            get {
                return ResourceManager.GetString("زرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zarghan.
        /// </summary>
        internal static string زرقان {
            get {
                return ResourceManager.GetString("زرقان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zargarmahaleh.
        /// </summary>
        internal static string زرگرمحله {
            get {
                return ResourceManager.GetString("زرگرمحله", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zarand.
        /// </summary>
        internal static string زرند {
            get {
                return ResourceManager.GetString("زرند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zarnagh.
        /// </summary>
        internal static string زرنق {
            get {
                return ResourceManager.GetString("زرنق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zarneh.
        /// </summary>
        internal static string زرنه {
            get {
                return ResourceManager.GetString("زرنه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZarinAbad.
        /// </summary>
        internal static string زرین_آباد {
            get {
                return ResourceManager.GetString("زرین آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zarrinroud.
        /// </summary>
        internal static string زرین_رود {
            get {
                return ResourceManager.GetString("زرین رود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZarrinShahr.
        /// </summary>
        internal static string زرین_شهر {
            get {
                return ResourceManager.GetString("زرین شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zarineh.
        /// </summary>
        internal static string زرینه {
            get {
                return ResourceManager.GetString("زرینه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zanjan.
        /// </summary>
        internal static string زنجان {
            get {
                return ResourceManager.GetString("زنجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zanganeh.
        /// </summary>
        internal static string زنگنه {
            get {
                return ResourceManager.GetString("زنگنه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZangiAbad.
        /// </summary>
        internal static string زنگی_آباد {
            get {
                return ResourceManager.GetString("زنگی آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zonooz.
        /// </summary>
        internal static string زنوز {
            get {
                return ResourceManager.GetString("زنوز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zahan.
        /// </summary>
        internal static string زهان {
            get {
                return ResourceManager.GetString("زهان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zohre.
        /// </summary>
        internal static string زهره {
            get {
                return ResourceManager.GetString("زهره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zahak.
        /// </summary>
        internal static string زهک {
            get {
                return ResourceManager.GetString("زهک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zavareh.
        /// </summary>
        internal static string زواره {
            get {
                return ResourceManager.GetString("زواره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ziyaratali.
        /// </summary>
        internal static string زیارتعلی {
            get {
                return ResourceManager.GetString("زیارتعلی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZibaShahr.
        /// </summary>
        internal static string زیباشهر {
            get {
                return ResourceManager.GetString("زیباشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZeidAbad.
        /// </summary>
        internal static string زیدآباد {
            get {
                return ResourceManager.GetString("زیدآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zirab.
        /// </summary>
        internal static string زیرآب {
            get {
                return ResourceManager.GetString("زیرآب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saroogh.
        /// </summary>
        internal static string ساروق {
            get {
                return ResourceManager.GetString("ساروق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sari.
        /// </summary>
        internal static string ساری {
            get {
                return ResourceManager.GetString("ساری", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saland.
        /// </summary>
        internal static string سالند {
            get {
                return ResourceManager.GetString("سالند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saman.
        /// </summary>
        internal static string سامان {
            get {
                return ResourceManager.GetString("سامان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Samen.
        /// </summary>
        internal static string سامن {
            get {
                return ResourceManager.GetString("سامن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saveh.
        /// </summary>
        internal static string ساوه {
            get {
                return ResourceManager.GetString("ساوه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sabzevar.
        /// </summary>
        internal static string سبزوار {
            get {
                return ResourceManager.GetString("سبزوار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SepidDasht.
        /// </summary>
        internal static string سپیددشت {
            get {
                return ResourceManager.GetString("سپیددشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sojas.
        /// </summary>
        internal static string سجاس {
            get {
                return ResourceManager.GetString("سجاس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sedeh.
        /// </summary>
        internal static string سده {
            get {
                return ResourceManager.GetString("سده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SedehLenjan.
        /// </summary>
        internal static string سده_لنجان {
            get {
                return ResourceManager.GetString("سده لنجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarab.
        /// </summary>
        internal static string سراب {
            get {
                return ResourceManager.GetString("سراب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SarabBagh.
        /// </summary>
        internal static string سراب_باغ {
            get {
                return ResourceManager.GetString("سراب باغ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarabdoreh.
        /// </summary>
        internal static string سراب_دوره {
            get {
                return ResourceManager.GetString("سراب دوره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarableh.
        /// </summary>
        internal static string سرابله {
            get {
                return ResourceManager.GetString("سرابله", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saravan.
        /// </summary>
        internal static string سراوان {
            get {
                return ResourceManager.GetString("سراوان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarayan.
        /// </summary>
        internal static string سرایان {
            get {
                return ResourceManager.GetString("سرایان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarbaz.
        /// </summary>
        internal static string سرباز {
            get {
                return ResourceManager.GetString("سرباز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarbisheh.
        /// </summary>
        internal static string سربیشه {
            get {
                return ResourceManager.GetString("سربیشه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarpolzahab.
        /// </summary>
        internal static string سرپل_ذهاب {
            get {
                return ResourceManager.GetString("سرپل ذهاب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorkhrood.
        /// </summary>
        internal static string سرخرود {
            get {
                return ResourceManager.GetString("سرخرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarakhs.
        /// </summary>
        internal static string سرخس {
            get {
                return ResourceManager.GetString("سرخس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorkhankalateh.
        /// </summary>
        internal static string سرخنکلاته {
            get {
                return ResourceManager.GetString("سرخنکلاته", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorkheh.
        /// </summary>
        internal static string سرخه {
            get {
                return ResourceManager.GetString("سرخه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarkhoon.
        /// </summary>
        internal static string سرخون {
            get {
                return ResourceManager.GetString("سرخون", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sardrood.
        /// </summary>
        internal static string سردرود {
            get {
                return ResourceManager.GetString("سردرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sardasht.
        /// </summary>
        internal static string سردشت {
            get {
                return ResourceManager.GetString("سردشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarein.
        /// </summary>
        internal static string سرعین {
            get {
                return ResourceManager.GetString("سرعین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serkan.
        /// </summary>
        internal static string سرکان {
            get {
                return ResourceManager.GetString("سرکان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sargaz.
        /// </summary>
        internal static string سرگز {
            get {
                return ResourceManager.GetString("سرگز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarmast.
        /// </summary>
        internal static string سرمست {
            get {
                return ResourceManager.GetString("سرمست", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sero.
        /// </summary>
        internal static string سرو {
            get {
                return ResourceManager.GetString("سرو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SarvAbad.
        /// </summary>
        internal static string سروآباد {
            get {
                return ResourceManager.GetString("سروآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sarvestan.
        /// </summary>
        internal static string سروستان {
            get {
                return ResourceManager.GetString("سروستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SerishAbad.
        /// </summary>
        internal static string سریش_آباد {
            get {
                return ResourceManager.GetString("سریش آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Satar.
        /// </summary>
        internal static string سطر {
            get {
                return ResourceManager.GetString("سطر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SaadatShahr.
        /// </summary>
        internal static string سعادت_شهر {
            get {
                return ResourceManager.GetString("سعادت شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SaedAbad.
        /// </summary>
        internal static string سعد_آباد {
            get {
                return ResourceManager.GetString("سعد آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sefiddasht.
        /// </summary>
        internal static string سفیددشت {
            get {
                return ResourceManager.GetString("سفیددشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sefidsang.
        /// </summary>
        internal static string سفیدسنگ {
            get {
                return ResourceManager.GetString("سفیدسنگ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SefidShahr.
        /// </summary>
        internal static string سفیدشهر {
            get {
                return ResourceManager.GetString("سفیدشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saghez.
        /// </summary>
        internal static string سقز {
            get {
                return ResourceManager.GetString("سقز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SagzAbad.
        /// </summary>
        internal static string سگزآباد {
            get {
                return ResourceManager.GetString("سگزآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Segzi.
        /// </summary>
        internal static string سگزی {
            get {
                return ResourceManager.GetString("سگزی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salami.
        /// </summary>
        internal static string سلامی {
            get {
                return ResourceManager.GetString("سلامی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SoltanAbad.
        /// </summary>
        internal static string سلطان_آباد {
            get {
                return ResourceManager.GetString("سلطان آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Soltanieh.
        /// </summary>
        internal static string سلطانیه {
            get {
                return ResourceManager.GetString("سلطانیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salafchegan.
        /// </summary>
        internal static string سلفچگان {
            get {
                return ResourceManager.GetString("سلفچگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salmas.
        /// </summary>
        internal static string سلماس {
            get {
                return ResourceManager.GetString("سلماس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SalmanShahr.
        /// </summary>
        internal static string سلمان_شهر {
            get {
                return ResourceManager.GetString("سلمان شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Somaleh.
        /// </summary>
        internal static string سماله {
            get {
                return ResourceManager.GetString("سماله", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Semnan.
        /// </summary>
        internal static string سمنان {
            get {
                return ResourceManager.GetString("سمنان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Samirom.
        /// </summary>
        internal static string سمیرم {
            get {
                return ResourceManager.GetString("سمیرم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Senjan.
        /// </summary>
        internal static string سنجان {
            get {
                return ResourceManager.GetString("سنجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sankhast.
        /// </summary>
        internal static string سنخواست {
            get {
                return ResourceManager.GetString("سنخواست", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Senderk.
        /// </summary>
        internal static string سندرک {
            get {
                return ResourceManager.GetString("سندرک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Songhor.
        /// </summary>
        internal static string سنقر {
            get {
                return ResourceManager.GetString("سنقر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sangan.
        /// </summary>
        internal static string سنگان {
            get {
                return ResourceManager.GetString("سنگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sangar.
        /// </summary>
        internal static string سنگر {
            get {
                return ResourceManager.GetString("سنگر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sanandaj.
        /// </summary>
        internal static string سنندج {
            get {
                return ResourceManager.GetString("سنندج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Se ghale.
        /// </summary>
        internal static string سه_قلعه {
            get {
                return ResourceManager.GetString("سه قلعه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sohrevard.
        /// </summary>
        internal static string سهرورد {
            get {
                return ResourceManager.GetString("سهرورد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sahand.
        /// </summary>
        internal static string سهند {
            get {
                return ResourceManager.GetString("سهند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Soodejan.
        /// </summary>
        internal static string سودجان {
            get {
                return ResourceManager.GetString("سودجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sooran.
        /// </summary>
        internal static string سوران {
            get {
                return ResourceManager.GetString("سوران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sooreshjan.
        /// </summary>
        internal static string سورشجان {
            get {
                return ResourceManager.GetString("سورشجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorak.
        /// </summary>
        internal static string سورک {
            get {
                return ResourceManager.GetString("سورک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Soormagh.
        /// </summary>
        internal static string سورمق {
            get {
                return ResourceManager.GetString("سورمق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sooza.
        /// </summary>
        internal static string سوزا {
            get {
                return ResourceManager.GetString("سوزا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Soosangerd.
        /// </summary>
        internal static string سوسنگرد {
            get {
                return ResourceManager.GetString("سوسنگرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sugh.
        /// </summary>
        internal static string سوق {
            get {
                return ResourceManager.GetString("سوق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Soomar.
        /// </summary>
        internal static string سومار {
            get {
                return ResourceManager.GetString("سومار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sisakht.
        /// </summary>
        internal static string سی_سخت {
            get {
                return ResourceManager.GetString("سی سخت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Siahkal.
        /// </summary>
        internal static string سیاهکل {
            get {
                return ResourceManager.GetString("سیاهکل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seyedan.
        /// </summary>
        internal static string سیدان {
            get {
                return ResourceManager.GetString("سیدان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Siraf.
        /// </summary>
        internal static string سیراف {
            get {
                return ResourceManager.GetString("سیراف", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sirjan.
        /// </summary>
        internal static string سیرجان {
            get {
                return ResourceManager.GetString("سیرجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sirdan.
        /// </summary>
        internal static string سیردان {
            get {
                return ResourceManager.GetString("سیردان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sirkan.
        /// </summary>
        internal static string سیرکان {
            get {
                return ResourceManager.GetString("سیرکان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sirik.
        /// </summary>
        internal static string سیریک {
            get {
                return ResourceManager.GetString("سیریک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sis.
        /// </summary>
        internal static string سیس {
            get {
                return ResourceManager.GetString("سیس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Silvaneh.
        /// </summary>
        internal static string سیلوانه {
            get {
                return ResourceManager.GetString("سیلوانه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SiminShahr.
        /// </summary>
        internal static string سیمین_شهر {
            get {
                return ResourceManager.GetString("سیمین شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Simineh.
        /// </summary>
        internal static string سیمینه {
            get {
                return ResourceManager.GetString("سیمینه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Siyahcheshmeh.
        /// </summary>
        internal static string سیه_چشمه {
            get {
                return ResourceManager.GetString("سیه چشمه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Siahrood.
        /// </summary>
        internal static string سیه_رود {
            get {
                return ResourceManager.GetString("سیه رود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ShapoorAbad.
        /// </summary>
        internal static string شاپورآباد {
            get {
                return ResourceManager.GetString("شاپورآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shadegan.
        /// </summary>
        internal static string شادگان {
            get {
                return ResourceManager.GetString("شادگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shadmehr.
        /// </summary>
        internal static string شادمهر {
            get {
                return ResourceManager.GetString("شادمهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shazand.
        /// </summary>
        internal static string شازند {
            get {
                return ResourceManager.GetString("شازند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shal.
        /// </summary>
        internal static string شال {
            get {
                return ResourceManager.GetString("شال", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shandiz.
        /// </summary>
        internal static string شاندیز {
            get {
                return ResourceManager.GetString("شاندیز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ShahedShahr.
        /// </summary>
        internal static string شاهدشهر {
            get {
                return ResourceManager.GetString("شاهدشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahedie.
        /// </summary>
        internal static string شاهدیه {
            get {
                return ResourceManager.GetString("شاهدیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahrood.
        /// </summary>
        internal static string شاهرود {
            get {
                return ResourceManager.GetString("شاهرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahoo.
        /// </summary>
        internal static string شاهو {
            get {
                return ResourceManager.GetString("شاهو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahindezh.
        /// </summary>
        internal static string شاهین_دژ {
            get {
                return ResourceManager.GetString("شاهین دژ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ShahinShahr.
        /// </summary>
        internal static string شاهین_شهر {
            get {
                return ResourceManager.GetString("شاهین شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shavoor.
        /// </summary>
        internal static string شاوور {
            get {
                return ResourceManager.GetString("شاوور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shabankareh.
        /// </summary>
        internal static string شبانکاره {
            get {
                return ResourceManager.GetString("شبانکاره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shabestar.
        /// </summary>
        internal static string شبستر {
            get {
                return ResourceManager.GetString("شبستر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sherafat.
        /// </summary>
        internal static string شرافت {
            get {
                return ResourceManager.GetString("شرافت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sharabian.
        /// </summary>
        internal static string شربیان {
            get {
                return ResourceManager.GetString("شربیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sharafkhaneh.
        /// </summary>
        internal static string شرفخانه {
            get {
                return ResourceManager.GetString("شرفخانه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SharifAbad.
        /// </summary>
        internal static string شریف_آباد {
            get {
                return ResourceManager.GetString("شریف آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sharifieh.
        /// </summary>
        internal static string شریفیه {
            get {
                return ResourceManager.GetString("شریفیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sheshtamad.
        /// </summary>
        internal static string ششتمد {
            get {
                return ResourceManager.GetString("ششتمد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sheshdeh.
        /// </summary>
        internal static string ششده {
            get {
                return ResourceManager.GetString("ششده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shaft.
        /// </summary>
        internal static string شفت {
            get {
                return ResourceManager.GetString("شفت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shelman.
        /// </summary>
        internal static string شلمان {
            get {
                return ResourceManager.GetString("شلمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shalamzar.
        /// </summary>
        internal static string شلمزار {
            get {
                return ResourceManager.GetString("شلمزار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shemshak.
        /// </summary>
        internal static string شمشک {
            get {
                return ResourceManager.GetString("شمشک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shanbeh.
        /// </summary>
        internal static string شنبه {
            get {
                return ResourceManager.GetString("شنبه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ShendAbad.
        /// </summary>
        internal static string شندآباد {
            get {
                return ResourceManager.GetString("شندآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahbaz.
        /// </summary>
        internal static string شهباز {
            get {
                return ResourceManager.GetString("شهباز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahdad.
        /// </summary>
        internal static string شهداد {
            get {
                return ResourceManager.GetString("شهداد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ShahrAbad.
        /// </summary>
        internal static string شهرآباد {
            get {
                return ResourceManager.GetString("شهرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahrebabak.
        /// </summary>
        internal static string شهربابک {
            get {
                return ResourceManager.GetString("شهربابک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahrepir.
        /// </summary>
        internal static string شهرپیر {
            get {
                return ResourceManager.GetString("شهرپیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parand.
        /// </summary>
        internal static string شهرجدید_پرند {
            get {
                return ResourceManager.GetString("شهرجدید پرند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ShahrejadidHashtgerd.
        /// </summary>
        internal static string شهرجدیدهشتگرد {
            get {
                return ResourceManager.GetString("شهرجدیدهشتگرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahrezoo.
        /// </summary>
        internal static string شهرزو {
            get {
                return ResourceManager.GetString("شهرزو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahrsadra.
        /// </summary>
        internal static string شهرصدرا {
            get {
                return ResourceManager.GetString("شهرصدرا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahreza.
        /// </summary>
        internal static string شهرضا {
            get {
                return ResourceManager.GetString("شهرضا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahrekord.
        /// </summary>
        internal static string شهرکرد {
            get {
                return ResourceManager.GetString("شهرکرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahryar.
        /// </summary>
        internal static string شهریار {
            get {
                return ResourceManager.GetString("شهریار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shahmirzad.
        /// </summary>
        internal static string شهمیرزاد {
            get {
                return ResourceManager.GetString("شهمیرزاد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shoosf.
        /// </summary>
        internal static string شوسف {
            get {
                return ResourceManager.GetString("شوسف", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shoosh.
        /// </summary>
        internal static string شوش {
            get {
                return ResourceManager.GetString("شوش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shooshtar.
        /// </summary>
        internal static string شوشتر {
            get {
                return ResourceManager.GetString("شوشتر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shoot.
        /// </summary>
        internal static string شوط {
            get {
                return ResourceManager.GetString("شوط", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shoghan.
        /// </summary>
        internal static string شوقان {
            get {
                return ResourceManager.GetString("شوقان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SholAbad.
        /// </summary>
        internal static string شول_آباد {
            get {
                return ResourceManager.GetString("شول آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shoysheh.
        /// </summary>
        internal static string شویشه {
            get {
                return ResourceManager.GetString("شویشه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shiban.
        /// </summary>
        internal static string شیبان {
            get {
                return ResourceManager.GetString("شیبان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shiraz.
        /// </summary>
        internal static string شیراز {
            get {
                return ResourceManager.GetString("شیراز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shirgah.
        /// </summary>
        internal static string شیرگاه {
            get {
                return ResourceManager.GetString("شیرگاه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shirvan.
        /// </summary>
        internal static string شیروان {
            get {
                return ResourceManager.GetString("شیروان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shirood.
        /// </summary>
        internal static string شیرود {
            get {
                return ResourceManager.GetString("شیرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shirinso.
        /// </summary>
        internal static string شیرین_سو {
            get {
                return ResourceManager.GetString("شیرین سو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saheb.
        /// </summary>
        internal static string صاحب {
            get {
                return ResourceManager.GetString("صاحب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SalehAbad.
        /// </summary>
        internal static string صالح_آباد {
            get {
                return ResourceManager.GetString("صالح آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SalehShahr.
        /// </summary>
        internal static string صالح_شهر {
            get {
                return ResourceManager.GetString("صالح شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salehmoshatat.
        /// </summary>
        internal static string صالح_مشطت {
            get {
                return ResourceManager.GetString("صالح مشطت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saeenghale.
        /// </summary>
        internal static string صایین_قلعه {
            get {
                return ResourceManager.GetString("صایین قلعه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SabaShahr.
        /// </summary>
        internal static string صباشهر {
            get {
                return ResourceManager.GetString("صباشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sahneh.
        /// </summary>
        internal static string صحنه {
            get {
                return ResourceManager.GetString("صحنه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Soghad.
        /// </summary>
        internal static string صغاد {
            get {
                return ResourceManager.GetString("صغاد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Safadasht.
        /// </summary>
        internal static string صفادشت {
            get {
                return ResourceManager.GetString("صفادشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SafaShahr.
        /// </summary>
        internal static string صفاشهر {
            get {
                return ResourceManager.GetString("صفاشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Safaaeeyeh.
        /// </summary>
        internal static string صفاییه {
            get {
                return ResourceManager.GetString("صفاییه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SafiAbad.
        /// </summary>
        internal static string صفی_آباد {
            get {
                return ResourceManager.GetString("صفی آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Samsami.
        /// </summary>
        internal static string صمصامی {
            get {
                return ResourceManager.GetString("صمصامی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Soofian.
        /// </summary>
        internal static string صوفیان {
            get {
                return ResourceManager.GetString("صوفیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Somesara.
        /// </summary>
        internal static string صومعه_سرا {
            get {
                return ResourceManager.GetString("صومعه سرا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seydun.
        /// </summary>
        internal static string صیدون {
            get {
                return ResourceManager.GetString("صیدون", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZiaAbad.
        /// </summary>
        internal static string ضیاءآباد {
            get {
                return ResourceManager.GetString("ضیاءآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taghanak.
        /// </summary>
        internal static string طاقانک {
            get {
                return ResourceManager.GetString("طاقانک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talkhoonche.
        /// </summary>
        internal static string طالخونچه {
            get {
                return ResourceManager.GetString("طالخونچه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taleghan.
        /// </summary>
        internal static string طالقان {
            get {
                return ResourceManager.GetString("طالقان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tabas.
        /// </summary>
        internal static string طبس {
            get {
                return ResourceManager.GetString("طبس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tabasmasina.
        /// </summary>
        internal static string طبس_مسینا {
            get {
                return ResourceManager.GetString("طبس مسینا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Torghabeh.
        /// </summary>
        internal static string طرقبه {
            get {
                return ResourceManager.GetString("طرقبه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AbbasAbad.
        /// </summary>
        internal static string عباس_آباد {
            get {
                return ResourceManager.GetString("عباس آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ajabshir.
        /// </summary>
        internal static string عجب_شیر {
            get {
                return ResourceManager.GetString("عجب شیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Asgaran.
        /// </summary>
        internal static string عسگران {
            get {
                return ResourceManager.GetString("عسگران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Asaloyeh.
        /// </summary>
        internal static string عسلویه {
            get {
                return ResourceManager.GetString("عسلویه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EshghAbad.
        /// </summary>
        internal static string عشق_آباد {
            get {
                return ResourceManager.GetString("عشق آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aghda.
        /// </summary>
        internal static string عقدا {
            get {
                return ResourceManager.GetString("عقدا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alamarvdasht.
        /// </summary>
        internal static string علامرودشت {
            get {
                return ResourceManager.GetString("علامرودشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alavijeh.
        /// </summary>
        internal static string علویجه {
            get {
                return ResourceManager.GetString("علویجه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AliAbad.
        /// </summary>
        internal static string علی_آباد {
            get {
                return ResourceManager.GetString("علی آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aliakbar.
        /// </summary>
        internal static string علی_اکبر {
            get {
                return ResourceManager.GetString("علی اکبر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Emadodeh.
        /// </summary>
        internal static string عمادده {
            get {
                return ResourceManager.GetString("عمادده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AnbarAbad.
        /// </summary>
        internal static string عنبرآباد {
            get {
                return ResourceManager.GetString("عنبرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anbaran.
        /// </summary>
        internal static string عنبران {
            get {
                return ResourceManager.GetString("عنبران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GharghAbad.
        /// </summary>
        internal static string غرق_آباد {
            get {
                return ResourceManager.GetString("غرق آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Farsan.
        /// </summary>
        internal static string فارسان {
            get {
                return ResourceManager.GetString("فارسان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Farghan.
        /// </summary>
        internal static string فارغان {
            get {
                return ResourceManager.GetString("فارغان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Farooj.
        /// </summary>
        internal static string فاروج {
            get {
                return ResourceManager.GetString("فاروج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Faryab.
        /// </summary>
        internal static string فاریاب {
            get {
                return ResourceManager.GetString("فاریاب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FazelAbad.
        /// </summary>
        internal static string فاضل_آباد {
            get {
                return ResourceManager.GetString("فاضل آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Famenin.
        /// </summary>
        internal static string فامنین {
            get {
                return ResourceManager.GetString("فامنین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FakhrAbad.
        /// </summary>
        internal static string فخرآباد {
            get {
                return ResourceManager.GetString("فخرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fadami.
        /// </summary>
        internal static string فدامی {
            get {
                return ResourceManager.GetString("فدامی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Faradonbeh.
        /// </summary>
        internal static string فرادنبه {
            get {
                return ResourceManager.GetString("فرادنبه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Farashband.
        /// </summary>
        internal static string فراشبند {
            get {
                return ResourceManager.GetString("فراشبند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FarrokhShahr.
        /// </summary>
        internal static string فرخ_شهر {
            get {
                return ResourceManager.GetString("فرخ شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Farkhi.
        /// </summary>
        internal static string فرخی {
            get {
                return ResourceManager.GetString("فرخی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ferdos.
        /// </summary>
        internal static string فردوس {
            get {
                return ResourceManager.GetString("فردوس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ferdosieh.
        /// </summary>
        internal static string فردوسیه {
            get {
                return ResourceManager.GetString("فردوسیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fardis.
        /// </summary>
        internal static string فردیس {
            get {
                return ResourceManager.GetString("فردیس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Faresfaj.
        /// </summary>
        internal static string فرسفج {
            get {
                return ResourceManager.GetString("فرسفج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Farmahin.
        /// </summary>
        internal static string فرمهین {
            get {
                return ResourceManager.GetString("فرمهین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Farhadgar.
        /// </summary>
        internal static string فرهادگرد {
            get {
                return ResourceManager.GetString("فرهادگرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FrounAbad.
        /// </summary>
        internal static string فرون_آباد {
            get {
                return ResourceManager.GetString("فرون آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FereidoonShahr.
        /// </summary>
        internal static string فریدونشهر {
            get {
                return ResourceManager.GetString("فریدونشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fereidoonkenar.
        /// </summary>
        internal static string فریدونکنار {
            get {
                return ResourceManager.GetString("فریدونکنار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Farim.
        /// </summary>
        internal static string فریم {
            get {
                return ResourceManager.GetString("فریم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fariman.
        /// </summary>
        internal static string فریمان {
            get {
                return ResourceManager.GetString("فریمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fasa.
        /// </summary>
        internal static string فسا {
            get {
                return ResourceManager.GetString("فسا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fasham.
        /// </summary>
        internal static string فشم {
            get {
                return ResourceManager.GetString("فشم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Falavarjan.
        /// </summary>
        internal static string فلاورجان {
            get {
                return ResourceManager.GetString("فلاورجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fanooj.
        /// </summary>
        internal static string فنوج {
            get {
                return ResourceManager.GetString("فنوج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fahraj.
        /// </summary>
        internal static string فهرج {
            get {
                return ResourceManager.GetString("فهرج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FooladShahr.
        /// </summary>
        internal static string فولادشهر {
            get {
                return ResourceManager.GetString("فولادشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fooman.
        /// </summary>
        internal static string فومن {
            get {
                return ResourceManager.GetString("فومن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Firooragh.
        /// </summary>
        internal static string فیرورق {
            get {
                return ResourceManager.GetString("فیرورق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FiroozAbad.
        /// </summary>
        internal static string فیروزآباد {
            get {
                return ResourceManager.GetString("فیروزآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Firoozan.
        /// </summary>
        internal static string فیروزان {
            get {
                return ResourceManager.GetString("فیروزان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Firoozkooh.
        /// </summary>
        internal static string فیروزکوه {
            get {
                return ResourceManager.GetString("فیروزکوه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Firuzeh.
        /// </summary>
        internal static string فیروزه {
            get {
                return ResourceManager.GetString("فیروزه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FeizAbad.
        /// </summary>
        internal static string فیض_آباد {
            get {
                return ResourceManager.GetString("فیض آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fin.
        /// </summary>
        internal static string فین {
            get {
                return ResourceManager.GetString("فین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GhaderAbad.
        /// </summary>
        internal static string قادرآباد {
            get {
                return ResourceManager.GetString("قادرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GhasemAbad.
        /// </summary>
        internal static string قاسم_آباد {
            get {
                return ResourceManager.GetString("قاسم آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghazi.
        /// </summary>
        internal static string قاضی {
            get {
                return ResourceManager.GetString("قاضی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GhaemShahr.
        /// </summary>
        internal static string قائم_شهر {
            get {
                return ResourceManager.GetString("قائم شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghaemieh.
        /// </summary>
        internal static string قایمیه {
            get {
                return ResourceManager.GetString("قایمیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghaen.
        /// </summary>
        internal static string قاین {
            get {
                return ResourceManager.GetString("قاین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghods.
        /// </summary>
        internal static string قدس {
            get {
                return ResourceManager.GetString("قدس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghadamgah.
        /// </summary>
        internal static string قدمگاه {
            get {
                return ResourceManager.GetString("قدمگاه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gharchak.
        /// </summary>
        internal static string قرچک {
            get {
                return ResourceManager.GetString("قرچک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gharehaghaj.
        /// </summary>
        internal static string قره_آغاج {
            get {
                return ResourceManager.GetString("قره آغاج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GhareZiaDin.
        /// </summary>
        internal static string قره_ضیاء_الدین {
            get {
                return ResourceManager.GetString("قره ضیاء الدین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghorveh.
        /// </summary>
        internal static string قروه {
            get {
                return ResourceManager.GetString("قروه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gharvedarjazin.
        /// </summary>
        internal static string قروه_درجزین {
            get {
                return ResourceManager.GetString("قروه درجزین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qazvin.
        /// </summary>
        internal static string قزوین {
            get {
                return ResourceManager.GetString("قزوین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gheshm.
        /// </summary>
        internal static string قشم {
            get {
                return ResourceManager.GetString("قشم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghasreshirin.
        /// </summary>
        internal static string قصرشیرین {
            get {
                return ResourceManager.GetString("قصرشیرین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghasreghand.
        /// </summary>
        internal static string قصرقند {
            get {
                return ResourceManager.GetString("قصرقند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GhotbAbad.
        /// </summary>
        internal static string قطب_آباد {
            get {
                return ResourceManager.GetString("قطب آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghatruyeh.
        /// </summary>
        internal static string قطرویه {
            get {
                return ResourceManager.GetString("قطرویه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghotur.
        /// </summary>
        internal static string قطور {
            get {
                return ResourceManager.GetString("قطور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghaletol.
        /// </summary>
        internal static string قلعه_تل {
            get {
                return ResourceManager.GetString("قلعه تل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghalekhajeh.
        /// </summary>
        internal static string قلعه_خواجه {
            get {
                return ResourceManager.GetString("قلعه خواجه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghaleraeesi.
        /// </summary>
        internal static string قلعه_رییسی {
            get {
                return ResourceManager.GetString("قلعه رییسی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghaleghazi.
        /// </summary>
        internal static string قلعه_قاضی {
            get {
                return ResourceManager.GetString("قلعه قاضی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghaleganj.
        /// </summary>
        internal static string قلعه_گنج {
            get {
                return ResourceManager.GetString("قلعه گنج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GhalandarAbad.
        /// </summary>
        internal static string قلندرآباد {
            get {
                return ResourceManager.GetString("قلندرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qom.
        /// </summary>
        internal static string قم {
            get {
                return ResourceManager.GetString("قم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghamsar.
        /// </summary>
        internal static string قمصر {
            get {
                return ResourceManager.GetString("قمصر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghanavat.
        /// </summary>
        internal static string قنوات {
            get {
                return ResourceManager.GetString("قنوات", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghahavand.
        /// </summary>
        internal static string قهاوند {
            get {
                return ResourceManager.GetString("قهاوند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghahderijan.
        /// </summary>
        internal static string قهدریجان {
            get {
                return ResourceManager.GetString("قهدریجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghohestan.
        /// </summary>
        internal static string قهستان {
            get {
                return ResourceManager.GetString("قهستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghoochan.
        /// </summary>
        internal static string قوچان {
            get {
                return ResourceManager.GetString("قوچان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghoorchebashi.
        /// </summary>
        internal static string قورچی_باشی {
            get {
                return ResourceManager.GetString("قورچی باشی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ghooshchi.
        /// </summary>
        internal static string قوشچی {
            get {
                return ResourceManager.GetString("قوشچی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gheidar.
        /// </summary>
        internal static string قیدار {
            get {
                return ResourceManager.GetString("قیدار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gheer.
        /// </summary>
        internal static string قیر {
            get {
                return ResourceManager.GetString("قیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaj.
        /// </summary>
        internal static string کاج {
            get {
                return ResourceManager.GetString("کاج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kakhak.
        /// </summary>
        internal static string کاخک {
            get {
                return ResourceManager.GetString("کاخک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Karchan.
        /// </summary>
        internal static string کارچان {
            get {
                return ResourceManager.GetString("کارچان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Karzin.
        /// </summary>
        internal static string کارزین_2_ {
            get {
                return ResourceManager.GetString("کارزین[2]", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kariz.
        /// </summary>
        internal static string کاریز {
            get {
                return ResourceManager.GetString("کاریز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kazeroon.
        /// </summary>
        internal static string کازرون {
            get {
                return ResourceManager.GetString("کازرون", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kashan.
        /// </summary>
        internal static string کاشان {
            get {
                return ResourceManager.GetString("کاشان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kashmar.
        /// </summary>
        internal static string کاشمر {
            get {
                return ResourceManager.GetString("کاشمر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KazemAbad.
        /// </summary>
        internal static string کاظم_آباد {
            get {
                return ResourceManager.GetString("کاظم آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaki.
        /// </summary>
        internal static string کاکی {
            get {
                return ResourceManager.GetString("کاکی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaamfirooz.
        /// </summary>
        internal static string کامفیروز {
            get {
                return ResourceManager.GetString("کامفیروز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kamyaran.
        /// </summary>
        internal static string کامیاران {
            get {
                return ResourceManager.GetString("کامیاران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kanidinar.
        /// </summary>
        internal static string کانی_دینار {
            get {
                return ResourceManager.GetString("کانی دینار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kanisor.
        /// </summary>
        internal static string کانی_سور {
            get {
                return ResourceManager.GetString("کانی سور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kabodarahang.
        /// </summary>
        internal static string کبودرآهنگ {
            get {
                return ResourceManager.GetString("کبودرآهنگ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Katalom.
        /// </summary>
        internal static string کتالم_وسادات_شهر {
            get {
                return ResourceManager.GetString("کتالم وسادات شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kodkan.
        /// </summary>
        internal static string کدکن {
            get {
                return ResourceManager.GetString("کدکن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Karaj.
        /// </summary>
        internal static string کرج {
            get {
                return ResourceManager.GetString("کرج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KordKooy.
        /// </summary>
        internal static string کردکوی {
            get {
                return ResourceManager.GetString("کردکوی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Karkevand.
        /// </summary>
        internal static string کرکوند {
            get {
                return ResourceManager.GetString("کرکوند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kerman.
        /// </summary>
        internal static string کرمان {
            get {
                return ResourceManager.GetString("کرمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kermanshah.
        /// </summary>
        internal static string کرمانشاه {
            get {
                return ResourceManager.GetString("کرمانشاه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kerend.
        /// </summary>
        internal static string کرند {
            get {
                return ResourceManager.GetString("کرند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Karehi.
        /// </summary>
        internal static string کره_ای {
            get {
                return ResourceManager.GetString("کره ای", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Karahrood.
        /// </summary>
        internal static string کرهرود {
            get {
                return ResourceManager.GetString("کرهرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Keshavarz.
        /// </summary>
        internal static string کشاورز {
            get {
                return ResourceManager.GetString("کشاورز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kashksaray.
        /// </summary>
        internal static string کشکسرای {
            get {
                return ResourceManager.GetString("کشکسرای", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Koshkooeeyeh.
        /// </summary>
        internal static string کشکوییه {
            get {
                return ResourceManager.GetString("کشکوییه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kalat.
        /// </summary>
        internal static string کلات {
            get {
                return ResourceManager.GetString("کلات", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kalatehkhij.
        /// </summary>
        internal static string کلاته_خیج {
            get {
                return ResourceManager.GetString("کلاته خیج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kalachay.
        /// </summary>
        internal static string کلاچای {
            get {
                return ResourceManager.GetString("کلاچای", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KelarAbad.
        /// </summary>
        internal static string کلارآباد {
            get {
                return ResourceManager.GetString("کلارآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kelardasht.
        /// </summary>
        internal static string کلاردشت {
            get {
                return ResourceManager.GetString("کلاردشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kolaleh.
        /// </summary>
        internal static string کلاله {
            get {
                return ResourceManager.GetString("کلاله", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kalameh.
        /// </summary>
        internal static string کلمه {
            get {
                return ResourceManager.GetString("کلمه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kalebast.
        /// </summary>
        internal static string کله_بست {
            get {
                return ResourceManager.GetString("کله بست", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kalvangh.
        /// </summary>
        internal static string کلوانق {
            get {
                return ResourceManager.GetString("کلوانق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaloor.
        /// </summary>
        internal static string کلور {
            get {
                return ResourceManager.GetString("کلور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kaleibar.
        /// </summary>
        internal static string کلیبر {
            get {
                return ResourceManager.GetString("کلیبر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KelishadSooderjan.
        /// </summary>
        internal static string کلیشادوسودرجان {
            get {
                return ResourceManager.GetString("کلیشادوسودرجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KamalShahr.
        /// </summary>
        internal static string کمال_شهر {
            get {
                return ResourceManager.GetString("کمال شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Komeshcheh.
        /// </summary>
        internal static string کمشچه {
            get {
                return ResourceManager.GetString("کمشچه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kameh.
        /// </summary>
        internal static string کمه {
            get {
                return ResourceManager.GetString("کمه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Komijan.
        /// </summary>
        internal static string کمیجان {
            get {
                return ResourceManager.GetString("کمیجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kenartakhteh.
        /// </summary>
        internal static string کنارتخته {
            get {
                return ResourceManager.GetString("کنارتخته", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Konarak.
        /// </summary>
        internal static string کنارک {
            get {
                return ResourceManager.GetString("کنارک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kondor.
        /// </summary>
        internal static string کندر {
            get {
                return ResourceManager.GetString("کندر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kong.
        /// </summary>
        internal static string کنگ {
            get {
                return ResourceManager.GetString("کنگ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kangavar.
        /// </summary>
        internal static string کنگاور {
            get {
                return ResourceManager.GetString("کنگاور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KahrizSang.
        /// </summary>
        internal static string کهریزسنگ {
            get {
                return ResourceManager.GetString("کهریزسنگ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kahrizak.
        /// </summary>
        internal static string کهریزک {
            get {
                return ResourceManager.GetString("کهریزک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kahak.
        /// </summary>
        internal static string کهک {
            get {
                return ResourceManager.GetString("کهک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kahnooj.
        /// </summary>
        internal static string کهنوج {
            get {
                return ResourceManager.GetString("کهنوج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kovar.
        /// </summary>
        internal static string کوار {
            get {
                return ResourceManager.GetString("کوار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Koochesfehan.
        /// </summary>
        internal static string کوچصفهان {
            get {
                return ResourceManager.GetString("کوچصفهان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kooraeem.
        /// </summary>
        internal static string کوراییم {
            get {
                return ResourceManager.GetString("کوراییم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kozaran.
        /// </summary>
        internal static string کوزران {
            get {
                return ResourceManager.GetString("کوزران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kuzehkonan.
        /// </summary>
        internal static string کوزه_کنان {
            get {
                return ResourceManager.GetString("کوزه کنان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kooshk.
        /// </summary>
        internal static string کوشک {
            get {
                return ResourceManager.GetString("کوشک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kushkenar.
        /// </summary>
        internal static string کوشکنار {
            get {
                return ResourceManager.GetString("کوشکنار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Koomleh.
        /// </summary>
        internal static string کومله {
            get {
                return ResourceManager.GetString("کومله", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Konani.
        /// </summary>
        internal static string کونانی {
            get {
                return ResourceManager.GetString("کونانی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Koohbanan.
        /// </summary>
        internal static string کوهبنان {
            get {
                return ResourceManager.GetString("کوهبنان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Koohpayeh.
        /// </summary>
        internal static string کوهپایه {
            get {
                return ResourceManager.GetString("کوهپایه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Koohdasht.
        /// </summary>
        internal static string کوهدشت {
            get {
                return ResourceManager.GetString("کوهدشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Koohsar.
        /// </summary>
        internal static string کوهسار {
            get {
                return ResourceManager.GetString("کوهسار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Koohenjan.
        /// </summary>
        internal static string کوهنجان {
            get {
                return ResourceManager.GetString("کوهنجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kohikheyl.
        /// </summary>
        internal static string کوهی_خیل {
            get {
                return ResourceManager.GetString("کوهی خیل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kouhin.
        /// </summary>
        internal static string کوهین {
            get {
                return ResourceManager.GetString("کوهین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kiyasar.
        /// </summary>
        internal static string کیاسر {
            get {
                return ResourceManager.GetString("کیاسر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KiaShahr.
        /// </summary>
        internal static string کیاشهر {
            get {
                return ResourceManager.GetString("کیاشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kiakala.
        /// </summary>
        internal static string کیاکلا {
            get {
                return ResourceManager.GetString("کیاکلا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kian.
        /// </summary>
        internal static string کیان {
            get {
                return ResourceManager.GetString("کیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KianShahr.
        /// </summary>
        internal static string کیانشهر {
            get {
                return ResourceManager.GetString("کیانشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kish.
        /// </summary>
        internal static string کیش {
            get {
                return ResourceManager.GetString("کیش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kilan.
        /// </summary>
        internal static string کیلان {
            get {
                return ResourceManager.GetString("کیلان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Galikesh.
        /// </summary>
        internal static string گالیکش {
            get {
                return ResourceManager.GetString("گالیکش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gatab.
        /// </summary>
        internal static string گتاب {
            get {
                return ResourceManager.GetString("گتاب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gotvand.
        /// </summary>
        internal static string گتوند {
            get {
                return ResourceManager.GetString("گتوند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Garab.
        /// </summary>
        internal static string گراب {
            get {
                return ResourceManager.GetString("گراب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Garabsofla.
        /// </summary>
        internal static string گراب_سفلی {
            get {
                return ResourceManager.GetString("گراب سفلی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerash.
        /// </summary>
        internal static string گراش {
            get {
                return ResourceManager.GetString("گراش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GerdKashaneh.
        /// </summary>
        internal static string گردکشانه {
            get {
                return ResourceManager.GetString("گردکشانه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gorgab.
        /// </summary>
        internal static string گرگاب {
            get {
                return ResourceManager.GetString("گرگاب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gorgan.
        /// </summary>
        internal static string گرگان {
            get {
                return ResourceManager.GetString("گرگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Garmab.
        /// </summary>
        internal static string گرماب {
            get {
                return ResourceManager.GetString("گرماب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Garmdareh.
        /// </summary>
        internal static string گرمدره {
            get {
                return ResourceManager.GetString("گرمدره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Garmsar.
        /// </summary>
        internal static string گرمسار {
            get {
                return ResourceManager.GetString("گرمسار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Garmeh.
        /// </summary>
        internal static string گرمه {
            get {
                return ResourceManager.GetString("گرمه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Germi.
        /// </summary>
        internal static string گرمی {
            get {
                return ResourceManager.GetString("گرمی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GazBarkhar.
        /// </summary>
        internal static string گزبرخوار {
            get {
                return ResourceManager.GetString("گزبرخوار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gaznak.
        /// </summary>
        internal static string گزنک {
            get {
                return ResourceManager.GetString("گزنک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gazik.
        /// </summary>
        internal static string گزیک {
            get {
                return ResourceManager.GetString("گزیک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gosht.
        /// </summary>
        internal static string گشت {
            get {
                return ResourceManager.GetString("گشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goltapeh.
        /// </summary>
        internal static string گل_تپه {
            get {
                return ResourceManager.GetString("گل تپه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Golbaf.
        /// </summary>
        internal static string گلباف {
            get {
                return ResourceManager.GetString("گلباف", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Golpayegan.
        /// </summary>
        internal static string گلپایگان {
            get {
                return ResourceManager.GetString("گلپایگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goldasht.
        /// </summary>
        internal static string گلدشت {
            get {
                return ResourceManager.GetString("گلدشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Golzar.
        /// </summary>
        internal static string گلزار {
            get {
                return ResourceManager.GetString("گلزار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Golsar.
        /// </summary>
        internal static string گلسار {
            get {
                return ResourceManager.GetString("گلسار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Golestan.
        /// </summary>
        internal static string گلستان {
            get {
                return ResourceManager.GetString("گلستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Golshan.
        /// </summary>
        internal static string گلشن {
            get {
                return ResourceManager.GetString("گلشن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Golshahr.
        /// </summary>
        internal static string گلشهر {
            get {
                return ResourceManager.GetString("گلشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Golmakan.
        /// </summary>
        internal static string گلمکان {
            get {
                return ResourceManager.GetString("گلمکان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Golmorti.
        /// </summary>
        internal static string گلمورتی {
            get {
                return ResourceManager.GetString("گلمورتی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Galledar.
        /// </summary>
        internal static string گله_دار {
            get {
                return ResourceManager.GetString("گله دار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Galoogah.
        /// </summary>
        internal static string گلوگاه {
            get {
                return ResourceManager.GetString("گلوگاه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gomeyshtapeh.
        /// </summary>
        internal static string گمیش_تپه {
            get {
                return ResourceManager.GetString("گمیش تپه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gonabad.
        /// </summary>
        internal static string گناباد {
            get {
                return ResourceManager.GetString("گناباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gonbadkavoos.
        /// </summary>
        internal static string گنبدکاووس {
            get {
                return ResourceManager.GetString("گنبدکاووس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gandoman.
        /// </summary>
        internal static string گندمان {
            get {
                return ResourceManager.GetString("گندمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gahroo.
        /// </summary>
        internal static string گهرو {
            get {
                return ResourceManager.GetString("گهرو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gahvareh.
        /// </summary>
        internal static string گهواره {
            get {
                return ResourceManager.GetString("گهواره", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gujan.
        /// </summary>
        internal static string گوجان {
            get {
                return ResourceManager.GetString("گوجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gorabzarmikh.
        /// </summary>
        internal static string گوراب_زرمیخ {
            get {
                return ResourceManager.GetString("گوراب زرمیخ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gurieh.
        /// </summary>
        internal static string گوریه {
            get {
                return ResourceManager.GetString("گوریه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Googan.
        /// </summary>
        internal static string گوگان {
            get {
                return ResourceManager.GetString("گوگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gogad.
        /// </summary>
        internal static string گوگد {
            get {
                return ResourceManager.GetString("گوگد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goharan.
        /// </summary>
        internal static string گوهران {
            get {
                return ResourceManager.GetString("گوهران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Giyan.
        /// </summary>
        internal static string گیان {
            get {
                return ResourceManager.GetString("گیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gilangharb.
        /// </summary>
        internal static string گیلانغرب {
            get {
                return ResourceManager.GetString("گیلانغرب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Givi.
        /// </summary>
        internal static string گیوی {
            get {
                return ResourceManager.GetString("گیوی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lar.
        /// </summary>
        internal static string لار {
            get {
                return ResourceManager.GetString("لار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Laljin.
        /// </summary>
        internal static string لالجین {
            get {
                return ResourceManager.GetString("لالجین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lalezar.
        /// </summary>
        internal static string لاله_زار {
            get {
                return ResourceManager.GetString("لاله زار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lali.
        /// </summary>
        internal static string لالی {
            get {
                return ResourceManager.GetString("لالی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lamerd.
        /// </summary>
        internal static string لامرد {
            get {
                return ResourceManager.GetString("لامرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lahrood.
        /// </summary>
        internal static string لاهرود {
            get {
                return ResourceManager.GetString("لاهرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lahijan.
        /// </summary>
        internal static string لاهیجان {
            get {
                return ResourceManager.GetString("لاهیجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Laybid.
        /// </summary>
        internal static string لای_بید {
            get {
                return ResourceManager.GetString("لای بید", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lapooee.
        /// </summary>
        internal static string لپویی {
            get {
                return ResourceManager.GetString("لپویی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lordegan.
        /// </summary>
        internal static string لردگان {
            get {
                return ResourceManager.GetString("لردگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lashtnesha.
        /// </summary>
        internal static string لشت_نشا {
            get {
                return ResourceManager.GetString("لشت نشا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LotfAbad.
        /// </summary>
        internal static string لطف_آباد {
            get {
                return ResourceManager.GetString("لطف آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Latifi.
        /// </summary>
        internal static string لطیفی {
            get {
                return ResourceManager.GetString("لطیفی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lendeh.
        /// </summary>
        internal static string لنده {
            get {
                return ResourceManager.GetString("لنده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Langerood.
        /// </summary>
        internal static string لنگرود {
            get {
                return ResourceManager.GetString("لنگرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lavasan.
        /// </summary>
        internal static string لواسان {
            get {
                return ResourceManager.GetString("لواسان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lojali.
        /// </summary>
        internal static string لوجلی {
            get {
                return ResourceManager.GetString("لوجلی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loshan.
        /// </summary>
        internal static string لوشان {
            get {
                return ResourceManager.GetString("لوشان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Looleman.
        /// </summary>
        internal static string لولمان {
            get {
                return ResourceManager.GetString("لولمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loomar.
        /// </summary>
        internal static string لومار {
            get {
                return ResourceManager.GetString("لومار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lavandevil.
        /// </summary>
        internal static string لوندویل {
            get {
                return ResourceManager.GetString("لوندویل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lisar.
        /// </summary>
        internal static string لیسار {
            get {
                return ResourceManager.GetString("لیسار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Likak.
        /// </summary>
        internal static string لیکک {
            get {
                return ResourceManager.GetString("لیکک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Leylan.
        /// </summary>
        internal static string لیلان {
            get {
                return ResourceManager.GetString("لیلان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Madavan.
        /// </summary>
        internal static string مادوان {
            get {
                return ResourceManager.GetString("مادوان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Margoon.
        /// </summary>
        internal static string مارگون {
            get {
                return ResourceManager.GetString("مارگون", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Masal.
        /// </summary>
        internal static string ماسال {
            get {
                return ResourceManager.GetString("ماسال", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Masooleh.
        /// </summary>
        internal static string ماسوله {
            get {
                return ResourceManager.GetString("ماسوله", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Makoo.
        /// </summary>
        internal static string ماکو {
            get {
                return ResourceManager.GetString("ماکو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Malekhalife.
        /// </summary>
        internal static string مال_خلیفه {
            get {
                return ResourceManager.GetString("مال خلیفه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mamoonieh.
        /// </summary>
        internal static string مامونیه {
            get {
                return ResourceManager.GetString("مامونیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mahneshan.
        /// </summary>
        internal static string ماه_نشان {
            get {
                return ResourceManager.GetString("ماه نشان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mahan.
        /// </summary>
        internal static string ماهان {
            get {
                return ResourceManager.GetString("ماهان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mahdasht.
        /// </summary>
        internal static string ماهدشت {
            get {
                return ResourceManager.GetString("ماهدشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobarakabaddiz.
        /// </summary>
        internal static string مبارک_آباددیز {
            get {
                return ResourceManager.GetString("مبارک آباددیز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobarakeh.
        /// </summary>
        internal static string مبارکه {
            get {
                return ResourceManager.GetString("مبارکه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Majlesi.
        /// </summary>
        internal static string مجلسی {
            get {
                return ResourceManager.GetString("مجلسی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mojen.
        /// </summary>
        internal static string مجن {
            get {
                return ResourceManager.GetString("مجن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mahallat.
        /// </summary>
        internal static string محلات {
            get {
                return ResourceManager.GetString("محلات", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MohammadAbad.
        /// </summary>
        internal static string محمدآباد {
            get {
                return ResourceManager.GetString("محمدآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mohammadan.
        /// </summary>
        internal static string محمدان {
            get {
                return ResourceManager.GetString("محمدان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MohammadShahr.
        /// </summary>
        internal static string محمدشهر {
            get {
                return ResourceManager.GetString("محمدشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mohammadi.
        /// </summary>
        internal static string محمدی {
            get {
                return ResourceManager.GetString("محمدی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mohammadyar.
        /// </summary>
        internal static string محمدیار {
            get {
                return ResourceManager.GetString("محمدیار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mohammadieh.
        /// </summary>
        internal static string محمدیه {
            get {
                return ResourceManager.GetString("محمدیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MahmoodAbad.
        /// </summary>
        internal static string محمودآباد {
            get {
                return ResourceManager.GetString("محمودآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MahmudAbadNemuneh.
        /// </summary>
        internal static string محمودآبادنمونه {
            get {
                return ResourceManager.GetString("محمودآبادنمونه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MohiAbad.
        /// </summary>
        internal static string محی_آباد {
            get {
                return ResourceManager.GetString("محی آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moradloo.
        /// </summary>
        internal static string مرادلو {
            get {
                return ResourceManager.GetString("مرادلو", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maragheh.
        /// </summary>
        internal static string مراغه {
            get {
                return ResourceManager.GetString("مراغه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maraveh.
        /// </summary>
        internal static string مراوه {
            get {
                return ResourceManager.GetString("مراوه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marjaghal.
        /// </summary>
        internal static string مرجقل {
            get {
                return ResourceManager.GetString("مرجقل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mardehak.
        /// </summary>
        internal static string مردهک {
            get {
                return ResourceManager.GetString("مردهک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MarzanAbad.
        /// </summary>
        internal static string مرزن_آباد {
            get {
                return ResourceManager.GetString("مرزن آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marzikela.
        /// </summary>
        internal static string مرزیکلا {
            get {
                return ResourceManager.GetString("مرزیکلا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marganlar.
        /// </summary>
        internal static string مرگنلر {
            get {
                return ResourceManager.GetString("مرگنلر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marand.
        /// </summary>
        internal static string مرند {
            get {
                return ResourceManager.GetString("مرند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marvdasht.
        /// </summary>
        internal static string مرودشت {
            get {
                return ResourceManager.GetString("مرودشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marvast.
        /// </summary>
        internal static string مروست {
            get {
                return ResourceManager.GetString("مروست", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meryanj.
        /// </summary>
        internal static string مریانج {
            get {
                return ResourceManager.GetString("مریانج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marivan.
        /// </summary>
        internal static string مریوان {
            get {
                return ResourceManager.GetString("مریوان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mazdavand.
        /// </summary>
        internal static string مزدآوند {
            get {
                return ResourceManager.GetString("مزدآوند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mesesarcheshmeh.
        /// </summary>
        internal static string مس_سرچشمه {
            get {
                return ResourceManager.GetString("مس سرچشمه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Masjedsoleiman.
        /// </summary>
        internal static string مسجدسلیمان {
            get {
                return ResourceManager.GetString("مسجدسلیمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meshrageh.
        /// </summary>
        internal static string مشراگه {
            get {
                return ResourceManager.GetString("مشراگه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meshkat.
        /// </summary>
        internal static string مشکات {
            get {
                return ResourceManager.GetString("مشکات", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meshkan.
        /// </summary>
        internal static string مشکان {
            get {
                return ResourceManager.GetString("مشکان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meshkindasht.
        /// </summary>
        internal static string مشکین_دشت {
            get {
                return ResourceManager.GetString("مشکین دشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MeshginShahr.
        /// </summary>
        internal static string مشگین_شهر {
            get {
                return ResourceManager.GetString("مشگین شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mashhad.
        /// </summary>
        internal static string مشهد {
            get {
                return ResourceManager.GetString("مشهد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MashhadSamen.
        /// </summary>
        internal static string مشهد_ثامن {
            get {
                return ResourceManager.GetString("مشهد ثامن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mashhadrize.
        /// </summary>
        internal static string مشهدریزه {
            get {
                return ResourceManager.GetString("مشهدریزه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Masiri.
        /// </summary>
        internal static string مصیری {
            get {
                return ResourceManager.GetString("مصیری", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moalemkelayeh.
        /// </summary>
        internal static string معلم_کلایه {
            get {
                return ResourceManager.GetString("معلم کلایه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maemolan.
        /// </summary>
        internal static string معمولان {
            get {
                return ResourceManager.GetString("معمولان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mollasani.
        /// </summary>
        internal static string ملاثانی {
            get {
                return ResourceManager.GetString("ملاثانی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Malard.
        /// </summary>
        internal static string ملارد {
            get {
                return ResourceManager.GetString("ملارد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Malayer.
        /// </summary>
        internal static string ملایر {
            get {
                return ResourceManager.GetString("ملایر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MolkAbad.
        /// </summary>
        internal static string ملک_آباد {
            get {
                return ResourceManager.GetString("ملک آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Malekan.
        /// </summary>
        internal static string ملکان {
            get {
                return ResourceManager.GetString("ملکان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mamaghan.
        /// </summary>
        internal static string ممقان {
            get {
                return ResourceManager.GetString("ممقان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monj.
        /// </summary>
        internal static string منج {
            get {
                return ResourceManager.GetString("منج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manjil.
        /// </summary>
        internal static string منجیل {
            get {
                return ResourceManager.GetString("منجیل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manzarieh.
        /// </summary>
        internal static string منظریه {
            get {
                return ResourceManager.GetString("منظریه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manoojan.
        /// </summary>
        internal static string منوجان {
            get {
                return ResourceManager.GetString("منوجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mahabad.
        /// </summary>
        internal static string مهاباد {
            get {
                return ResourceManager.GetString("مهاباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mohajeran.
        /// </summary>
        internal static string مهاجران {
            get {
                return ResourceManager.GetString("مهاجران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MehdiShahr.
        /// </summary>
        internal static string مهدی_شهر {
            get {
                return ResourceManager.GetString("مهدی شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mehr.
        /// </summary>
        internal static string مهر {
            get {
                return ResourceManager.GetString("مهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mehran.
        /// </summary>
        internal static string مهران {
            get {
                return ResourceManager.GetString("مهران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mehraban.
        /// </summary>
        internal static string مهربان {
            get {
                return ResourceManager.GetString("مهربان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mehrdasht.
        /// </summary>
        internal static string مهردشت {
            get {
                return ResourceManager.GetString("مهردشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mehriz.
        /// </summary>
        internal static string مهریز {
            get {
                return ResourceManager.GetString("مهریز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mochesh.
        /// </summary>
        internal static string موچش {
            get {
                return ResourceManager.GetString("موچش", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moud.
        /// </summary>
        internal static string مود {
            get {
                return ResourceManager.GetString("مود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Murmuri.
        /// </summary>
        internal static string مورموری {
            get {
                return ResourceManager.GetString("مورموری", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moosian.
        /// </summary>
        internal static string موسیان {
            get {
                return ResourceManager.GetString("موسیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MomenAbad.
        /// </summary>
        internal static string مومن_آباد {
            get {
                return ResourceManager.GetString("مومن آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Miamey.
        /// </summary>
        internal static string میامی {
            get {
                return ResourceManager.GetString("میامی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Miyanrahan.
        /// </summary>
        internal static string میان_راهان {
            get {
                return ResourceManager.GetString("میان راهان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Miandoab.
        /// </summary>
        internal static string میاندوآب {
            get {
                return ResourceManager.GetString("میاندوآب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mianrood.
        /// </summary>
        internal static string میانرود {
            get {
                return ResourceManager.GetString("میانرود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mianeh.
        /// </summary>
        internal static string میانه {
            get {
                return ResourceManager.GetString("میانه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meibod.
        /// </summary>
        internal static string میبد {
            get {
                return ResourceManager.GetString("میبد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meidavood.
        /// </summary>
        internal static string میداود {
            get {
                return ResourceManager.GetString("میداود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MirAbad.
        /// </summary>
        internal static string میرآباد {
            get {
                return ResourceManager.GetString("میرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mirjaveh.
        /// </summary>
        internal static string میرجاوه {
            get {
                return ResourceManager.GetString("میرجاوه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Milajerd.
        /// </summary>
        internal static string میلاجرد {
            get {
                return ResourceManager.GetString("میلاجرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meimand.
        /// </summary>
        internal static string میمند {
            get {
                return ResourceManager.GetString("میمند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meimeh.
        /// </summary>
        internal static string میمه {
            get {
                return ResourceManager.GetString("میمه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minab.
        /// </summary>
        internal static string میناب {
            get {
                return ResourceManager.GetString("میناب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minoodasht.
        /// </summary>
        internal static string مینودشت {
            get {
                return ResourceManager.GetString("مینودشت", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MinooShahr.
        /// </summary>
        internal static string مینوشهر {
            get {
                return ResourceManager.GetString("مینوشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NazokOlia.
        /// </summary>
        internal static string نازک_علیا {
            get {
                return ResourceManager.GetString("نازک علیا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Naghan.
        /// </summary>
        internal static string ناغان {
            get {
                return ResourceManager.GetString("ناغان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nafch.
        /// </summary>
        internal static string نافچ {
            get {
                return ResourceManager.GetString("نافچ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Naloos.
        /// </summary>
        internal static string نالوس {
            get {
                return ResourceManager.GetString("نالوس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Naeen.
        /// </summary>
        internal static string نایین {
            get {
                return ResourceManager.GetString("نایین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NajafAbad.
        /// </summary>
        internal static string نجف_آباد {
            get {
                return ResourceManager.GetString("نجف آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NajafShahr.
        /// </summary>
        internal static string نجف_شهر {
            get {
                return ResourceManager.GetString("نجف شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nakhltaghi.
        /// </summary>
        internal static string نخل_تقی {
            get {
                return ResourceManager.GetString("نخل تقی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nadoshan.
        /// </summary>
        internal static string ندوشن {
            get {
                return ResourceManager.GetString("ندوشن", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Naragh.
        /// </summary>
        internal static string نراق {
            get {
                return ResourceManager.GetString("نراق", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Narje.
        /// </summary>
        internal static string نرجه {
            get {
                return ResourceManager.GetString("نرجه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Narmashir.
        /// </summary>
        internal static string نرماشیر {
            get {
                return ResourceManager.GetString("نرماشیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NasimShahr.
        /// </summary>
        internal static string نسیم_شهر {
            get {
                return ResourceManager.GetString("نسیم شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nashtarood.
        /// </summary>
        internal static string نشتارود {
            get {
                return ResourceManager.GetString("نشتارود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nashtifan.
        /// </summary>
        internal static string نشتیفان {
            get {
                return ResourceManager.GetString("نشتیفان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NasrAbad.
        /// </summary>
        internal static string نصرآباد {
            get {
                return ResourceManager.GetString("نصرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NosratAbad.
        /// </summary>
        internal static string نصرت_آباد {
            get {
                return ResourceManager.GetString("نصرت آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NasirShahr.
        /// </summary>
        internal static string نصیرشهر {
            get {
                return ResourceManager.GetString("نصیرشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Natanz.
        /// </summary>
        internal static string نطنز {
            get {
                return ResourceManager.GetString("نطنز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NezamShahr.
        /// </summary>
        internal static string نظام_شهر {
            get {
                return ResourceManager.GetString("نظام شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NazarAbad.
        /// </summary>
        internal static string نظرآباد {
            get {
                return ResourceManager.GetString("نظرآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nazarkahrizi.
        /// </summary>
        internal static string نظرکهریزی {
            get {
                return ResourceManager.GetString("نظرکهریزی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neghab.
        /// </summary>
        internal static string نقاب {
            get {
                return ResourceManager.GetString("نقاب", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Naghadeh.
        /// </summary>
        internal static string نقده {
            get {
                return ResourceManager.GetString("نقده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Naghneh.
        /// </summary>
        internal static string نقنه {
            get {
                return ResourceManager.GetString("نقنه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neka.
        /// </summary>
        internal static string نکا {
            get {
                return ResourceManager.GetString("نکا", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Negar.
        /// </summary>
        internal static string نگار {
            get {
                return ResourceManager.GetString("نگار", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Negor.
        /// </summary>
        internal static string نگور {
            get {
                return ResourceManager.GetString("نگور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NeginShahr.
        /// </summary>
        internal static string نگین_شهر {
            get {
                return ResourceManager.GetString("نگین شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Namin.
        /// </summary>
        internal static string نمین {
            get {
                return ResourceManager.GetString("نمین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nahavand.
        /// </summary>
        internal static string نهاوند {
            get {
                return ResourceManager.GetString("نهاوند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nehbandan.
        /// </summary>
        internal static string نهبندان {
            get {
                return ResourceManager.GetString("نهبندان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nobaran.
        /// </summary>
        internal static string نوبران {
            get {
                return ResourceManager.GetString("نوبران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nobandegan.
        /// </summary>
        internal static string نوبندگان {
            get {
                return ResourceManager.GetString("نوبندگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nojein.
        /// </summary>
        internal static string نوجین {
            get {
                return ResourceManager.GetString("نوجین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nokhandan.
        /// </summary>
        internal static string نوخندان {
            get {
                return ResourceManager.GetString("نوخندان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nodan.
        /// </summary>
        internal static string نودان {
            get {
                return ResourceManager.GetString("نودان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nodezh.
        /// </summary>
        internal static string نودژ {
            get {
                return ResourceManager.GetString("نودژ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nowdeshah.
        /// </summary>
        internal static string نودشه {
            get {
                return ResourceManager.GetString("نودشه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nodekhanduz.
        /// </summary>
        internal static string نوده_خاندوز {
            get {
                return ResourceManager.GetString("نوده خاندوز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Noor.
        /// </summary>
        internal static string نور {
            get {
                return ResourceManager.GetString("نور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NoorAbad.
        /// </summary>
        internal static string نورآباد {
            get {
                return ResourceManager.GetString("نورآباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nosoud.
        /// </summary>
        internal static string نوسود {
            get {
                return ResourceManager.GetString("نوسود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NooshAbad.
        /// </summary>
        internal static string نوش_آباد {
            get {
                return ResourceManager.GetString("نوش آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Noshahr.
        /// </summary>
        internal static string نوشهر {
            get {
                return ResourceManager.GetString("نوشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Noshin.
        /// </summary>
        internal static string نوشین {
            get {
                return ResourceManager.GetString("نوشین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NookAbad.
        /// </summary>
        internal static string نوک_آباد {
            get {
                return ResourceManager.GetString("نوک آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nokandeh.
        /// </summary>
        internal static string نوکنده {
            get {
                return ResourceManager.GetString("نوکنده", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neyriz.
        /// </summary>
        internal static string نی_ریز {
            get {
                return ResourceManager.GetString("نی ریز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Niasar.
        /// </summary>
        internal static string نیاسر {
            get {
                return ResourceManager.GetString("نیاسر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nayer.
        /// </summary>
        internal static string نیر {
            get {
                return ResourceManager.GetString("نیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neyshaboor.
        /// </summary>
        internal static string نیشابور {
            get {
                return ResourceManager.GetString("نیشابور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NikAbad.
        /// </summary>
        internal static string نیک_آباد {
            get {
                return ResourceManager.GetString("نیک آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NikShahr.
        /// </summary>
        internal static string نیک_شهر {
            get {
                return ResourceManager.GetString("نیک شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NilShahr.
        /// </summary>
        internal static string نیل_شهر {
            get {
                return ResourceManager.GetString("نیل شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nimbolook.
        /// </summary>
        internal static string نیمبلوک {
            get {
                return ResourceManager.GetString("نیمبلوک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nimoor.
        /// </summary>
        internal static string نیمور {
            get {
                return ResourceManager.GetString("نیمور", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HadiShahr.
        /// </summary>
        internal static string هادیشهر {
            get {
                return ResourceManager.GetString("هادیشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hojedak.
        /// </summary>
        internal static string هجدک {
            get {
                return ResourceManager.GetString("هجدک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Harat.
        /// </summary>
        internal static string هرات {
            get {
                return ResourceManager.GetString("هرات", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hersin.
        /// </summary>
        internal static string هرسین {
            get {
                return ResourceManager.GetString("هرسین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hormoz.
        /// </summary>
        internal static string هرمز {
            get {
                return ResourceManager.GetString("هرمز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Harand.
        /// </summary>
        internal static string هرند {
            get {
                return ResourceManager.GetString("هرند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Haris.
        /// </summary>
        internal static string هریس {
            get {
                return ResourceManager.GetString("هریس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hashtbandi.
        /// </summary>
        internal static string هشتبندی {
            get {
                return ResourceManager.GetString("هشتبندی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hashtjin.
        /// </summary>
        internal static string هشتجین {
            get {
                return ResourceManager.GetString("هشتجین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hashtrood.
        /// </summary>
        internal static string هشترود {
            get {
                return ResourceManager.GetString("هشترود", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hashtgerd.
        /// </summary>
        internal static string هشتگرد {
            get {
                return ResourceManager.GetString("هشتگرد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Haftcheshmeh.
        /// </summary>
        internal static string هفت_چشمه {
            get {
                return ResourceManager.GetString("هفت چشمه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Haftgol.
        /// </summary>
        internal static string هفتگل {
            get {
                return ResourceManager.GetString("هفتگل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hafshejan.
        /// </summary>
        internal static string هفشجان {
            get {
                return ResourceManager.GetString("هفشجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Halashi.
        /// </summary>
        internal static string هلشی {
            get {
                return ResourceManager.GetString("هلشی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HomaShahr.
        /// </summary>
        internal static string هماشهر {
            get {
                return ResourceManager.GetString("هماشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HemmatAbad.
        /// </summary>
        internal static string همت_آباد {
            get {
                return ResourceManager.GetString("همت آباد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hamedan.
        /// </summary>
        internal static string همدان {
            get {
                return ResourceManager.GetString("همدان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hendodar.
        /// </summary>
        internal static string هندودر {
            get {
                return ResourceManager.GetString("هندودر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hendijan.
        /// </summary>
        internal static string هندیجان {
            get {
                return ResourceManager.GetString("هندیجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Horand.
        /// </summary>
        internal static string هوراند {
            get {
                return ResourceManager.GetString("هوراند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hoveizeh.
        /// </summary>
        internal static string هویزه {
            get {
                return ResourceManager.GetString("هویزه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hidaj.
        /// </summary>
        internal static string هیدج {
            get {
                return ResourceManager.GetString("هیدج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hidooch.
        /// </summary>
        internal static string هیدوچ {
            get {
                return ResourceManager.GetString("هیدوچ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hir.
        /// </summary>
        internal static string هیر {
            get {
                return ResourceManager.GetString("هیر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vajargah.
        /// </summary>
        internal static string واجارگاه {
            get {
                return ResourceManager.GetString("واجارگاه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vaighan.
        /// </summary>
        internal static string وایقان {
            get {
                return ResourceManager.GetString("وایقان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vahdatiyeh.
        /// </summary>
        internal static string وحدتیه {
            get {
                return ResourceManager.GetString("وحدتیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vahidieh.
        /// </summary>
        internal static string وحیدیه {
            get {
                return ResourceManager.GetString("وحیدیه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Varamin.
        /// </summary>
        internal static string ورامین {
            get {
                return ResourceManager.GetString("ورامین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Varavi.
        /// </summary>
        internal static string وراوی {
            get {
                return ResourceManager.GetString("وراوی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vardanjan.
        /// </summary>
        internal static string وردنجان {
            get {
                return ResourceManager.GetString("وردنجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Varzaghan.
        /// </summary>
        internal static string ورزقان {
            get {
                return ResourceManager.GetString("ورزقان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Varzaneh.
        /// </summary>
        internal static string ورزنه {
            get {
                return ResourceManager.GetString("ورزنه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Varnamkhast.
        /// </summary>
        internal static string ورنامخواست {
            get {
                return ResourceManager.GetString("ورنامخواست", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vezvan.
        /// </summary>
        internal static string وزوان {
            get {
                return ResourceManager.GetString("وزوان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vanak.
        /// </summary>
        internal static string ونک {
            get {
                return ResourceManager.GetString("ونک", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Veys.
        /// </summary>
        internal static string ویس {
            get {
                return ResourceManager.GetString("ویس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Veysian.
        /// </summary>
        internal static string ویسیان {
            get {
                return ResourceManager.GetString("ویسیان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yasooj.
        /// </summary>
        internal static string یاسوج {
            get {
                return ResourceManager.GetString("یاسوج", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yasokand.
        /// </summary>
        internal static string یاسوکند {
            get {
                return ResourceManager.GetString("یاسوکند", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yamchi.
        /// </summary>
        internal static string یامچی {
            get {
                return ResourceManager.GetString("یامچی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yazd.
        /// </summary>
        internal static string یزد {
            get {
                return ResourceManager.GetString("یزد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to YazdanShahr.
        /// </summary>
        internal static string یزدان_شهر {
            get {
                return ResourceManager.GetString("یزدان شهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Younesi.
        /// </summary>
        internal static string یونسی {
            get {
                return ResourceManager.GetString("یونسی", resourceCulture);
            }
        }
    }
}
