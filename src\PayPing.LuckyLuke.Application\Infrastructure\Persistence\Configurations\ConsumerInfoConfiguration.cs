﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class ConsumerInfoConfiguration : IEntityTypeConfiguration<ConsumerInfo>
{
    public void Configure(EntityTypeBuilder<ConsumerInfo> builder)
    {
        builder.HasIndex(x => x.ConsumerUserId).IsUnique().HasDatabaseName("IX_ConsumerInfo_ConsumerUserId");

        builder.Property(nameof(ConsumerInfo.FirstNameEnglish)).HasMaxLength(128);
        builder.Property(nameof(ConsumerInfo.LastNameEnglish)).HasMaxLength(128);
        builder.Property(nameof(ConsumerInfo.NationalIdSeries)).HasMaxLength(128);
        builder.Property(nameof(ConsumerInfo.PostalCode)).HasMaxLength(10);
        builder.Property(nameof(ConsumerInfo.Address)).HasMaxLength(2048);
        builder.Property(nameof(ConsumerInfo.IBan)).HasMaxLength(26);
        builder.Property(nameof(ConsumerInfo.CardNumber)).HasMaxLength(16);
        builder.Property(nameof(ConsumerInfo.Email)).HasMaxLength(128);
        builder.Property(nameof(ConsumerInfo.ProvinceEnglish)).HasMaxLength(64);
        builder.Property(nameof(ConsumerInfo.CityEnglish)).HasMaxLength(64);

        builder.Property(nameof(ConsumerInfo.UserName)).HasMaxLength(64);
        builder.Property(nameof(ConsumerInfo.FirstName)).HasMaxLength(128);
        builder.Property(nameof(ConsumerInfo.LastName)).HasMaxLength(128);
        builder.Property(nameof(ConsumerInfo.FullName)).HasMaxLength(256);
        builder.Property(nameof(ConsumerInfo.NationalCode)).HasMaxLength(64);
        builder.Property(nameof(ConsumerInfo.PersianBirthDate)).HasMaxLength(64);
        builder.Property(nameof(ConsumerInfo.PhoneNumber)).HasMaxLength(64);
        builder.Property(nameof(ConsumerInfo.FatherName)).HasMaxLength(128);

        builder.Property(nameof(ConsumerInfo.SignatureImageFileId)).HasMaxLength(128);
        builder.Property(nameof(ConsumerInfo.SignatureImageFileName)).HasMaxLength(128);

        builder.Property(nameof(ConsumerInfo.SignatureVideoFileId)).HasMaxLength(128);
        builder.Property(nameof(ConsumerInfo.SignatureVideoFileName)).HasMaxLength(128);
        builder.Property(nameof(ConsumerInfo.SignatureVideoRandomSentence)).HasMaxLength(2048);


    }
}
