﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;
using System.Reflection.Emit;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class OrderCreditValidationConfiguration : IEntityTypeConfiguration<OrderCreditValidation>
{
    public void Configure(EntityTypeBuilder<OrderCreditValidation> builder)
    {
        builder.Property(nameof(OrderCreditValidation.Description)).HasMaxLength(1024);
        builder.Property(nameof(OrderCreditValidation.Risk)).HasMaxLength(1024);
    }
}
