﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedCardNumberToConsumer : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CardNumber",
                table: "ConsumerInfos",
                type: "character varying(16)",
                maxLength: 16,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Card<PERSON>um<PERSON>",
                table: "ConsumerInfos");
        }
    }
}
