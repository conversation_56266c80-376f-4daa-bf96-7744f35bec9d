﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PayPing.LuckyLuke.Application.Shared.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ValidatorDictionary {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ValidatorDictionary() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PayPing.LuckyLuke.Application.Shared.Resources.ValidatorDictionary", typeof(ValidatorDictionary).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to آدرس الزامیست.
        /// </summary>
        internal static string address_is_required {
            get {
                return ResourceManager.GetString("address_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبلغ کل سفارش باید بزرگتر از 1000 تومان باشد.
        /// </summary>
        internal static string amount_greater_1000 {
            get {
                return ResourceManager.GetString("amount_greater_1000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبلغ باید بزرگتر از صفر باشد.
        /// </summary>
        internal static string amount_greater_zero {
            get {
                return ResourceManager.GetString("amount_greater_zero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حداقل مبلغ قابل قبول 50 هزار تومان است.
        /// </summary>
        internal static string amount_greaterorequal_50000 {
            get {
                return ResourceManager.GetString("amount_greaterorequal_50000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره شناسنامه الزامی می باشد.
        /// </summary>
        internal static string birth_certificate_no_is_required {
            get {
                return ResourceManager.GetString("birth_certificate_no_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره شناسنامه صحیح نمی باشد.
        /// </summary>
        internal static string birth_certificate_no_not_valid {
            get {
                return ResourceManager.GetString("birth_certificate_no_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاریخ تولد صحیح نیست.
        /// </summary>
        internal static string birthdate_is_not_valid {
            get {
                return ResourceManager.GetString("birthdate_is_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاریخ تولد الزامیست.
        /// </summary>
        internal static string birthdate_is_required {
            get {
                return ResourceManager.GetString("birthdate_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to آدرس مقصد اطلاعات پرداخت الزامیست.
        /// </summary>
        internal static string call_back_url_is_required {
            get {
                return ResourceManager.GetString("call-back_url_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره کارت الزامیست.
        /// </summary>
        internal static string card_number_is_required {
            get {
                return ResourceManager.GetString("card_number_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره کارت صحیح نیست.
        /// </summary>
        internal static string card_number_not_valid {
            get {
                return ResourceManager.GetString("card_number_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شهر به انگلیسی الزامیست.
        /// </summary>
        internal static string city_english_is_required {
            get {
                return ResourceManager.GetString("city_english_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شهر به انگلیسی صحیح نیست.
        /// </summary>
        internal static string city_english_not_valid {
            get {
                return ResourceManager.GetString("city_english_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to موافقت با قرارداد صحیح نیست.
        /// </summary>
        internal static string contract_acceptance_not_valid {
            get {
                return ResourceManager.GetString("contract_acceptance_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه قراداد الزامی می باشد.
        /// </summary>
        internal static string contract_id_is_required {
            get {
                return ResourceManager.GetString("contract_id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to کد اعتبار الزامیست.
        /// </summary>
        internal static string credit_code_is_required {
            get {
                return ResourceManager.GetString("credit_code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه اعتباری الزامیست.
        /// </summary>
        internal static string credit_id_is_required {
            get {
                return ResourceManager.GetString("credit_id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ایمیل الزامیست.
        /// </summary>
        internal static string email_is_required {
            get {
                return ResourceManager.GetString("email_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ایمیل صحیح نیست.
        /// </summary>
        internal static string email_not_valid {
            get {
                return ResourceManager.GetString("email_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نام فایل الزامیست.
        /// </summary>
        internal static string file_name_is_required {
            get {
                return ResourceManager.GetString("file_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نام به انگلیسی الزامیست.
        /// </summary>
        internal static string first_name_english_is_required {
            get {
                return ResourceManager.GetString("first_name_english_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نام به انگلیسی صحیح نیست.
        /// </summary>
        internal static string first_name_english_not_valid {
            get {
                return ResourceManager.GetString("first_name_english_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to طبقه.
        /// </summary>
        internal static string Floor {
            get {
                return ResourceManager.GetString("Floor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه مرجع گارانتی الزامیست.
        /// </summary>
        internal static string guarantee_reference_id_is_required {
            get {
                return ResourceManager.GetString("guarantee_reference_id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نوع ضمانت باید از مقادیر از قبل تعریف شده باشد.
        /// </summary>
        internal static string guarantee_type_must_enum {
            get {
                return ResourceManager.GetString("guarantee_type_must_enum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره شبا الزامیست.
        /// </summary>
        internal static string iban_is_required {
            get {
                return ResourceManager.GetString("iban_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره شبا صحیح نیست.
        /// </summary>
        internal static string iban_not_valid {
            get {
                return ResourceManager.GetString("iban_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  کد قسط الزامیست.
        /// </summary>
        internal static string installment_code_is_required {
            get {
                return ResourceManager.GetString("installment_code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تعداد اقساط باید بیشتر از صفر باشد.
        /// </summary>
        internal static string installment_count_greater_zero {
            get {
                return ResourceManager.GetString("installment_count_greater_zero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دیتای قسط الزامیست.
        /// </summary>
        internal static string installment_verify_data_is_required {
            get {
                return ResourceManager.GetString("installment_verify_data_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to درصد سود باید بزرگتر یا برابر با صفر باشد.
        /// </summary>
        internal static string interest_rate_greater {
            get {
                return ResourceManager.GetString("interest_rate_greater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نام خانوادگی به انگلیسی الزامیست.
        /// </summary>
        internal static string last_name_english_is_required {
            get {
                return ResourceManager.GetString("last_name_english_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نام خانوادگی به انگلیسی صحیح نیست.
        /// </summary>
        internal static string last_name_english_not_valid {
            get {
                return ResourceManager.GetString("last_name_english_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to محله.
        /// </summary>
        internal static string locality {
            get {
                return ResourceManager.GetString("locality", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حداکثر مبلغ اعتباری الزامیست.
        /// </summary>
        internal static string max_credit_amount_is_required {
            get {
                return ResourceManager.GetString("max_credit_amount_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه مرچنت الزامی است.
        /// </summary>
        internal static string merchant_id_is_required {
            get {
                return ResourceManager.GetString("merchant_id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه کاربر مرچنت الزامی است.
        /// </summary>
        internal static string merchant_user_id_is_required {
            get {
                return ResourceManager.GetString("merchant-user-id-is-required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to درصد پیش پرداخت باید بزرگتر یا برابر با صفر درصد باشد.
        /// </summary>
        internal static string min_prePayment_rate_greater {
            get {
                return ResourceManager.GetString("min_prePayment_rate_greater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to درصد پیش پرداخت باید کمتر از صد درصد باشد.
        /// </summary>
        internal static string min_prePayment_rate_lessthan {
            get {
                return ResourceManager.GetString("min_prePayment_rate_lessthan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره موبایل الزامیست.
        /// </summary>
        internal static string mobile_is_required {
            get {
                return ResourceManager.GetString("mobile_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره موبایل صحیح نیست. شماره موبایل باید مطابق الگوی 09121112233 باشد.
        /// </summary>
        internal static string mobile_not_valid {
            get {
                return ResourceManager.GetString("mobile_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ماه باید از 1 تا 12 باشد.
        /// </summary>
        internal static string month_between_1_12 {
            get {
                return ResourceManager.GetString("month_between_1_12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره سریال پشت کارت ملی الزامی باشد.
        /// </summary>
        internal static string national_card_serial_number_is_required {
            get {
                return ResourceManager.GetString("national_card_serial_number_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره سریال پشت کارت ملی صحیح نمی باشد.
        /// </summary>
        internal static string national_card_serial_number_not_valid {
            get {
                return ResourceManager.GetString("national_card_serial_number_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه ملی الزامیست.
        /// </summary>
        internal static string national_id_series_is_required {
            get {
                return ResourceManager.GetString("national_id_series_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه ملی صحیح نیست.
        /// </summary>
        internal static string national_id_series_not_valid {
            get {
                return ResourceManager.GetString("national_id_series_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to کد ملی صحیح نیست.
        /// </summary>
        internal static string nationalcode_is_not_valid {
            get {
                return ResourceManager.GetString("nationalcode_is_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to کد ملی الزامیست.
        /// </summary>
        internal static string nationalcode_is_required {
            get {
                return ResourceManager.GetString("nationalcode_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to پلاک.
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه سفارش باید بزرگتر از صفر باشد.
        /// </summary>
        internal static string order_id_greater_zero {
            get {
                return ResourceManager.GetString("order_id_greater_zero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه سفارش الزامیست.
        /// </summary>
        internal static string order_id_is_required {
            get {
                return ResourceManager.GetString("order_id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to کد رهگیری سفارش الزامیست.
        /// </summary>
        internal static string order_tracking_code_is_required {
            get {
                return ResourceManager.GetString("order_tracking_code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to کد پیامک شده الزامیست.
        /// </summary>
        internal static string otp_code_is_required {
            get {
                return ResourceManager.GetString("otp_code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شماره صفحه باید بزرگتر از صفر باشد.
        /// </summary>
        internal static string page_number_greater_zero {
            get {
                return ResourceManager.GetString("page_number_greater_zero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تعداد آیتم های هر صفحه باید بزرگتر از صفر باشد.
        /// </summary>
        internal static string page_size_greater_zero {
            get {
                return ResourceManager.GetString("page_size_greater_zero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to کد پرداختی الزامیست.
        /// </summary>
        internal static string payment_code_is_required {
            get {
                return ResourceManager.GetString("payment_code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه ارجاع پرداخت باید بزرگتر از صفر باشد.
        /// </summary>
        internal static string payment_ref_id_greater_zero {
            get {
                return ResourceManager.GetString("payment_ref_id_greater_zero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to کد طرح الزامیست.
        /// </summary>
        internal static string plan_code_is_required {
            get {
                return ResourceManager.GetString("plan_code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه طرح الزامیست.
        /// </summary>
        internal static string plan_id_is_required {
            get {
                return ResourceManager.GetString("plan_id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to کد پستی الزامیست.
        /// </summary>
        internal static string postal_code_is_required {
            get {
                return ResourceManager.GetString("postal_code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to کد پستی صحیح نیست.
        /// </summary>
        internal static string postal_code_not_valid {
            get {
                return ResourceManager.GetString("postal_code_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to وضعیت پرداختی سفته الزامیست.
        /// </summary>
        internal static string promissory_payment_status_is_required {
            get {
                return ResourceManager.GetString("promissory_payment_status_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to استان به انگلیسی الزامیست.
        /// </summary>
        internal static string province_english_is_required {
            get {
                return ResourceManager.GetString("province_english_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to استان به انگلیسی صحیح نیست.
        /// </summary>
        internal static string province_english_not_valid {
            get {
                return ResourceManager.GetString("province_english_not_valid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه سفارش یکتا پذیرنده الزامیست.
        /// </summary>
        internal static string ref_id_is_required {
            get {
                return ResourceManager.GetString("ref_id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تصویر امضا الزامیست.
        /// </summary>
        internal static string signature_image_not_null {
            get {
                return ResourceManager.GetString("signature_image_not_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ویدیو احراز هویت الزامیست.
        /// </summary>
        internal static string signature_video_not_null {
            get {
                return ResourceManager.GetString("signature_video_not_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to متن ویدیو احراز هویت الزامیست.
        /// </summary>
        internal static string signature_video_sentence_not_null {
            get {
                return ResourceManager.GetString("signature_video_sentence_not_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to کد رهگیری الزامیست.
        /// </summary>
        internal static string tracking_code_is_required {
            get {
                return ResourceManager.GetString("tracking_code_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شناسه کیف پول الزامیست.
        /// </summary>
        internal static string wallet_id_is_required {
            get {
                return ResourceManager.GetString("wallet_id_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سال باید 1403 به بعد باشد.
        /// </summary>
        internal static string year_greater_1403 {
            get {
                return ResourceManager.GetString("year_greater_1403", resourceCulture);
            }
        }
    }
}
