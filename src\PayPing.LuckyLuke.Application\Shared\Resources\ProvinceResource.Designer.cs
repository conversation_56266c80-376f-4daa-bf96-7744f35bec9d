﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PayPing.LuckyLuke.Application.Shared.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ProvinceResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ProvinceResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PayPing.LuckyLuke.Application.Shared.Resources.ProvinceResource", typeof(ProvinceResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AZE.
        /// </summary>
        internal static string آذربایجان_شرقی {
            get {
                return ResourceManager.GetString("آذربایجان شرقی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AZW.
        /// </summary>
        internal static string آذربایجان_غربی {
            get {
                return ResourceManager.GetString("آذربایجان غربی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ARD.
        /// </summary>
        internal static string اردبیل {
            get {
                return ResourceManager.GetString("اردبیل", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ESF.
        /// </summary>
        internal static string اصفهان {
            get {
                return ResourceManager.GetString("اصفهان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ALB.
        /// </summary>
        internal static string البرز {
            get {
                return ResourceManager.GetString("البرز", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EIL.
        /// </summary>
        internal static string ایلام {
            get {
                return ResourceManager.GetString("ایلام", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BSH.
        /// </summary>
        internal static string بوشهر {
            get {
                return ResourceManager.GetString("بوشهر", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to THR.
        /// </summary>
        internal static string تهران {
            get {
                return ResourceManager.GetString("تهران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CHB.
        /// </summary>
        internal static string چهارمحال_وبختیاری {
            get {
                return ResourceManager.GetString("چهارمحال وبختیاری", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KHS.
        /// </summary>
        internal static string خراسان_جنوبی {
            get {
                return ResourceManager.GetString("خراسان جنوبی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KHR.
        /// </summary>
        internal static string خراسان_رضوی {
            get {
                return ResourceManager.GetString("خراسان رضوی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KHN.
        /// </summary>
        internal static string خراسان_شمالی {
            get {
                return ResourceManager.GetString("خراسان شمالی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KHO.
        /// </summary>
        internal static string خوزستان {
            get {
                return ResourceManager.GetString("خوزستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZNJ.
        /// </summary>
        internal static string زنجان {
            get {
                return ResourceManager.GetString("زنجان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SEM.
        /// </summary>
        internal static string سمنان {
            get {
                return ResourceManager.GetString("سمنان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SBA.
        /// </summary>
        internal static string سیستان_وبلوچستان {
            get {
                return ResourceManager.GetString("سیستان وبلوچستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FAR.
        /// </summary>
        internal static string فارس {
            get {
                return ResourceManager.GetString("فارس", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QAZ.
        /// </summary>
        internal static string قزوین {
            get {
                return ResourceManager.GetString("قزوین", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QOM.
        /// </summary>
        internal static string قم {
            get {
                return ResourceManager.GetString("قم", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KRD.
        /// </summary>
        internal static string کردستان {
            get {
                return ResourceManager.GetString("کردستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KRM.
        /// </summary>
        internal static string کرمان {
            get {
                return ResourceManager.GetString("کرمان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KSH.
        /// </summary>
        internal static string کرمانشاه {
            get {
                return ResourceManager.GetString("کرمانشاه", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KOB.
        /// </summary>
        internal static string کهگیلویه_وبویراحمد {
            get {
                return ResourceManager.GetString("کهگیلویه وبویراحمد", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GOL.
        /// </summary>
        internal static string گلستان {
            get {
                return ResourceManager.GetString("گلستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GIL.
        /// </summary>
        internal static string گیلان {
            get {
                return ResourceManager.GetString("گیلان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LOR.
        /// </summary>
        internal static string لرستان {
            get {
                return ResourceManager.GetString("لرستان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MAZ.
        /// </summary>
        internal static string مازندران {
            get {
                return ResourceManager.GetString("مازندران", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MAR.
        /// </summary>
        internal static string مرکزی {
            get {
                return ResourceManager.GetString("مرکزی", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HOR.
        /// </summary>
        internal static string هرمزگان {
            get {
                return ResourceManager.GetString("هرمزگان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HAM.
        /// </summary>
        internal static string همدان {
            get {
                return ResourceManager.GetString("همدان", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to YAZ.
        /// </summary>
        internal static string یزد {
            get {
                return ResourceManager.GetString("یزد", resourceCulture);
            }
        }
    }
}
