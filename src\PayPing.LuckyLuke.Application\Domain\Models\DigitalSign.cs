﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Domain.Models
{
    public class DigitalSign : BaseEntity<long>, IAuditableEntity
    {
        public int UserId { get; set; }

        public string Data { get; set; }

        public DateTimeOffset? ExpiredAt { get; set; }

        public DigitalSignType Type { get; set; }

        public string SignatureRefId { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }

        public int? LastModifiedBy { get; set; }
    }
}
