﻿using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kiahooshan
{
    public interface IKiahooshanPromissoryService
    {
        ValueTask<object> HandleAsync(KiahooshanPromissoryContextV1 context, CancellationToken cancellationToken = default);
    }
}
