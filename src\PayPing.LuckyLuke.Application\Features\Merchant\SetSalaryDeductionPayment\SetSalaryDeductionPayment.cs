﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._12_PlanCreate
{
    public record SetSalaryDeductionPaymentRequest(Guid trackingCode, Guid installmentCode);

    public record SetSalaryDeductionPaymentResponse(Guid installmentCode);

    public class SetSalaryDeductionPaymentEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPut(
                MerchantRoutes.SetSalaryDeductionPayment,
                async (SetSalaryDeductionPaymentRequest request, ISetSalaryDeductionPaymentRequestHandler handler, IValidator<SetSalaryDeductionPaymentRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("SetSalaryDeductionPayment")
            .WithApiVersionSet(builder.NewApiVersionSet("Plan").Build())
            .Produces<SetSalaryDeductionPaymentResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Set Salary Deduction Payment")
            .WithDescription("Set Salary Deduction Payment")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class SetSalaryDeductionPaymentRequestHandler : ISetSalaryDeductionPaymentRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IWalletService _walletService;
        private readonly ICreditService _creditService;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public SetSalaryDeductionPaymentRequestHandler(ApplicationDbContext dbContext,
            IWalletService walletService,
            ICreditService creditService,
            IUserContext userContext,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _walletService = walletService;
            _creditService = creditService;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<SetSalaryDeductionPaymentResponse> HandleAsync(SetSalaryDeductionPaymentRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var installment = await _dbContext.Installments
                .Where(x => x.Code == request.installmentCode && x.Order.MerchantUserId == userId && x.Status == Domain.Enums.InstallmentStatus.SalaryDeductionWaiting)
                .Include(x => x.Order)
                .FirstOrDefaultAsync(cancellationToken);

            if (installment == null)
            {
                throw new ArgumentException("بازپرداخت مورد نظر یافت نشد");
            }

            var ipRunningSumResult = await _creditService.IncreaseInstallmentSumOnCreditAndSaveAsync(
                installment.Order.OrderPlan.CreditId,
                installment.FinalAmount,
                cancellationToken);

            if (!ipRunningSumResult.Success)
            {
                await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                    installment.Order.OrderPlan.CreditId,
                    installment.Order.OrderPlan.WalletId,
                    installment.FinalAmount,
                CreditTransactionType.InstallmentPaymentRS,
                null,
                ipRunningSumResult.Error);
            }

            if (installment.Number == installment.Order.OrderPlan.InstallmentCount)
            {
                // if inst is last inst, we should set order to paidoff
                installment.Order.Status = OrderStatus.PaidOff;
            }
            else if (installment.Order.Status != OrderStatus.InstallmentsPaymentInProgress)
            {
                // if order status is PrePaymentSucceeded we are at first inst paid, set to InstallmentsPaymentInProgress
                // if order status is one of InstallmentsPaymentDelayed or InstallmentsPaymentDefaulted, set to InstallmentsPaymentInProgress
                // if there is more delayed or defaulted installments, related jobs would set them accordingly
                installment.Order.Status = OrderStatus.InstallmentsPaymentInProgress;
            }

            installment.Status = Domain.Enums.InstallmentStatus.SalaryDeductionPaidOff;
            installment.PaymentStatus = PaymentStatus.PaymentSucceeded;
            installment.PaymentDate = DateTimeOffset.UtcNow;



            await _dbContext.SaveChangesAsync();

            return new SetSalaryDeductionPaymentResponse(installment.Code);
        }

    }
    public interface ISetSalaryDeductionPaymentRequestHandler
    {
        ValueTask<SetSalaryDeductionPaymentResponse> HandleAsync(SetSalaryDeductionPaymentRequest request, CancellationToken cancellationToken);
    }

    public class SetSalaryDeductionPaymentValidator : AbstractValidator<SetSalaryDeductionPaymentRequest>
    {
        public SetSalaryDeductionPaymentValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
            RuleFor(x => x.installmentCode).NotEmpty().WithResourceError(() => ValidatorDictionary.installment_code_is_required);

        }
    }

}
