﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
using PayPing.LuckyLuke.Application.Shared.Clients.Http;
using PayPing.LuckyLuke.Application.Shared.Mappers;

namespace PayPing.LuckyLuke.Application.Shared.Services.PromissorySettlement.Kiah<PERSON>han
{
    public class KiaIssueSettlementHandler : KiahooshanS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IKiahooshanPromissorySettlementService
    {
        private readonly IKiahooshanPromissorySettlementService _next;
        private readonly IUploadGrpcClient _uploadGrpcClient;

        public KiaIssueSettlementHandler(
            IKiahooshanPromissorySettlementService next,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IUploadGrpcClient uploadGrpcClient,
            IKiahooshanApiHttpClient kihooApi,
            IOptions<KiahooshanOptions> kihooOptions)
            : base(dbContext, userContext, kihooApi, kihooOptions)
        {
            _next = next;
            _uploadGrpcClient = uploadGrpcClient;
        }

        public async ValueTask<object> HandleAsync(KiahooshanPromissorySettlementContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KiahooshanPromissorySettlementStatusV1.Issue)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var result = await _kihooApi.InitialSettlement(
                new KiahooshanApiInitialSettlementPromissoryRequest(context.PromissoryUniqueId, context.Amount, context.PromissoryTreasuryId),
                context.OrderTrackingCode,
                cancellationToken);

            
            byte[] pdfBytes = Convert.FromBase64String(result.data.unSignedPdf);

            var dsc = await GetValidDigitalSignAsync(context.ConsumerUserId);

            var signResult = await _kihooApi.SignDoc(dsc.SignatureUniqueId, new MemoryStream(pdfBytes), "rawcontract.pdf", context.OrderTrackingCode, cancellationToken);
            if (signResult == null)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "KiaSignRawDocumentHandler; could not sign raw settlement document", false);
            }

            var signedBytes = await signResult.ToByteArrayAsync();

            base.SignedSettlementDocBytes = signedBytes;

            string validName = Path.ChangeExtension(Path.GetRandomFileName(), ".pdf");

            var uresult = await _uploadGrpcClient.UploadUserDocumentAsync(signedBytes, Path.GetExtension(validName).TrimStart('.'), context.ConsumerUserId, cancellationToken);
            if (!uresult.Succeeded)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "KiaSignRawDocumentHandler; could not upload signed promissory settlement to storage service", false);
            }


            context.Status = KiahooshanPromissorySettlementStatusV1.Finalize;
            context.InitialSettleUniqueId = result.data.settleUniqueId;
            context.SettlementSignedDocumentFileId = uresult.SuccessResult;
            context.SettlementSignedDocumentFileContentType = "application/pdf";

            //// save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

    }
}
