﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Application.Infrastructure.Persistence.NpgSql;
using PayPing.BNPL.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class OrderPaymentConfiguration : IEntityTypeConfiguration<OrderPayment>
{
    public void Configure(EntityTypeBuilder<OrderPayment> builder)
    {
        builder.Property(nameof(OrderPayment.PaymentCode)).HasMaxLength(256);
        builder.HasIndex(x => x.PaymentCode).HasDatabaseName("IX_OrderPayment_PaymentCode");

        builder.Property(nameof(OrderPayment.PayDate)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);

        builder.Property(nameof(OrderPayment.CardNumber)).HasMaxLength(26); // secure for sheba too !

    }
}
