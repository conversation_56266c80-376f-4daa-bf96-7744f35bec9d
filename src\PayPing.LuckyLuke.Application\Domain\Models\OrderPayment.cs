﻿using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;
using System.ComponentModel;

namespace PayPing.BNPL.Domain.Models
{
    public class OrderPayment : BaseEntity<long>, IAuditableEntity
    {
        public long OrderId { get; set; }

        public long? InstallmentId { get; set; }

        public long? PaymentGroupId { get; set; }

        public decimal Amount { get; set; }

        public decimal DiscountAmount { get; set; }

        public OrderPaymentStatus PaymentStatus { get; set; }

        public DateTimeOffset? PayDate { get; set; }

        public string PaymentCode { get; set; }

        public OrderPaymentType PaymentType { get; set; }

        public string CardNumber { get; set; }

        public Guid WalletId { get; set; }

        public int CreditId { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }

        public int? LastModifiedBy { get; set; }

        public Order Order { get; set; }

        public Installment Installment { get; set; }

        public PaymentGroup PaymentGroup { get; set; }

    }

    public enum OrderPaymentType
    {
        [Description("پیش پرداخت")]
        PrePayment = 0,
        [Description("قسط")]
        Installment = 1
    }

    public enum OrderPaymentStatus
    {
        [Description("در انتظار")]
        Init = 0,
        [Description("پرداخت موفق")]
        Paid = 1,
        [Description("پرداخت ناموفق")]
        Failed = 2
    }
}
