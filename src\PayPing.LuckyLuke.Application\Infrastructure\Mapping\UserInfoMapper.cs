﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Mapping
{
    public static class UserInfoMapper
    {
        public static UserInfoDto ToUserInfoDto(this UserApiGetUserExtraResponse source)
        {
            if (source == null)
                return null;

            return new UserInfoDto()
            {
                UserId = source.UserId,
                UserName = source.UserName.Trim(),
                PhoneNumber = source.PhoneNumber?.Trim(),
                FirstName = source.FirstName?.Trim(),
                LastName = source.LastName?.Trim(),
                FatherName = source.FatherName?.Trim(),
                BusinessName = source.BusinessName?.Trim(),
                NationalCode = source.NationalCode?.Trim(),
                BirthDate = source.BirthDate,
                IsLegal = source.IsOfficialSubmited ?? false,
                BusinessNationalCode = source.BNationalCode?.Trim(),
                Address = source.Address?.Trim(),
                PostalCode = source.PostalCode?.Trim(),
                FullName = $"{source.FirstName} {source.LastName}".Trim(),
            };
        }
    }
}
