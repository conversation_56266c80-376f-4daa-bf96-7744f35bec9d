﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Extensions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Features.Consumer._22_InstallmentVerify
{
    public record InstallmentVerifyRequest(int status, string errorCode, string data);

    public record InstallmentVerifyDataRequest(string clientRefId, string paymentCode, long amount, long paymentRefId);

    public record InstallmentVerifyResponse(string redirectUrl);

    public class InstallmentVerifyEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.InstallmentPaymentVerify,
                async (
                    [FromForm] InstallmentVerifyRequest request,
                    IInstallmentVerifyHandler handler,
                    IValidator<InstallmentVerifyRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(string.Empty, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Extensions.Html(@$"<!doctype html>
                        <html>
                            <head><title>در حال ارسال ...</title></head>
                            <body>
                                <h3>در حال ارسال ...</h3>
                                
                            </body>
                            <script type='text/javascript'>
                                window.location.replace('{result.redirectUrl}');
                            </script>
                        </html>");
                })
            .AllowAnonymous()
            .DisableAntiforgery()
            .RequireCors(c => c.AllowAnyOrigin())
            .WithName("InstallmentVerify")
            .WithApiVersionSet(builder.NewApiVersionSet("Installment").Build())
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Installment Verify")
            .WithDescription("Installment Verify")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class InstallmentVerifyHandler : IInstallmentVerifyHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IWalletService _walletService;
        private readonly IPaymentGrpcClient _paymentGrpcClient;
        private readonly ICreditService _creditService;
        private readonly ILogger<InstallmentVerifyHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public InstallmentVerifyHandler(ApplicationDbContext dbContext,
            IWalletService walletService,
            IPaymentGrpcClient paymentGrpcClient,
            ICreditService creditService,
            IOptions<BNPLOptions> bnplOptions,
            ILogger<InstallmentVerifyHandler> logger)
        {
            _dbContext = dbContext;
            _walletService = walletService;
            _paymentGrpcClient = paymentGrpcClient;
            _creditService = creditService;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<InstallmentVerifyResponse> HandleAsync(InstallmentVerifyRequest request, CancellationToken cancellationToken)
        {
            var data = JsonSerializer.Deserialize<InstallmentVerifyDataRequest>(request.data);
            if (data == null)
            {
                _logger.LogWarning($"At InstallmentVerify, bank data is null, request data: {request.data}");
                return new InstallmentVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new("success", "false"), new("message", "اطلاعات ارسالی نامعتبر است"), new("amount", "0"), new("code", string.Empty)]));
            }

            var orderPayment = await _dbContext.OrderPayments
                .Where(x => x.PaymentCode == data.paymentCode && x.Id.ToString() == data.clientRefId)
                .Include(x => x.Order)
                .Include(x => x.Installment)
                .FirstOrDefaultAsync();

            string message = string.Empty;

            if (orderPayment == null)
            {
                return new InstallmentVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new ("success", "false"), new ("message", "اطلاعات ارسالی نامعتبر است"), new ("amount", "0"), new ("code", data.paymentCode)]));
            }

            if (orderPayment.PaymentType != OrderPaymentType.Installment || !orderPayment.InstallmentId.HasValue)
            {
                return new InstallmentVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new ("success", "false"), new ("message", "پرداخت باید از نوع پرداخت قسط باشد"), new ("amount", "0"), new ("code", data.paymentCode)]));
            }

            if (orderPayment.PaymentStatus != OrderPaymentStatus.Init)
            {
                return new InstallmentVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new ("success", "false"), new ("message", "اطلاعات ارسالی تکراریست"), new ("amount", "0"), new ("code", data.paymentCode)]));
            }

            var installmentPaymentAcceptableStatuses = OrderStatusProvider.GetInstallmentPaymentAcceptable();
            if (!installmentPaymentAcceptableStatuses.Contains(orderPayment.Order.Status))
            {
                return new InstallmentVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new ("success", "false"), new ("message", "سفارش در مرحله پرداخت قسط نیست"), new ("amount", "0"), new ("code", data.paymentCode)]));
            }

            var verifyResult = await _paymentGrpcClient
                .VerifyPaymentAsync(new PaymentVerifyGrpcRequest(orderPayment.Order.MerchantUserId, data.paymentRefId, orderPayment.PaymentCode));

            var now = DateTimeOffset.UtcNow;
            if (verifyResult != null)
            {
                var ipRunningSumResult = await _creditService.IncreaseInstallmentSumOnCreditAndSaveAsync(
                    orderPayment.CreditId,
                    orderPayment.Amount,
                    cancellationToken);

                if (!ipRunningSumResult.Success)
                {
                    await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                        orderPayment.CreditId,
                        orderPayment.WalletId,
                        orderPayment.Amount,
                        CreditTransactionType.InstallmentPaymentRS,
                        null,
                        ipRunningSumResult.Error);
                }

                if (orderPayment.Installment.Number == orderPayment.Order.OrderPlan.InstallmentCount)
                {
                    // if inst is last inst, we should set order to paidoff
                    orderPayment.Order.Status = OrderStatus.PaidOff;
                }
                else if (orderPayment.Order.Status != OrderStatus.InstallmentsPaymentInProgress)
                {
                    // if order status is PrePaymentSucceeded we are at first inst paid, set to InstallmentsPaymentInProgress
                    // if order status is one of InstallmentsPaymentDelayed or InstallmentsPaymentDefaulted, set to InstallmentsPaymentInProgress
                    // if there is more delayed or defaulted installments, related jobs would set them accordingly
                    orderPayment.Order.Status = OrderStatus.InstallmentsPaymentInProgress;
                }

                orderPayment.PaymentStatus = OrderPaymentStatus.Paid;
                orderPayment.PayDate = now;
                orderPayment.CardNumber = verifyResult.cardNumber;


                orderPayment.Installment.Status = InstallmentStatus.PaidOff;
                orderPayment.Installment.PaymentStatus = PaymentStatus.PaymentSucceeded;
                orderPayment.Installment.PaymentDate = now;

                await _dbContext.SaveChangesAsync();

                return new InstallmentVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new ("success", "true"), new ("message", ""),  new ("amount", orderPayment.Amount.ToString()), new ("code", data.paymentCode)]));
            }
            else
            {
                _logger.LogWarning($"At InstallmentVerify, verify failed for order payment id: {orderPayment.Id}, installment id: {orderPayment.InstallmentId}");

                orderPayment.PaymentStatus = OrderPaymentStatus.Failed;
                orderPayment.Installment.PaymentStatus = PaymentStatus.PaymentFailed;

                await _dbContext.SaveChangesAsync();

                return new InstallmentVerifyResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.InstallmentVerifyUI, [new ("success", "false"), new ("message", string.Empty), new ("amount", "0"), new ("code", data.paymentCode)]));
            }
        }
    }

    public interface IInstallmentVerifyHandler
    {
        ValueTask<InstallmentVerifyResponse> HandleAsync(InstallmentVerifyRequest request, CancellationToken cancellationToken);
    }

    public class InstallmentVerifyValidator : AbstractValidator<InstallmentVerifyRequest>
    {
        public InstallmentVerifyValidator()
        {
            RuleFor(x => x.data).NotEmpty().WithResourceError(() => ValidatorDictionary.installment_verify_data_is_required);
        }
    }

}
