﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._003_SetWalletCreditAmount
{
    public record SetWalletCreditAmountRequest(int contractId, decimal walletCreditAmount);

    public record SetWalletCreditAmountResponse(int contractId, bool IsSuccess);

    public class SetWalletCreditAmountEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.SetWalletCreditAmount,
                async (SetWalletCreditAmountRequest request, ISetWalletCreditAmountRequestHandler handler, IValidator<SetWalletCreditAmountRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("SetWalletCreditAmount")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<SetWalletCreditAmountResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Set Wallet Credit Amount")
            .WithDescription("Set Wallet Credit Amount")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class SetWalletCreditAmountRequestHandler : ISetWalletCreditAmountRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;
        private readonly IWalletService _walletService;

        public SetWalletCreditAmountRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions, IWalletService walletService)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
            _walletService = walletService;
        }

        public async ValueTask<SetWalletCreditAmountResponse> HandleAsync(SetWalletCreditAmountRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var statuses = ActivationStatusProvider.ResetableCreditBalanceContractStatuses();

            var contract = await _dbContext.Contracts
               .Where(c => c.Id == request.contractId && statuses.Contains(c.ActivationStatus))
               .Include(p => p.Credits)
               .FirstOrDefaultAsync(cancellationToken);

            if (contract == null)
            {
                throw new ArgumentException("قرارداد فروش اقساطی فعال یافت نشد");
            }

            var activeCredit = contract.Credits.FirstOrDefault(c => c.IsActive);

            if (activeCredit == null)
            {
                throw new ArgumentException("کیف پول اعتباری فعال قرارداد یافت نشد");
            }

            // Here we let user to reset his own wallet balance on contract create and Renewal

            var result = await _walletService.ResetWalletCreditAmountAndSaveAsync(activeCredit.WalletId, request.walletCreditAmount, userId, cancellationToken);

            contract.ActivationStatus = ActivationStatus.SetWalletCreditAmount;

            await _dbContext.SaveChangesAsync();

            return new SetWalletCreditAmountResponse(contract.Id, result.Success);
        }

    }
    public interface ISetWalletCreditAmountRequestHandler
    {
        ValueTask<SetWalletCreditAmountResponse> HandleAsync(SetWalletCreditAmountRequest request, CancellationToken cancellationToken);
    }

    public class SetWalletCreditAmountValidator : AbstractValidator<SetWalletCreditAmountRequest>
    {
        public SetWalletCreditAmountValidator()
        {
            RuleFor(c => c.contractId)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.contract_id_is_required);
            RuleFor(x => x.walletCreditAmount).NotEmpty();
        }
    }

}
