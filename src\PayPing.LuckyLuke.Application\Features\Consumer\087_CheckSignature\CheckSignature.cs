﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.Threading;

namespace PayPing.LuckyLuke.Application.Features.Consumer._086_CheckSignature
{
    public record CheckSignatureRequest(Guid trackingCode);

    public record CheckSignatureResponse(Guid trackingCode);

    public class CheckSignatureEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.CheckSignature,
                async (
                    CheckSignatureRequest request,
                    ICheckSignatureRequestHandler handler,
                    IValidator<CheckSignatureRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .DisableAntiforgery()
            .WithName("CheckSignature")
            .WithApiVersionSet(builder.NewApiVersionSet("Signature").Build())
            .Produces<CheckSignatureResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Check Signature")
            .WithDescription("Check Signature")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CheckSignatureRequestHandler : ICheckSignatureRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IEnumerable<IDigitalSignProvider> _digitalSignProviders;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IUserContext _userContext;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly IMemoryCache _memoryCache;
        private readonly BNPLOptions _bnplOptions;

        public CheckSignatureRequestHandler(
            ApplicationDbContext dbContext,
            IEnumerable<IDigitalSignProvider> digitalSignProviders,
            IGuaranteeService guaranteeService,
            IUserContext userContext,
            IUploadGrpcClient uploadGrpcClient,
            IMemoryCache memoryCache,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _digitalSignProviders = digitalSignProviders;
            _guaranteeService = guaranteeService;
            _userContext = userContext;
            _uploadGrpcClient = uploadGrpcClient;
            _memoryCache = memoryCache;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<CheckSignatureResponse> HandleAsync(CheckSignatureRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders
                .Where(o => o.TrackingCode == request.trackingCode)
                .FirstOrDefaultAsync(cancellationToken);

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            {
                throw new OrderExpiredException(request.trackingCode.ToString(), "سفارش منقضی شده است");
            }

            if (order.ConsumerUserId != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }

            var consumerInfo = await _dbContext.ConsumerInfos.Where(x => x.ConsumerUserId == userId).FirstOrDefaultAsync();

            DigitalSignType digitalSignType = await _guaranteeService.GetGuarantorCompliantSignatureTypeByGuarantorId(order.OrderPlan.GuarantorId);

            var provider = ChooseProvider(digitalSignType);

            var currentds = await provider.GetUserInProgressOrValidDigitalSignatureAsync(order.ConsumerUserId.Value, true);
            if (currentds == null)
            {
                currentds = new Domain.Models.DigitalSign
                {
                    Type = digitalSignType,
                    UserId = order.ConsumerUserId.Value,
                };

                _dbContext.DigitalSigns.Add(currentds);

                var result = await _dbContext.SaveChangesAsync();
                if (result == 0)
                {
                    throw new Exception("At CheckSignatureRequestHandler; could not save DigitalSign");
                }
            }

            if (!string.IsNullOrEmpty(currentds.SignatureRefId))
            {
                return new(order.TrackingCode);
            }

            string data = currentds.Data;

            if (string.IsNullOrWhiteSpace(data))
            {
                data = await provider.CreateDigitalSignAsync(order.TrackingCode, currentds.Id, order.ConsumerUserId.Value, true, cancellationToken);

                currentds.Data = data;

                await _dbContext.SaveChangesAsync();
            }


            var checkPoint = await provider.PushToNextCheckPointAsync(currentds.Data, cancellationToken);

            currentds.SignatureRefId = checkPoint.data;

            order.Status = OrderStatus.DigitalSignatureCreated;

            await _dbContext.SaveChangesAsync();


            return new(order.TrackingCode);
        }

        private IDigitalSignProvider ChooseProvider(DigitalSignType digitalSignType)
        {
            return _digitalSignProviders.First(f => f.DigitalSignType == digitalSignType);
        }
    }

    public interface ICheckSignatureRequestHandler
    {
        ValueTask<CheckSignatureResponse> HandleAsync(CheckSignatureRequest request, CancellationToken cancellationToken);
    }

    public class CheckSignatureValidator : AbstractValidator<CheckSignatureRequest>
    {
        public CheckSignatureValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}
