﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;

namespace PayPing.LuckyLuke.Application.Features.Merchant._001_CreateContract
{
    public record CreateContractRequest();

    public record CreateContractResponse(int contractId);

    public class CreateContractEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.CreateContract,
                async (CreateContractRequest request, ICreateContractRequestHandler handler, IValidator<CreateContractRequest> validator, CancellationToken cancellationToken) =>
            {

                var validationResult = await validator.ValidateAsync(request);
                if (!validationResult.IsValid)
                {
                    throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                }

                var result = await handler.HandleAsync(request, cancellationToken);

                return Results.Ok(result);
            })
            .RequireAuthorization("write")
            .WithName("CreateContract")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<CreateContractResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Create Contract")
            .WithDescription("Create Contract")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CreateContractRequestHandler : ICreateContractRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public CreateContractRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<CreateContractResponse> HandleAsync(CreateContractRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var now = DateTimeOffset.UtcNow;

            var currentContract = await _dbContext.Contracts.AsNoTracking()
                .Where(p => p.MerchantUserId == userId)
                .Include(p => p.Credits)
                .OrderByDescending(p => p.ExpireDate)
                .FirstOrDefaultAsync();

            if (currentContract != null && currentContract.ExpireDate > now)
            {
                throw new ArgumentException("پذیرنده قرارداد فروش اقساطی فعال دارد");
            }

            var merchantInfo = await _dbContext.MerchantInfos.AsNoTracking()
                .Where(p => p.MerchantUserId == userId)
                .FirstOrDefaultAsync();

            if (merchantInfo == null)
            {
                merchantInfo = new MerchantInfo()
                {
                    InterestRate = 0,
                    MinPrePaymentRate = 20,
                    MerchantUserId = userId,
                };

                _dbContext.MerchantInfos.Add(merchantInfo);
            }

            var contract = new Contract()
            {
                MerchantUserId = userId,
                StartDate = now,
                ExpireDate = now.AddYears(1),
                OperationCostStrategy = MerchantOperationCostStrategy.PercentOfOrderTotalAmount,
                OperationCost = 1.5m,
                ActivationStatus = ActivationStatus.CreateContract,
            };

            _dbContext.Contracts.Add(contract);

            await _dbContext.SaveChangesAsync();

            return new CreateContractResponse(contract.Id);
        }

    }
    public interface ICreateContractRequestHandler
    {
        ValueTask<CreateContractResponse> HandleAsync(CreateContractRequest request, CancellationToken cancellationToken);
    }

    public class CreateContractValidator : AbstractValidator<CreateContractRequest>
    {
        public CreateContractValidator()
        {

        }
    }

}
