﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class Plan : BaseEntity<int>, IAuditableEntity, ISoftDeletableEntity
    {

        // shop
        public int MerchantUserId { get; set; }

        // lender may differ from client
        public int CreditId { get; set; }

        public GuaranteeType GuaranteeType { get; set; }

        public Guid Code { get; set; }


        public decimal MinOrderAmount { get; set; }
        public decimal MaxCreditAmount { get; set; }
        public decimal MinPrePaymentRate { get; set; }
        
        /// <summary>
        /// at least 1
        /// </summary>
        public int InstallmentCount { get; set; } = 1;

        /// <summary>
        /// at least 1
        /// </summary>
        public int InstallmentPeriodInMonths { get; set; } = 1;

        public decimal InterestRate { get; set; }

        public bool HasInstallmentDelayPenalty { get; set; }
        public decimal InstallmentDelayPenaltyRatePerDay { get; set; }
        public int InstallmentDelayPenaltyFreeInDays { get; set; }

        public bool ContractIsMandatory { get; set; }

        public bool IsActive { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }
        public int? LastModifiedBy { get; set; }

        public bool IsDeleted { get; set; }
        public DateTimeOffset? DeletedAt { get; set; }

        public Credit Credit { get; set; }
        public ICollection<Order> Orders { get; } = new HashSet<Order>();
        public ICollection<OrderTargetPlan> OrderTargetPlans { get; } = new HashSet<OrderTargetPlan>();
        public ICollection<PlanEmployee> PlanEmployees { get; } = new HashSet<PlanEmployee>();

    }


}
