﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Application.Infrastructure.Persistence.NpgSql;
using PayPing.BNPL.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class InstallmentConfiguration : IEntityTypeConfiguration<Installment>
{
    public void Configure(EntityTypeBuilder<Installment> builder)
    {
        builder.HasIndex(x => x.Code).IsUnique().HasDatabaseName("IX_Installment_Code");

        builder.Property(nameof(Installment.DueDate)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);
        builder.Property(nameof(Installment.PaymentDate)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);

        builder
            .HasMany(e => e.OrderPayments)
            .WithOne(e => e.Installment)
            .HasForeignKey(e => e.InstallmentId)
            .IsRequired(false);
    }
}
