﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class FixedOrderAndPlan : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "OrderPlan_PrePaymentAmount",
                table: "Orders",
                newName: "PrePaymentAmount");

            migrationBuilder.AddColumn<string>(
                name: "CreditLockId",
                table: "Orders",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TrackingCode",
                table: "Orders",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_Order_ClientRefId",
                table: "Orders",
                column: "ClientRefId");

            migrationBuilder.CreateIndex(
                name: "IX_Order_TrackingCode",
                table: "Orders",
                column: "TrackingCode",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Order_ClientRefId",
                table: "Orders");

            migrationBuilder.DropIndex(
                name: "IX_Order_TrackingCode",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "CreditLockId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "TrackingCode",
                table: "Orders");

            migrationBuilder.RenameColumn(
                name: "PrePaymentAmount",
                table: "Orders",
                newName: "OrderPlan_PrePaymentAmount");
        }
    }
}
