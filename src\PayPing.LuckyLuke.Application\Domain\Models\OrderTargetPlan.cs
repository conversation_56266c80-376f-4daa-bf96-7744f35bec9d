﻿using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.BNPL.Domain.Models
{
    public class OrderTargetPlan : BaseEntity<long>
    {
        public long OrderId { get; set; }
        public int PlanId { get; set; }


        public Plan Plan { get; set; }
        public Order Order { get; set; }
    }
}
