﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer._21_InstallmentPayment
{
    public record InstallmentPaymentRequest(Guid trackingCode, Guid installmentCode);

    public record InstallmentPaymentResponse(string url);

    public class InstallmentPaymentEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.InstallmentPayment,
                async (
                    InstallmentPaymentRequest request,
                    IInstallmentPaymentRequestHandler handler,
                    IValidator<InstallmentPaymentRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, 1, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("InstallmentPayment")
            .WithApiVersionSet(builder.NewApiVersionSet("Installment").Build())
            .Produces<InstallmentPaymentResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Installment Payment")
            .WithDescription("Installment Payment")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class InstallmentPaymentRequestHandler : IInstallmentPaymentRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IPaymentGrpcClient _paymentGrpcClient;
        private readonly IUserService _userService;
        private readonly IUserContext _userContext;
        private readonly ILogger<InstallmentPaymentRequestHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public InstallmentPaymentRequestHandler(ApplicationDbContext dbContext, IPaymentGrpcClient paymentGrpcClient, IUserService userService, IUserContext userContext, IOptions<BNPLOptions> bnplOptions, ILogger<InstallmentPaymentRequestHandler> logger)
        {
            _dbContext = dbContext;
            _paymentGrpcClient = paymentGrpcClient;
            _userService = userService;
            _userContext = userContext;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<InstallmentPaymentResponse> HandleAsync(InstallmentPaymentRequest request, int apiVersion, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.Where(o => o.TrackingCode == request.trackingCode).FirstOrDefaultAsync();

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ConsumerUserId != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }

            var installmentPaymentAcceptableStatuses = OrderStatusProvider.GetInstallmentPaymentAcceptable();

            if (!installmentPaymentAcceptableStatuses.Contains(order.Status))
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش در مرحله پرداخت قسط نیست", string.Empty);
            }

            var installment = await _dbContext.Installments.Where(o => o.Code == request.installmentCode && o.OrderId == order.Id).FirstOrDefaultAsync();

            if (installment == null)
            {
                throw new NotFoundException(request.installmentCode.ToString(), "installment");
            }

            if (installment.PaymentStatus == PaymentStatus.PaymentSucceeded || installment.Status == InstallmentStatus.PaidOff || installment.Status == InstallmentStatus.SalaryDeductionPaidOff)
            {
                throw new Exception("قسط پرداخت شده است");
            }

            var consumerInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);

            string returnUrl = UrlHelpers.BuildFullApiUrl(_bnplOptions.BaseUrl, ConsumerRoutes.InstallmentPaymentVerify, apiVersion);

            var op = new OrderPayment()
            {
                InstallmentId = installment.Id,
                OrderId = order.Id,
                Amount = installment.FinalAmount,
                PaymentStatus = OrderPaymentStatus.Init,
                PaymentType = OrderPaymentType.Installment,
                WalletId = order.OrderPlan.WalletId,
                CreditId = order.OrderPlan.CreditId,
            };

            order.OrderPayments.Add(op);
            await _dbContext.SaveChangesAsync();

            try
            {
                var payResp = await _paymentGrpcClient.CreatePaymentAsync(
                new PaymentGrpcRequest(
                    order.MerchantUserId,
                    (int)installment.FinalAmount,
                    0,
                    0,
                    returnUrl,
                    consumerInfo.FullName,
                    "پرداخت قسط سرویس اعتباری",
                    op.Id.ToString(),
                    consumerInfo.UserName));

                if (payResp != null && !string.IsNullOrWhiteSpace(payResp.paymentCode))
                {
                    op.PaymentCode = payResp.paymentCode;

                    installment.Status = InstallmentStatus.Waiting;
                    installment.PaymentStatus = PaymentStatus.PaymentInProgress;

                    await _dbContext.SaveChangesAsync();

                    return new InstallmentPaymentResponse(payResp.url);
                }
                else
                {
                    _logger.LogWarning($"installment creation failed for order: {order.TrackingCode}, installment: {installment.Code}");

                    installment.PaymentStatus = PaymentStatus.PaymentFailed;

                    await _dbContext.SaveChangesAsync();

                    throw new PaymentException(request.trackingCode.ToString(), $"installment creation failed for order: {order.TrackingCode}, installment: {installment.Code}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.InnerException?.Message ?? ex.Message);

                installment.PaymentStatus = PaymentStatus.PaymentFailed;

                await _dbContext.SaveChangesAsync();

                throw new PaymentException(request.trackingCode.ToString(), ex.Message);
            }
        }
    }

    public interface IInstallmentPaymentRequestHandler
    {
        ValueTask<InstallmentPaymentResponse> HandleAsync(InstallmentPaymentRequest request, int apiVersion, CancellationToken cancellationToken);
    }

    public class InstallmentPaymentValidator : AbstractValidator<InstallmentPaymentRequest>
    {
        public InstallmentPaymentValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required); 
            RuleFor(x => x.installmentCode).NotEmpty().WithResourceError(() => ValidatorDictionary.installment_code_is_required);
        }
    }
}

