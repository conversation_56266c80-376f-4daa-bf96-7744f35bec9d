﻿using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kuknos;

namespace PayPing.LuckyLuke.Application.Shared.Services.PromissorySettlement.Kuknos
{
    public class IssuePromissorySettlementHandler : KuknosSettlementBaseHandler, IKuknosPromissorySettlementService
    {
        private readonly IKuknosPromissorySettlementService _next;
        private readonly IUploadGrpcClient _uploadGrpcClient;

        public IssuePromissorySettlementHandler(
            IKuknosPromissorySettlementService next,
            IUploadGrpcClient uploadGrpcClient,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _next = next;
            _uploadGrpcClient = uploadGrpcClient;
        }

        public async ValueTask<object> HandleAsync(KuknosPromissorySettlementContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosPromissorySettlementStatusV1.IssueSettlement)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var result = await _kuknosApi.IssueSettlementAsync(context.PromissoryId, context.OrderTrackingCode, cancellationToken);

            var raw = await _kuknosApi.DownloadRawDocumentAsync(result.data.unsigned_document, context.OrderTrackingCode);

            if (raw == null || raw.contentBytes == null || raw.contentBytes.Length == 0)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not download raw promissory settlement document", false);
            }

            base.SettlementRawDocBytes = raw.contentBytes;

            context.Status = KuknosPromissorySettlementStatusV1.Sign;
            context.SettlementRawDocumentHash = result.data.unsigned_document;
            context.SettlementRawDocumentFileContentType = raw.contentType;

            //// save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

    }
}
