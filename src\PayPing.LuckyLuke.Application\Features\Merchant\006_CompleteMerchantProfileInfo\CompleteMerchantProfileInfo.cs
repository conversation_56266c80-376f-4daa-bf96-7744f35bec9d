﻿using DocumentFormat.OpenXml.Wordprocessing;
using Elastic.CommonSchema;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Org.BouncyCastle.Asn1.X509;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._006_CompleteMerchantProfileInfo
{
    public record CompleteMerchantProfileInfoRequest
    {
        public int contractId { get; set; }
        public string firstNameEnglish { get; set; }
        public string lastNameEnglish { get; set; }
        public string nationalCode { get; set; }
        public string birthDate { get; set; }
        public bool isMale { get; set; }
        public string nationalIdSeries { get; set; }
        public string mobileNumber { get; set; }

        public string email { get; set; }
        public string provinceEnglish { get; set; }
        public string cityEnglish { get; set; }
        public string postalCode { get; set; }
        public string address { get; set; }

    }

    public record CompleteMerchantProfileInfoResponse(int contractId, bool hasSignatureImageFile);

    public class CompleteMerchantProfileInfoEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.CompleteMerchantProfileInfo,
                async (CompleteMerchantProfileInfoRequest request, ICompleteMerchantProfileInfoRequestHandler handler, IValidator<CompleteMerchantProfileInfoRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("CompleteMerchantProfileInfo")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<CompleteMerchantProfileInfoResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Complete Merchant Profile Info")
            .WithDescription("Complete Merchant Profile Info")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CompleteMerchantProfileInfoRequestHandler : ICompleteMerchantProfileInfoRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;
        private readonly IInquiryApiClient _inquiryApiClient;

        public CompleteMerchantProfileInfoRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IInquiryApiClient inquiryApiClient, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
            _inquiryApiClient = inquiryApiClient;
        }

        public async ValueTask<CompleteMerchantProfileInfoResponse> HandleAsync(CompleteMerchantProfileInfoRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var contract = await _dbContext.Contracts
               .Where(c => c.Id == request.contractId)
               .Include(p => p.Credits)
               .FirstOrDefaultAsync();

            if (contract == null)
            {
                throw new ArgumentException("قرارداد فروش اقساطی فعال یافت نشد");
            }

            var merchantInfo = await _dbContext.MerchantInfos
                .Where(p => p.MerchantUserId == userId)
                .FirstOrDefaultAsync();

            if (merchantInfo == null)
            {
                throw new ArgumentException("اطلاعات مرچنت یافت نشد");
            }


            // shahkar check mobile,nationalcode
            var shahkarMatched = await _inquiryApiClient.IsMatchingMobileWithNationalCode(request.mobileNumber, request.nationalCode, cancellationToken);
            if (!shahkarMatched.Succeeded)
            {
                throw new ArgumentException(shahkarMatched.Message);
            }

            if (!shahkarMatched.SuccessResult.Matched)
            {
                throw new ArgumentException("شماره موبایل با کد ملی مطابقت ندارد");
            }

            // sejel inquiry nationalcode,birthdate
            var personalInfoResult = await _inquiryApiClient.GetPersonalInfoByNationalCode(request.nationalCode, request.birthDate, cancellationToken);
            if (!personalInfoResult.Succeeded)
            {
                throw new ArgumentException(personalInfoResult.Message);
            }

            if (!personalInfoResult.SuccessResult.FirstName.HasValue() ||
                !personalInfoResult.SuccessResult.LastName.HasValue() ||
                !personalInfoResult.SuccessResult.FatherName.HasValue())
            {
                throw new ArgumentException("تاریخ تولد یا کد ملی صحیح نیست");
            }

            merchantInfo.Email = request.email;
            merchantInfo.ProvinceEnglish = request.provinceEnglish;
            merchantInfo.CityEnglish = request.cityEnglish;
            merchantInfo.PostalCode = request.postalCode;
            merchantInfo.Address = request.address;

            merchantInfo.MerchantInfoAgent = new MerchantInfoAgent(
                personalInfoResult.SuccessResult.FirstName,
                personalInfoResult.SuccessResult.LastName,
                request.firstNameEnglish,
                request.lastNameEnglish,
                request.nationalCode,
                request.birthDate,
                request.isMale,
                request.nationalIdSeries,
                personalInfoResult.SuccessResult.FatherName,
                request.mobileNumber
                );


            contract.ActivationStatus = ActivationStatus.CompleteMerchantProfileInfo;

            await _dbContext.SaveChangesAsync();

            return new CompleteMerchantProfileInfoResponse(contract.Id, merchantInfo.SignatureImageFileId.HasValue());
        }

    }
    public interface ICompleteMerchantProfileInfoRequestHandler
    {
        ValueTask<CompleteMerchantProfileInfoResponse> HandleAsync(CompleteMerchantProfileInfoRequest request, CancellationToken cancellationToken);
    }

    public class CompleteMerchantProfileInfoValidator : AbstractValidator<CompleteMerchantProfileInfoRequest>
    {
        public CompleteMerchantProfileInfoValidator()
        {
            RuleFor(c => c.contractId)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.contract_id_is_required);

            RuleFor(c => c.firstNameEnglish)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.first_name_english_is_required)
                .Matches("^[A-Za-z ]+$").WithResourceError(() => ValidatorDictionary.first_name_english_not_valid);

            RuleFor(c => c.lastNameEnglish)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.last_name_english_is_required)
                .Matches("^[A-Za-z ]+$").WithResourceError(() => ValidatorDictionary.last_name_english_not_valid);


            RuleFor(c => c.nationalCode)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.nationalcode_is_required)
                .Matches("^[0-9]{10}$").WithResourceError(() => ValidatorDictionary.nationalcode_is_not_valid);

            RuleFor(c => c.birthDate)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.birthdate_is_required)
                .Matches("^[0-9]{4}/[0-9]{2}/[0-9]{2}$").WithResourceError(() => ValidatorDictionary.birthdate_is_not_valid);

            RuleFor(c => c.mobileNumber)
                 .NotEmpty().WithResourceError(() => ValidatorDictionary.mobile_is_required)
                 .Matches("^09[0-9]{9}$").WithResourceError(() => ValidatorDictionary.mobile_not_valid);

            RuleFor(c => c.postalCode)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.postal_code_is_required)
                .Matches("[0-9]{10}").WithResourceError(() => ValidatorDictionary.postal_code_not_valid);

            RuleFor(c => c.address)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.address_is_required);


            RuleFor(c => c.nationalIdSeries)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.national_id_series_is_required)
                .Matches("^[A-Za-z0-9]+$").WithResourceError(() => ValidatorDictionary.national_id_series_not_valid);

            RuleFor(c => c.email)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.email_is_required)
                .EmailAddress().WithResourceError(() => ValidatorDictionary.email_not_valid);

            RuleFor(c => c.provinceEnglish)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.province_english_is_required)
                .Matches("^[A-Za-z]+$").WithResourceError(() => ValidatorDictionary.province_english_not_valid);

            RuleFor(c => c.cityEnglish)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.city_english_is_required)
                .Matches("^[A-Za-z]+$").WithResourceError(() => ValidatorDictionary.city_english_not_valid);

        }
    }

}