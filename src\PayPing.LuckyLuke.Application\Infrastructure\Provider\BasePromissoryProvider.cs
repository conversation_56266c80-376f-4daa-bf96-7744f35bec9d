﻿using PayPing.LuckyLuke.Application.Infrastructure.Web;

namespace PayPing.LuckyLuke.Application.Infrastructure.Provider
{
    public abstract class BasePromissoryProvider
    {
        protected (string name, OrderSteps code) ZeroPitStop => ("صدور سفته", OrderSteps.IssuePromissory);
        protected (string name, OrderSteps code) SecondPitStop => ("امضا سفته الکترونیک", OrderSteps.SignPromissory);
        protected (string name, OrderSteps code) ThirdPitStop => ("پرداخت هزینه سفته", OrderSteps.PayPromissory);
    }
}
