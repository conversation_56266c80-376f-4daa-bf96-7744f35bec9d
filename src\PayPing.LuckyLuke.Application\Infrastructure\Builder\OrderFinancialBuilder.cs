﻿using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Domain.Models;
using PayPing.Integrations.Models.Helpers;
using PayPing.LuckyLuke.Application.Domain.Enums;

namespace PayPing.LuckyLuke.Application.Infrastructure.Builder
{
    public class OrderFinancialBuilder : FinancialBuilder
    {
        public OrderFinancialBuilder(decimal totalAmount, decimal maxCreditAmount, decimal minPrePaymentRate, int installmentCount, int installmentPeriodInMonths, decimal interestRate, decimal operationCost, MerchantOperationCostStrategy operationCostStrategy, GuaranteeType guaranteeType, bool isDevelopment)
            : base(totalAmount, maxCreditAmount, minPrePaymentRate, installmentCount, installmentPeriodInMonths, interestRate, operationCost, operationCostStrategy, guaranteeType, isDevelopment)
        {
        }


        public override FinancialBuilder SetInstallments()
        {
            if (step != 2)
            {
                throw new Exception($"OrderFinancialBuilder wrong step. step: {step}");
            }

            step += 1;

            var mod = decimal.Remainder(InstallmentsTotalRawAmount, _installmentCount);

            var newTotal = decimal.Subtract(InstallmentsTotalRawAmount, mod);

            decimal eachInstallmentRawAmount = decimal.Divide(newTotal, _installmentCount);

            var baseDueDate = GetBaseDuteDate();

            // Monthly interest
            var interest = Math.Ceiling(decimal.Multiply(InstallmentsTotalRawAmount, decimal.Divide(_interestRate, 100m)));

            for (int i = 0; i < _installmentCount; i++)
            {
                decimal principal = eachInstallmentRawAmount;
                decimal totalAmount = decimal.Add(principal, interest);

                if (i == 0)
                {
                    principal = decimal.Add(eachInstallmentRawAmount, mod);
                    totalAmount = decimal.Add(totalAmount, mod);
                }

                Installments.Add(new(
                    principal,
                    interest,
                    totalAmount,
                    0m,
                    totalAmount,
                    GetInstallmentDueDate(baseDueDate, i)
                ));
            }

            return this;
        }

        private DateTimeOffset GetBaseDuteDate()
        {
            var now = DateTimeOffset.UtcNow;
            var dueStart = now.Hour >= 20 ? now.AddDays(1) : now;
            return new DateTimeOffset(dueStart.Year, dueStart.Month, dueStart.Day, 4, 30, 0, TimeSpan.Zero);
        }

        private DateTimeOffset GetInstallmentDueDate(DateTimeOffset baseDate, int iteration)
        {
            var pBaseDate = _persianCalendar.AddMonths(baseDate.DateTime, (iteration + 1) * _installmentPeriodInMonths);
            return new DateTimeOffset(pBaseDate.Year, pBaseDate.Month, pBaseDate.Day, 4, 30, 0, TimeSpan.Zero);
        }

        public override OrderFinancialDto Build() => new OrderFinancialDto(CreditedAmount, GuaranteeAmount, RawPrePaymentAmount, MerchantOperationCostAmount, ConsumerOperationCostAmount, InstallmentsTotalRawAmount, Installments);

    }
}
