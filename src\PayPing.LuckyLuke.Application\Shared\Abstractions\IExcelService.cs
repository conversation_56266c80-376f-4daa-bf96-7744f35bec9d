﻿using PayPing.LuckyLuke.Application.Features.Merchant.OrderListExcel;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Shared.Abstractions
{
    public interface IExcelService
    {
        ValueTask<MemoryStream> CreateOrderInfoExcelFile(OrderInfos OrderInfosModel, CancellationToken cancellationToken = default);
        DataSet ReadExcelFileIntoDataSet(Stream excelFileStream);
    }
}
