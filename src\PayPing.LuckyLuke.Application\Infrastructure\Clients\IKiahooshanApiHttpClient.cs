﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.Tools.SdkBase.Types;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public interface IKiahooshanApiHttpClient
    {
        ValueTask<KiahooshanApiResponse<string>> DeleteAsync(KiahooshanApiDeletePromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<ServiceResult<KiahooshanApiResponse<KiahooshanApiFinalizeResponse>>> FinalizeAsync(KiahooshanApiFinalizePromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<KiahooshanApiVideoVerifyResponse> FinalizeSettlement(KiahooshanApiFinalizeSettlementPromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<Stream> GetSignedDoc(string signedDocUniqueId, Guid? orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<string> GetTokenAsync(Guid? orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<KiahooshanApiGetVideoVerifyResultResponse> GetVideoVerifyResult(string selfieVideoUniqueId, Guid? orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<KiahooshanApiResponse<KiahooshanApiSettlementResponse>> InitialSettlement(KiahooshanApiInitialSettlementPromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<KiahooshanApiResponse<KiahooshanApiIssueDigiProNoteResponse>> IssueDigiProNote(KiahooshanApiIssuePromissoryRequest model, Guid orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<KiahooshanApiIssueDigiSigResponse> IssueDigiSig(
            string birthDate,
            string mobileNumber,
            string nationalCode,
            string randText,
            byte[] selfieVideo,
            string selfieVideoName,
            string vvsId,
            Guid? orderTrackingCode,
            CancellationToken cancellationToken = default);
        ValueTask<Stream> SignDoc(string sigUniqueId, Stream docFile, string docFileName, Guid? orderTrackingCode, CancellationToken cancellationToken = default);
        ValueTask<KiahooshanApiVideoVerifyResponse> VerifyVideoWithNationalId(
            string birthDate,
            string nationalCode,
            string randText,
            byte[] selfieVideo,
            string selfieVideoName,
            Guid? orderTrackingCode,
            CancellationToken cancellationToken = default);
    }
}