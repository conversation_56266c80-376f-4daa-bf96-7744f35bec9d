﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Admin._001_MerchantContractList
{
    public record MerchantContractListRequest(ActivationStatus[] statuses, int pageNumber = 1, int pageSize = 10);

    public class MerchantContractListResponse
    {
        public MerchantContractListResponse(List<MerchantContract> merchantContracts, int total)
        {
            this.MerchantContracts = merchantContracts;
            this.Total = total;
        }
        public List<MerchantContract> MerchantContracts { get; set; }
        public long Total { get; set; }
    }

    public class MerchantContract
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string UserId { get; set; }
        public ActivationStatus? ActivationStatus { get; set; }
        public DateTimeOffset? CreatedDate { get; set; }
        public DateTimeOffset? StartDate { get; set; }
        public DateTimeOffset? ExpireDate { get; set; }
    }

    public class MerchantContractListEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                AdminRoutes.MerchantContractList,
                async ([AsParameters] MerchantContractListRequest request, IMerchantContractListRequestHandler handler, IValidator<MerchantContractListRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("admin")
            .WithName("MerchantContractList")
            .WithApiVersionSet(builder.NewApiVersionSet("MerchantContract").Build())
            .Produces<MerchantContractListResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Merchant Contract List")
            .WithDescription("Merchant Contract List")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class MerchantContractListRequestHandler : IMerchantContractListRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;

        public MerchantContractListRequestHandler(ApplicationDbContext dbContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<MerchantContractListResponse> HandleAsync(MerchantContractListRequest request, CancellationToken cancellationToken)
        {
            var now = DateTimeOffset.UtcNow;

            IQueryable<MerchantContract> merchantContracts = null;

            if (request.statuses == null || request.statuses.Length == 0)
            {
                merchantContracts = from merchantInfo in _dbContext.MerchantInfos
                                    join contract in _dbContext.Contracts
                                    on merchantInfo.MerchantUserId equals contract.MerchantUserId into contractGroup
                                    from contract in contractGroup.DefaultIfEmpty()
                                    where (contract == null || contract.ExpireDate >= now)
                                    select new MerchantContract
                                    {
                                        FirstName = merchantInfo.MerchantInfoAgent.FirstName,
                                        LastName = merchantInfo.MerchantInfoAgent.LastName,
                                        ActivationStatus = contract != null ? contract.ActivationStatus : null,
                                        CreatedDate = contract != null ? contract.CreatedAt : null,
                                        StartDate = contract != null ? contract.StartDate : null,
                                        ExpireDate = contract != null ? contract.ExpireDate : null,
                                        Email = merchantInfo.Email,
                                        UserId = merchantInfo.MerchantUserId.ToString()
                                    };

            }
            else
            {
                merchantContracts = from merchantInfo in _dbContext.MerchantInfos
                                    join contract in _dbContext.Contracts
                                    on merchantInfo.MerchantUserId equals contract.MerchantUserId into contractGroup
                                    from contract in contractGroup
                                    where contract.ExpireDate >= now && request.statuses.Contains(contract.ActivationStatus)
                                    select new MerchantContract
                                    {
                                        FirstName = merchantInfo.MerchantInfoAgent.FirstName,
                                        LastName = merchantInfo.MerchantInfoAgent.LastName,
                                        ActivationStatus = contract.ActivationStatus,
                                        CreatedDate = contract.CreatedAt,
                                        StartDate = contract.StartDate,
                                        ExpireDate = contract.ExpireDate,
                                        Email = merchantInfo.Email,
                                        UserId = merchantInfo.MerchantUserId.ToString()
                                    };
            }

            var totalCount = await merchantContracts
             .CountAsync(cancellationToken);

            var merchantContractList = await merchantContracts
              .OrderByDescending(o => o.ExpireDate)
              .Skip((request.pageNumber - 1) * request.pageSize)
              .Take(request.pageSize)
              .ToListAsync(cancellationToken);

            return new MerchantContractListResponse(merchantContractList, totalCount);
        }

    }
    public interface IMerchantContractListRequestHandler
    {
        ValueTask<MerchantContractListResponse> HandleAsync(MerchantContractListRequest request, CancellationToken cancellationToken);
    }

    public class MerchantContractListValidator : AbstractValidator<MerchantContractListRequest>
    {
        public MerchantContractListValidator()
        {
            RuleFor(x => x.pageSize).GreaterThan(0).WithResourceError(() => ValidatorDictionary.page_size_greater_zero);
            RuleFor(x => x.pageNumber).GreaterThan(0).WithResourceError(() => ValidatorDictionary.page_number_greater_zero);
        }
    }

}
