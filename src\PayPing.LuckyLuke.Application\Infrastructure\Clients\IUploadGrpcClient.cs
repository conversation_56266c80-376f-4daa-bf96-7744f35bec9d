﻿using PayPing.FileManager.Grpc;
using PayPing.Tools.SdkBase.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public interface IUploadGrpcClient
    {
        ValueTask<ServiceResult<string>> GetPresignedUrlAsync(string fileName, int userId, UploadTypeEnum uploadType, CancellationToken cancellationToken = default);
        ValueTask<ServiceResult<string>> UploadExcelAsync(Stream stream, int userId, CancellationToken cancellationToken = default);
        ValueTask<ServiceResult<string>> UploadUserDocumentAsync(byte[] file, string extension, int userId, CancellationToken cancellationToken = default);
    }
}
