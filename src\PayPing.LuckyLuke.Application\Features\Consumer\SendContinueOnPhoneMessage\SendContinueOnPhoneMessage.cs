﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Features.Consumer.SendContinueOnPhoneMessage
{
    public record SendContinueOnPhoneMessageRequest(Guid trackingCode);

    public class SendContinueOnPhoneMessageEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.SendContinueOnPhoneMessage,
                async (
                    SendContinueOnPhoneMessageRequest request,
                    ISendContinueOnPhoneMessageRequestHandler handler,
                    IValidator<SendContinueOnPhoneMessageRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok();
                })
            .RequireAuthorization("write")
            .WithName("SendContinueOnPhoneMessage")
            .WithApiVersionSet(builder.NewApiVersionSet("Message").Build())
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Send Continue On Phone Message")
            .WithDescription("Send Continue On Phone Message")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class SendContinueOnPhoneMessageRequestHandler : ISendContinueOnPhoneMessageRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly INotifyService _notifyService;
        private readonly BNPLOptions _bnplOptions;

        public SendContinueOnPhoneMessageRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            INotifyService notifyService,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _notifyService = notifyService;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask HandleAsync(SendContinueOnPhoneMessageRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var orderExists = await _dbContext.Orders
                .Where(o => o.TrackingCode == request.trackingCode && o.ConsumerUserId == userId)
                .AnyAsync(cancellationToken);

            if (!orderExists)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            await _notifyService.SendGoToMobileMessage(_userContext.CurrentUserName, UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.GetOrderUI, [new("trackingCode", request.trackingCode.ToString())]), cancellationToken);
        }
    }

    public interface ISendContinueOnPhoneMessageRequestHandler
    {
        ValueTask HandleAsync(SendContinueOnPhoneMessageRequest request, CancellationToken cancellationToken);
    }

    public class SendContinueOnPhoneMessageValidator : AbstractValidator<SendContinueOnPhoneMessageRequest>
    {
        public SendContinueOnPhoneMessageValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}
