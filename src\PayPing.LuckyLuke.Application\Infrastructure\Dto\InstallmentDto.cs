﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Dto
{
    public class InstallmentDto
    {
        public Guid InstallmentCode { get; set; }
        public Guid OrderTrackingCode { get; set; }
        public long InstallmentId { get; set; }
        public long OrderId { get; set; }
        public int MerchantUserId { get; set; }
        public string MerchantName { get; set; }
        public DateTimeOffset OrderDate { get; set; }
        public decimal InstallmentAmount { get; set; }
        public decimal DelayPenaltyAmount { get; set; }
        public bool IsPaid { get; set; }
        public DateTimeOffset DueDate { get; set; }
        public DateTimeOffset? PaymentDate { get; set; }
        public InstallmentStatus InstallmentStatus { get; set; }
        public int Number { get; set; }
    }
}
