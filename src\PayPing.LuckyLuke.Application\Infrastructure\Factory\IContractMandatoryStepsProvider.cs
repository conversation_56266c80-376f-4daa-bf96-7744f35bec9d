﻿using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public interface IContractMandatoryStepsProvider
    {
        GuaranteeType GuaranteeType { get; }
        ValueTask<OrderStepsDto> CreateContractMandatorySteps(Order order, int userId);
    }
}
