﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class CreditConfiguration : IEntityTypeConfiguration<Credit>
{
    public void Configure(EntityTypeBuilder<Credit> builder)
    {
        builder.HasIndex(x => x.Code).IsUnique().HasDatabaseName("IX_Credit_Code");

        builder
            .HasMany(e => e.Plans)
            .WithOne(e => e.Credit)
            .HasForeignKey(e => e.CreditId)
            .IsRequired();

        builder
            .HasMany(e => e.CreditTransactions)
            .WithOne(e => e.Credit)
            .HasForeignKey(e => e.CreditId)
            .IsRequired();

        builder
            .HasMany(e => e.CreditFailedTransactions)
            .WithOne(e => e.Credit)
            .HasForeignKey(e => e.CreditId)
            .IsRequired();
    }
}
