﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using RobinHood.Services.ServiceManager.Protos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public interface IInquiryGrpcClient
    {
        ValueTask<CreditScoreReportInfoResponse> CreditScoreReportInfoAsync(CreditScoreReportInfoRequest request);
        ValueTask<MatchingResponse> MachingCardWithNationalCodeAsync(MachingCardWithNationalCodeRequest request);
        ValueTask<MatchingResponse> MachingMobileWithNationalCodeAsync(MachingMobileWithNationalCodeRequest request);
        ValueTask<NationalCodeInquiryWithPersonalInfoResponse> NationalCodeInquiryWithPersonalInfoAsync(NationalCodeInquiryWithPersonalInfoRequest request);
        ValueTask<PostalCodeInquiryResponse> PostalCodeInquiryAsync(PostalCodeInquiryRequest request);
        ValueTask<ReportLinkCreditScoreInfoResponse> ReportLinkCreditScoreInfoAsync(ReportLinkCreditScoreInfoRequest request);
        ValueTask<StartCreditScoreInfoResponse> StartCreditScoreInfoAsync(StartCreditScoreInfoRequest request);
        ValueTask<ValidateCreditScoreInfoResponse> ValidateCreditScoreInfoAsync(ValidateCreditScoreInfoRequest request);
    }
}
