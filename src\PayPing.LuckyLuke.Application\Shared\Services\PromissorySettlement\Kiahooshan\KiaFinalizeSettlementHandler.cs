﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Clients.Http;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Services.PromissorySettlement.<PERSON><PERSON><PERSON>han
{
    public class KiaFinalizeSettlementHandler : KiahooshanSettlementBase<PERSON>andler, IKiahooshanPromissorySettlementService
    {
        private readonly IS3ServiceApiClient _s3ServiceApiClient;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly ILogger<KiaFinalizeSettlementHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public KiaFinalizeSettlementHandler(
            IOptions<BNPLOptions> bnplOptions,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IS3ServiceApiClient s3ServiceApiClient,
            IKiahooshanApiHttpClient kihooApi,
            IUploadGrpcClient uploadGrpcClient,
            IOptions<KiahooshanOptions> kihooOptions,
            ILogger<KiaFinalizeSettlementHandler> logger)
            : base(dbContext, userContext, kihooApi, kihooOptions)
        {
            _s3ServiceApiClient = s3ServiceApiClient;
            _uploadGrpcClient = uploadGrpcClient;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<object> HandleAsync(KiahooshanPromissorySettlementContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KiahooshanPromissorySettlementStatusV1.Finalize)
                throw new Exception("promissory settlement not in Finalize status");

            ValidateContext(context);

            string signedPdf = string.Empty;
            if (base.SignedSettlementDocBytes != null && base.SignedSettlementDocBytes.Length > 0)
            {
                signedPdf = Convert.ToBase64String(base.SignedSettlementDocBytes);
            }
            else 
            {
                var signedpdfDlResult = await _uploadGrpcClient.GetPresignedUrlAsync(context.SettlementSignedDocumentFileId, context.ConsumerUserId, FileManager.Grpc.UploadTypeEnum.Misc, cancellationToken);
                if (!signedpdfDlResult.Succeeded)
                {
                    throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "KiaFinalizeHandler; could not download signed promissory settlement", false);
                }

                using Stream signedpdfStream = await _s3ServiceApiClient.DownloadAsync(signedpdfDlResult.SuccessResult, cancellationToken);

                var signedbytes = await signedpdfStream.ToByteArrayAsync();

                signedPdf = Convert.ToBase64String(signedbytes);
            }
            

            var dsc = await GetValidDigitalSignAsync(context.ConsumerUserId);

            var result = await _kihooApi.FinalizeSettlement(
                new KiahooshanApiFinalizeSettlementPromissoryRequest()
                {
                    settleUniqueId = context.InitialSettleUniqueId,
                    signedPdf = signedPdf,
                    dsigUniqueId = dsc.SignatureUniqueId
                }, context.OrderTrackingCode, cancellationToken);

            
            context.Status = KiahooshanPromissorySettlementStatusV1.Done;
            context.FinalizedSettleUniqueId = result.uniqueId;

            //// save context
            await UpdateContextAsync(context);

            return new DoneResult(JsonSerializer.Serialize(new { signedPdf = context.SettlementSignedDocumentFileId }));
        }

        protected override void ValidateContext(KiahooshanPromissorySettlementContextV1 context)
        {
            base.ValidateContext(context);
            Guard.Against.NullOrEmpty(context.InitialSettleUniqueId);
            Guard.Against.NullOrEmpty(context.SettlementSignedDocumentFileId);

        }
    }
}
