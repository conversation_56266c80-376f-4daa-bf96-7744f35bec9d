﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class CreditTransactionConfiguration : IEntityTypeConfiguration<CreditTransaction>
{
    public void Configure(EntityTypeBuilder<CreditTransaction> builder)
    {
        builder.HasIndex(x => x.WalletId).HasDatabaseName("IX_CreditTransaction_WalletId");
        builder.HasIndex(x => x.CreditCode).HasDatabaseName("IX_CreditTransaction_CreditCode");


        builder.Property(nameof(CreditTransaction.Description)).HasMaxLength(1024);

    }
}