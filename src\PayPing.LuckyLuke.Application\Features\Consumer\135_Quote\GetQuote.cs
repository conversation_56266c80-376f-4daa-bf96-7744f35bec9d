﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer._135_Quote
{
    public record GetQuoteRequest(Guid trackingCode);

    public record GetQuoteResponse(decimal prepayment, decimal infraCost);

    public class GetQuoteEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.GetQuote,
                async (
                    [AsParameters] GetQuoteRequest request,
                    IGetQuoteRequestHandler handler,
                    IValidator<GetQuoteRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetQuote")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<GetQuoteResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Order")
            .WithDescription("Get Order")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetQuoteRequestHandler : IGetQuoteRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;

        public GetQuoteRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext)
        {
            _dbContext = dbContext;
            _userContext = userContext;
        }

        public async ValueTask<GetQuoteResponse> HandleAsync(GetQuoteRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.AsNoTracking()
                .Where(o => o.TrackingCode == request.trackingCode)
                .FirstOrDefaultAsync();

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            {
                throw new OrderExpiredException(request.trackingCode.ToString(), "سفارش منقضی شده است");
            }

            if (order.ConsumerUserId != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }

            return new GetQuoteResponse(order.PrePaymentAmount, order.ConsumerOperationCostAmount);
        }
    }
    public interface IGetQuoteRequestHandler
    {
        ValueTask<GetQuoteResponse> HandleAsync(GetQuoteRequest request, CancellationToken cancellationToken);
    }

    public class GetQuoteValidator : AbstractValidator<GetQuoteRequest>
    {
        public GetQuoteValidator()
        {
            RuleFor(x => x.trackingCode).NotEqual(Guid.Empty).WithResourceError(() => ValidatorDictionary.tracking_code_is_required); 
        }
    }
}
