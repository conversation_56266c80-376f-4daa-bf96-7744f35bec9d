﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34723.18
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PayPing.LuckyLuke.Api", "src\PayPing.LuckyLuke.Api\PayPing.LuckyLuke.Api.csproj", "{AA1FE6FF-897F-46A2-AFB2-E06E4C96D219}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PayPing.LuckyLuke.Application", "src\PayPing.LuckyLuke.Application\PayPing.LuckyLuke.Application.csproj", "{FA788A3F-F102-4EB5-A502-2F3357BC4FCB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PayPing.LuckyLuke.Api.Merchant", "src\PayPing.LuckyLuke.Api.Merchant\PayPing.LuckyLuke.Api.Merchant.csproj", "{160D61F1-F577-4906-BE2A-95DB15632D0B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ConsumerLoginSample", "src\ConsumerLoginSample\ConsumerLoginSample.csproj", "{C7F03645-9B8A-47ED-B395-0F4D4C22C61E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PayPing.LuckyLuke.Api.Admin", "src\PayPing.LuckyLuke.Api.Admin\PayPing.LuckyLuke.Api.Admin.csproj", "{8910EB13-3AA1-431D-961A-46D4C078BF18}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{CFA91A8E-1426-48DC-860D-34EC6F883778}"
	ProjectSection(SolutionItems) = preProject
		.dockerignore = .dockerignore
		.gitignore = .gitignore
		.gitlab-ci.yml = .gitlab-ci.yml
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AA1FE6FF-897F-46A2-AFB2-E06E4C96D219}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA1FE6FF-897F-46A2-AFB2-E06E4C96D219}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA1FE6FF-897F-46A2-AFB2-E06E4C96D219}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA1FE6FF-897F-46A2-AFB2-E06E4C96D219}.Release|Any CPU.Build.0 = Release|Any CPU
		{FA788A3F-F102-4EB5-A502-2F3357BC4FCB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FA788A3F-F102-4EB5-A502-2F3357BC4FCB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FA788A3F-F102-4EB5-A502-2F3357BC4FCB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FA788A3F-F102-4EB5-A502-2F3357BC4FCB}.Release|Any CPU.Build.0 = Release|Any CPU
		{160D61F1-F577-4906-BE2A-95DB15632D0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{160D61F1-F577-4906-BE2A-95DB15632D0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{160D61F1-F577-4906-BE2A-95DB15632D0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{160D61F1-F577-4906-BE2A-95DB15632D0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7F03645-9B8A-47ED-B395-0F4D4C22C61E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7F03645-9B8A-47ED-B395-0F4D4C22C61E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7F03645-9B8A-47ED-B395-0F4D4C22C61E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7F03645-9B8A-47ED-B395-0F4D4C22C61E}.Release|Any CPU.Build.0 = Release|Any CPU
		{8910EB13-3AA1-431D-961A-46D4C078BF18}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8910EB13-3AA1-431D-961A-46D4C078BF18}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8910EB13-3AA1-431D-961A-46D4C078BF18}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8910EB13-3AA1-431D-961A-46D4C078BF18}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0DDB4C34-C55C-4E1E-8C8D-106898F9F4DC}
	EndGlobalSection
EndGlobal
