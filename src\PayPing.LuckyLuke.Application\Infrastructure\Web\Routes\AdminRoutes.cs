﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Web.Routes
{
    public static class AdminRoutes
    {
        private const string AdminApplicationPrefixUri = "v{version:apiVersion}/bnpl/admin";

        public static string OrderList => $"{AdminApplicationPrefixUri}/order/list";
        public static string OrderDetail => $"{AdminApplicationPrefixUri}/order/detail";
        public static string OrderCancel => $"{AdminApplicationPrefixUri}/order/cancel";
        public static string CreditTransactionList => $"{AdminApplicationPrefixUri}/credit/transaction/list";
        public static string CreditSettingGet => $"{AdminApplicationPrefixUri}/credit/get";
        public static string CreditSettingUpdate => $"{AdminApplicationPrefixUri}/credit/update";
        public static string ContractList => $"{AdminApplicationPrefixUri}/contract/list";
        public static string GuarantorCreate => $"{AdminApplicationPrefixUri}/guarantor/create";
        public static string GuarantorUpdate => $"{AdminApplicationPrefixUri}/guarantor/update";
        public static string GuarantorDelete => $"{AdminApplicationPrefixUri}/guarantor/delete";
        public static string GetDownloadPath => $"{AdminApplicationPrefixUri}/file/get";
        public static string OrderTimeLine => $"{AdminApplicationPrefixUri}/order/timeline";

        public static string MerchantContractList => $"{AdminApplicationPrefixUri}/merchant-contract/list";
        public static string GetMerchantContract => $"{AdminApplicationPrefixUri}/merchant-contract/get";
        public static string AdminContractUpdate => $"{AdminApplicationPrefixUri}/merchant-contract/update";
        public static string AdminMerchantInfoGet => $"{AdminApplicationPrefixUri}/merchant-info/get";

    }
}
