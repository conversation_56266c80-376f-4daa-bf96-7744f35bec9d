﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class RemovedGuarantorFromPlan : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Plans_Guarantors_GuarantorId",
                table: "Plans");

            migrationBuilder.DropIndex(
                name: "IX_Plans_GuarantorId",
                table: "Plans");

            migrationBuilder.RenameColumn(
                name: "GuarantorId",
                table: "Plans",
                newName: "GuaranteeType");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "OrderCreditValidations",
                type: "character varying(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "HasLoans",
                table: "OrderCreditValidations",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Risk",
                table: "OrderCreditValidations",
                type: "character varying(1024)",
                maxLength: 1024,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Description",
                table: "OrderCreditValidations");

            migrationBuilder.DropColumn(
                name: "HasLoans",
                table: "OrderCreditValidations");

            migrationBuilder.DropColumn(
                name: "Risk",
                table: "OrderCreditValidations");

            migrationBuilder.RenameColumn(
                name: "GuaranteeType",
                table: "Plans",
                newName: "GuarantorId");

            migrationBuilder.CreateIndex(
                name: "IX_Plans_GuarantorId",
                table: "Plans",
                column: "GuarantorId");

            migrationBuilder.AddForeignKey(
                name: "FK_Plans_Guarantors_GuarantorId",
                table: "Plans",
                column: "GuarantorId",
                principalTable: "Guarantors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
