﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedOrderCancellation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "OrderCancellation_Description",
                table: "Orders",
                type: "character varying(4092)",
                maxLength: 4092,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "OrderCancellation_IsRefundActive",
                table: "Orders",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "OrderCancellation_IsRefundBalanceEnough",
                table: "Orders",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "OrderCancellation_IsRefundNeeded",
                table: "Orders",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "OrderCancellation_Reason",
                table: "Orders",
                type: "character varying(2046)",
                maxLength: 2046,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OrderCancellation_RefundRefId",
                table: "Orders",
                type: "character varying(64)",
                maxLength: 64,
                nullable: true);

            migrationBuilder.Sql("CREATE INDEX IX_Order_OrderCancellation_RefundRefId ON Orders (OrderCancellation_RefundRefId);");

            migrationBuilder.AddColumn<string>(
                name: "CardNumber",
                table: "OrderPayments",
                type: "character varying(26)",
                maxLength: 26,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SettlementData",
                table: "OrderGuarantees",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "DeletedAt",
                table: "Installments",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "Installments",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OrderCancellation_Description",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "OrderCancellation_IsRefundActive",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "OrderCancellation_IsRefundBalanceEnough",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "OrderCancellation_IsRefundNeeded",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "OrderCancellation_Reason",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "OrderCancellation_RefundRefId",
                table: "Orders");

            migrationBuilder.DropIndex("IX_Order_OrderCancellation_RefundRefId");

            migrationBuilder.DropColumn(
                name: "CardNumber",
                table: "OrderPayments");

            migrationBuilder.DropColumn(
                name: "SettlementData",
                table: "OrderGuarantees");

            migrationBuilder.DropColumn(
                name: "DeletedAt",
                table: "Installments");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "Installments");
        }
    }
}
