﻿using MassTransit;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using PayPing.BNPL.Application.Infrastructure.Persistence.Extensions;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.Linq.Expressions;
using System.Reflection;
using System.Reflection.Emit;
using System.Threading;

namespace PayPing.BNPL.Application.Infrastructure.Persistence;

public class ApplicationDbContext : DbContext
{
    private readonly IUserContext _userContext;
    private readonly IServiceProvider _serviceProvider;

    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IUserContext userContext, IServiceProvider serviceProvider) : base(options)
    {
        _userContext = userContext;
        _serviceProvider = serviceProvider;
    }

    public DbSet<ConsumerInfo> ConsumerInfos { get; set; }
    public DbSet<MerchantInfo> MerchantInfos { get; set; }
    public DbSet<Contract> Contracts { get; set; }
    public DbSet<Credit> Credits { get; set; }
    public DbSet<CreditTransaction> CreditTransactions { get; set; }
    public DbSet<CreditTransactionOutbox> CreditTransactionOutboxes { get; set; }

    public DbSet<Guarantor> Guarantors { get; set; }
    public DbSet<Installment> Installments { get; set; }
    public DbSet<Order> Orders { get; set; }
    public DbSet<OrderCreditValidation> OrderCreditValidations { get; set; }
    public DbSet<OrderGuarantee> OrderGuarantees { get; set; }
    public DbSet<OrderPayment> OrderPayments { get; set; }
    public DbSet<OrderExternalEventOutbox> OrderExternalEventOutboxes { get; set; }

    public DbSet<Plan> Plans { get; set; }
    public DbSet<PaymentGroup> PaymentGroups { get; set; }
    public DbSet<DigitalSign> DigitalSigns { get; set; }
    public DbSet<EventLog> EventLogs { get; set; }
    public DbSet<OrderTargetPlan> OrderTargetPlans { get; set; }
    public DbSet<EmployeeInfo> EmployeeInfos { get; set; }
    public DbSet<PlanEmployee> PlanEmployees { get; set; }



    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        var oels = new List<EventLog>(); 
        foreach (var entry in ChangeTracker.Entries<IBaseEntity>())
        {
            cancellationToken.ThrowIfCancellationRequested();

            OnBeforeSavingOrderChanges(entry, oels);

            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedBy = _userContext.CurrentUserId ?? default;
                    entry.Entity.CreatedAt = DateTimeOffset.UtcNow;
                    break;
                case EntityState.Modified:
                    if (entry.Entity is IAuditableEntity)
                    {
                        (entry.Entity as IAuditableEntity).LastModifiedBy = _userContext.CurrentUserId ?? default;
                        (entry.Entity as IAuditableEntity).LastModifiedAt = DateTimeOffset.UtcNow;
                    }
                    break;
                case EntityState.Detached:
                    break;
                case EntityState.Unchanged:
                    break;
                case EntityState.Deleted:
                    if (entry.Entity is ISoftDeletableEntity)
                    {
                        entry.State = EntityState.Modified;
                        (entry.Entity as ISoftDeletableEntity).IsDeleted = true;
                        (entry.Entity as ISoftDeletableEntity).DeletedAt = DateTimeOffset.UtcNow;
                    }
                    break;
                default:
                    break;
            }
        }

        var result = await base.SaveChangesAsync(cancellationToken);

        if (oels.Count > 0)
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var newDbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                newDbContext.EventLogs.AddRange(oels);
                await newDbContext.SaveChangesAsync();
            }
        }

        return result;
    }

    private void OnBeforeSavingOrderChanges(EntityEntry<IBaseEntity> entry, List<EventLog> oels)
    {
        if (entry.Entity is Order order)
        {
            // Action when creating a new entity
            if (entry.State == EntityState.Added)
            {
                var oel = new EventLog()
                {
                    RefId = entry.CurrentValues[nameof(Order.TrackingCode)].ToString(),
                    LogType = EventLogType.OrderStatusChanged,
                    Name = "payping.order_status_changed",
                    UserId = _userContext.CurrentUserId ?? default,
                    Status = (OrderStatus?)entry.CurrentValues[nameof(Order.Status)]
                };

                oels.Add(oel);
            }
            else if (entry.State == EntityState.Modified)
            {
                var statusEntry = entry.OriginalValues[nameof(Order.Status)];
                var statusCurrentValue = entry.CurrentValues[nameof(Order.Status)];

                if (statusEntry != statusCurrentValue)
                {
                    var oel = new EventLog()
                    {
                        RefId = entry.CurrentValues[nameof(Order.TrackingCode)].ToString(),
                        LogType = EventLogType.OrderStatusChanged,
                        Name = "payping.order_status_changed",
                        UserId = _userContext.CurrentUserId ?? default,
                        Status = (OrderStatus?)statusCurrentValue
                    };

                    oels.Add(oel);
                }
            }
        }
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        builder.ConfigureBaseEntityTypes();
        builder.ConfigureAuditableEntityTypes();
        builder.ConfigureSoftDeletableEntityTypes();

        base.OnModelCreating(builder);
    }
}