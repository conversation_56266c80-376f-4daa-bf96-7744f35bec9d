﻿using Elastic.Apm.Api.Kubernetes;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using System.Globalization;

namespace PayPing.LuckyLuke.Application.Infrastructure.Builder
{
    public record OrderFinancialDto(decimal CreditedAmount, decimal GuaranteeAmount, decimal RawPrePaymentAmount, decimal MerchantOperationCostAmount, decimal ConsumerOperationCostAmount, decimal InstallmentsTotalRawAmount, List<OrderFinancialBuilderInstallment> Installments);

    public record OrderFinancialBuilderInstallment(decimal OriginalAmount, decimal InterestAmount, decimal TotalAmount, decimal DelayPenaltyAmount, decimal FinalAmount, DateTimeOffset DueDate);

    public abstract class FinancialBuilder
    {
        protected readonly decimal _totalAmount;
        protected readonly decimal _maxCreditAmount;
        protected readonly decimal _minPrePaymentRate;
        protected readonly int _installmentCount;
        protected readonly int _installmentPeriodInMonths;
        protected readonly decimal _interestRate;
        protected readonly decimal _operationCost;
        protected readonly MerchantOperationCostStrategy _operationCostStrategy;
        protected readonly GuaranteeType _guaranteeType;
        protected readonly PersianCalendar _persianCalendar;
        protected readonly bool _isDevelopment;

        public FinancialBuilder(
            decimal totalAmount,
            decimal maxCreditAmount,
            decimal minPrePaymentRate,
            int installmentCount,
            int installmentPeriodInMonths,
            decimal interestRate,
            decimal operationCost,
            MerchantOperationCostStrategy operationCostStrategy,
            GuaranteeType guaranteeType,
            bool isDevelopment)
        {
            _totalAmount = totalAmount;
            _maxCreditAmount = maxCreditAmount;
            _minPrePaymentRate = minPrePaymentRate;
            _installmentCount = installmentCount;
            _installmentPeriodInMonths = installmentPeriodInMonths;
            _interestRate = interestRate;
            _operationCost = operationCost;
            _operationCostStrategy = operationCostStrategy;
            _guaranteeType = guaranteeType;
            _persianCalendar = new PersianCalendar();
            _isDevelopment = isDevelopment;
        }

        protected int step = 0;

        #region Public

        public decimal CreditedAmount { get; set; }
        public decimal GuaranteeAmount { get; set; }
        public decimal RawPrePaymentAmount { get; set; }
        public decimal MerchantOperationCostAmount { get; set; }
        public decimal ConsumerOperationCostAmount { get; set; }
        public decimal InstallmentsTotalRawAmount { get; set; }
        public List<OrderFinancialBuilderInstallment> Installments { get; set; } = new List<OrderFinancialBuilderInstallment> { };


        #endregion

        public virtual FinancialBuilder SetPrePayment()
        {
            if (step != 0)
            {
                throw new Exception($"OrderFinancialBuilder wrong step. step: {step}");
            }

            step += 1;

            
            decimal prePayment = Math.Ceiling(decimal.Divide(decimal.Multiply(_minPrePaymentRate, _totalAmount), 100m));

            decimal remainedAmount = decimal.Subtract(_totalAmount, prePayment);

            decimal remainedOverMaxCredit = decimal.Subtract(remainedAmount, _maxCreditAmount);

            if (remainedOverMaxCredit > decimal.Zero)
            {
                prePayment = decimal.Add(prePayment, remainedOverMaxCredit);

                CreditedAmount = InstallmentsTotalRawAmount = _maxCreditAmount;
            }
            else
            {
                CreditedAmount = InstallmentsTotalRawAmount = remainedAmount;
            }

            if (CreditedAmount > 100_000m)
            {
                var mod = decimal.Remainder(CreditedAmount, 100_000m);

                prePayment = decimal.Add(prePayment, mod);

                InstallmentsTotalRawAmount = CreditedAmount = decimal.Subtract(InstallmentsTotalRawAmount, mod);
            }

            
            decimal merchCost = decimal.Zero;
            decimal consumerCost = decimal.Zero;
            switch (_operationCostStrategy)
            {
                case MerchantOperationCostStrategy.PercentOfOrderTotalAmount:
                    merchCost = Math.Ceiling(decimal.Divide(decimal.Multiply(_totalAmount, _operationCost), 100m));
                    break;
                default:
                    throw new Exception("wrong Merchant Operation Cost Strategy");
            }

            decimal coca = _isDevelopment ? 1000m : ApplicationConstants.ConsumerMinOperationCostAmount;

            consumerCost = Math.Ceiling(Math.Max(decimal.Multiply(CreditedAmount, ApplicationConstants.ConsumerOperationCostRatioOfCredit), coca));

            ConsumerOperationCostAmount = consumerCost;
            MerchantOperationCostAmount = merchCost;

            RawPrePaymentAmount = prePayment;

            return this;
        }

        public virtual FinancialBuilder SetGuaranteeAmount()
        {
            if (step != 1)
            {
                throw new Exception($"OrderFinancialBuilder wrong step. step: {step}");
            }

            step += 1;

            switch (_guaranteeType)
            {
                case GuaranteeType.Promissory:
                    GuaranteeAmount = Math.Max(decimal.Multiply(1.5m, CreditedAmount), ApplicationConstants.PromissoryMinAmount);
                    break;
                case GuaranteeType.None:
                case GuaranteeType.Salary:
                    GuaranteeAmount = decimal.Zero;
                    break;
                case GuaranteeType.Cheque:
                default:
                    throw new Exception($"OrderFinancialBuilder wrong guaranteeType. guaranteeType: {_guaranteeType}");
            }
            return this;
        }

        public abstract FinancialBuilder SetInstallments();

        public abstract OrderFinancialDto Build();

    }
}
