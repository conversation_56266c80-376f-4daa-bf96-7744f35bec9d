﻿using Microsoft.Extensions.Logging;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class MerchantAfterPaymentCanceler : IMerchantCanceler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly INotifyService _notifyService;
        private readonly IWalletService _walletService;
        private readonly ICreditService _creditService;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IRefundGrpcClient _refundGrpcClient;
        private readonly ILogger<MerchantAfterPaymentCanceler> _logger;

        public MerchantAfterPaymentCanceler(
            ApplicationDbContext dbContext,
            INotifyService notifyService,
            IWalletService walletService,
            ICreditService creditService,
            IGuaranteeService guaranteeService,
            IRefundGrpcClient refundGrpcClient,
            ILogger<MerchantAfterPaymentCanceler> logger)
        {
            _dbContext = dbContext;
            _notifyService = notifyService;
            _walletService = walletService;
            _creditService = creditService;
            _guaranteeService = guaranteeService;
            _refundGrpcClient = refundGrpcClient;
            _logger = logger;
        }

        public async ValueTask CancelAsync(Order order, string reason, string description, CancellationToken cancellationToken)
        {
            var beforeStatuses = OrderStatusProvider.GetMerchantBeforePaymentCancelable();
            var afterStatuses = OrderStatusProvider.GetMerchantAfterPaymentCancelable();

            if (!beforeStatuses.Contains(order.Status) && !afterStatuses.Contains(order.Status))
            {
                throw new ArgumentException("امکان لغو سفارش وجود ندارد");
            }

            var orderPayment = order.OrderPayments?.Where(x => x.PaymentType == OrderPaymentType.PrePayment && x.PaymentStatus == OrderPaymentStatus.Paid).FirstOrDefault();
            if (orderPayment == null)
            {
                // even if prepayment was zero, there must be a paid orderpayment record because of consumer wage
                throw new ArgumentException("پیش پرداخت یافت نشد");
            }


            // If REFUND Needed (PrePaymentAmount > 0 and has not created)
            if (order.PrePaymentAmount > decimal.Zero && !order.OrderCancellation.RefundRefId.HasValue())
            {
                var refundInfo = await _refundGrpcClient.GetUserConfigurationAsync(
                    new Refund.WebApi.gRPC.GetUserConfigurationRequest()
                    {
                        BusinessId = order.MerchantUserId,
                        UserId = order.MerchantUserId
                    },
                    cancellationToken);

                switch (refundInfo)
                {
                    case RefundGrpcUserConfigurationResponseSuccess refundGrpcUserConfigurationResponseSuccess:
                        break;
                    case RefundGrpcResponseError refundGrpcResponseError:
                    default:
                        throw new RefundNotActiveException(order.TrackingCode.ToString(), "refund service is not active");
                }


                // create refund request
                var rfcreateResult = await _refundGrpcClient.CreateRefundAsync(
                    new Refund.WebApi.gRPC.CreateRefundRequest()
                    {
                        UserId = order.MerchantUserId,
                        BusinessId = order.MerchantUserId,
                        PaymentCode = orderPayment.PaymentCode,
                        ClientReference = order.Id.ToString(),
                        Amount = (int)order.PrePaymentAmount,
                        WagePayer = Refund.WebApi.gRPC.Types.WagePayer.Business
                    },
                    cancellationToken);

                switch (rfcreateResult)
                {
                    case RefundGrpcCreateResponseSuccess refundGrpcCreateResponseSuccess:
                        order.OrderCancellation = new OrderCancellation(true, true, !refundGrpcCreateResponseSuccess.isPendingRecharge, refundGrpcCreateResponseSuccess.code, reason, description, DateTimeOffset.UtcNow);
                        break;
                    case RefundGrpcResponseError refundGrpcResponseError:
                    default:
                        throw new Exception("create refund failed");
                }
            }
            else
            {
                order.OrderCancellation = new OrderCancellation(false, false, false, null, reason, description, DateTimeOffset.UtcNow);
            }

            // settle guarantee (IF guaranteeferid exists -> settle ELSE delete)
            //          add OrderExternalEventOutboxes for failure
            if (order.OrderGuarantees != null && order.OrderGuarantees.Any() && !order.GuaranteeRevoked)
            {
                var guaranteeProvider = await _guaranteeService.GetGuaranteeProviderByGuarantorId(order.OrderPlan.GuarantorId);

                // revoke guarantee
                var guarantee = order.OrderGuarantees.OrderByDescending(g => g.CreatedAt).First();

                (bool success, string error) revokeResult = (false, string.Empty);

                if (guaranteeProvider.GuaranteeType == GuaranteeType.Promissory && guarantee.GuaranteeRefId.HasValue() && guaranteeProvider is IPromissoryGuaranteeProvider pp)
                {
                    guarantee.SettlementData = pp.RenewSettlementContextIfNeeded(guarantee);

                    var settleResult = await pp.SettleGuaranteeAsync(guarantee.SettlementData, cancellationToken);

                    revokeResult = (settleResult.success, settleResult.error);
                }
                else
                {
                    var deleteResult = await guaranteeProvider.DeleteGuaranteeAsync(guarantee.Data);
                    revokeResult = (deleteResult.success, deleteResult.error);
                }

                if (!revokeResult.success)
                {
                    _logger.LogWarning($"could not revoke guarantee order id: {order.Id}, message: {revokeResult.error}");

                    _dbContext.OrderExternalEventOutboxes.Add(new OrderExternalEventOutbox()
                    {
                        OrderId = order.Id,
                        EventType = OrderExternalEventType.RevokeGuarantee,
                        HasFailed = true,
                        FailCount = 1,
                        Message = revokeResult.error
                    });
                }

                order.GuaranteeRevoked = revokeResult.success;
            }


            if (order.PrePaymentAmount > decimal.Zero)
            {
                order.Status = OrderStatus.SemiCanceledAfterPaymentByMerchant;
            }
            else
            {
                order.Status = OrderStatus.FullyCanceledAfterPaymentByMerchant;
            }


            // Soft delete installments
            foreach (var item in order.Installments)
            {
                item.IsDeleted = true;
            }


            await _dbContext.SaveChangesAsync();

            // deposit as much as credited amount
            var depositResult = await _walletService.IncreaseWalletCreditAndSaveAsync(
                order.OrderPlan.WalletId,
                order.CreditedAmount,
                cancellationToken);

            // add a suspecious credit on failure
            if (!depositResult.Success)
            {
                await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                    order.OrderPlan.CreditId,
                    order.OrderPlan.WalletId,
                    order.CreditedAmount,
                    CreditTransactionType.Deposit,
                    string.Empty,
                    depositResult.Error);
            }

            // Add a DecreasePrepaymentSum on credit 
            var ppRunningSumResult = await _creditService.DecreaseRawPrePaymentSumOnCreditAndSaveAsync(
                order.OrderPlan.CreditId,
                order.PrePaymentAmount,
                cancellationToken);

            if (!ppRunningSumResult.Success)
            {
                await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                    order.OrderPlan.CreditId,
                    order.OrderPlan.WalletId,
                    order.PrePaymentAmount,
                    CreditTransactionType.PrepaymentRSNegative,
                    null,
                    ppRunningSumResult.Error);
            }

            // schedule a webhook for client with clientrefid
            if (!string.IsNullOrWhiteSpace(order.ClientCancelUrl))
            {
                _dbContext.OrderExternalEventOutboxes.Add(new OrderExternalEventOutbox()
                {
                    OrderId = order.Id,
                    ClientRefId = order.ClientRefId,
                    ClientCancelUrl = order.ClientCancelUrl,
                    EventType = OrderExternalEventType.CancelHook
                });
            }
        }

        public async ValueTask PostCancelAsync(Order order, CancellationToken cancellationToken)
        {
            order.Status = OrderStatus.FullyCanceledAfterPaymentByMerchant;

            await _dbContext.SaveChangesAsync();

            // send consunmer sms
            await _notifyService.SendCanceledByMerchantMessage(order.ConsumerUserName, order.MerchantUserId, cancellationToken);
        }
    }
}
