﻿using DNTPersianUtils.Core;
using ExcelDataReader;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.Data;
using System.Text;

namespace PayPing.LuckyLuke.Application.Shared.Services.Excel
{
    public class ExcelService : IExcelService
    {
        private readonly ILogger<ExcelService> _logger;

        public ExcelService(ILogger<ExcelService> logger)
        {
            _logger = logger;
        }

        public async ValueTask<MemoryStream> CreateOrderInfoExcelFile(OrderInfos orderInfosModel, CancellationToken cancellationToken)
        {
            ExcelPackage.License.SetNonCommercialPersonal("Payping");
            var stream = new MemoryStream();

            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add(InfrastructureExcelConstants.PersianOrderList);
                worksheet.View.RightToLeft = true;

                worksheet.Cells[1, 1].Value = InfrastructureExcelConstants.PersianOrderTrackingCode;
                worksheet.Cells[1, 2].Value = InfrastructureExcelConstants.PersianCreatedDate;
                worksheet.Cells[1, 3].Value = InfrastructureExcelConstants.PersianCreditAmount;
                worksheet.Cells[1, 4].Value = InfrastructureExcelConstants.PersianConsumer;
                worksheet.Cells[1, 5].Value = InfrastructureExcelConstants.PersianTotalInstallmentCount;
                worksheet.Cells[1, 6].Value = InfrastructureExcelConstants.PersianPaidoffInstallmentCount;
                worksheet.Cells[1, 7].Value = InfrastructureExcelConstants.PersianIsCancelable;
                worksheet.Cells[1, 8].Value = InfrastructureExcelConstants.PersianMobile;

                for (var i = 0; i < orderInfosModel.orders.Count; i++)
                {
                    var orderInfo = orderInfosModel.orders[i];
                    worksheet.Cells[i + 2, 1].Value = orderInfo.orderTrackingCode;
                    worksheet.Cells[i + 2, 2].Value = orderInfo.createdDate.ToShortPersianDateString().ToString();
                    worksheet.Cells[i + 2, 3].Value = orderInfo.creditAmount;
                    worksheet.Cells[i + 2, 4].Value = orderInfo.consumer;
                    worksheet.Cells[i + 2, 5].Value = orderInfo.totalInstallmentCount;
                    worksheet.Cells[i + 2, 6].Value = orderInfo.paidoffInstallmentCount;
                    worksheet.Cells[i + 2, 7].Value = orderInfo.isCancelable;
                    worksheet.Cells[i + 2, 8].Value = orderInfo.mobile;
                }

                await package.SaveAsAsync(stream, cancellationToken);
            }

            stream.Position = 0;
            return stream;
        }

        public DataSet ReadExcelFileIntoDataSet(Stream excelFileStream)
        {
            if (excelFileStream is null)
                throw new ArgumentNullException(nameof(excelFileStream));

            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            using var reader = ExcelReaderFactory.CreateReader(excelFileStream);

            var toReturn = reader.AsDataSet();

            return toReturn;
        }
    }
}
