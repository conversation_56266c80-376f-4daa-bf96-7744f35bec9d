﻿using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class ConsumerInfo : BaseEntity<int>, IAuditableEntity
    {
        public int ConsumerUserId { get; set; }

        public string FirstNameEnglish { get; set; }

        public string LastNameEnglish { get; set; }

        public bool IsMale { get; set; }

        public string NationalIdSeries { get; set; }

        public string PostalCode { get; set; }

        public string Address { get; set; }

        public string IBan { get; set; }

        public string CardNumber { get; set; }

        public string Email { get; set; }

        public string ProvinceEnglish { get; set; }

        public string CityEnglish { get; set; }

        public string UserName { get; set; }
        public string PhoneNumber { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string FullName { get; set; }
        public string NationalCode { get; set; }
        public string PersianBirthDate { get; set; }
        public string FatherName { get; set; }

        // signature image jpg under 50kb
        public string SignatureImageFileId { get; set; }
        public string SignatureImageFileName { get; set; }

        // video under 20mb | webm with vp8 codec | under 15 seconds
        public string SignatureVideoFileId { get; set; }
        public string SignatureVideoFileName { get; set; }
        public string SignatureVideoRandomSentence { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }
        public int? LastModifiedBy { get; set; }

    }

}
