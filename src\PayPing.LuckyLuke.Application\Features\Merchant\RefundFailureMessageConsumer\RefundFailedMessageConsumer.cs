﻿using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.Messaging.Contracts.IntegrationEvents.Refund.RefundOperation;

namespace PayPing.LuckyLuke.Application.Features.Merchant.RefundFailureMessageConsumer
{
    public class RefundFailedMessageConsumer : IConsumer<RefundOperationFailed>
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<RefundFailedMessageConsumer> _logger;

        public RefundFailedMessageConsumer(ApplicationDbContext dbContext, ILogger<RefundFailedMessageConsumer> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task Consume(ConsumeContext<RefundOperationFailed> context)
        {
            _logger.LogInformation("Received RefundOperationFailed message: RefundOperationCode={RefundOperationCode}, FailureDateTime={FailureDateTime}, FailureReason={FailureReason}",
                context.Message.RefundOperationCode,
                context.Message.FailureDateTime,
                context.Message.FailureReason);

            var order = await _dbContext.Orders
                .Where(x =>
                    x.Status == Domain.Enums.OrderStatus.SemiCanceledAfterPaymentByMerchant &&
                    x.OrderCancellation.RefundRefId == context.Message.RefundOperationCode.ToString())
                .FirstOrDefaultAsync();

            if (order != null)
            {
                // re-create refund request and assign proper values to order

            }
        }
    }
}
