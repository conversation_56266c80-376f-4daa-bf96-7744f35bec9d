﻿using DNTPersianUtils.Core;
using Elastic.CommonSchema;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._061_GetMerchantProfileInfo
{
    public record GetMerchantProfileInfoRequest();

    public class GetMerchantProfileInfoResponse
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string FatherName { get; set; }
        public string FirstNameEnglish { get; set; }
        public string LastNameEnglish { get; set; }
        public string NationalCode { get; set; }
        public string BirthDate { get; set; }
        public bool IsMale { get; set; }
        public string NationalIdSeries { get; set; }
        public string MobileNumber { get; set; }

        public string Email { get; set; }
        public string ProvinceEnglish { get; set; }
        public string CityEnglish { get; set; }
        public string PostalCode { get; set; }
        public string Address { get; set; }
    }

    public class GetMerchantProfileInfoEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                MerchantRoutes.GetMerchantProfileInfo,
                async ([AsParameters] GetMerchantProfileInfoRequest request, IGetMerchantProfileInfoRequestHandler handler, IValidator<GetMerchantProfileInfoRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetMerchantProfileInfo")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<GetMerchantProfileInfoResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Merchant Profile Info")
            .WithDescription("Get Merchant Profile Info")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetMerchantProfileInfoRequestHandler : IGetMerchantProfileInfoRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;
        private readonly IUserService _userService;

        public GetMerchantProfileInfoRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions, IUserService userService)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
            _userService = userService;
        }

        public async ValueTask<GetMerchantProfileInfoResponse> HandleAsync(GetMerchantProfileInfoRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var merchantInfo = await _dbContext.MerchantInfos.AsNoTracking()
                .Where(p => p.MerchantUserId == userId)
                .FirstOrDefaultAsync();

            if (merchantInfo == null)
            {
                throw new ArgumentException("اطلاعات مرچنت یافت نشد");
            }

            var merchantProfile = await _userService.GetMerchantExtraInfoAsync(userId, cancellationToken);

            GetMerchantProfileInfoResponse result = new GetMerchantProfileInfoResponse
            {
                FirstName = merchantInfo.MerchantInfoAgent.FirstName.ValueOrNull() ?? merchantProfile.FirstName,
                LastName = merchantInfo.MerchantInfoAgent.LastName.ValueOrNull() ?? merchantProfile.LastName,
                FirstNameEnglish = merchantInfo.MerchantInfoAgent.FirstNameEnglish,
                LastNameEnglish = merchantInfo.MerchantInfoAgent.LastNameEnglish,
                FatherName = merchantInfo.MerchantInfoAgent.FatherName.ValueOrNull() ?? merchantProfile.FatherName,
                BirthDate = merchantInfo.MerchantInfoAgent.PersianBirthDate.ValueOrNull() ?? merchantProfile.BirthDate.ToShortPersianDateString(),
                NationalCode = merchantInfo.MerchantInfoAgent.NationalCode.ValueOrNull() ?? merchantProfile.NationalCode,
                NationalIdSeries = merchantInfo.MerchantInfoAgent.NationalIdSeries.ValueOrNull() ?? merchantProfile.NationalIdSeries,
                Email = merchantInfo.Email.ValueOrNull() ?? merchantProfile.Email,
                IsMale = merchantInfo.MerchantInfoAgent.IsMale,
                PostalCode = merchantInfo.PostalCode.ValueOrNull() ?? merchantProfile.PostalCode,
                CityEnglish = merchantInfo.CityEnglish,
                ProvinceEnglish = merchantInfo.ProvinceEnglish,
                Address = merchantInfo.Address.ValueOrNull() ?? merchantProfile.Address,
                MobileNumber = merchantInfo.MerchantInfoAgent.MobileNumber.ValueOrNull() ?? merchantProfile.PhoneNumber,
                
            };

            return result;
        }

    }
    public interface IGetMerchantProfileInfoRequestHandler
    {
        ValueTask<GetMerchantProfileInfoResponse> HandleAsync(GetMerchantProfileInfoRequest request, CancellationToken cancellationToken);
    }

    public class GetMerchantProfileInfoValidator : AbstractValidator<GetMerchantProfileInfoRequest>
    {
        public GetMerchantProfileInfoValidator()
        {

        }
    }

}
