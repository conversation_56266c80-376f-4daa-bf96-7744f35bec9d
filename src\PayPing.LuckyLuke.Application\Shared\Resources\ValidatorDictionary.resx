﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="address_is_required" xml:space="preserve">
    <value>آدرس الزامیست</value>
  </data>
  <data name="amount_greaterorequal_50000" xml:space="preserve">
    <value>حداقل مبلغ قابل قبول 50 هزار تومان است</value>
  </data>
  <data name="amount_greater_1000" xml:space="preserve">
    <value>مبلغ کل سفارش باید بزرگتر از 1000 تومان باشد</value>
  </data>
  <data name="amount_greater_zero" xml:space="preserve">
    <value>مبلغ باید بزرگتر از صفر باشد</value>
  </data>
  <data name="birthdate_is_not_valid" xml:space="preserve">
    <value>تاریخ تولد صحیح نیست</value>
  </data>
  <data name="birthdate_is_required" xml:space="preserve">
    <value>تاریخ تولد الزامیست</value>
  </data>
  <data name="birth_certificate_no_is_required" xml:space="preserve">
    <value>شماره شناسنامه الزامی می باشد</value>
  </data>
  <data name="birth_certificate_no_not_valid" xml:space="preserve">
    <value>شماره شناسنامه صحیح نمی باشد</value>
  </data>
  <data name="call-back_url_is_required" xml:space="preserve">
    <value>آدرس مقصد اطلاعات پرداخت الزامیست</value>
  </data>
  <data name="card_number_is_required" xml:space="preserve">
    <value>شماره کارت الزامیست</value>
  </data>
  <data name="card_number_not_valid" xml:space="preserve">
    <value>شماره کارت صحیح نیست</value>
  </data>
  <data name="city_english_is_required" xml:space="preserve">
    <value>شهر به انگلیسی الزامیست</value>
  </data>
  <data name="city_english_not_valid" xml:space="preserve">
    <value>شهر به انگلیسی صحیح نیست</value>
  </data>
  <data name="contract_acceptance_not_valid" xml:space="preserve">
    <value>موافقت با قرارداد صحیح نیست</value>
  </data>
  <data name="contract_id_is_required" xml:space="preserve">
    <value>شناسه قراداد الزامی می باشد</value>
  </data>
  <data name="credit_code_is_required" xml:space="preserve">
    <value>کد اعتبار الزامیست</value>
  </data>
  <data name="credit_id_is_required" xml:space="preserve">
    <value>شناسه اعتباری الزامیست</value>
  </data>
  <data name="email_is_required" xml:space="preserve">
    <value>ایمیل الزامیست</value>
  </data>
  <data name="email_not_valid" xml:space="preserve">
    <value>ایمیل صحیح نیست</value>
  </data>
  <data name="file_name_is_required" xml:space="preserve">
    <value>نام فایل الزامیست</value>
  </data>
  <data name="first_name_english_is_required" xml:space="preserve">
    <value>نام به انگلیسی الزامیست</value>
  </data>
  <data name="first_name_english_not_valid" xml:space="preserve">
    <value>نام به انگلیسی صحیح نیست</value>
  </data>
  <data name="Floor" xml:space="preserve">
    <value>طبقه</value>
  </data>
  <data name="guarantee_reference_id_is_required" xml:space="preserve">
    <value>شناسه مرجع گارانتی الزامیست</value>
  </data>
  <data name="guarantee_type_must_enum" xml:space="preserve">
    <value>نوع ضمانت باید از مقادیر از قبل تعریف شده باشد</value>
  </data>
  <data name="iban_is_required" xml:space="preserve">
    <value>شماره شبا الزامیست</value>
  </data>
  <data name="iban_not_valid" xml:space="preserve">
    <value>شماره شبا صحیح نیست</value>
  </data>
  <data name="installment_code_is_required" xml:space="preserve">
    <value> کد قسط الزامیست</value>
  </data>
  <data name="installment_count_greater_zero" xml:space="preserve">
    <value>تعداد اقساط باید بیشتر از صفر باشد</value>
  </data>
  <data name="installment_verify_data_is_required" xml:space="preserve">
    <value>دیتای قسط الزامیست</value>
  </data>
  <data name="interest_rate_greater" xml:space="preserve">
    <value>درصد سود باید بزرگتر یا برابر با صفر باشد</value>
  </data>
  <data name="last_name_english_is_required" xml:space="preserve">
    <value>نام خانوادگی به انگلیسی الزامیست</value>
  </data>
  <data name="last_name_english_not_valid" xml:space="preserve">
    <value>نام خانوادگی به انگلیسی صحیح نیست</value>
  </data>
  <data name="locality" xml:space="preserve">
    <value>محله</value>
  </data>
  <data name="max_credit_amount_is_required" xml:space="preserve">
    <value>حداکثر مبلغ اعتباری الزامیست</value>
  </data>
  <data name="merchant-user-id-is-required" xml:space="preserve">
    <value>شناسه کاربر مرچنت الزامی است</value>
  </data>
  <data name="merchant_id_is_required" xml:space="preserve">
    <value>شناسه مرچنت الزامی است</value>
  </data>
  <data name="min_prePayment_rate_greater" xml:space="preserve">
    <value>درصد پیش پرداخت باید بزرگتر یا برابر با صفر درصد باشد</value>
  </data>
  <data name="min_prePayment_rate_lessthan" xml:space="preserve">
    <value>درصد پیش پرداخت باید کمتر از صد درصد باشد</value>
  </data>
  <data name="mobile_is_required" xml:space="preserve">
    <value>شماره موبایل الزامیست</value>
  </data>
  <data name="mobile_not_valid" xml:space="preserve">
    <value>شماره موبایل صحیح نیست. شماره موبایل باید مطابق الگوی 09121112233 باشد</value>
  </data>
  <data name="month_between_1_12" xml:space="preserve">
    <value>ماه باید از 1 تا 12 باشد</value>
  </data>
  <data name="nationalcode_is_not_valid" xml:space="preserve">
    <value>کد ملی صحیح نیست</value>
  </data>
  <data name="nationalcode_is_required" xml:space="preserve">
    <value>کد ملی الزامیست</value>
  </data>
  <data name="national_card_serial_number_is_required" xml:space="preserve">
    <value>شماره سریال پشت کارت ملی الزامی باشد</value>
  </data>
  <data name="national_card_serial_number_not_valid" xml:space="preserve">
    <value>شماره سریال پشت کارت ملی صحیح نمی باشد</value>
  </data>
  <data name="national_id_series_is_required" xml:space="preserve">
    <value>شناسه ملی الزامیست</value>
  </data>
  <data name="national_id_series_not_valid" xml:space="preserve">
    <value>شناسه ملی صحیح نیست</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>پلاک</value>
  </data>
  <data name="order_id_greater_zero" xml:space="preserve">
    <value>شناسه سفارش باید بزرگتر از صفر باشد</value>
  </data>
  <data name="order_id_is_required" xml:space="preserve">
    <value>شناسه سفارش الزامیست</value>
  </data>
  <data name="order_tracking_code_is_required" xml:space="preserve">
    <value>کد رهگیری سفارش الزامیست</value>
  </data>
  <data name="otp_code_is_required" xml:space="preserve">
    <value>کد پیامک شده الزامیست</value>
  </data>
  <data name="page_number_greater_zero" xml:space="preserve">
    <value>شماره صفحه باید بزرگتر از صفر باشد</value>
  </data>
  <data name="page_size_greater_zero" xml:space="preserve">
    <value>تعداد آیتم های هر صفحه باید بزرگتر از صفر باشد</value>
  </data>
  <data name="payment_code_is_required" xml:space="preserve">
    <value>کد پرداختی الزامیست</value>
  </data>
  <data name="payment_ref_id_greater_zero" xml:space="preserve">
    <value>شناسه ارجاع پرداخت باید بزرگتر از صفر باشد</value>
  </data>
  <data name="plan_code_is_required" xml:space="preserve">
    <value>کد طرح الزامیست</value>
  </data>
  <data name="plan_id_is_required" xml:space="preserve">
    <value>شناسه طرح الزامیست</value>
  </data>
  <data name="postal_code_is_required" xml:space="preserve">
    <value>کد پستی الزامیست</value>
  </data>
  <data name="postal_code_not_valid" xml:space="preserve">
    <value>کد پستی صحیح نیست</value>
  </data>
  <data name="promissory_payment_status_is_required" xml:space="preserve">
    <value>وضعیت پرداختی سفته الزامیست</value>
  </data>
  <data name="province_english_is_required" xml:space="preserve">
    <value>استان به انگلیسی الزامیست</value>
  </data>
  <data name="province_english_not_valid" xml:space="preserve">
    <value>استان به انگلیسی صحیح نیست</value>
  </data>
  <data name="ref_id_is_required" xml:space="preserve">
    <value>شناسه سفارش یکتا پذیرنده الزامیست</value>
  </data>
  <data name="signature_image_not_null" xml:space="preserve">
    <value>تصویر امضا الزامیست</value>
  </data>
  <data name="signature_video_not_null" xml:space="preserve">
    <value>ویدیو احراز هویت الزامیست</value>
  </data>
  <data name="signature_video_sentence_not_null" xml:space="preserve">
    <value>متن ویدیو احراز هویت الزامیست</value>
  </data>
  <data name="tracking_code_is_required" xml:space="preserve">
    <value>کد رهگیری الزامیست</value>
  </data>
  <data name="wallet_id_is_required" xml:space="preserve">
    <value>شناسه کیف پول الزامیست</value>
  </data>
  <data name="year_greater_1403" xml:space="preserve">
    <value>سال باید 1403 به بعد باشد</value>
  </data>
</root>