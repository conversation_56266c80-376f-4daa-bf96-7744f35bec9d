﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using PayPing.BNPL.Application.Infrastructure.Persistence;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.ConsumerInfo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)");

                    b.Property<string>("CardNumber")
                        .HasMaxLength(16)
                        .HasColumnType("character varying(16)");

                    b.Property<string>("CityEnglish")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<int>("ConsumerUserId")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<string>("Email")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("FatherName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("FirstNameEnglish")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("FullName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("IBan")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)");

                    b.Property<bool>("IsMale")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<string>("LastName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("LastNameEnglish")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("NationalCode")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("NationalIdSeries")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("PersianBirthDate")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("ProvinceEnglish")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("SignatureImageFileId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("SignatureImageFileName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("SignatureVideoFileId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("SignatureVideoFileName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("SignatureVideoRandomSentence")
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)");

                    b.Property<string>("UserName")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("ConsumerUserId")
                        .IsUnique()
                        .HasDatabaseName("IX_ConsumerInfo_ConsumerUserId");

                    b.ToTable("ConsumerInfos");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Contract", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ActivationStatus")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("ExpireDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FilledContractId")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<int>("MerchantUserId")
                        .HasColumnType("integer");

                    b.Property<decimal>("OperationCost")
                        .HasColumnType("numeric");

                    b.Property<int>("OperationCostStrategy")
                        .HasColumnType("integer");

                    b.Property<string>("SignedContractId")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("MerchantUserId")
                        .HasDatabaseName("IX_Contract_MerchantUserId");

                    b.ToTable("Contracts");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Credit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("Code")
                        .HasColumnType("uuid");

                    b.Property<int>("ContractId")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<decimal>("TotalConsumerWageAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalInstallmentPaymentAmount")
                        .HasColumnType("numeric");

                    b.Property<long>("TotalInstallmentPaymentCount")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalMerchantWageAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalRawPrePaymentAmount")
                        .HasColumnType("numeric");

                    b.Property<long>("TotalRawPrePaymentCount")
                        .HasColumnType("bigint");

                    b.Property<Guid>("WalletId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Credit_Code");

                    b.HasIndex("ContractId");

                    b.ToTable("Credits");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.CreditTransaction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<Guid>("CorrelationId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<Guid>("CreditCode")
                        .HasColumnType("uuid");

                    b.Property<int>("CreditId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<int>("TransactionType")
                        .HasColumnType("integer");

                    b.Property<Guid>("WalletId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreditCode")
                        .HasDatabaseName("IX_CreditTransaction_CreditCode");

                    b.HasIndex("CreditId");

                    b.HasIndex("WalletId")
                        .HasDatabaseName("IX_CreditTransaction_WalletId");

                    b.ToTable("CreditTransactions");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.CreditTransactionOutbox", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<int>("CreditId")
                        .HasColumnType("integer");

                    b.Property<int>("FailCount")
                        .HasColumnType("integer");

                    b.Property<bool>("HasFailed")
                        .HasColumnType("boolean");

                    b.Property<string>("LockId")
                        .HasColumnType("text");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<int>("TransactionType")
                        .HasColumnType("integer");

                    b.Property<Guid>("WalletId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreditId");

                    b.HasIndex("WalletId")
                        .HasDatabaseName("IX_CreditTransactionOutbox_WalletId");

                    b.ToTable("CreditTransactionOutboxes");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.EmployeeInfo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<int>("EmployerUserId")
                        .HasColumnType("integer");

                    b.Property<string>("MobileNumber")
                        .HasMaxLength(11)
                        .HasColumnType("character varying(11)");

                    b.Property<string>("NationalCode")
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.HasKey("Id");

                    b.HasIndex("EmployerUserId")
                        .HasDatabaseName("IX_EmployeeInfo_EmployerUserId");

                    b.HasIndex("MobileNumber")
                        .HasDatabaseName("IX_EmployeeInfo_MobileNumber");

                    b.HasIndex("EmployerUserId", "MobileNumber", "NationalCode")
                        .IsUnique();

                    b.ToTable("EmployeeInfos");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.EventLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("Code")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<int>("LogType")
                        .HasColumnType("integer");

                    b.Property<string>("Message")
                        .HasMaxLength(10000)
                        .HasColumnType("character varying(10000)");

                    b.Property<string>("Name")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("RefId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int?>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RefId")
                        .HasDatabaseName("IX_EventLog_RefId");

                    b.ToTable("EventLogs");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Guarantor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("Code")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Domain")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("GuaranteeProvider")
                        .HasColumnType("integer");

                    b.Property<int>("GuaranteeType")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Guarantor_Code");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Guarantor_Name");

                    b.ToTable("Guarantors");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Installment", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<Guid>("Code")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<decimal>("DelayPenaltyAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTimeOffset>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("FinalAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("InterestAmount")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<int>("Number")
                        .HasColumnType("integer");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("OriginalAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTimeOffset?>("PaymentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Installment_Code");

                    b.HasIndex("OrderId");

                    b.ToTable("Installments");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.MerchantInfo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)");

                    b.Property<string>("CityEnglish")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<string>("Email")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<decimal>("InterestRate")
                        .HasColumnType("numeric");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<int>("MerchantUserId")
                        .HasColumnType("integer");

                    b.Property<decimal>("MinPrePaymentRate")
                        .HasColumnType("numeric");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("PredefinedCode")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("ProvinceEnglish")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("SignatureImageFileId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("SignatureImageFileName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.ComplexProperty<Dictionary<string, object>>("MerchantInfoAgent", "PayPing.BNPL.Domain.Models.MerchantInfo.MerchantInfoAgent#MerchantInfoAgent", b1 =>
                        {
                            b1.Property<string>("FatherName")
                                .HasMaxLength(128)
                                .HasColumnType("character varying(128)");

                            b1.Property<string>("FirstName")
                                .HasMaxLength(128)
                                .HasColumnType("character varying(128)");

                            b1.Property<string>("FirstNameEnglish")
                                .HasMaxLength(128)
                                .HasColumnType("character varying(128)");

                            b1.Property<bool>("IsMale")
                                .HasColumnType("boolean");

                            b1.Property<string>("LastName")
                                .HasMaxLength(128)
                                .HasColumnType("character varying(128)");

                            b1.Property<string>("LastNameEnglish")
                                .HasMaxLength(128)
                                .HasColumnType("character varying(128)");

                            b1.Property<string>("MobileNumber")
                                .HasMaxLength(64)
                                .HasColumnType("character varying(64)");

                            b1.Property<string>("NationalCode")
                                .HasMaxLength(64)
                                .HasColumnType("character varying(64)");

                            b1.Property<string>("NationalIdSeries")
                                .HasMaxLength(128)
                                .HasColumnType("character varying(128)");

                            b1.Property<string>("PersianBirthDate")
                                .HasMaxLength(64)
                                .HasColumnType("character varying(64)");
                        });

                    b.HasKey("Id");

                    b.HasIndex("MerchantUserId")
                        .IsUnique()
                        .HasDatabaseName("IX_MerchantInfo_MerchantUserId");

                    b.ToTable("MerchantInfos");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Order", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ClientCallbackUrl")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("ClientCancelUrl")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("ClientRefId")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("ConsumerIdentifier")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ConsumerName")
                        .HasMaxLength(2046)
                        .HasColumnType("character varying(2046)");

                    b.Property<string>("ConsumerNationalHashId")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<decimal>("ConsumerOperationCostAmount")
                        .HasColumnType("numeric");

                    b.Property<int?>("ConsumerUserId")
                        .HasColumnType("integer");

                    b.Property<string>("ConsumerUserName")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<string>("CreditLockId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<decimal>("CreditedAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("ExpireDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("GuaranteeAmount")
                        .HasColumnType("numeric");

                    b.Property<bool>("GuaranteeRevoked")
                        .HasColumnType("boolean");

                    b.Property<decimal>("InstallmentsTotalRawAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("LatestPrePaymentTryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MerchantName")
                        .HasMaxLength(2046)
                        .HasColumnType("character varying(2046)");

                    b.Property<decimal>("MerchantOperationCostAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("MerchantUserId")
                        .HasColumnType("integer");

                    b.Property<decimal>("OrderTotalAmount")
                        .HasColumnType("numeric");

                    b.Property<int?>("PlanId")
                        .HasColumnType("integer");

                    b.Property<bool>("PlanIsLocked")
                        .HasColumnType("boolean");

                    b.Property<decimal>("PrePaymentAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("SignedContractFileId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid>("TrackingCode")
                        .HasColumnType("uuid");

                    b.ComplexProperty<Dictionary<string, object>>("OrderCancellation", "PayPing.BNPL.Domain.Models.Order.OrderCancellation#OrderCancellation", b1 =>
                        {
                            b1.Property<DateTimeOffset>("CanceledDate")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<string>("Description")
                                .HasMaxLength(4092)
                                .HasColumnType("character varying(4092)");

                            b1.Property<bool>("IsRefundActive")
                                .HasColumnType("boolean");

                            b1.Property<bool>("IsRefundBalanceEnough")
                                .HasColumnType("boolean");

                            b1.Property<bool>("IsRefundNeeded")
                                .HasColumnType("boolean");

                            b1.Property<string>("Reason")
                                .HasMaxLength(2046)
                                .HasColumnType("character varying(2046)");

                            b1.Property<string>("RefundRefId")
                                .HasMaxLength(64)
                                .HasColumnType("character varying(64)");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("OrderPlan", "PayPing.BNPL.Domain.Models.Order.OrderPlan#OrderPlan", b1 =>
                        {
                            b1.Property<bool>("ContractIsMandatory")
                                .HasColumnType("boolean");

                            b1.Property<decimal>("CreditAmount")
                                .HasColumnType("numeric");

                            b1.Property<int>("CreditId")
                                .HasColumnType("integer");

                            b1.Property<int>("GuarantorId")
                                .HasColumnType("integer");

                            b1.Property<bool>("HasInstallmentDelayPenalty")
                                .HasColumnType("boolean");

                            b1.Property<int>("InstallmentCount")
                                .HasColumnType("integer");

                            b1.Property<int>("InstallmentDelayPenaltyFreeInDays")
                                .HasColumnType("integer");

                            b1.Property<decimal>("InstallmentDelayPenaltyRatePerDay")
                                .HasColumnType("numeric");

                            b1.Property<int>("InstallmentPeriodInMonths")
                                .HasColumnType("integer");

                            b1.Property<decimal>("InterestRate")
                                .HasColumnType("numeric");

                            b1.Property<Guid>("WalletId")
                                .HasColumnType("uuid");
                        });

                    b.HasKey("Id");

                    b.HasIndex("ClientRefId")
                        .HasDatabaseName("IX_Order_ClientRefId");

                    b.HasIndex("ConsumerName")
                        .HasDatabaseName("IX_Order_ConsumerName");

                    b.HasIndex("ConsumerNationalHashId")
                        .HasDatabaseName("IX_Order_ConsumerNationalHashId");

                    b.HasIndex("ConsumerUserId")
                        .HasDatabaseName("IX_Order_ConsumerUserId");

                    b.HasIndex("ConsumerUserName")
                        .HasDatabaseName("IX_Order_ConsumerUserName");

                    b.HasIndex("MerchantUserId")
                        .HasDatabaseName("IX_Order_MerchantUserId");

                    b.HasIndex("PlanId");

                    b.HasIndex("TrackingCode")
                        .IsUnique()
                        .HasDatabaseName("IX_Order_TrackingCode");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.OrderCreditValidation", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<bool>("HasLoans")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Risk")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<int>("Score")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("OrderCreditValidations");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.OrderExternalEventOutbox", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ClientCancelUrl")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("ClientRefId")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<int>("EventType")
                        .HasColumnType("integer");

                    b.Property<int>("FailCount")
                        .HasColumnType("integer");

                    b.Property<bool>("HasFailed")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("OrderExternalEventOutboxes");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.OrderGuarantee", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<string>("Data")
                        .HasColumnType("jsonb");

                    b.Property<string>("GuaranteeRefId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int>("GuarantorId")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("SettlementData")
                        .HasColumnType("jsonb");

                    b.HasKey("Id");

                    b.HasIndex("GuaranteeRefId")
                        .HasDatabaseName("IX_OrderGuarantee_GuaranteeRefId");

                    b.HasIndex("GuarantorId");

                    b.HasIndex("OrderId");

                    b.ToTable("OrderGuarantees");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.OrderPayment", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<string>("CardNumber")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<int>("CreditId")
                        .HasColumnType("integer");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<long?>("InstallmentId")
                        .HasColumnType("bigint");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTimeOffset?>("PayDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PaymentCode")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<long?>("PaymentGroupId")
                        .HasColumnType("bigint");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("integer");

                    b.Property<int>("PaymentType")
                        .HasColumnType("integer");

                    b.Property<Guid>("WalletId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("InstallmentId");

                    b.HasIndex("OrderId");

                    b.HasIndex("PaymentCode")
                        .HasDatabaseName("IX_OrderPayment_PaymentCode");

                    b.HasIndex("PaymentGroupId");

                    b.ToTable("OrderPayments");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.OrderTargetPlan", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("PlanId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("PlanId");

                    b.ToTable("OrderTargetPlans");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.PaymentGroup", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("ConsumerUserId")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<string>("PaymentCode")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("PaymentCode")
                        .HasDatabaseName("IX_PaymentGroup_PaymentCode");

                    b.ToTable("PaymentGroups");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Plan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid>("Code")
                        .HasColumnType("uuid");

                    b.Property<bool>("ContractIsMandatory")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<int>("CreditId")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("GuaranteeType")
                        .HasColumnType("integer");

                    b.Property<bool>("HasInstallmentDelayPenalty")
                        .HasColumnType("boolean");

                    b.Property<int>("InstallmentCount")
                        .HasColumnType("integer");

                    b.Property<int>("InstallmentDelayPenaltyFreeInDays")
                        .HasColumnType("integer");

                    b.Property<decimal>("InstallmentDelayPenaltyRatePerDay")
                        .HasColumnType("numeric");

                    b.Property<int>("InstallmentPeriodInMonths")
                        .HasColumnType("integer");

                    b.Property<decimal>("InterestRate")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<decimal>("MaxCreditAmount")
                        .HasColumnType("numeric");

                    b.Property<int>("MerchantUserId")
                        .HasColumnType("integer");

                    b.Property<decimal>("MinOrderAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("MinPrePaymentRate")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Plan_Code");

                    b.HasIndex("CreditId");

                    b.ToTable("Plans");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.PlanEmployee", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<int>("EmployeeInfoId")
                        .HasColumnType("integer");

                    b.Property<int>("PlanId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeInfoId");

                    b.HasIndex("PlanId", "EmployeeInfoId")
                        .IsUnique();

                    b.ToTable("PlanEmployees");
                });

            modelBuilder.Entity("PayPing.LuckyLuke.Application.Domain.Models.DigitalSign", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CreatedBy")
                        .HasColumnType("integer");

                    b.Property<string>("Data")
                        .HasColumnType("jsonb");

                    b.Property<DateTimeOffset?>("ExpiredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("LastModifiedBy")
                        .HasColumnType("integer");

                    b.Property<string>("SignatureRefId")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SignatureRefId")
                        .HasDatabaseName("IX_DigitalSign_SignatureRefId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_DigitalSign_UserId");

                    b.ToTable("DigitalSigns");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Credit", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Contract", "Contract")
                        .WithMany("Credits")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.CreditTransaction", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Credit", "Credit")
                        .WithMany("CreditTransactions")
                        .HasForeignKey("CreditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Credit");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.CreditTransactionOutbox", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Credit", "Credit")
                        .WithMany("CreditFailedTransactions")
                        .HasForeignKey("CreditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Credit");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Installment", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Order", "Order")
                        .WithMany("Installments")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Order", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Plan", "Plan")
                        .WithMany("Orders")
                        .HasForeignKey("PlanId");

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.OrderCreditValidation", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Order", "Order")
                        .WithMany("OrderCreditValidations")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.OrderExternalEventOutbox", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Order", "Order")
                        .WithMany("OrderExternalEventOutboxes")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.OrderGuarantee", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Guarantor", "Guarantor")
                        .WithMany("OrderGuarantees")
                        .HasForeignKey("GuarantorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PayPing.BNPL.Domain.Models.Order", "Order")
                        .WithMany("OrderGuarantees")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Guarantor");

                    b.Navigation("Order");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.OrderPayment", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Installment", "Installment")
                        .WithMany("OrderPayments")
                        .HasForeignKey("InstallmentId");

                    b.HasOne("PayPing.BNPL.Domain.Models.Order", "Order")
                        .WithMany("OrderPayments")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PayPing.BNPL.Domain.Models.PaymentGroup", "PaymentGroup")
                        .WithMany("OrderPayments")
                        .HasForeignKey("PaymentGroupId");

                    b.Navigation("Installment");

                    b.Navigation("Order");

                    b.Navigation("PaymentGroup");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.OrderTargetPlan", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Order", "Order")
                        .WithMany("OrderTargetPlans")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PayPing.BNPL.Domain.Models.Plan", "Plan")
                        .WithMany("OrderTargetPlans")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Plan", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.Credit", "Credit")
                        .WithMany("Plans")
                        .HasForeignKey("CreditId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Credit");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.PlanEmployee", b =>
                {
                    b.HasOne("PayPing.BNPL.Domain.Models.EmployeeInfo", "EmployeeInfo")
                        .WithMany("PlanEmployees")
                        .HasForeignKey("EmployeeInfoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PayPing.BNPL.Domain.Models.Plan", "Plan")
                        .WithMany("PlanEmployees")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EmployeeInfo");

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Contract", b =>
                {
                    b.Navigation("Credits");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Credit", b =>
                {
                    b.Navigation("CreditFailedTransactions");

                    b.Navigation("CreditTransactions");

                    b.Navigation("Plans");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.EmployeeInfo", b =>
                {
                    b.Navigation("PlanEmployees");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Guarantor", b =>
                {
                    b.Navigation("OrderGuarantees");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Installment", b =>
                {
                    b.Navigation("OrderPayments");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Order", b =>
                {
                    b.Navigation("Installments");

                    b.Navigation("OrderCreditValidations");

                    b.Navigation("OrderExternalEventOutboxes");

                    b.Navigation("OrderGuarantees");

                    b.Navigation("OrderPayments");

                    b.Navigation("OrderTargetPlans");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.PaymentGroup", b =>
                {
                    b.Navigation("OrderPayments");
                });

            modelBuilder.Entity("PayPing.BNPL.Domain.Models.Plan", b =>
                {
                    b.Navigation("OrderTargetPlans");

                    b.Navigation("Orders");

                    b.Navigation("PlanEmployees");
                });
#pragma warning restore 612, 618
        }
    }
}
