﻿using Ardalis.GuardClauses;
using Currency.Coin;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.Tools.SdkBase.Types;
using System.Text.Json;
using Wallet;
using Wallet.Type;

namespace PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
public class WalletGrpcClient(
    WalletGrpc.WalletGrpcClient walletGrpcClient,
    CoinGrpc.CoinGrpcClient coinGrpcClient,
    WalletTypeGrpc.WalletTypeGrpcClient walletTypeGrpcClient,
    IDistributedCache cache,
    IUserService userService,
    ILogger<WalletGrpcClient> logger) : IWalletGrpcClient
{
    public async Task<ServiceResult<CreateWalletResponse>> CreateWallet(int userId, Guid creditCode, CancellationToken cancellationToken = default)
    {
        Guard.Against.NegativeOrZero(userId, nameof(userId));
        Guard.Against.NullOrEmpty(creditCode, nameof(creditCode));
        try
        {
            var walletTypes = await walletTypeGrpcClient.GetWalletTypesAsync(new GetWalletTypesRequest(), cancellationToken: cancellationToken);

            var bnplWalletType = walletTypes.WalletTypes.FirstOrDefault(t => t.Title.Equals("BNPL", StringComparison.InvariantCultureIgnoreCase));

            var userInfo = await userService.GetMerchantExtraInfoAsync(userId, cancellationToken);

            var request = new CreateWalletRequest
            {
                ClientId = userId,
                BusinessId = userId.ToString(),

                CoinSymbol = "IRT",
                WalletTypeId = bnplWalletType?.Id,

                ClientTrackId = creditCode.ToString(),

                FirstName = userInfo.DisplayName,
                NationalId = userInfo.DisplayNationalCode,
                PhoneNumber = userInfo.PhoneNumber,
                // Optional properties
                LastName = string.Empty,
            };
            request.Policies.AddRange(bnplWalletType?.WalletTypePolicies.Select(p => p.Id));

            var response = await walletGrpcClient.CreateWalletAsync(request, cancellationToken: cancellationToken);
            if (response.ProblemDetail is null)
            {
                return new ServiceResult<CreateWalletResponse>
                {
                    Succeeded = true,
                    SuccessResult = response
                };
            }

            logger.LogError("An error occurred while creating the wallet. Error details: {ProblemDetails}", JsonSerializer.Serialize(response.ProblemDetail));
            return new ServiceResult<CreateWalletResponse>()
            {
                Succeeded = false,
                SuccessResult = response,
                Message = "An error occurred while creating the wallet.",
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while creating the wallet.");
            return new ServiceResult<CreateWalletResponse>
            {
                Succeeded = false,
                Message = "An error occurred while creating the wallet."
            };
        }
    }

    public async Task<ServiceResult<GetWalletTransactionTotalAmountResponse>> GetWalletTransactionTotalAmount(
        Guid walletId,
        TransactionType transactionType,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        try
        {
            var request = new GetWalletTransactionTotalAmountRequest
            {
                WalletId = walletId.ToString(),
                Type = transactionType
            };

            var response = await walletGrpcClient.GetWalletTransactionTotalAmountAsync(request, cancellationToken: cancellationToken);
            if (response.ProblemDetail is null)
            {
                return new ServiceResult<GetWalletTransactionTotalAmountResponse>
                {
                    Succeeded = true,
                    SuccessResult = response
                };
            }

            logger.LogError("An error occurred while getting wallet transaction total amount. Error details: {ProblemDetails}", JsonSerializer.Serialize(response.ProblemDetail));
            return new ServiceResult<GetWalletTransactionTotalAmountResponse>
            {
                Succeeded = false,
                SuccessResult = response,
                Message = "An error occurred while getting wallet transaction total amount."
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while getting wallet transaction total amount.");
            return new ServiceResult<GetWalletTransactionTotalAmountResponse>
            {
                Succeeded = false,
                Message = "An error occurred while getting wallet transaction total amount."
            };
        }
    }

    public async Task<ServiceResult<GetWalletBalanceByIdResponse>> GetWalletBalanceById(Guid walletId, CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        try
        {
            var request = new GetWalletBalanceByIdRequest
            {
                WalletId = walletId.ToString()
            };

            var response = await walletGrpcClient.GetWalletBalanceByIdAsync(request, cancellationToken: cancellationToken);
            if (response.ProblemDetail is null)
            {
                return new ServiceResult<GetWalletBalanceByIdResponse>
                {
                    Succeeded = true,
                    SuccessResult = response
                };
            }

            logger.LogError("An error occurred while getting wallet balance. Error details: {ProblemDetails}", JsonSerializer.Serialize(response.ProblemDetail));
            return new ServiceResult<GetWalletBalanceByIdResponse>
            {
                Succeeded = false,
                SuccessResult = response,
                Message = "An error occurred while getting wallet balance."
            };
        }
        catch (Exception e)
        {
            logger.LogError(e, "An error occurred while getting wallet balance.");
            return new ServiceResult<GetWalletBalanceByIdResponse>
            {
                Succeeded = false,
                Message = "An error occurred while getting wallet balance."
            };
        }
    }


    public async Task<ServiceResult<GetWalletDetailByIdResponse>> GetWalletDetailsById(Guid walletId, CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        try
        {
            var request = new GetWalletDetailByIdRequest { WalletId = walletId.ToString() };

            var response = await walletGrpcClient.GetWalletDetailByIdAsync(request, cancellationToken: cancellationToken);
            if (response.ProblemDetail is null)
            {
                return new ServiceResult<GetWalletDetailByIdResponse>
                {
                    Succeeded = true,
                    SuccessResult = response,
                };
            }

            logger.LogError("An error occurred while getting wallet details. Error details: {ProblemDetails}", JsonSerializer.Serialize(response.ProblemDetail));
            return new ServiceResult<GetWalletDetailByIdResponse>
            {
                Succeeded = false,
                SuccessResult = response,
                Message = "An error occurred while getting wallet details."
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while getting wallet details.");
            return new ServiceResult<GetWalletDetailByIdResponse>
            {
                Succeeded = false,
                Message = "An error occurred while getting wallet details."
            };
        }
    }

    public async Task<ServiceResult<BlockWalletResponse>> Freeze(Guid walletId,
        Guid correlationId,
        decimal amount,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        Guard.Against.NullOrEmpty(correlationId, nameof(correlationId));
        Guard.Against.NegativeOrZero(amount, nameof(amount));
        try
        {
            var response = await walletGrpcClient.BlockWalletAsync(new BlockWalletRequest
            {
                WalletId = walletId.ToString(),
                CorrelationId = correlationId.ToString(),
                Amount = amount.ToWalletDecimalValue()
            }, cancellationToken: cancellationToken);

            if (response.ProblemDetail is null)
            {
                return new ServiceResult<BlockWalletResponse>
                {
                    Succeeded = true,
                    SuccessResult = response
                };
            }

            logger.LogError("An error occurred while freezing the wallet. Error details: {ProblemDetails}", JsonSerializer.Serialize(response.ProblemDetail));
            return new ServiceResult<BlockWalletResponse>
            {
                Succeeded = false,
                SuccessResult = response,
                Message = "An error occurred while freezing the wallet."
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while freezing the wallet.");
            return new ServiceResult<BlockWalletResponse>
            {
                Succeeded = false,
                Message = "An error occurred while freezing the wallet."
            };
        }
    }

    public async Task<ServiceResult<UnBlockWalletResponse>> Unfreeze(Guid walletId,
        Guid correlationId,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        Guard.Against.NullOrEmpty(correlationId, nameof(correlationId));
        try
        {
            var response = await walletGrpcClient.UnBlockWalletAsync(new UnBlockWalletRequest
            {
                WalletId = walletId.ToString(),
                CorrelationId = correlationId.ToString(),
            }, cancellationToken: cancellationToken);

            if (response.ProblemDetail is null)
            {
                return new ServiceResult<UnBlockWalletResponse>
                {
                    Succeeded = true,
                    SuccessResult = response,
                };
            }

            logger.LogError("An error occurred while unfreezing the wallet. Error details: {ProblemDetails}", JsonSerializer.Serialize(response.ProblemDetail));
            return new ServiceResult<UnBlockWalletResponse>
            {
                Succeeded = false,
                SuccessResult = response,
                Message = "An error occurred while unfreezing the wallet."
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while unfreezing the wallet.");
            return new ServiceResult<UnBlockWalletResponse>
            {
                Succeeded = false,
                Message = "An error occurred while unfreezing the wallet."
            };
        }
    }

    public async Task<ServiceResult<UnBlockWalletResponse>> UnfreezeAndWithdraw(Guid walletId,
        Guid correlationId,
        decimal amount,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        Guard.Against.NullOrEmpty(correlationId, nameof(correlationId));
        Guard.Against.NegativeOrZero(amount, nameof(amount));
        try
        {
            var request = new UnBlockWalletRequest
            {
                WalletId = walletId.ToString(),
                CorrelationId = correlationId.ToString(),
            };

            request.UnBlockActions.Add(new UnBlockAction
            {
                Amount = amount.ToWalletDecimalValue(),
                Type = UnblockActionType.Withdraw,
                Subject = TransactionSubject.Main,
                DestinationWalletId = string.Empty,
            });

            var response = await walletGrpcClient.UnBlockWalletAsync(request, cancellationToken: cancellationToken);

            if (response.ProblemDetail is null)
            {
                return new ServiceResult<UnBlockWalletResponse>
                {
                    Succeeded = true,
                    SuccessResult = response,
                };
            }

            logger.LogError("An error occurred while unfreezing the wallet. Error details: {ProblemDetails}", JsonSerializer.Serialize(response.ProblemDetail));
            return new ServiceResult<UnBlockWalletResponse>
            {
                Succeeded = false,
                SuccessResult = response,
                Message = "An error occurred while unfreezing the wallet."
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while unfreezing and withdrawing from the wallet.");
            return new ServiceResult<UnBlockWalletResponse>
            {
                Succeeded = false,
                Message = "An error occurred while unfreezing and withdrawing from the wallet."
            };
        }
    }

    public async Task<ServiceResult<DepositWalletResponse>> Deposit(Guid walletId,
        Guid correlationId,
        decimal amount,
        string description,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        Guard.Against.NullOrEmpty(correlationId, nameof(correlationId));
        Guard.Against.NegativeOrZero(amount, nameof(amount));
        try
        {
            var response = await walletGrpcClient.DepositWalletAsync(new DepositWalletRequest
            {
                WalletId = walletId.ToString(),
                CorrelationId = correlationId.ToString(),
                Amount = amount.ToWalletDecimalValue(),
                Subject = TransactionSubject.Main,
                Description = description,
            }, cancellationToken: cancellationToken);

            if (response.ProblemDetail is null)
            {
                return new ServiceResult<DepositWalletResponse>
                {
                    Succeeded = true,
                    SuccessResult = response
                };
            }

            logger.LogError("An error occurred while processing the deposit request. Error details: {ProblemDetails}", JsonSerializer.Serialize(response.ProblemDetail));
            return new ServiceResult<DepositWalletResponse>
            {
                Succeeded = false,
                SuccessResult = response,
                Message = "An error occurred while processing the deposit request."
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while processing the deposit request.");
            return new ServiceResult<DepositWalletResponse>
            {
                Succeeded = false,
                Message = "An error occurred while processing the deposit request."
            };
        }
    }

    public async Task<ServiceResult<WithdrawWalletResponse>> Withdraw(Guid walletId,
        Guid correlationId,
        decimal amount,
        string description,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        Guard.Against.NullOrEmpty(correlationId, nameof(correlationId));
        Guard.Against.NegativeOrZero(amount, nameof(amount));
        try
        {
            var response = await walletGrpcClient.WithdrawWalletAsync(new WithdrawWalletRequest
            {
                WalletId = walletId.ToString(),
                CorrelationId = correlationId.ToString(),
                Amount = amount.ToWalletDecimalValue(),
                Subject = TransactionSubject.Main,
                Description = description
            }, cancellationToken: cancellationToken);

            if (response.ProblemDetail is null)
            {
                return new ServiceResult<WithdrawWalletResponse>
                {
                    Succeeded = true,
                    SuccessResult = response,
                };
            }

            logger.LogError(
                "An error occurred while processing the withdrawal request. Error details: {ProblemDetails}",
                JsonSerializer.Serialize(response.ProblemDetail));
            return new ServiceResult<WithdrawWalletResponse>
            {
                Succeeded = false,
                SuccessResult = response,
                Message = "An error occurred while processing the withdrawal request."
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while processing the withdrawal request.");
            return new ServiceResult<WithdrawWalletResponse>
            {
                Succeeded = false,
                Message = "An error occurred while processing the withdrawal request."
            };
        }
    }
}
