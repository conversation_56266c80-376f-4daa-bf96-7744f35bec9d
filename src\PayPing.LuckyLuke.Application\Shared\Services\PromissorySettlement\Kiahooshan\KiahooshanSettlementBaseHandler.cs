﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Services.PromissorySettlement.Kiahooshan
{
    public abstract class KiahooshanSettlementBaseHandler
    {
        protected readonly ApplicationDbContext _dbContext;
        protected readonly IUserContext _userContext;
        protected readonly IKiahooshanApiHttpClient _kihooApi;
        protected readonly KiahooshanOptions _kihooOptions;
        protected KiahooshanSettlementBaseHandler(ApplicationDbContext dbContext, IUserContext userContext, IKiahooshanApiHttpClient kihooApi, IOptions<KiahooshanOptions> kihooOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _kihooApi = kihooApi;
            _kihooOptions = kihooOptions.Value;
        }

        protected string DigitalSignContextString { get; set; }
        protected byte[] SignedSettlementDocBytes { get; set; }

        protected async ValueTask<int> UpdateContextAsync(KiahooshanPromissorySettlementContextV1 context)
        {
            var og = await _dbContext.OrderGuarantees.FindAsync(context.OrderGuaranteeId);

            Guard.Against.Null(og);

            og.SettlementData = JsonSerializer.Serialize(context);

            return await _dbContext.SaveChangesAsync();
        }

        protected async ValueTask<KiahooshanDigitalSignContextV1> GetValidDigitalSignAsync(int userId)
        {
            if (string.IsNullOrEmpty(DigitalSignContextString))
            {
                var ds = await _dbContext.DigitalSigns.AsNoTracking()
                    .Where(x => x.UserId == userId && x.Type == DigitalSignType.KiahooshanVersion1 && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                    .OrderByDescending(x => x.CreatedAt)
                    .FirstOrDefaultAsync();

                if (ds == null || string.IsNullOrWhiteSpace(ds.Data))
                {
                    throw new PromissoryProviderException(null, $"kihoo digital sign not found for user: {userId}", false);
                }

                DigitalSignContextString = ds.Data;
            }

            return JsonSerializer.Deserialize<KiahooshanDigitalSignContextV1>(DigitalSignContextString);
        }

        protected virtual void ValidateContext(KiahooshanPromissorySettlementContextV1 context)
        {
            Guard.Against.Null(context);

            Guard.Against.Default(context.OrderTrackingCode);
            Guard.Against.Default(context.OrderId);
            Guard.Against.Default(context.Amount);
            Guard.Against.Default(context.OrderGuaranteeId);
            Guard.Against.Default(context.RecipientUserId);
            Guard.Against.Default(context.ConsumerUserId);
            Guard.Against.Default(context.PromissoryUniqueId);
            Guard.Against.Default(context.PromissoryTreasuryId);

        }


    }
}
