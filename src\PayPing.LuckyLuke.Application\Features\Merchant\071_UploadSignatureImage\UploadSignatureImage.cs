﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Features.Consumer._085_UploadSignature;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using Microsoft.AspNetCore.Mvc;

namespace PayPing.LuckyLuke.Application.Features.Merchant._071_UploadSignatureImage
{
    public record UploadSignatureImageRequest(IFormFile signatureImage);

    public record UploadSignatureImageResponse();

    public class UploadSignatureImageEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.UploadSignatureImage,
                async ([FromForm] UploadSignatureImageRequest request, IUploadSignatureImageRequestHandler handler, IValidator<UploadSignatureImageRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .DisableAntiforgery()
            .WithName("UploadSignatureImage")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<UploadSignatureImageResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .Accepts<UploadSignatureRequest>("multipart/form-data")
            .WithSummary("Upload Signature Image")
            .WithDescription("Upload Signature Image")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class UploadSignatureImageRequestHandler : IUploadSignatureImageRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly IEnumerable<IDigitalSignProvider> _digitalSignProviders;

        public UploadSignatureImageRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions,
            IUploadGrpcClient uploadGrpcClient, IEnumerable<IDigitalSignProvider> digitalSignProviders)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
            _uploadGrpcClient = uploadGrpcClient;
            _digitalSignProviders=digitalSignProviders;
        }

        public async ValueTask<UploadSignatureImageResponse> HandleAsync(UploadSignatureImageRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var merchantInfo = await _dbContext.MerchantInfos
                .Where(p => p.MerchantUserId == userId)
                .FirstOrDefaultAsync();

            if (merchantInfo == null)
            {
                throw new ArgumentException("اطلاعات مرچنت یافت نشد");
            }

            ModelStateDictionary fileValidationDic = new ModelStateDictionary();
            var validFile = await FileHelpers.ProcessFormFile(request.signatureImage, fileValidationDic, ApplicationConstants.SignatureImageValidExtensions, ApplicationConstants.SignatureImageMaxSizeInBytes);

            if (validFile.Length == 0 || !fileValidationDic.IsValid)
            {
                throw new ArgumentException($"تصویر امضا معتبر نیست. {string.Join(", ", fileValidationDic.Values.SelectMany(v => v.Errors).Select(x => x.ErrorMessage))}");
            }

            string validName = Path.ChangeExtension(Path.GetRandomFileName(), Path.GetExtension(request.signatureImage.FileName));

            var uploadedFile = await _uploadGrpcClient.UploadUserDocumentAsync(validFile, Path.GetExtension(validName).TrimStart('.'), userId, cancellationToken);
            if (!uploadedFile.Succeeded)
            {
                throw new ArgumentException("خطا در آپلود تصویر امضا");
            }
            merchantInfo.SignatureImageFileId = uploadedFile.SuccessResult;
            merchantInfo.SignatureImageFileName = validName;
            await _dbContext.SaveChangesAsync();

            var provider = ChooseProvider();

            var currentds = await provider.GetUserValidDigitalSignatureAsync(userId, true);
            if (currentds == null)
            {
                currentds = new Domain.Models.DigitalSign
                {
                    Type = DigitalSignType.KuknosVersion1,
                    UserId = userId,
                };

                _dbContext.DigitalSigns.Add(currentds);

                var result = await _dbContext.SaveChangesAsync();
                if (result == 0)
                {
                    throw new Exception("At UploadSignatureImageRequestHandler ; could not save DigitalSign");
                }
            }

            var data = await provider.CreateDigitalSignAsync(null, currentds.Id, userId, false, cancellationToken);

            currentds.Data = data;

            await _dbContext.SaveChangesAsync();

            var checkPoint = await provider.PushToNextCheckPointAsync(currentds.Data, cancellationToken);

            currentds.SignatureRefId = checkPoint.data;

            await _dbContext.SaveChangesAsync();

            return new UploadSignatureImageResponse();
        }

        private IDigitalSignProvider ChooseProvider()
        {
            var provider = _digitalSignProviders.FirstOrDefault(f => f.DigitalSignType == DigitalSignType.KuknosVersion1);

            return provider ?? _digitalSignProviders.OfType<KuknosDigitalSignProvider>().FirstOrDefault()
                ?? throw new ArgumentException("No valid digital sign provider found.");
        }

    }
    public interface IUploadSignatureImageRequestHandler
    {
        ValueTask<UploadSignatureImageResponse> HandleAsync(UploadSignatureImageRequest request, CancellationToken cancellationToken);
    }

    public class UploadSignatureImageValidator : AbstractValidator<UploadSignatureImageRequest>
    {
        public UploadSignatureImageValidator()
        {
            RuleFor(x => x.signatureImage).NotNull().WithResourceError(() => ValidatorDictionary.signature_image_not_null);
        }
    }

}
