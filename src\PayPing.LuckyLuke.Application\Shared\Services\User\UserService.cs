﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Caching;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Mapping;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using DNTPersianUtils;
using DNTPersianUtils.Core;

namespace PayPing.LuckyLuke.Application.Shared.Services.User
{
    public class UserService : IUserService
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IDistributedCache _cache;
        private readonly IUserApiClient _userApiClient;
        private readonly IUserContext _userContext;

        public UserService(ApplicationDbContext dbContext, IDistributedCache cache, IUserApiClient userApiClient, IUserContext userContext)
        {
            _dbContext = dbContext;
            _cache = cache;
            _userApiClient = userApiClient;
            _userContext = userContext;
        }

        public async ValueTask<UserInfoDto> GetMerchantExtraInfoAsync(int merchantUserId, CancellationToken cancellationToken)
        {
            var merchantInfo = await _cache.GetOrSetAsync(
                $"UserInfo_{merchantUserId}",
                async () =>
                {
                    var userInfo = await _userApiClient.GetUserExtraAsync(merchantUserId, cancellationToken);
                    if (!userInfo.Succeeded)
                    {
                        throw new Exception($"User Service User Info not found consumer id: {merchantUserId}");
                    }

                    return userInfo.SuccessResult.ToUserInfoDto();
                },
                new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1) });

            return merchantInfo;
        }

        public async ValueTask<UserIsMerchantDto> IsUserMerchantAsync(int userId, CancellationToken cancellationToken)
        {
            var merchantInfo = await _cache.GetOrSetAsync(
                $"UserIsMerchant_{userId}",
                async () =>
                {
                    var userInfo = await _userApiClient.GetUserExtraAsync(userId, cancellationToken);
                    if (!userInfo.Succeeded)
                    {
                        return new UserIsMerchantDto(false);
                    }

                    return new UserIsMerchantDto(true);
                },
                new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(90) });

            return merchantInfo;
        }

        public async ValueTask<UserInfoDto> GetConsumerInfoAsync(int consumerUserId, CancellationToken cancellationToken)
        {
            var consumerInfo = await _cache.GetOrSetAsync(
                $"UserInfo_{consumerUserId}",
                async () =>
                {
                    UserInfoDto infoDto = new UserInfoDto();
                    var consumerInfo = await _dbContext.ConsumerInfos.AsNoTracking().Where(c => c.ConsumerUserId == consumerUserId).FirstOrDefaultAsync(cancellationToken);
                    if (consumerInfo != null)
                    {
                        infoDto.UserId = consumerInfo.ConsumerUserId;
                        infoDto.UserName = consumerInfo.UserName;
                        infoDto.PhoneNumber = consumerInfo.UserName;
                        infoDto.FirstName = consumerInfo.FirstName;
                        infoDto.LastName = consumerInfo.LastName;
                        infoDto.FullName = $"{consumerInfo.FirstName} {consumerInfo.LastName}".Trim();
                        infoDto.NationalCode = consumerInfo.NationalCode;
                        infoDto.BirthDate = consumerInfo.PersianBirthDate.ToGregorianDateTime();
                        infoDto.FatherName = consumerInfo.FatherName;

                        infoDto.PostalCode = consumerInfo.PostalCode;
                        infoDto.Address = consumerInfo.Address;
                        infoDto.FirstNameEnglish = consumerInfo.FirstNameEnglish;
                        infoDto.LastNameEnglish = consumerInfo.LastNameEnglish;
                        infoDto.IsMale = consumerInfo.IsMale;
                        infoDto.NationalIdSeries = consumerInfo.NationalIdSeries;
                        infoDto.IBan = consumerInfo.IBan;
                        infoDto.CardNumber = consumerInfo.CardNumber;
                        infoDto.Email = consumerInfo.Email;
                        infoDto.ProvinceEnglish = consumerInfo.ProvinceEnglish;
                        infoDto.CityEnglish = consumerInfo.CityEnglish;
                    }

                    return infoDto;
                },
                new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1) });

            return consumerInfo;
        }

        public async ValueTask<UserInfoDto> SetNewConsumerInfoAsync(int consumerUserId, ConsumerInfo consumerInfo, CancellationToken cancellationToken)
        {
            UserInfoDto infoDto = new UserInfoDto(
                consumerUserId,
                consumerInfo.UserName,
                consumerInfo.UserName,
                consumerInfo.FirstName,
                consumerInfo.LastName,
                consumerInfo.NationalCode,
                consumerInfo.PersianBirthDate.ToGregorianDateTime(),
                consumerInfo.FatherName);

            infoDto.PostalCode = consumerInfo.PostalCode;
            infoDto.Address = consumerInfo.Address;
            infoDto.FirstNameEnglish = consumerInfo.FirstNameEnglish;
            infoDto.LastNameEnglish = consumerInfo.LastNameEnglish;
            infoDto.IsMale = consumerInfo.IsMale;
            infoDto.NationalIdSeries = consumerInfo.NationalIdSeries;
            infoDto.IBan = consumerInfo.IBan;
            infoDto.CardNumber = consumerInfo.CardNumber;
            infoDto.Email = consumerInfo.Email;
            infoDto.ProvinceEnglish = consumerInfo.ProvinceEnglish;
            infoDto.CityEnglish = consumerInfo.CityEnglish;

            await _cache.SetAsync<UserInfoDto>(
                $"UserInfo_{consumerUserId}",
                infoDto,
                new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1) });

            return infoDto;
        }
    }
}
