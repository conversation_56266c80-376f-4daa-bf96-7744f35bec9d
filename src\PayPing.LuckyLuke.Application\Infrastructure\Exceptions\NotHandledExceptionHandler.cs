﻿using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PayPing.LuckyLuke.Application.Shared.Extensions;

namespace PayPing.LuckyLuke.Application.Infrastructure.Exceptions;

public class NotHandledExceptionHandler : IExceptionHandler
{
    private readonly ILogger<NotHandledExceptionHandler> _logger;

    public NotHandledExceptionHandler(ILogger<NotHandledExceptionHandler> logger)
    {
        _logger = logger;
    }
    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
    {
        _logger.LogError(exception, exception?.Message ?? "Something Went Wrong..!");
        httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
        var problemDetail = new BnplProblemDetails
        {
            Key = 130,
            Title = "payping.unhandled_exception",
            Status = StatusCodes.Status500InternalServerError,
            Detail = "server unhandled exception occured",
            Instance = httpContext.Request.Path
        };
        problemDetail.Extensions.AddCausationId(httpContext.TraceIdentifier);
        await httpContext.Response.WriteAsJsonAsync(problemDetail, cancellationToken: cancellationToken);
        return true;
    }
}