﻿using PayPing.LuckyLuke.Application.Infrastructure.Web.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Web
{
    // steps sort is important
    public enum OrderSteps
    {
        [Description("در انتظار")]
        NoNext = 0,
        [Description("انتخاب طرح")]
        PlanList = 1,
        [Description("رتبه سنجی اعتباری")]
        CreditScore,
        [Description("تکمیل اطلاعات")]
        FillProfile,
        [Description("ثبت نمونه امضا")]
        UploadSignature,
        [Description("احراز هویت تصویری")]
        UploadVideoAndSignature,
        [Description("صدور پیش نویس سفته")]
        IssuePromissory,
        [Description("امضا سفته الکترونیک")]
        SignPromissory,
        [Description("پرداخت هزینه صدور سفته")]
        PayPromissory,
        [Description("امضا قرارداد خرید اقساطی")]
        SignContract,
        [Description("پیش فاکتور")]
        Quote,
        [Description("پیش پرداخت")]
        PrePay,
        [Description("نمایش اقساط")]
        GetInstallments,
        [Description("تسویه شده")]
        PaidOff,
        [Description("لغو شده")]
        Canceled,
        [Description("در انتظار لغو")]
        SemiCanceled,
        [OpenApiIgnoreEnum]
        Unknown = 100
    }
}
