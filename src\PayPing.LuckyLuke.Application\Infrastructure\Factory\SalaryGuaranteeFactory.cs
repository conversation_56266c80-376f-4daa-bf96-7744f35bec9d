﻿using Microsoft.Extensions.DependencyInjection;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class SalaryGuaranteeFactory : IGuaranteeFactory
    {
        private readonly IServiceProvider _serviceProvider;

        public SalaryGuaranteeFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public GuaranteeProvider GuaranteeProvider => GuaranteeProvider.Salary;

        public IPromissoryGuaranteeProvider CreatePromissory()
        {
            throw new NotImplementedException();
        }

        public IChequeGuaranteeProvider CreateCheque()
        {
            throw new NotImplementedException();
        }

        public INoneGuaranteeProvider CreateNoneGuarantor()
        {
            throw new NotImplementedException();
        }

        public ISalaryGuaranteeProvider CreateSalaryGuarantor()
        {
            return _serviceProvider.GetServices<ISalaryGuaranteeProvider>().First();

        }
    }
}
