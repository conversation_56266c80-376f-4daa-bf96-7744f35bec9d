﻿namespace PayPing.LuckyLuke.Application.Infrastructure.Services.Context
{
    public struct KuknosDigitalSignContextV1
    {
        public Guid? OrderTrackingCode { get; set; }
        public long DigitalSignId { get; set; }
        public int UserId { get; set; }
        public KuknosDigitalSignStatusV1 Status { get; set; }
        public string FullNameFa { get; set; }
        public string Address { get; set; }

        public string Mobile { get; set; }
        public string NationalCode { get; set; }
        public string Province { get; set; }
        public string City { get; set; }
        public string Email { get; set; }

        /// <summary>
        /// format  yyyy/mm/dd
        /// </summary>
        public string BirthDate { get; set; }
        public string NationalCardSeries { get; set; }
        public string PostalCode { get; set; }
        public string FirstNameEn { get; set; }
        public string LastNameEn { get; set; }
        public string FirstNameFa { get; set; }
        public string LastNameFa { get; set; }
        public string FatherNameFa { get; set; }
        public bool IsMale { get; set; }

        public string PublicKey { get; set; }
        public string PrivateKey { get; set; }
        public string UserMobileSignature { get; set; }

        // signature image jpg under 50kb
        //public string SignatureImageFileId { get; set; }
        //public string SignatureImageFileName { get; set; }

        public string CertificateSigningRequest { get; set; }
        public string CertificateTrackingCode { get; set; }
        public string Certificate { get; set; }

    }

    public enum KuknosDigitalSignStatusV1
    {
        CreateAccount = 20,
        CreateCertificate = 30,
        GetCertificate = 40,
        CertificateDone = 50
    }
}
