﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.Refund.WebApi.gRPC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public interface IRefundGrpcClient
    {
        ValueTask<RefundGrpcResponseBase> CreateRefundAsync(CreateRefundRequest request, CancellationToken cancellationToken);
        ValueTask<RefundGrpcResponseBase> GetRefundDetailsAsync(GetRefundDetailsRequest request, CancellationToken cancellationToken);
        ValueTask<RefundGrpcResponseBase> GetUserConfigurationAsync(GetUserConfigurationRequest request, CancellationToken cancellationToken);
    }
}
