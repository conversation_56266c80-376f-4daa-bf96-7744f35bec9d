﻿using DNTPersianUtils.Core;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Admin.AdminMerchantInfoGet
{
    public record AdminMerchantInfoGetRequest(int merchantUserId);

    public class AdminMerchantInfoGetResponse
    {
        public string FullName { get; set; }
        public string NationalCode { get; set; }
        public string AgentName { get; set; }
        public string PhoneNumber { get; set; }
        public string PostalCode { get; set; }
        public string Address { get; set; }
    }

    public class AdminMerchantInfoGetEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                AdminRoutes.AdminMerchantInfoGet,
                async (
                    [AsParameters] AdminMerchantInfoGetRequest request,
                    IAdminMerchantInfoGetRequestHandler handler,
                    IValidator<AdminMerchantInfoGetRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
                .RequireAuthorization("admin")
                .WithName("AdminMerchantInfoGet")
                .WithApiVersionSet(builder.NewApiVersionSet("Merchant").Build())
                .Produces<AdminMerchantInfoGetResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Admin Merchant Info Get")
                .WithDescription("Admin Merchant Info Get")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class AdminMerchantInfoGetRequestHandler : IAdminMerchantInfoGetRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserService _userService;

        public AdminMerchantInfoGetRequestHandler(ApplicationDbContext dbContext, IUserService userService)
        {
            _dbContext = dbContext;
            _userService = userService;
        }

        public async ValueTask<AdminMerchantInfoGetResponse> HandleAsync(AdminMerchantInfoGetRequest request, CancellationToken cancellationToken)
        {
            var mi = await _dbContext.MerchantInfos.AsNoTracking().Where(m => m.MerchantUserId == request.merchantUserId).FirstOrDefaultAsync();

            if (mi == null)
            {
                throw new ArgumentException("پذیرنده یافت نشد");
            }

            var merchantProfile = await _userService.GetMerchantExtraInfoAsync(request.merchantUserId, cancellationToken);

            return new AdminMerchantInfoGetResponse
            {
                FullName = merchantProfile.DisplayName,
                NationalCode = merchantProfile.DisplayNationalCode,
                PostalCode = mi.PostalCode,
                Address = mi.Address,
                AgentName = $"{mi.MerchantInfoAgent.FirstName} {mi.MerchantInfoAgent.LastName}",
                PhoneNumber = mi.MerchantInfoAgent.MobileNumber,
            };

        }
    }

    public interface IAdminMerchantInfoGetRequestHandler
    {
        ValueTask<AdminMerchantInfoGetResponse> HandleAsync(AdminMerchantInfoGetRequest request, CancellationToken cancellationToken);
    }

    public class AdminMerchantInfoGetValidator : AbstractValidator<AdminMerchantInfoGetRequest>
    {
        public AdminMerchantInfoGetValidator()
        {
            RuleFor(x => x.merchantUserId).NotEmpty().WithResourceError(() => ValidatorDictionary.merchant_user_id_is_required);
        }
    }
}
