﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Caching;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer._07_CreditValidationOTP
{
    public record CreditScoreValidationOTPRequest(Guid trackingCode);

    public class CreditScoreValidationOTPEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.CreditScoreValidationOTP,
                async (
                    CreditScoreValidationOTPRequest request,
                    ICreditScoreValidationOTPRequestHandler handler,
                    IValidator<CreditScoreValidationOTPRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok();
                })
            .RequireAuthorization("write")
            .WithName("CreditScoreValidationOTP")
            .WithApiVersionSet(builder.NewApiVersionSet("CreditScore").Build())
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Credit Score Validation OTP")
            .WithDescription("Credit Score Validation OTP")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CreditScoreValidationOTPRequestHandler : ICreditScoreValidationOTPRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly IUserService _userService;
        private readonly IInquiryApiClient _inquiryApiClient;
        private readonly IDistributedCache _cache;

        public CreditScoreValidationOTPRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IUserService userService,
            IInquiryApiClient inquiryApiClient,
            IDistributedCache cache)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _userService = userService;
            _inquiryApiClient = inquiryApiClient;
            _cache = cache;
        }

        public async ValueTask HandleAsync(CreditScoreValidationOTPRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders
                .Where(o => o.TrackingCode == request.trackingCode && o.ConsumerUserId == userId)
                .FirstOrDefaultAsync(cancellationToken);

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            var consumerInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);

            var creditScoreOtpResult = await _inquiryApiClient.StartCreditScore(consumerInfo.NationalCode, consumerInfo.UserName);

            if (creditScoreOtpResult == null || !creditScoreOtpResult.Success)
            {
                await ByPassCreditScore(order, 1);

                throw new CreditScoreStepException(request.trackingCode.ToString(), $"provider error at creditscore start with message: {creditScoreOtpResult?.Message}");
            }

            await _cache.SetAsync(
                   $"CreditScoreToken_{consumerInfo.NationalCode}_{consumerInfo.UserName}",
                   creditScoreOtpResult.Token,
                   new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5) });

            order.Status = Domain.Enums.OrderStatus.CreditValidationInProgress;

            await _dbContext.SaveChangesAsync();
        }

        private async ValueTask ByPassCreditScore(Order order, int score)
        {
            OrderCreditValidation orderCreditValidation = new OrderCreditValidation
            {
                OrderId = order.Id,
                Score = score,
                Risk = string.Empty,
                Description = string.Empty,
                HasLoans = false
            };

            _dbContext.OrderCreditValidations.Add(orderCreditValidation);


            order.Status = Domain.Enums.OrderStatus.CreditValidationSucceeded;

            await _dbContext.SaveChangesAsync();
        }
    }

    public interface ICreditScoreValidationOTPRequestHandler
    {
        ValueTask HandleAsync(CreditScoreValidationOTPRequest request, CancellationToken cancellationToken);
    }

    public class CreditScoreValidationOTPValidator : AbstractValidator<CreditScoreValidationOTPRequest>
    {
        public CreditScoreValidationOTPValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}

