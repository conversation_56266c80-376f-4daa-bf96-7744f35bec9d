﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Services.PromissorySettlement.Kuknos
{
    public abstract class KuknosSettlementBaseHandler
    {
        protected readonly ApplicationDbContext _dbContext;
        protected readonly IUserContext _userContext;
        protected readonly IKuknosApiHttpClient _kuknosApi;
        protected readonly KuknosOptions _kuknosOptions;
        protected KuknosSettlementBaseHandler(ApplicationDbContext dbContext, IUserContext userContext, IKuknosApiHttpClient kuknosApi, IOptions<KuknosOptions> kuknosOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _kuknosApi = kuknosApi;
            _kuknosOptions = kuknosOptions.Value;
        }

        protected string DigitalSignContextString { get; set; }
        protected byte[] SettlementRawDocBytes { get; set; }



        protected async ValueTask<int> UpdateContextAsync(KuknosPromissorySettlementContextV1 context)
        {
            var og = await _dbContext.OrderGuarantees.FindAsync(context.OrderGuaranteeId);

            Guard.Against.Null(og);

            og.SettlementData = JsonSerializer.Serialize(context);

            return await _dbContext.SaveChangesAsync();
        }

        protected async ValueTask<KuknosDigitalSignContextV1> GetValidDigitalSignAsync(int userId)
        {
            if (string.IsNullOrEmpty(DigitalSignContextString))
            {
                var ds = await _dbContext.DigitalSigns.AsNoTracking()
                    .Where(x => x.UserId == userId && x.Type == DigitalSignType.KuknosVersion1 && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                    .OrderByDescending(x => x.CreatedAt)
                    .FirstOrDefaultAsync();

                if (ds == null || string.IsNullOrWhiteSpace(ds.Data))
                {
                    throw new PromissoryProviderException(null, $"kuknos digital sign not found for user: {userId}", false);
                }

                DigitalSignContextString = ds.Data;
            }

            return JsonSerializer.Deserialize<KuknosDigitalSignContextV1>(DigitalSignContextString);
        }

        protected virtual void ValidateContext(KuknosPromissorySettlementContextV1 context)
        {
            Guard.Against.Null(context);
            Guard.Against.Default(context.OrderId);
            Guard.Against.Default(context.OrderTrackingCode);
            Guard.Against.Default(context.OrderGuaranteeId);
            Guard.Against.Default(context.PromissoryId);
            Guard.Against.Default(context.RecipientUserId);
            Guard.Against.Default(context.ConsumerUserId);

        }


    }
}
