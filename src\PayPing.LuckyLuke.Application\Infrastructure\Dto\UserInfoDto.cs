﻿using DNTPersianUtils.Core.IranCities;
using Elastic.CommonSchema;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Dto
{
    public record UserInfoDto
    {
        public UserInfoDto()
        {
        }

        public UserInfoDto(int userId, string userName, string phoneNumber, string firstName, string lastName, string nationalCode, DateTime? birthDate, string fathername)
        {
            UserId = userId;
            UserName = userName;
            PhoneNumber = phoneNumber;
            FirstName = firstName;
            LastName = lastName;
            NationalCode = nationalCode;
            BirthDate = birthDate;
            FullName = $"{firstName} {lastName}".Trim();
            FatherName = fathername;
        }

        public int UserId { get; set; }
        public string UserName { get; set; }
        public string PhoneNumber { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string FatherName { get; set; }
        public string FullName { get; set; }
        public string BusinessName { get; set; }
        public string NationalCode { get; set; }
        public DateTime? BirthDate { get; set; }
        public bool IsLegal { get; set; }
        public string BusinessNationalCode { get; set; }


        public string PostalCode { get; set; }
        public string Address { get; set; }


        public string FirstNameEnglish { get; set; }
        public string LastNameEnglish { get; set; }
        public bool IsMale { get; set; }
        public string NationalIdSeries { get; set; }
        public string IBan { get; set; }
        public string CardNumber { get; set; }

        public string Email { get; set; }
        public string ProvinceEnglish { get; set; }
        public string CityEnglish { get; set; }

        public string DisplayName => string.IsNullOrWhiteSpace(BusinessName) ? FullName : BusinessName;
        public string DisplayNationalCode => IsLegal && !string.IsNullOrWhiteSpace(BusinessNationalCode) ? BusinessNationalCode : NationalCode;
    };
}
