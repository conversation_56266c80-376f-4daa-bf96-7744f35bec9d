﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Mapping;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Consumer._20_InstallmentsList
{
    public record InstallmentsListRequest(Guid? orderTrackingCode, InstallmentPaymentFilterStatus? paymentStatus, InstallmentStatusFilterStatus? status);
    public enum InstallmentPaymentFilterStatus
    {
        [Description("پرداخت نشده")]
        NotPaid,
        [Description("پرداخت شده")]
        Paid
    }
    public enum InstallmentStatusFilterStatus
    {
        [Description("سررسید نشده")]
        Waiting = 1,
        [Description("معوق")]
        Delayed = 2,
        [Description("پرداخت شده")]
        PaidOff = 3
    }
    public class InstallmentsListResponse
    {
        [Required]
        public List<InstallmentResponse> Installments { get; set; }
    }

    public class InstallmentResponse
    {
        public Guid InstallmentCode { get; set; }
        public Guid OrderTrackingCode { get; set; }
        public string MerchantName { get; set; }
        public DateTimeOffset OrderDate { get; set; }
        public decimal DelayPenaltyAmount { get; set; }
        public decimal InstallmentFinalAmount { get; set; }
        public bool IsPaid { get; set; }
        public DateTimeOffset DueDate { get; set; }
        public DateTimeOffset? PaymentDate { get; set; }

        [Required]
        public InstallmentStatus InstallmentStatus { get; set; }

        public int Number { get; set; }

        public bool IsPayable { get; set; }
    }

    

    public class InstallmentsListEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.InstallmentsList,
                async (
                    [AsParameters] InstallmentsListRequest request,
                    IInstallmentsListRequestHandler handler,
                    IValidator<InstallmentsListRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("InstallmentsList")
            .WithApiVersionSet(builder.NewApiVersionSet("Installment").Build())
            .Produces<InstallmentsListResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Installment List")
            .WithDescription("Installment List")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class InstallmentsListRequestHandler : IInstallmentsListRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserService _userService;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public InstallmentsListRequestHandler(ApplicationDbContext dbContext, IUserService userService, IOptions<BNPLOptions> bnplOptions, IUserContext userContext)
        {
            _dbContext = dbContext;
            _userService = userService;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<InstallmentsListResponse> HandleAsync(InstallmentsListRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            // we want to show pay-able installments

            var payables = OrderStatusProvider.GetInstallmentDueJobAcceptable();

            var query = _dbContext.Installments.AsNoTracking()
                .Where(i => i.Order.ConsumerUserId == userId && payables.Contains(i.Order.Status));

            switch (request.paymentStatus)
            {
                case InstallmentPaymentFilterStatus.NotPaid:
                    query = query.Where(i => i.Status != InstallmentStatus.PaidOff && i.Status != InstallmentStatus.SalaryDeductionPaidOff);
                    break;
                case InstallmentPaymentFilterStatus.Paid:
                    query = query.Where(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff);
                    break;
                default:
                    break;
            }

            switch (request.status)
            {
                case InstallmentStatusFilterStatus.Waiting:
                    query = query.Where(i => i.Status == InstallmentStatus.Waiting);
                    break;
                case InstallmentStatusFilterStatus.Delayed:
                    query = query.Where(i => i.Status == InstallmentStatus.Delayed || i.Status == InstallmentStatus.SalaryDeductionWaiting || i.Status == InstallmentStatus.Defaulted);
                    break;
                case InstallmentStatusFilterStatus.PaidOff:
                    query = query.Where(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff);
                    break;
                default:
                    break;
            }


            if (request.orderTrackingCode.HasValue)
            {
                query = query.Where(i => i.Order.TrackingCode == request.orderTrackingCode.Value);
            }
            
            var installments = await query
                .OrderBy(i => i.DueDate)
                .Select(i => new InstallmentDto
                {
                    MerchantName = i.Order.MerchantName,
                    InstallmentCode = i.Code,
                    OrderTrackingCode = i.Order.TrackingCode,
                    OrderId = i.OrderId,
                    InstallmentId = i.Id,
                    OrderDate = i.Order.CreatedAt,
                    MerchantUserId = i.Order.MerchantUserId,
                    InstallmentAmount = i.FinalAmount,
                    DelayPenaltyAmount = i.DelayPenaltyAmount,
                    DueDate = i.DueDate,
                    InstallmentStatus = i.Status,
                    PaymentDate = i.PaymentDate,
                    IsPaid = ((i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff) &&
                              i.PaymentStatus == PaymentStatus.PaymentSucceeded &&
                              i.PaymentDate != null),
                    Number = i.Number,
                    
                })
                .ToListAsync(cancellationToken);


            var resp = new InstallmentsListResponse
            {
                Installments = new List<InstallmentResponse>()
            };

            bool firstNotPaidFound = false;
            foreach (var item in installments)
            {
                var inst = new InstallmentResponse()
                {
                    MerchantName = item.MerchantName,
                    OrderTrackingCode = item.OrderTrackingCode,
                    InstallmentCode = item.InstallmentCode,
                    DelayPenaltyAmount = item.DelayPenaltyAmount,
                    DueDate = item.DueDate,
                    InstallmentFinalAmount = item.InstallmentAmount,
                    InstallmentStatus = item.InstallmentStatus,
                    IsPaid = item.IsPaid,
                    OrderDate = item.OrderDate,
                    PaymentDate = item.PaymentDate,
                    Number = item.Number,
                };

                if (!firstNotPaidFound &&
                    item.InstallmentStatus != InstallmentStatus.PaidOff &&
                    item.InstallmentStatus != InstallmentStatus.SalaryDeductionPaidOff &&
                    item.InstallmentStatus != InstallmentStatus.SalaryDeductionWaiting)
                {
                    inst.IsPayable = true;
                    firstNotPaidFound = true;
                }

                resp.Installments.Add(inst);
            }

            return resp;
        }

    }
    public interface IInstallmentsListRequestHandler
    {
        ValueTask<InstallmentsListResponse> HandleAsync(InstallmentsListRequest request, CancellationToken cancellationToken);
    }

    public class InstallmentsListValidator : AbstractValidator<InstallmentsListRequest>
    {
        public InstallmentsListValidator()
        {
            //RuleFor(x => x.orderTrackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.order_id_greater_zero); 
        }
    }
}
