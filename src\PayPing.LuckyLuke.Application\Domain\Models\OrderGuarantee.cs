﻿using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class OrderGuarantee : BaseEntity<long>, IAuditableEntity
    {

        public long OrderId { get; set; }

        public int GuarantorId { get; set; }

        // json dependent on guarantor type
        public string Data { get; set; }

        // json dependent on guarantor type
        public string SettlementData { get; set; }

        public string GuaranteeRefId { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }

        public int? LastModifiedBy { get; set; }

        public Order Order { get; set; }
        public Guarantor Guarantor { get; set; }
    }


}
