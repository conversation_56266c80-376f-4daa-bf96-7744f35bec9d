﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Merchant.OrderList
{
    public record OrderListRequest(DateTime? fromDate, DateTime? toDate, string consumerName, string mobile, decimal? creditAmount, Guid? trackingCode, OrderStatusRequest? orderStatusRequest, int pageSize = 10, int pageNumber = 1);

    public enum OrderStatusRequest
    {
        [Description("در حال پرداخت")]
        Paying = 1,
        [Description("تسویه شده")]
        Completed = 2,
        [Description("معوق")]
        Delayed = 3,
        [Description("نکول شده")]
        Defaulted = 4,
        [Description("در انتظار لغو")]
        SemiCanceled = 5,
        [Description("لغو شده")]
        Canceled = 6,
        [Description("در انتظار کسر از حقوق")]
        SalaryDeductionWaiting = 7,
    }
    public class OrderListResponse
    {
        public OrderListResponse(long total, List<OrderInfo> orders)
        {
            this.total = total;
            this.orders = orders;
        }

        public long total { get; set; }

        [Required]
        public List<OrderInfo> orders { get; set; }
    }
    public class OrderInfo
    {
        public OrderInfo(Guid orderTrackingCode, DateTimeOffset createdDate, decimal creditAmount, string consumer, int totalInstallmentCount, int paidoffInstallmentCount, bool isCancelable, string mobile, MerchantOrderSteps orderStep, string refundRefId, bool refundBalanceEnough)
        {
            this.orderTrackingCode = orderTrackingCode;
            this.createdDate = createdDate;
            this.creditAmount = creditAmount;
            this.consumer = consumer;
            this.totalInstallmentCount = totalInstallmentCount;
            this.paidoffInstallmentCount = paidoffInstallmentCount;
            this.isCancelable = isCancelable;
            this.mobile = mobile;
            this.orderStep = orderStep;
            this.refundRefId = refundRefId;
            this.refundBalanceEnough = refundBalanceEnough;
        }

        public Guid orderTrackingCode { get; set; }
        public DateTimeOffset createdDate { get; set; }
        public decimal creditAmount { get; set; }
        public string consumer { get; set; }
        public string mobile { get; set; }
        public int totalInstallmentCount { get; set; }
        public int paidoffInstallmentCount { get; set; }
        public bool isCancelable { get; set; }
        public MerchantOrderSteps orderStep { get; set; }
        public string refundRefId { get; set; }
        public bool refundBalanceEnough { get; set; }

    }

    public class OrderListEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                MerchantRoutes.OrderList,
                async (
                    [AsParameters] OrderListRequest request,
                    IOrderListRequestHandler handler,
                    IValidator<OrderListRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("OrderList")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<OrderListResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Order List")
            .WithDescription("Order List")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OrderListRequestHandler : IOrderListRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly BNPLOptions _bnplOptions;

        public OrderListRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<OrderListResponse> HandleAsync(OrderListRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;
            var prepaidOrMerchCanceledOrdersStatus = OrderStatusProvider.GetPrePaidOrMerchantCanceled();

            var q = _dbContext.Orders.AsNoTracking().Where(o => o.MerchantUserId == userId);

            switch (request.orderStatusRequest)
            {
                case OrderStatusRequest.Paying:
                    q = q.Where(o => o.Status == OrderStatus.PrePaymentSucceeded || o.Status == OrderStatus.InstallmentsPaymentInProgress);
                    break;
                case OrderStatusRequest.Completed:
                    q = q.Where(o => o.Status == OrderStatus.PaidOff);
                    break;
                case OrderStatusRequest.Delayed:
                    q = q.Where(o => o.Status == OrderStatus.InstallmentsPaymentDelayed);
                    break;
                case OrderStatusRequest.Defaulted:
                    q = q.Where(o => o.Status == OrderStatus.InstallmentsPaymentDefaulted);
                    break;
                case OrderStatusRequest.SemiCanceled:
                    q = q.Where(o => o.Status == OrderStatus.SemiCanceledAfterPaymentByMerchant);
                    break;
                case OrderStatusRequest.Canceled:
                    q = q.Where(o => o.Status == OrderStatus.FullyCanceledAfterPaymentByMerchant || o.Status == OrderStatus.CanceledBeforePaymentByMerchant);
                    break;
                case OrderStatusRequest.SalaryDeductionWaiting:
                    q = q.Where(o => o.Status == OrderStatus.InstallmentsWaitingForSalaryDeduction);
                    break;
                default:
                    q = q.Where(o => prepaidOrMerchCanceledOrdersStatus.Contains(o.Status));
                    break;
            }

            if (request.fromDate.HasValue)
            {
                q = q.Where(o => o.CreatedAt >= request.fromDate);
            }

            if (request.toDate.HasValue)
            {
                q = q.Where(o => o.CreatedAt <= request.toDate);
            }

            if (!string.IsNullOrEmpty(request.consumerName))
            {
                q = q.Where(o => o.ConsumerUserId.HasValue && o.ConsumerName.Contains(request.consumerName));
            }

            if (!string.IsNullOrEmpty(request.mobile))
            {
                q = q.Where(o => o.ConsumerUserName == request.mobile);
            }

            if (request.creditAmount.HasValue)
            {
                q = q.Where(o => o.CreditedAmount == request.creditAmount);
            }

            if (request.trackingCode != null)
            {
                q = q.Where(o => o.TrackingCode == request.trackingCode);
            }

            var total = await q.LongCountAsync(cancellationToken);

            var beforePayCancelableStatuses = OrderStatusProvider.GetMerchantBeforePaymentCancelable();
            var afterPayCancelableStatuses = OrderStatusProvider.GetMerchantAfterPaymentCancelable();

            //TODO: check n sql may happen !
            var orders = await q
            .OrderByDescending(o => o.CreatedAt)
            .Skip((request.pageNumber - 1) * request.pageSize)
            .Take(request.pageSize)
            .Select(o => new OrderInfo(
                 o.TrackingCode,
                 o.CreatedAt,
                 o.CreditedAmount,
                 o.ConsumerName,
                 o.Installments.Where(x => true).Count(),
                 o.Installments.Count(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff),
                 beforePayCancelableStatuses.Contains(o.Status) || afterPayCancelableStatuses.Contains(o.Status),
                 o.ConsumerUserName,
                 o.Status.ToMerchantOrderStep(),
                 o.OrderCancellation.RefundRefId,
                 o.OrderCancellation.IsRefundBalanceEnough))
             .ToListAsync(cancellationToken);

            foreach ( var order in orders)
            {
                if (order.orderStep == MerchantOrderSteps.Unknown)
                {
                    if (order.refundRefId.HasValue())
                    {
                        if (order.refundBalanceEnough)
                        {
                            order.orderStep = MerchantOrderSteps.SemiCanceledRefundInProgress;
                        }
                        else
                        {
                            order.orderStep = MerchantOrderSteps.SemiCanceledRefundBalanceDeficit;
                        }
                    }
                    else
                    {
                        order.orderStep = MerchantOrderSteps.SemiCanceledRefundInactive;
                    }
                }
            }

            return new OrderListResponse(total, orders);
        }
    }

    public interface IOrderListRequestHandler
    {
        ValueTask<OrderListResponse> HandleAsync(OrderListRequest request, CancellationToken cancellationToken);
    }

    public class OrderListValidator : AbstractValidator<OrderListRequest>
    {
        public OrderListValidator()
        {
            RuleFor(x => x.consumerName).MinimumLength(3).When(x => !string.IsNullOrWhiteSpace(x.consumerName));
            RuleFor(x => x.pageSize).GreaterThan(0);
            RuleFor(x => x.pageNumber).GreaterThan(0);
        }
    }
}
