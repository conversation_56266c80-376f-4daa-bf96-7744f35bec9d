﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class MerchantInfoConfiguration : IEntityTypeConfiguration<MerchantInfo>
{
    public void Configure(EntityTypeBuilder<MerchantInfo> builder)
    {
        builder.HasIndex(x => x.MerchantUserId).IsUnique().HasDatabaseName("IX_MerchantInfo_MerchantUserId");

        builder.Property(nameof(MerchantInfo.PredefinedCode)).HasMaxLength(128);
        builder.Property(nameof(MerchantInfo.SignatureImageFileId)).HasMaxLength(128);
        builder.Property(nameof(MerchantInfo.SignatureImageFileName)).HasMaxLength(128);

        builder.ComplexProperty(e => e.MerchantInfoAgent).Property(a => a.FirstNameEnglish).HasMaxLength(128);
        builder.ComplexProperty(e => e.MerchantInfoAgent).Property(a => a.LastNameEnglish).HasMaxLength(128);
        builder.ComplexProperty(e => e.MerchantInfoAgent).Property(a => a.FirstName).HasMaxLength(128);
        builder.ComplexProperty(e => e.MerchantInfoAgent).Property(a => a.LastName).HasMaxLength(128);
        builder.ComplexProperty(e => e.MerchantInfoAgent).Property(a => a.MobileNumber).HasMaxLength(64);
        builder.ComplexProperty(e => e.MerchantInfoAgent).Property(a => a.FatherName).HasMaxLength(128);
        builder.ComplexProperty(e => e.MerchantInfoAgent).Property(a => a.NationalCode).HasMaxLength(64);
        builder.ComplexProperty(e => e.MerchantInfoAgent).Property(a => a.NationalIdSeries).HasMaxLength(128);
        builder.ComplexProperty(e => e.MerchantInfoAgent).Property(a => a.PersianBirthDate).HasMaxLength(64);

        builder.Property(nameof(MerchantInfo.PostalCode)).HasMaxLength(10);
        builder.Property(nameof(MerchantInfo.Address)).HasMaxLength(2048);
        builder.Property(nameof(MerchantInfo.Email)).HasMaxLength(128);
        builder.Property(nameof(MerchantInfo.ProvinceEnglish)).HasMaxLength(64);
        builder.Property(nameof(MerchantInfo.CityEnglish)).HasMaxLength(64);

    }
}
