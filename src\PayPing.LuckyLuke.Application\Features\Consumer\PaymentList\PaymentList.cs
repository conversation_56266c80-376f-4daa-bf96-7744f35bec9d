﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Consumer.PaymentList
{
    public record PaymentListRequest(DateTime? FromDate, DateTime? ToDate, int PageSize = 10, int PageNumber = 1);
    public class PaymentListResponse
    {
        public PaymentListResponse(List<PaymentInfo> paymentInfos, long total)
        {
            PaymentInfos = paymentInfos;
            this.total = total;
        }

        public long total { get; set; }

        [Required]
        public List<PaymentInfo> PaymentInfos { get; set; }
    }

    public class PaymentInfo
    {
        public PaymentInfo(DateTimeOffset createDate, DateTimeOffset? paymentDate, Guid trackingCode, OrderPaymentStatus paymentStatus, decimal paymentAmount, string paymentCode)
        {
            CreateDate = createDate;
            PaymentDate = paymentDate;
            TrackingCode = trackingCode;
            PaymentStatus = paymentStatus;
            PaymentAmount = paymentAmount;
            PaymentCode = paymentCode;
        }

        public DateTimeOffset CreateDate { get; set; }
        public DateTimeOffset? PaymentDate { get; set; }
        public Guid TrackingCode { get; set; }
        public OrderPaymentStatus PaymentStatus { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentCode { get; set; }
    }

    public class PaymentListEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.PaymentList,
                async (
                    [AsParameters] PaymentListRequest request,
                    IPaymentListRequestHandler handler,
                    IValidator<PaymentListRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var result = await handler.HandleAsync(request, cancellationToken);
                    return Results.Ok(result);
                })
                .RequireAuthorization("read")
                .WithName("Dashboard PaymentList")
                .WithApiVersionSet(builder.NewApiVersionSet("Dashboard").Build())
                .Produces<PaymentListResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Dashboard Payment List")
                .WithDescription("Dashboard Payment List")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class PaymentListRequestHandler : IPaymentListRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;

        public PaymentListRequestHandler(ApplicationDbContext dbContext, IUserContext userContext)
        {
            _dbContext = dbContext;
            _userContext = userContext;
        }

        public async ValueTask<PaymentListResponse> HandleAsync(PaymentListRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var query = _dbContext.OrderPayments
                .AsNoTracking()
                .Where(p => p.Order.ConsumerUserId == userId);


            if (request.FromDate.HasValue)
            {
                query = query.Where(o => o.CreatedAt >= request.FromDate);
            }
            if (request.ToDate.HasValue)
            {
                query = query.Where(o => o.CreatedAt <= request.ToDate);
            }

            var total = await query.LongCountAsync();

            var paymentInfos = await query
                .OrderByDescending(o => o.CreatedAt)
                .Select(p => new PaymentInfo(
                    p.CreatedAt,
                    p.PayDate,
                    p.Order.TrackingCode,
                    p.PaymentStatus,
                    p.Amount,
                    p.PaymentCode))
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            return new PaymentListResponse(paymentInfos, total);
        }
    }

    public interface IPaymentListRequestHandler
    {
        ValueTask<PaymentListResponse> HandleAsync(PaymentListRequest request, CancellationToken cancellationToken);
    }

    public class PaymentListValidator : AbstractValidator<PaymentListRequest>
    {
        public PaymentListValidator()
        {

        }
    }
}
