﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public interface IPaymentGrpcClient
    {
        ValueTask<PaymentGrpcResponse> CreatePaymentAsync(PaymentGrpcRequest request);
        ValueTask<PaymentVerifyGrpcResponse> VerifyPaymentAsync(PaymentVerifyGrpcRequest request);
    }
}
