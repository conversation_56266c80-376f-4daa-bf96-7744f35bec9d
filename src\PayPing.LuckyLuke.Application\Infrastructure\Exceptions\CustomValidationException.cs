﻿using Elastic.Apm.Api;
using FluentValidation.Results;
using System.Net;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Infrastructure.Exceptions;


public class CustomValidationException : BaseException
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="eventId">
    ///     just provide order.trackingcode in consumer flows to add exception to order timeline eventlog 
    /// </param>
    /// <param name="validationResultModel"></param>
    public CustomValidationException(string eventId, ValidationResultModel validationResultModel) : base(eventId, validationResultModel.Message)
    {
        ValidationResultModel = validationResultModel;
    }

    public CustomValidationException(string eventId, string message, string field) : base(eventId, message)
    {
        ValidationResultModel = new ValidationResultModel(field, message);
    }

    public ValidationResultModel ValidationResultModel { get; }
}


public class ValidationResultModel
{
    public ValidationResultModel(ValidationResult validationResult = null) =>
        Errors = validationResult?.Errors
            .Select(error => new ValidationError(error.PropertyName, error.ErrorMessage, error.ErrorCode.Substring(0, error.ErrorCode.Length - 4)))
            .ToList();


    public ValidationResultModel(IList<ValidationError> validationErrors) =>
        Errors = validationErrors;

    public ValidationResultModel(ValidationError validationError) : this(new List<ValidationError> { validationError }) { }

    public ValidationResultModel(string field, string message) : this(new ValidationError(field, message)) { }

    public int StatusCode { get; set; } = (int)HttpStatusCode.BadRequest;
    public string Message { get; set; } = "Validation Failed.";

    public IList<ValidationError> Errors { get; }

    public override string ToString()
    {
        return JsonSerializer.Serialize(this);
    }
}

public class ValidationError
{
    public ValidationError(string field, string message, string code = "validation.error")
    {
        Field = !string.IsNullOrEmpty(field) ? field : null;
        Message = message;
        Code = code;
    }

    public string Field { get; }
    public string Code { get; }
    public string Message { get; }
}