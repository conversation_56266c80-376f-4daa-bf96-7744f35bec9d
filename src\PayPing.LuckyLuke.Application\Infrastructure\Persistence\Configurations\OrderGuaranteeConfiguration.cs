﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;
using System.Reflection.Emit;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class OrderGuaranteeConfiguration : IEntityTypeConfiguration<OrderGuarantee>
{
    public void Configure(EntityTypeBuilder<OrderGuarantee> builder)
    {
        builder.Property(nameof(OrderGuarantee.GuaranteeRefId)).HasMaxLength(128);

        builder.HasIndex(x => x.GuaranteeRefId).HasDatabaseName("IX_OrderGuarantee_GuaranteeRefId");

        builder
            .Property(b => b.Data)
            .HasColumnType("jsonb");

        builder
            .Property(b => b.SettlementData)
            .HasColumnType("jsonb");
    }
}
