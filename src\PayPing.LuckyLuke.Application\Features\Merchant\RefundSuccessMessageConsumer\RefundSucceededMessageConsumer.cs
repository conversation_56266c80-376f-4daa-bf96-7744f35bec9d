﻿using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Factory;
using PayPing.Messaging.Contracts.IntegrationEvents.Refund.RefundOperation;

namespace PayPing.LuckyLuke.Application.Features.Merchant.RefundSuccessMessageConsumer
{
    public class RefundSucceededMessageConsumer : IConsumer<RefundOperationSucceeded>
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IMerchantCancelFactory _merchantCancelFactory;
        private readonly ILogger<RefundSucceededMessageConsumer> _logger;

        public RefundSucceededMessageConsumer(ApplicationDbContext dbContext, IMerchantCancelFactory merchantCancelFactory, ILogger<RefundSucceededMessageConsumer> logger)
        {
            _dbContext = dbContext;
            _merchantCancelFactory = merchantCancelFactory;
            _logger = logger;
        }

        public async Task Consume(ConsumeContext<RefundOperationSucceeded> context)
        {
            _logger.LogInformation("Received RefundOperationSucceeded message: RefundOperationCode={RefundOperationCode}, SuccessDateTime={SuccessDateTime}",
                context.Message.RefundOperationCode,
                context.Message.SuccessDateTime);

            var order = await _dbContext.Orders
                .Where(x => 
                    x.Status == Domain.Enums.OrderStatus.SemiCanceledAfterPaymentByMerchant &&
                    x.OrderCancellation.RefundRefId == context.Message.RefundOperationCode.ToString())
                .FirstOrDefaultAsync();

            if (order != null) 
            {
                var canceler = _merchantCancelFactory.Create(order.Status);
                await canceler.PostCancelAsync(order, CancellationToken.None);
            }
            else
            {
                _logger.LogInformation("Not found Related Order on RefundOperationSucceeded message: RefundOperationCode={RefundOperationCode}, SuccessDateTime={SuccessDateTime}",
                context.Message.RefundOperationCode,
                context.Message.SuccessDateTime);
            }
        }
    }
}
