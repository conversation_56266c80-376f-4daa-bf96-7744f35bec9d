﻿using DNTPersianUtils.Core;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kuknos;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kuknos;
using PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kuknos;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Infrastructure.Provider
{
    public class KuknosPromissoryProvider : BasePromissoryProvider, IPromissoryGuaranteeProvider
    {
        private readonly IKuknosPromissorySettlementService _kuknosPromissorySettlementService;
        private readonly IWebHostEnvironment _environment;
        private readonly IUserService _userService;
        private readonly ApplicationDbContext _dbContext;
        private readonly IKuknosPromissoryService _kuknosPromissoryService;
        private readonly IKuknosApiHttpClient _kuknosApi;

        public KuknosPromissoryProvider(IKuknosPromissorySettlementService kuknosPromissorySettlementService, IWebHostEnvironment environment, IUserService userService, ApplicationDbContext dbContext, IKuknosPromissoryService kuknosPromissoryService, IKuknosApiHttpClient kuknosApi)
        {
            _kuknosPromissorySettlementService = kuknosPromissorySettlementService;
            _environment = environment;
            _userService = userService;
            _dbContext = dbContext;
            _kuknosPromissoryService = kuknosPromissoryService;
            _kuknosApi = kuknosApi;
        }

        public GuaranteeProvider GuaranteeProvider => GuaranteeProvider.Kuknos;

        public GuaranteeType GuaranteeType => GuaranteeType.Promissory;

        public async ValueTask<(bool isValid, string error, long? guaranteeId)> ValidateIssuanceAsync(Order order)
        {
            switch (order.Status)
            {
                case OrderStatus.ProfileFilled:
                    {
                        var ds = await _dbContext.DigitalSigns.AsNoTracking()
                            .Where(x => x.UserId == order.ConsumerUserId.Value && x.Type == DigitalSignType.KuknosVersion1 && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                            .AnyAsync();

                        if (ds)
                        {
                            return (true, null, null);
                        }
                        else
                        {
                            return (false, "امضا دیجیتال یافت نشد", null);
                        }
                    }
                case OrderStatus.DigitalSignatureCreated:
                case OrderStatus.GuaranteeFailed:
                    return (true, null, null);
                case OrderStatus.GuaranteeInProgress:
                    {
                        var guarantee = order.OrderGuarantees?.OrderByDescending(x => x.CreatedAt).FirstOrDefault();
                        if (guarantee != null)
                        {
                            var context = JsonSerializer.Deserialize<KuknosPromissoryContextV1>(guarantee.Data);
                            if (context.Status == KuknosPromissoryStatusV1.IssuePromissory || context.Status == KuknosPromissoryStatusV1.PostIssuePromissory)
                            {
                                return (true, null, guarantee.Id);
                            }
                            else
                            {
                                return (false, "اطلاعات پروفایل تکمیل نشده یا سفته در جریان است", null);
                            }
                        }
                        else
                        {
                            return (true, null, null);
                        }
                    }
                default:
                    return (false, "اطلاعات پروفایل تکمیل نشده یا سفته در جریان است", null);
            }
        }

        public async ValueTask<int> CurrentGuarantorIdAsync(CancellationToken cancellationToken)
        {
            return await _dbContext.Guarantors.Where(x => x.GuaranteeType == GuaranteeType.Promissory && x.GuaranteeProvider == GuaranteeProvider.Kuknos).Select(x => x.Id).FirstOrDefaultAsync(cancellationToken);
        }

        public async ValueTask<OrderGuaranteeDto> CreateOrderGuaranteeAsync(
            long id,
            long orderId,
            Guid orderTrackingCode,
            int guarantorId,
            int consumerId,
            int recepientId,
            int promissoryAmount,
            DateTimeOffset dueDate,
            string predefinedRecipientName,
            CancellationToken cancellationToken)
        {
            
            var recipientInfo = await _userService.GetMerchantExtraInfoAsync(recepientId, cancellationToken);

            if (_environment.IsDevelopment() || predefinedRecipientName == "PAYPING")
            {
                recipientInfo = new UserInfoDto()
                {
                    FullName = "مانا تدبیر آواتک",
                    Address = "تهران، خیابان شریعتی، منظر نژاد، نبش کوچه جوادی، پلاک ۴",
                    PhoneNumber = "***********",
                    IsLegal = true,
                    BusinessNationalCode = "***********",
                    NationalCode = "***********",
                    PostalCode = "1948847413",
                };

                // keep for develop
                predefinedRecipientName = "PAYPING";
            }
            else
            {
                var merchantInfo = await _dbContext.MerchantInfos.Where(p => p.MerchantUserId == recepientId).FirstOrDefaultAsync();
                if (merchantInfo != null)
                {
                    recipientInfo.Address = merchantInfo.Address;
                    recipientInfo.PhoneNumber = recipientInfo.PhoneNumber ?? merchantInfo.MerchantInfoAgent.MobileNumber;
                    recipientInfo.PostalCode = merchantInfo.PostalCode;
                }
            }

            var consumerInfo = await _userService.GetConsumerInfoAsync(consumerId, cancellationToken);

            var context = new KuknosPromissoryContextV1
            {
                OrderId = orderId,
                OrderTrackingCode = orderTrackingCode,
                OrderGuaranteeId = id,
                Amount = promissoryAmount.ToRial(),
                DueDate = dueDate.ToShortPersianDateString(),
                Description = "جهت ضمانت خرید اقساطی",
                IbanWithIR = consumerInfo.IBan?.Trim(),
                Recipient = new KuknosPromissoryContextRecipientV1
                {
                    FullName = recipientInfo.FullName?.Trim(),
                    Address = recipientInfo.Address?.Trim(),
                    MobileNumber = recipientInfo.PhoneNumber?.Trim(),
                    NationalCode = recipientInfo.DisplayNationalCode?.Trim(),
                    PostalCode = recipientInfo.PostalCode?.Trim(),
                    IsLegal = recipientInfo.IsLegal
                },

                PredefinedRecipientName = predefinedRecipientName?.Trim(),
                RecipientUserId = recepientId,
                ConsumerUserId = consumerId,
                IsLegal = consumerInfo.IsLegal,
                PromissoryNotePaymentStatus = PromissoryNotePaymentStatus.Unknown,
                Status = KuknosPromissoryStatusV1.IssuePromissory,
            };

            return new OrderGuaranteeDto(orderId, guarantorId, JsonSerializer.Serialize(context));
        }

        public string GetPromissoryRefId(string data)
        {
            var context = JsonSerializer.Deserialize<KuknosPromissoryContextV1>(data);
            return context.PromissoryId;
        }

        public bool IsPaymentSuccessful(string status)
        {
            return status == "0";
        }

        public string SetPaymentStatusToGuaranteeData(string data, bool success)
        {
            var context = JsonSerializer.Deserialize<KuknosPromissoryContextV1>(data);
            context.PromissoryNotePaymentStatus = success ? PromissoryNotePaymentStatus.Paid : PromissoryNotePaymentStatus.Failed;

            if (!success)
            {
                // make repay possible
                context.Status = KuknosPromissoryStatusV1.Pay;
            }

            return JsonSerializer.Serialize(context);
        }

        public async ValueTask<PromissoryCheckPointResponse> PushToNextCheckPointAsync(string contextData, CancellationToken cancellationToken)
        {
            var context = JsonSerializer.Deserialize<KuknosPromissoryContextV1>(contextData);

            var resp = await _kuknosPromissoryService.HandleAsync(context, cancellationToken);

            var result = resp switch
            {
                PayResult payResult => new PromissoryCheckPointResponse(true, false, string.Empty, OrderSteps.NoNext, payResult.redirectUrl),
                DoneResult doneResult => new PromissoryCheckPointResponse(true, true, string.Empty, OrderSteps.NoNext, doneResult.finalJson),
                _ => throw new NotImplementedException("at PushToNextCheckPointAsync, unknown handle result")
            };

            return result;
        }

        public (bool setNext, OrderSteps? next, List<(string name, OrderSteps code)> steps) GetSteps(string context)
        {
            OrderSteps? next = null;
            bool setNext = false;

            if (!string.IsNullOrWhiteSpace(context))
            {
                var kuknosContext = JsonSerializer.Deserialize<KuknosPromissoryContextV1>(context);
                switch (kuknosContext.Status)
                {
                    case KuknosPromissoryStatusV1.IssuePromissory:
                    case KuknosPromissoryStatusV1.PostIssuePromissory:
                        
                    case KuknosPromissoryStatusV1.SignDocumentWithSignerPdf:
                    case KuknosPromissoryStatusV1.Signed:

                    case KuknosPromissoryStatusV1.Pay:
                    case KuknosPromissoryStatusV1.Finalize:
                        next = base.ZeroPitStop.code;
                        setNext = true;
                        break;
                    case KuknosPromissoryStatusV1.Done:
                    default:
                        break;
                }
            }

            return (setNext, next, new List<(string name, OrderSteps code)>()
            {
                base.ZeroPitStop,
                //base.SecondPitStop,
                //base.ThirdPitStop,
            });
        }

        public async ValueTask<(bool success, string error)> DeleteGuaranteeAsync(string contextData)
        {
            if (string.IsNullOrWhiteSpace(contextData))
            {
                return (true, null);
            }

            var context = JsonSerializer.Deserialize<KuknosPromissoryContextV1>(contextData);
            if (string.IsNullOrWhiteSpace(context.PromissoryId))
            {
                return (true, null);
            }
            else if (context.Status == KuknosPromissoryStatusV1.Done)
            {
                return (false, "kuknos promissory can not revoke after finalization");
            }
            else
            {
                var deleteResult = await _kuknosApi.DeleteAsync(context.PromissoryId, context.OrderTrackingCode);
                return (deleteResult.data, deleteResult.meta?.description);
            }
        }

        public string GetFinalGuaranteeDocumentId(string contextData)
        {
            var context = JsonSerializer.Deserialize<KuknosPromissoryContextV1>(contextData);
            return context.PromissoryFinalDocumentFileId;
        }

        public (bool hasPayment, int amountToman) GetPromissoryPayment(decimal orderGuaranteeAmount)
        {
            return (true, (int)decimal.Multiply(0.0005m, orderGuaranteeAmount));
        }

        public string RenewSettlementContextIfNeeded(OrderGuarantee orderGuarantee)
        {
            if (!orderGuarantee.SettlementData.HasValue())
            {
                var pc = JsonSerializer.Deserialize<KiahooshanPromissoryContextV1>(orderGuarantee.Data);

                return JsonSerializer.Serialize(new KuknosPromissorySettlementContextV1() 
                {
                    ConsumerUserId = pc.ConsumerUserId,
                    IssueDate = DateTime.UtcNow,
                    OrderGuaranteeId = orderGuarantee.Id,
                    OrderId = orderGuarantee.OrderId,
                    OrderTrackingCode = pc.OrderTrackingCode,
                    PromissoryId = orderGuarantee.GuaranteeRefId,
                    RecipientUserId = pc.RecipientUserId,
                    Status = KuknosPromissorySettlementStatusV1.IssueSettlement
                });
            }

            var context = JsonSerializer.Deserialize<KuknosPromissorySettlementContextV1>(orderGuarantee.SettlementData);

            var today = DateTimeOffset.UtcNow.ConvertToPersianDay();

            var issueDay = context.IssueDate.ConvertToPersianDay();

            if(!today.Equals(issueDay))
            {
                return JsonSerializer.Serialize(new KuknosPromissorySettlementContextV1()
                {
                    ConsumerUserId = context.ConsumerUserId,
                    IssueDate = DateTime.UtcNow,
                    OrderGuaranteeId = context.OrderGuaranteeId,
                    OrderId = context.OrderId,
                    OrderTrackingCode = context.OrderTrackingCode,
                    PromissoryId = context.PromissoryId,
                    RecipientUserId = context.RecipientUserId,
                    Status = KuknosPromissorySettlementStatusV1.IssueSettlement
                });
            }

            return orderGuarantee.SettlementData;
        }

        public async ValueTask<(bool success, string error, string data)> SettleGuaranteeAsync(string contextData, CancellationToken cancellationToken)
        {
            var context = JsonSerializer.Deserialize<KuknosPromissorySettlementContextV1>(contextData);

            var resp = await _kuknosPromissorySettlementService.HandleAsync(context, cancellationToken);

            (bool success, string error, string data) result = resp switch
            {
                DoneResult doneResult => new (true, string.Empty, doneResult.finalJson),
                _ => throw new NotImplementedException("at SettleGuaranteeAsync, unknown handle result")
            };

            return result;
        }

        public string GetFinalSettlementGuaranteeDocumentId(string contextData)
        {
            var context = JsonSerializer.Deserialize<KuknosPromissorySettlementContextV1>(contextData);
            return context.SettlementFinalDocumentFileId;
        }
    }
}
