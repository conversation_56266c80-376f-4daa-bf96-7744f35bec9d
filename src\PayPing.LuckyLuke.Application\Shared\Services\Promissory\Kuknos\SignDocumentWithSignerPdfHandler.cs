﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kuknos;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kuknos
{
    public class SignDocumentWithSignerPdfHandler : KuknosBaseHandler, IKuknosPromissoryService
    {
        private readonly IKuknosPromissoryService _next;

        public SignDocumentWithSignerPdfHandler(
            IKuknosPromissoryService next,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _next = next;
        }

        public async ValueTask<object> HandleAsync(KuknosPromissoryContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosPromissoryStatusV1.SignDocumentWithSignerPdf)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var rawDoc = await _kuknosApi.DownloadRawDocumentAsync(context.PromissoryRawDocumentHash, context.OrderTrackingCode);

            if (rawDoc == null || rawDoc.contentBytes == null || rawDoc.contentBytes.Length == 0)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not download raw promissory document", false);
            }

            var dsc = await GetValidDigitalSignAsync(context.ConsumerUserId);

            Guard.Against.NullOrEmpty(dsc.Certificate);

            var result = await _kuknosApi.SignDocumentWithSignerPdfAsync(dsc.Certificate, dsc.PrivateKey, rawDoc.contentBytes, context.OrderTrackingCode, cancellationToken);

            if (result == null || result.contentBytes == null || result.contentBytes.Length == 0)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not download raw promissory document", false);
            }

            // upload file to kuknos ipfs
            var ipfsHash = await _kuknosApi.UploadAsync(result.contentBytes, $"signed-safte-{context.OrderGuaranteeId}.pdf", context.OrderTrackingCode, cancellationToken);

            if (string.IsNullOrWhiteSpace(ipfsHash.data))
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not upload signed promissory to kuknos ipfs service", false);
            }

            
            context.Status = KuknosPromissoryStatusV1.Signed;
            context.PromissorySignedDocumentHash = ipfsHash.data;


            //// save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

        protected override void ValidateContext(KuknosPromissoryContextV1 context)
        {
            base.ValidateContext(context);
        }
    }
}
