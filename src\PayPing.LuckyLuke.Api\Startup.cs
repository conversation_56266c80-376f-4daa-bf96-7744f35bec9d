﻿using PayPing.BNPL.Application.Infrastructure.Configurations;

namespace PayPing.LuckyLuke.Api
{
    public class Startup
    {
        private IWebHostEnvironment WebHostEnvironment { get; }
        private IConfiguration Configuration { get; }

        public Startup(
            IWebHostEnvironment webHostEnvironment,
            IConfiguration configuration)
        {
            WebHostEnvironment = webHostEnvironment;
            Configuration = configuration;
        }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddConsumerApplication(Configuration, WebHostEnvironment);
            services.AddInfrastructure(Configuration, WebHostEnvironment);

        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseConsumerApplication(env);
        }
    }
}