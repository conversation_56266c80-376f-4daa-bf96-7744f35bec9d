﻿using Ardalis.GuardClauses;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kuknos;
using PayPing.LuckyLuke.Application.Shared.Services.User;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;

namespace PayPing.LuckyLuke.Application.Shared.Services.DigitalSign.Kuknos
{
    public class CreateCertificateHandler : KuknosBaseDigitalSignHandler, IKuknosDigitalSignService
    {
        private readonly IKuknosDigitalSignService _next;
        private readonly IS3ServiceApiClient _s3ServiceApiClient;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly IUserService _userService;

        public CreateCertificateHandler(
            IKuknosDigitalSignService next,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IUserService userService,
            IKuknosApiHttpClient kuknosApi,
            IS3ServiceApiClient s3ServiceApiClient,
            IUploadGrpcClient uploadGrpcClient,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _next = next;
            _s3ServiceApiClient = s3ServiceApiClient;
            _uploadGrpcClient = uploadGrpcClient;
            _userService = userService;
        }

        public async ValueTask<object> HandleAsync(KuknosDigitalSignContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosDigitalSignStatusV1.CreateCertificate)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var csr = GenerateCsr(
                context.PublicKey,
                context.PrivateKey,
                $"{context.FullNameFa} {context.UserMobileSignature}",
                "IR",
                context.Province,
                context.City,
                context.NationalCode,
                context.Email,
                context.FirstNameEn,
                context.LastNameEn,
                string.Concat("+98", context.Mobile.TrimStart('0')));

            // download sig img from storage

            var userIsMerch = await _userService.IsUserMerchantAsync(context.UserId, cancellationToken);

            string signatureImageFileId = string.Empty;
            string signatureImageFileName = string.Empty;

            if (userIsMerch.isMerch)
            {
                var userInfo = await _dbContext.MerchantInfos.AsNoTracking().Where(x => x.MerchantUserId == context.UserId).FirstOrDefaultAsync();
                signatureImageFileId = userInfo.SignatureImageFileId;
                signatureImageFileName = userInfo.SignatureImageFileName;
            }
            else
            {
               var userInfo = await _dbContext.ConsumerInfos.AsNoTracking().Where(x => x.ConsumerUserId == context.UserId).FirstOrDefaultAsync();
                signatureImageFileId = userInfo.SignatureImageFileId;
                signatureImageFileName = userInfo.SignatureImageFileName;
            }


            var presignedUrl = await _uploadGrpcClient.GetPresignedUrlAsync(signatureImageFileId, context.UserId, FileManager.Grpc.UploadTypeEnum.Misc, cancellationToken);

            using Stream sigImage = await _s3ServiceApiClient.DownloadAsync(presignedUrl.SuccessResult, cancellationToken);

            var result = await _kuknosApi.CreateCertificateAsync(
                context.PublicKey, context.BirthDate, csr,
                context.NationalCode, context.NationalCardSeries, context.PostalCode, context.FirstNameEn, context.LastNameEn,
                context.FirstNameFa, context.LastNameFa, context.FatherNameFa, context.IsMale, sigImage, signatureImageFileName, context.OrderTrackingCode,
                cancellationToken);

            Guard.Against.Null(result.data);

            context.CertificateTrackingCode = result.data.tracking_code;
            context.Certificate = result.data.certificate;
            context.Status = KuknosDigitalSignStatusV1.GetCertificate;
            context.CertificateSigningRequest = csr;

            // save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

        protected override void ValidateContext(KuknosDigitalSignContextV1 context)
        {
            base.ValidateContext(context);
            Guard.Against.NullOrEmpty(context.PublicKey);
            Guard.Against.NullOrEmpty(context.PrivateKey);
            Guard.Against.NullOrEmpty(context.UserMobileSignature);

        }

        private string GenerateCsr(
        string publicKey,
        string privateKey,
        string commonName,
        string country,
        string province,
        string locality,
        string nationalcode,
        string email,
        string firstname,
        string lastname,
        string mobile,
        string org = "Unaffiliated",
        string orgUnit = "Unaffiliated")
        {
            using var rsa = RSA.Create(2048);
            //rsa.ImportPublicKey(RSAKeyType.Pkcs1, publicKey, false); no need
            rsa.ImportPrivateKey(RSAKeyType.Pkcs1, privateKey, false);

            X500DistinguishedNameBuilder dn = new X500DistinguishedNameBuilder();
            dn.AddCommonName(commonName);
            dn.AddCountryOrRegion(country);
            dn.AddLocalityName(locality);
            dn.AddStateOrProvinceName(province);
            dn.AddEmailAddress(email);
            dn.AddOrganizationName(org);
            dn.AddOrganizationalUnitName(orgUnit);
            dn.Add(Oid.FromFriendlyName("SERIALNUMBER", OidGroup.Attribute), nationalcode);
            dn.Add(Oid.FromFriendlyName("GN", OidGroup.Attribute), firstname);
            dn.Add(Oid.FromFriendlyName("SN", OidGroup.Attribute), lastname);
            dn.Add(Oid.FromOidValue("********", OidGroup.Attribute), mobile);

            var dnb = dn.Build();

            var req = new CertificateRequest(
                dnb.Name,
                rsa,
                HashAlgorithmName.SHA256,
                RSASignaturePadding.Pkcs1);

            var signed = req.CreateSigningRequest();

            return Convert.ToBase64String(signed);
        }
    }
}
