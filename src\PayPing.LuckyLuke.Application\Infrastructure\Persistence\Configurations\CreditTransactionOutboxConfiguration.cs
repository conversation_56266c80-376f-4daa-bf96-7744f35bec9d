﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class CreditTransactionOutboxConfiguration : IEntityTypeConfiguration<CreditTransactionOutbox>
{
    public void Configure(EntityTypeBuilder<CreditTransactionOutbox> builder)
    {
        builder.HasIndex(x => x.WalletId).HasDatabaseName("IX_CreditTransactionOutbox_WalletId");
    }
}