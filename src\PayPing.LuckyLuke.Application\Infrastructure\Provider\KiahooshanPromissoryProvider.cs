﻿using DNTPersianUtils.Core;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kuknos;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Infrastructure.Provider
{
    public class KiahooshanPromissoryProvider : BasePromissoryProvider, IPromissoryGuaranteeProvider
    {
        private readonly IKiahooshanPromissorySettlementService _kiahooshanPromissorySettlementService;
        private readonly IWebHostEnvironment _environment;
        private readonly IUserService _userService;
        private readonly ApplicationDbContext _dbContext;
        private readonly IKiahooshanPromissoryService _kihooPromissoryService;
        private readonly IKiahooshanApiHttpClient _kihooApi;

        public KiahooshanPromissoryProvider(IKiahooshanPromissorySettlementService kiahooshanPromissorySettlementService, IWebHostEnvironment environment, IUserService userService, ApplicationDbContext dbContext, IKiahooshanPromissoryService kihooPromissoryService, IKiahooshanApiHttpClient kihooApi)
        {
            _kiahooshanPromissorySettlementService = kiahooshanPromissorySettlementService;
            _environment = environment;
            _userService = userService;
            _dbContext = dbContext;
            _kihooPromissoryService = kihooPromissoryService;
            _kihooApi = kihooApi;
        }

        public GuaranteeProvider GuaranteeProvider => GuaranteeProvider.Kiahooshan;

        public GuaranteeType GuaranteeType => GuaranteeType.Promissory;

        public async ValueTask<(bool isValid, string error, long? guaranteeId)> ValidateIssuanceAsync(Order order)
        {
            switch (order.Status)
            {
                case OrderStatus.ProfileFilled:
                    {
                        var ds = await _dbContext.DigitalSigns.AsNoTracking()
                            .Where(x => x.UserId == order.ConsumerUserId.Value && x.Type == DigitalSignType.KiahooshanVersion1 && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                            .AnyAsync();

                        if (ds)
                        {
                            return (true, null, null);
                        }
                        else
                        {
                            return (false, "امضا دیجیتال یافت نشد", null);
                        }
                    }
                case OrderStatus.DigitalSignatureCreated:
                case OrderStatus.GuaranteeFailed:
                    return (true, null, null);
                case OrderStatus.GuaranteeInProgress:
                    {
                        var guarantee = order.OrderGuarantees?.OrderByDescending(x => x.CreatedAt).FirstOrDefault();
                        if (guarantee != null)
                        {
                            var context = JsonSerializer.Deserialize<KiahooshanPromissoryContextV1>(guarantee.Data);
                            if (context.Status == KiahooshanPromissoryStatusV1.IssuePromissory)
                            {
                                return (true, null, guarantee.Id);
                            }
                            else
                            {
                                return (false, "اطلاعات پروفایل تکمیل نشده یا سفته در جریان است", null);
                            }
                        }
                        else
                        {
                            return (true, null, null);
                        }
                    }
                default:
                    return (false, "اطلاعات پروفایل تکمیل نشده یا سفته در جریان است", null);
            }
        }

        public async ValueTask<int> CurrentGuarantorIdAsync(CancellationToken cancellationToken)
        {
            return await _dbContext.Guarantors.Where(x => x.GuaranteeType == GuaranteeType.Promissory && x.GuaranteeProvider == GuaranteeProvider.Kiahooshan).Select(x => x.Id).FirstOrDefaultAsync(cancellationToken);
        }

        public async ValueTask<OrderGuaranteeDto> CreateOrderGuaranteeAsync(
            long id,
            long orderId,
            Guid orderTrackingCode,
            int guarantorId,
            int consumerId,
            int recepientId,
            int promissoryAmount,
            DateTimeOffset dueDate,
            string predefinedRecipientName,
            CancellationToken cancellationToken)
        {
            
            var recipientInfo = await _userService.GetMerchantExtraInfoAsync(recepientId, cancellationToken);

            if (_environment.IsDevelopment())
            {
                recipientInfo = new UserInfoDto()
                {
                    FullName = "مانا تدبیر آواتک",
                    Address = "تهران، خیابان شریعتی، منظر نژاد، نبش کوچه جوادی، پلاک ۴",
                    PhoneNumber = "***********",
                    IsLegal = true,
                    BusinessNationalCode = "***********",
                    NationalCode = "***********",
                    PostalCode = "1948847413",
                };

                predefinedRecipientName = "PAYPING";
            }
            else
            {
                var merchantInfo = await _dbContext.MerchantInfos.Where(p => p.MerchantUserId == recepientId).FirstOrDefaultAsync();
                if (merchantInfo != null)
                {
                    recipientInfo.Address = merchantInfo.Address;
                    recipientInfo.PhoneNumber = recipientInfo.PhoneNumber ?? merchantInfo.MerchantInfoAgent.MobileNumber;
                    recipientInfo.PostalCode = merchantInfo.PostalCode;
                }

                if (recipientInfo.IsLegal) 
                {
                    // to fix kiahooshan problem with mobile numbers for legal customers
                    recipientInfo.PhoneNumber = "***********";
                }
            }

            var consumerInfo = await _userService.GetConsumerInfoAsync(consumerId, cancellationToken);

            var context = new KiahooshanPromissoryContextV1
            {
                OrderId = orderId,
                OrderTrackingCode = orderTrackingCode,
                OrderGuaranteeId = id,
                Amount = promissoryAmount.ToRial(),
                DueDate = dueDate.ToShortPersianDateString().Replace("/", ""),
                Description = "جهت ضمانت خرید اقساطی",
                IbanWithIR = consumerInfo.IBan?.Trim(),
                Recipient = new KiahooshanPromissoryContextRecipientV1
                {
                    FullName = recipientInfo.FullName?.Trim(),
                    MobileNumber = recipientInfo.PhoneNumber?.Trim(),
                    NationalCode = recipientInfo.DisplayNationalCode?.Trim(),
                    IsLegal = recipientInfo.IsLegal,
                    Address = recipientInfo.Address?.Trim(),
                },
                RecipientUserId = recepientId,
                ConsumerUserId = consumerId,
                IsLegal = consumerInfo.IsLegal,
                Status = KiahooshanPromissoryStatusV1.IssuePromissory,
            };

            return new OrderGuaranteeDto(orderId, guarantorId, JsonSerializer.Serialize(context));
        }

        public string GetPromissoryRefId(string data)
        {
            var context = JsonSerializer.Deserialize<KiahooshanPromissoryContextV1>(data);
            return context.PromissoryUniqueId;
        }

        public bool IsPaymentSuccessful(string status)
        {
            return status == "0";
        }

        public string SetPaymentStatusToGuaranteeData(string data, bool success)
        {
            return data;
        }

        public async ValueTask<PromissoryCheckPointResponse> PushToNextCheckPointAsync(string contextData, CancellationToken cancellationToken)
        {
            var context = JsonSerializer.Deserialize<KiahooshanPromissoryContextV1>(contextData);

            var resp = await _kihooPromissoryService.HandleAsync(context, cancellationToken);

            var result = resp switch
            {
                DoneResult doneResult => new PromissoryCheckPointResponse(true, true, string.Empty, OrderSteps.NoNext, doneResult.finalJson),
                _ => throw new NotImplementedException("at PushToNextCheckPointAsync, unknown handle result")
            };

            return result;
        }

        public (bool setNext, OrderSteps? next, List<(string name, OrderSteps code)> steps) GetSteps(string context)
        {
            OrderSteps? next = null;
            bool setNext = false;

            if (!string.IsNullOrWhiteSpace(context))
            {
                var kihooContext = JsonSerializer.Deserialize<KiahooshanPromissoryContextV1>(context);
                switch (kihooContext.Status)
                {
                    case KiahooshanPromissoryStatusV1.IssuePromissory:
                    case KiahooshanPromissoryStatusV1.Sign:
                    case KiahooshanPromissoryStatusV1.Finalize:
                        next = base.ZeroPitStop.code;
                        setNext = true;
                        break;
                    case KiahooshanPromissoryStatusV1.Done:
                    default:
                        break;
                }
            }

            return (setNext, next, new List<(string name, OrderSteps code)>()
            {
                base.ZeroPitStop,
            });
        }

        public async ValueTask<(bool success, string error)> DeleteGuaranteeAsync(string contextData)
        {
            if (string.IsNullOrWhiteSpace(contextData))
            {
                return (true, null);
            }

            var context = JsonSerializer.Deserialize<KiahooshanPromissoryContextV1>(contextData);
            if (string.IsNullOrWhiteSpace(context.PromissoryUniqueId))
            {
                return (true, null);
            }
            else if (context.Status == KiahooshanPromissoryStatusV1.Done)
            {
                return (false, "kihoo promissory can not revoke after finalization");
            }
            else
            {
                var deleteResult = await _kihooApi.DeleteAsync(new(context.PromissoryUniqueId), context.OrderTrackingCode);
                return (deleteResult.status == "completed", deleteResult.message);
            }
        }

        public string GetFinalGuaranteeDocumentId(string contextData)
        {
            var context = JsonSerializer.Deserialize<KiahooshanPromissoryContextV1>(contextData);
            return context.PromissoryFinalDocumentFileId;
        }

        public string GetFinalSettlementGuaranteeDocumentId(string contextData)
        {
            var context = JsonSerializer.Deserialize<KiahooshanPromissorySettlementContextV1>(contextData);
            return context.SettlementSignedDocumentFileId;
        }

        public (bool hasPayment, int amountToman) GetPromissoryPayment(decimal orderGuaranteeAmount)
        {
            return (false, 0);
        }

        public string RenewSettlementContextIfNeeded(OrderGuarantee orderGuarantee)
        {
            if (!orderGuarantee.SettlementData.HasValue())
            {
                var pc = JsonSerializer.Deserialize<KiahooshanPromissoryContextV1>(orderGuarantee.Data);

                return JsonSerializer.Serialize(new KiahooshanPromissorySettlementContextV1()
                {
                    ConsumerUserId = pc.ConsumerUserId,
                    OrderGuaranteeId = orderGuarantee.Id,
                    OrderId = orderGuarantee.OrderId,
                    OrderTrackingCode = pc.OrderTrackingCode,
                    PromissoryUniqueId = orderGuarantee.GuaranteeRefId,
                    RecipientUserId = pc.RecipientUserId,
                    Status = KiahooshanPromissorySettlementStatusV1.Issue,
                    Amount = pc.Amount,
                    PromissoryTreasuryId = pc.PromissoryTreasuryId,
                });
            }

            return orderGuarantee.SettlementData;
        }

        public async ValueTask<(bool success, string error, string data)> SettleGuaranteeAsync(string contextData, CancellationToken cancellationToken)
        {
            var context = JsonSerializer.Deserialize<KiahooshanPromissorySettlementContextV1>(contextData);

            var resp = await _kiahooshanPromissorySettlementService.HandleAsync(context, cancellationToken);

            (bool success, string error, string data) result = resp switch
            {
                DoneResult doneResult => new(true, string.Empty, doneResult.finalJson),
                _ => throw new NotImplementedException("at SettleGuaranteeAsync, unknown handle result")
            };

            return result;
        }
    }
}
