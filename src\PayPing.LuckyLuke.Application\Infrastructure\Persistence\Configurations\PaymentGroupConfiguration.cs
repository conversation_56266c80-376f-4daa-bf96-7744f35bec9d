﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Application.Infrastructure.Persistence.NpgSql;
using PayPing.BNPL.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class PaymentGroupConfiguration : IEntityTypeConfiguration<PaymentGroup>
{
    public void Configure(EntityTypeBuilder<PaymentGroup> builder)
    {
        builder.Property(nameof(PaymentGroup.PaymentCode)).HasMaxLength(256);
        builder.HasIndex(x => x.PaymentCode).HasDatabaseName("IX_PaymentGroup_PaymentCode");

        builder
            .HasMany(e => e.OrderPayments)
            .WithOne(e => e.PaymentGroup)
            .HasForeignKey(e => e.PaymentGroupId)
            .IsRequired(false);
    }
}
