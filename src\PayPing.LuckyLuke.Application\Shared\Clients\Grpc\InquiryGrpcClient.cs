﻿using Grpc.Net.Client;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PaymentGrpc;
using Microsoft.Extensions.Options;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using Microsoft.Extensions.Logging;
using Grpc.Core;
using RobinHood.Services.ServiceManager.Protos;
using Microsoft.AspNetCore.Http.HttpResults;

namespace PayPing.LuckyLuke.Application.Shared.Clients.Grpc
{
    public class InquiryGrpcClient : IInquiryGrpcClient
    {
        private readonly InquiryService.InquiryServiceClient _client;
        private readonly ILogger<InquiryGrpcClient> _logger;

        public InquiryGrpcClient(InquiryService.InquiryServiceClient client, ILogger<InquiryGrpcClient> logger)
        {
            _client = client;
            _logger = logger;
        }

        public async ValueTask<MatchingResponse> MachingMobileWithNationalCodeAsync(MachingMobileWithNationalCodeRequest request)
        {
            try
            {
                var mresponse = await _client.MachingMobileWithNationalCodeAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return mresponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc MachingMobileWithNationalCode failed for user: {request.MobileNumber}");
                return null;
            }
        }

        public async ValueTask<NationalCodeInquiryWithPersonalInfoResponse> NationalCodeInquiryWithPersonalInfoAsync(NationalCodeInquiryWithPersonalInfoRequest request)
        {
            try
            {
                var mresponse = await _client.NationalCodeInquiryWithPersonalInfoAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return mresponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc NationalCodeInquiryWithPersonalInfoAsync failed");
                return null;
            }
        }

        public async ValueTask<MatchingResponse> MachingCardWithNationalCodeAsync(MachingCardWithNationalCodeRequest request)
        {
            try
            {
                var mresponse = await _client.MachingCardWithNationalCodeAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return mresponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc MachingCardWithNationalCode failed");
                return null;
            }
        }

        public async ValueTask<StartCreditScoreInfoResponse> StartCreditScoreInfoAsync(StartCreditScoreInfoRequest request)
        {
            try
            {
                var mresponse = await _client.StartCreditScoreInfoAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return mresponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc StartCreditScoreInfo failed");
                return null;
            }
        }

        public async ValueTask<ValidateCreditScoreInfoResponse> ValidateCreditScoreInfoAsync(ValidateCreditScoreInfoRequest request)
        {
            try
            {
                var mresponse = await _client.ValidateCreditScoreInfoAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return mresponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc ValidateCreditScoreInfo failed");
                return null;
            }
        }

        public async ValueTask<ReportLinkCreditScoreInfoResponse> ReportLinkCreditScoreInfoAsync(ReportLinkCreditScoreInfoRequest request)
        {
            try
            {
                var mresponse = await _client.ReportLinkCreditScoreInfoAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return mresponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc ReportLinkCreditScoreInfo failed");
                return null;
            }
        }

        public async ValueTask<CreditScoreReportInfoResponse> CreditScoreReportInfoAsync(CreditScoreReportInfoRequest request)
        {
            try
            {
                var mresponse = await _client.CreditScoreReportInfoAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return mresponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc CreditScoreReportInfo failed");
                return null;
            }
        }

        public async ValueTask<PostalCodeInquiryResponse> PostalCodeInquiryAsync(PostalCodeInquiryRequest request)
        {
            try
            {
                var mresponse = await _client.PostalCodeInquiryAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return mresponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc PostalCodeInquiry failed");
                return null;
            }
        }

        public async ValueTask<GetCardToShebaResponse> GetCardToShebaAsync(GetCardToShebaRequest request)
        {
            try
            {
                var mresponse = await _client.GetCardToShebaAsync(request, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return mresponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc GetCardToSheba failed");
                return null;
            }
        }
    }
}
