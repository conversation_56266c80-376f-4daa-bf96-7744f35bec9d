﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer.InstallmentGroupPayment
{
    public record InstallmentGroupPaymentRequest(List<Guid> installmentCodes, Guid trackingCode);

    public record InstallmentGroupPaymentResponse(string url);

    public class InstallmentGroupPaymentEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.InstallmentGroupPayment,
                async (
                    InstallmentGroupPaymentRequest request,
                    IInstallmentGroupPaymentRequestHandler handler,
                    IValidator<InstallmentGroupPaymentRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, 1, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("InstallmentGroupPayment")
            .WithApiVersionSet(builder.NewApiVersionSet("Installment").Build())
            .Produces<InstallmentGroupPaymentResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Installment Group Payment")
            .WithDescription("Installment Group Payment")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class InstallmentGroupPaymentRequestHandler : IInstallmentGroupPaymentRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IPaymentGrpcClient _paymentGrpcClient;
        private readonly IUserService _userService;
        private readonly IUserContext _userContext;
        private readonly ILogger<InstallmentGroupPaymentRequestHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public InstallmentGroupPaymentRequestHandler(ApplicationDbContext dbContext, IPaymentGrpcClient paymentGrpcClient, IUserService userService, IUserContext userContext, IOptions<BNPLOptions> bnplOptions, ILogger<InstallmentGroupPaymentRequestHandler> logger)
        {
            _dbContext = dbContext;
            _paymentGrpcClient = paymentGrpcClient;
            _userService = userService;
            _userContext = userContext;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<InstallmentGroupPaymentResponse> HandleAsync(InstallmentGroupPaymentRequest request, int apiVersion, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.Where(o => o.TrackingCode == request.trackingCode).Include(x => x.Installments).FirstOrDefaultAsync();

            if (order == null)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "خرید اقساطی یافت نشد", string.Empty);
            }

            if (order.ConsumerUserId != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "خرید اقساطی متعلق به کاربر نیست", string.Empty);
            }

            if (order.Status == OrderStatus.InstallmentsWaitingForSalaryDeduction)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "امکان تسویه برای سفارش در انتظار کسر از حقوق فراهم نیست", string.Empty);
            }

            var payableInstallments = order.Installments
                .Where(i => i.Status != InstallmentStatus.PaidOff && i.Status != InstallmentStatus.SalaryDeductionPaidOff && i.PaymentStatus != PaymentStatus.PaymentSucceeded)
                .OrderBy(o => o.DueDate)
                .ToList();

            if (request.installmentCodes.Any(x =>  !payableInstallments.Any(xx => xx.Code == x)))
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "حداقل یکی از آیتم های ارسالی یافت نشد", string.Empty);
            }

            var requestedInstallments = payableInstallments.Where(x => request.installmentCodes.Contains(x.Code)).OrderBy(x => x.DueDate).ToList();

            if (requestedInstallments.Count < 2)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "پرداخت حداقل دو آیتم الزامیست", string.Empty);
            }

            var installmentPaymentAcceptableStatuses = OrderStatusProvider.GetInstallmentPaymentAcceptable();

            if (!installmentPaymentAcceptableStatuses.Contains(order.Status))
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "خرید اقساطی در مرحله پرداخت قسط نیست", string.Empty);
            }

            // check if selected installments are correctly sorted since there must be no gap in order installment selection
            for (int i = 0; i < requestedInstallments.Count; i++)
            {
                if (requestedInstallments[i].Number != payableInstallments[i].Number)
                {
                    throw new CustomValidationException(request.trackingCode.ToString(), "ترتیب آیتم های انتخابی صحیح نیست", string.Empty);
                }
            }

            var consumerInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);

            string returnUrl = UrlHelpers.BuildFullApiUrl(_bnplOptions.BaseUrl, ConsumerRoutes.InstallmentGroupPaymentVerify, apiVersion);


            var pg = new PaymentGroup
            {
                ConsumerUserId = userId,
                TotalAmount = 0m
            };

            var now = DateTimeOffset.UtcNow;
            bool isEarlyPayOff = requestedInstallments.Count == payableInstallments.Count && requestedInstallments.Where(r => r.DueDate >= now).Count() > 1;

            foreach (var installment in requestedInstallments)
            {
                var op = new OrderPayment()
                {
                    InstallmentId = installment.Id,
                    OrderId = order.Id,
                    Amount = installment.FinalAmount,
                    PaymentStatus = OrderPaymentStatus.Init,
                    PaymentType = OrderPaymentType.Installment,
                    WalletId = order.OrderPlan.WalletId,
                    CreditId = order.OrderPlan.CreditId,
                };

                if (isEarlyPayOff && installment.DueDate >= now)
                {
                    op.Amount = decimal.Add(installment.OriginalAmount, installment.DelayPenaltyAmount);
                    op.DiscountAmount = installment.InterestAmount;
                }

                pg.TotalAmount = decimal.Add(pg.TotalAmount, op.Amount);

                pg.OrderPayments.Add(op);
            }

            _dbContext.PaymentGroups.Add(pg);

            await _dbContext.SaveChangesAsync();

            try
            {
                var payResp = await _paymentGrpcClient.CreatePaymentAsync(
                new PaymentGrpcRequest(
                    order.MerchantUserId,
                    (int)pg.TotalAmount,
                    0,
                    0,
                    returnUrl,
                    consumerInfo.FullName,
                    "پرداخت گروهی قسط سرویس اعتباری",
                    pg.Id.ToString(),
                    consumerInfo.UserName));

                if (payResp != null && !string.IsNullOrWhiteSpace(payResp.paymentCode))
                {
                    pg.PaymentCode = payResp.paymentCode;

                    foreach (var installment in requestedInstallments)
                    {
                        installment.Status = InstallmentStatus.Waiting;
                        installment.PaymentStatus = PaymentStatus.PaymentInProgress;
                    }

                    await _dbContext.SaveChangesAsync();

                    return new InstallmentGroupPaymentResponse(payResp.url);
                }
                else
                {
                    _logger.LogWarning($"installment creation failed for order: {order.TrackingCode}, payment group: {pg.Id}");

                    foreach (var installment in requestedInstallments)
                        installment.PaymentStatus = PaymentStatus.PaymentFailed;

                    await _dbContext.SaveChangesAsync();

                    throw new PaymentException(request.trackingCode.ToString(), $"installment creation failed for order: {order.TrackingCode}, payment group: {pg.Id}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.InnerException?.Message ?? ex.Message);

                foreach (var installment in requestedInstallments)
                    installment.PaymentStatus = PaymentStatus.PaymentFailed;

                await _dbContext.SaveChangesAsync();

                throw new PaymentException(request.trackingCode.ToString(), ex.Message);
            }
        }
    }

    public interface IInstallmentGroupPaymentRequestHandler
    {
        ValueTask<InstallmentGroupPaymentResponse> HandleAsync(InstallmentGroupPaymentRequest request, int apiVersion, CancellationToken cancellationToken);
    }

    public class InstallmentGroupPaymentValidator : AbstractValidator<InstallmentGroupPaymentRequest>
    {
        public InstallmentGroupPaymentValidator()
        {
            RuleFor(x => x.installmentCodes).NotEmpty().WithResourceError(() => ValidatorDictionary.installment_code_is_required);
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);

        }
    }
}

