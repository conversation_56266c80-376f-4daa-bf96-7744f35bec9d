﻿using FluentValidation;
using PayPing.BNPL.Domain.Models;

namespace PayPing.LuckyLuke.Application.Shared.Validators
{
    public class ConsumerInfoValidator : AbstractValidator<ConsumerInfo>
    {
        public ConsumerInfoValidator()
        {
            RuleFor(c => c.PostalCode).NotEmpty();
            RuleFor(c => c.Address).NotEmpty();
            RuleFor(c => c.FirstNameEnglish).NotEmpty();
            RuleFor(c => c.LastNameEnglish).NotEmpty();
            RuleFor(c => c.NationalIdSeries).NotEmpty();
            RuleFor(c => c.IBan).NotEmpty();
            RuleFor(c => c.Email).NotEmpty();
            RuleFor(c => c.ProvinceEnglish).NotEmpty();
            RuleFor(c => c.CityEnglish).NotEmpty();
        }
    }
}
