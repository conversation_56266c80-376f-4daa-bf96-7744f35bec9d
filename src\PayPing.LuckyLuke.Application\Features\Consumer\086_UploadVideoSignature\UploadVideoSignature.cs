﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Features.Consumer._085_UploadSignature;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.Threading;

namespace PayPing.LuckyLuke.Application.Features.Consumer._086_UploadVideoSignature
{
    public record UploadVideoSignatureRequest(Guid trackingCode, IFormFile signatureImage, IFormFile signatureVideo, string videoSentence);

    public record UploadVideoSignatureResponse(string imgfileId, string videofileId);

    public class UploadVideoSignatureEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.UploadVideoSignature,
                async (
                    [FromForm] UploadVideoSignatureRequest request,
                    IUploadVideoSignatureRequestHandler handler,
                    IValidator<UploadVideoSignatureRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .DisableAntiforgery()
            .WithName("UploadVideoSignature")
            .WithApiVersionSet(builder.NewApiVersionSet("Signature").Build())
            .Accepts<UploadVideoSignatureRequest>("multipart/form-data")
            .Produces<UploadVideoSignatureResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Upload Signature Video")
            .WithDescription("Upload Signature Video")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class UploadVideoSignatureRequestHandler : IUploadVideoSignatureRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IEnumerable<IDigitalSignProvider> _digitalSignProviders;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IUserContext _userContext;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly IMemoryCache _memoryCache;
        private readonly BNPLOptions _bnplOptions;

        public UploadVideoSignatureRequestHandler(
            ApplicationDbContext dbContext,
            IEnumerable<IDigitalSignProvider> digitalSignProviders,
            IGuaranteeService guaranteeService,
            IUserContext userContext,
            IUploadGrpcClient uploadGrpcClient,
            IMemoryCache memoryCache,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _digitalSignProviders = digitalSignProviders;
            _guaranteeService = guaranteeService;
            _userContext = userContext;
            _uploadGrpcClient = uploadGrpcClient;
            _memoryCache = memoryCache;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<UploadVideoSignatureResponse> HandleAsync(UploadVideoSignatureRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders
                .Where(o => o.TrackingCode == request.trackingCode)
                .FirstOrDefaultAsync(cancellationToken);

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            {
                throw new OrderExpiredException(request.trackingCode.ToString(), "سفارش منقضی شده است");
            }

            if (order.ConsumerUserId != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }

            // validate image
            var imageResult = await UploadImage(request.trackingCode, userId, request.signatureImage, request.signatureImage.FileName, cancellationToken);

            // validate video
            var videoResult = await UploadVideo(request.trackingCode, userId, request.signatureVideo, request.signatureVideo.FileName, cancellationToken);

            var consumerInfo = await _dbContext.ConsumerInfos.Where(x => x.ConsumerUserId == userId).FirstOrDefaultAsync();

            consumerInfo.SignatureImageFileId = imageResult.fileId;
            consumerInfo.SignatureImageFileName = imageResult.filename;
            consumerInfo.SignatureVideoFileId = videoResult.fileId;
            consumerInfo.SignatureVideoFileName = videoResult.filename;
            consumerInfo.SignatureVideoRandomSentence = request.videoSentence;

            await _dbContext.SaveChangesAsync();


            DigitalSignType digitalSignType = await _guaranteeService.GetGuarantorCompliantSignatureTypeByGuarantorId(order.OrderPlan.GuarantorId);

            var provider = ChooseProvider(digitalSignType);

            var currentds = await provider.GetUserValidDigitalSignatureAsync(order.ConsumerUserId.Value, true);
            if (currentds == null)
            {
                currentds = new Domain.Models.DigitalSign
                {
                    Type = digitalSignType,
                    UserId = order.ConsumerUserId.Value,
                };

                _dbContext.DigitalSigns.Add(currentds);

                var result = await _dbContext.SaveChangesAsync();
                if (result == 0)
                {
                    throw new Exception("At UploadVideoSignatureRequestHandler; could not save DigitalSign");
                }
            }

            var data = await provider.CreateDigitalSignAsync(order.TrackingCode, currentds.Id, order.ConsumerUserId.Value, true, cancellationToken);

            currentds.Data = data;
            currentds.SignatureRefId = null;

            await _dbContext.SaveChangesAsync();

            var checkPoint = await provider.PushToNextCheckPointAsync(currentds.Data, cancellationToken);

            currentds.SignatureRefId = checkPoint.data;

            order.Status = OrderStatus.DigitalSignatureCreated;

            await _dbContext.SaveChangesAsync();


            return new(imageResult.fileId, videoResult.fileId);
        }

        private IDigitalSignProvider ChooseProvider(DigitalSignType digitalSignType)
        {
            return _digitalSignProviders.First(f => f.DigitalSignType == digitalSignType);
        }

        private async ValueTask<(string fileId, string filename)> UploadVideo(Guid orderTrackingCode, int userId, IFormFile file, string filename, CancellationToken cancellationToken)
        {
            var validPack = await ValidateFile(orderTrackingCode, file, filename, ApplicationConstants.SignatureVideoValidExtensions, ApplicationConstants.SignatureVideoMaxSizeInBytes, cancellationToken);

            var uploadedFile = await _uploadGrpcClient.UploadUserDocumentAsync(validPack.validFile, Path.GetExtension(validPack.validName).TrimStart('.'), userId, cancellationToken);
            if (!uploadedFile.Succeeded)
            {
                throw new CustomValidationException(orderTrackingCode.ToString(), "خطا در آپلود فایل", "signatureVideo");
            }

            string key = $"{ApplicationConstants.KiahooshanVideoCacheKey}_{uploadedFile.SuccessResult}";

            var cacheEntryOptions = new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5) };

            _memoryCache.Set(key, validPack.validFile, cacheEntryOptions);

            return (uploadedFile.SuccessResult, validPack.validName);
        }

        private async ValueTask<(string fileId, string filename)> UploadImage(Guid orderTrackingCode, int userId, IFormFile file, string filename, CancellationToken cancellationToken)
        {
            var validPack = await ValidateFile(orderTrackingCode, file, filename, ApplicationConstants.SignatureImageValidExtensions, ApplicationConstants.SignatureImageMaxSizeInBytes, cancellationToken);

            var uploadedFile = await _uploadGrpcClient.UploadUserDocumentAsync(validPack.validFile, Path.GetExtension(validPack.validName).TrimStart('.'), userId, cancellationToken);
            if (!uploadedFile.Succeeded)
            {
                throw new CustomValidationException(orderTrackingCode.ToString(), "خطا در آپلود فایل", "signatureImage");
            }

            return (uploadedFile.SuccessResult, validPack.validName);
        }

        private async ValueTask<(byte[] validFile, string validName)> ValidateFile(Guid orderTrackingCode, IFormFile file, string filename, string[] validextensions, long validsize, CancellationToken cancellationToken)
        {
            ModelStateDictionary fileValidationDic = new ModelStateDictionary();
            var validFile = await FileHelpers.ProcessFormFile(file, fileValidationDic, validextensions, validsize);

            if (validFile.Length == 0 || !fileValidationDic.IsValid)
            {
                throw new CustomValidationException(orderTrackingCode.ToString(), $"فایل معتبر نیست. {string.Join(", ", fileValidationDic.Values.SelectMany(v => v.Errors).Select(x => x.ErrorMessage))}", string.Empty);
            }

            string validName = Path.ChangeExtension(Path.GetRandomFileName(), Path.GetExtension(filename));

            return (validFile, validName);
        }
    }

    public interface IUploadVideoSignatureRequestHandler
    {
        ValueTask<UploadVideoSignatureResponse> HandleAsync(UploadVideoSignatureRequest request, CancellationToken cancellationToken);
    }

    public class UploadVideoSignatureValidator : AbstractValidator<UploadVideoSignatureRequest>
    {
        public UploadVideoSignatureValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
            RuleFor(x => x.signatureImage).NotNull().WithResourceError(() => ValidatorDictionary.signature_image_not_null);
            RuleFor(x => x.signatureVideo).NotNull().WithResourceError(() => ValidatorDictionary.signature_video_not_null);
            RuleFor(x => x.videoSentence).NotEmpty().WithResourceError(() => ValidatorDictionary.signature_video_sentence_not_null);

        }
    }
}
