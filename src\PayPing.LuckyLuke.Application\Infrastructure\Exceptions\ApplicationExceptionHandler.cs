﻿using Ardalis.GuardClauses;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using Refit;
using System;
using System.Runtime.InteropServices;
using PayPing.LuckyLuke.Application.Shared.Extensions;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using PayPing.LuckyLuke.Application.Shared.Services.User;


namespace PayPing.LuckyLuke.Application.Infrastructure.Exceptions;

public partial class ApplicationExceptionHandler : IExceptionHandler
{
    private readonly ILogger<ApplicationExceptionHandler> _logger;
    private readonly IServiceProvider _serviceProvider;

    public ApplicationExceptionHandler(ILogger<ApplicationExceptionHandler> logger, IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }
    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
    {
        var oel = new EventLog()
        {
            Code = default,
            LogType = Domain.Enums.EventLogType.ExceptionHappened,
            Message = exception.Message,
            Name = "payping.unhandled_exception",
        };

        var problemDetailError = new BnplProblemDetails
        {
            Key = 130,
            Title = "payping.unhandled_exception",
            Status = 500,
            Detail = "server unhandled exception occured",
            Instance = httpContext.Request.Path,

        };
        problemDetailError.Extensions.AddCausationId(httpContext.TraceIdentifier);
        int? statusCode = default;
        string errorMessage = default;
        switch (exception)
        {
            case PaymentException paymentException:
                {
                    problemDetailError.Key = 100;
                    problemDetailError.Title = "payping.payment_error";
                    (statusCode, errorMessage) = (StatusCodes.Status500InternalServerError, "خطا در فرایند پرداخت");
                    problemDetailError.Title = "payping.payment_error";
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "خطا در فرایند پرداخت";
                    oel.Code = 100;
                    oel.RefId = paymentException.EventId;
                    break;
                }
            case CustomValidationException customValidationException:
                {
                    problemDetailError.Key = 101;
                    problemDetailError.Title = "payping.validation_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "خطا در پارامترهای ارسالی");
                    problemDetailError.Extensions.Add("errors",
                        customValidationException.ValidationResultModel.Errors
                            .Select(x => new PaypingParameterError(x.Code, x.Message, x.Field)).ToList());
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "ارسال پارامترهای نامعتبر";
                    oel.Code = 101;
                    oel.RefId = customValidationException.EventId;
                    break;
                }
            case CreditTransactionException creditTransactionException:
                {
                    problemDetailError.Key = 102;
                    (statusCode, errorMessage) = (StatusCodes.Status500InternalServerError, "خطا در فرایند تراکنش صندوق اعتباری");
                    problemDetailError.Title = "payping.credit_error";
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = StatusCodes.Status500InternalServerError;
                    problemDetailError.Extensions.Add("errors",
                        new PaypingParameterError("credit", creditTransactionException.Message));

                    oel.Name = "خطا در فرایند تراکنش صندوق اعتباری";
                    oel.Code = 102;
                    oel.RefId = creditTransactionException.EventId;
                    break;
                }
            case ArgumentException argumentException:
                {
                    problemDetailError.Key = 101;
                    problemDetailError.Title = "payping.validation_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "خطا در پارامترهای ارسالی");
                    var error = new List<PaypingParameterError>(1);
                    error.Add(new PaypingParameterError("unkown_parameter_error", argumentException.Message, argumentException.ParamName));
                    problemDetailError.Extensions.Add("errors", error);
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;
                    break;
                }

            case NotFoundException:
                problemDetailError.Key = 104;
                problemDetailError.Title = "payping.not_found";
                (statusCode, errorMessage) = (StatusCodes.Status404NotFound, exception.Message);
                problemDetailError.Detail = errorMessage;
                problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;
                break;
            case CertificateNotReadyException cnrException:
                {
                    problemDetailError.Key = 103;
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "موجودیت درخواستی آماده نیست");
                    problemDetailError.Title = "payping.certificate_not_ready_error";
                    problemDetailError.Detail = cnrException.Message;
                    problemDetailError.Status = StatusCodes.Status400BadRequest;
                    problemDetailError.Extensions.Add("errors",
                        new PaypingParameterError("not_ready", "true"));

                    oel.Name = "سرتیفیکیت آماده نیست";
                    oel.Code = 103;
                    oel.RefId = cnrException.EventId;
                    break;
                }
            case CreditScoreNotReadyException cnrException:
                {
                    problemDetailError.Key = 105;
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "موجودیت درخواستی آماده نیست");
                    problemDetailError.Title = "payping.creditscore_not_ready_error";
                    problemDetailError.Detail = cnrException.Message;
                    problemDetailError.Status = StatusCodes.Status400BadRequest;
                    problemDetailError.Extensions.Add("errors",
                        new PaypingParameterError("not_ready", "true"));

                    oel.Name = "گزارش رتبه سنجی اعتباری آماده نیست";
                    oel.Code = 105;
                    oel.RefId = cnrException.EventId;
                    break;
                }
            case PromissoryProviderException promissoryException:
                {
                    problemDetailError.Key = 106;
                    problemDetailError.Title = "payping.promissory_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "خطا در فرایند صدور سفته دیجیتال");
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;
                    problemDetailError.Detail = promissoryException.ExposeMessage ? promissoryException.Message : errorMessage;

                    oel.Name = promissoryException.ExposeMessage ? promissoryException.Message : errorMessage;
                    oel.Code = 106;
                    oel.RefId = promissoryException.EventId;
                    break;
                }
            case DigitalSignatureProviderException dspException:
                {
                    problemDetailError.Key = 107;
                    problemDetailError.Title = "payping.signature_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "خطا در فرایند امضا دیجیتال");
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;
                    problemDetailError.Detail = dspException.ExposeMessage ? dspException.Message : errorMessage;

                    oel.Name = dspException.ExposeMessage ? dspException.Message : errorMessage;
                    oel.Code = 107;
                    oel.RefId = dspException.EventId;
                    break;
                }
            case OrderExpiredException oeException:
                {
                    problemDetailError.Key = 109;
                    problemDetailError.Title = "payping.order_expired_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "خطای سفارش منقضی شده");
                    var error = new List<PaypingParameterError>(1);
                    error.Add(new PaypingParameterError("payping.order_expired", oeException.Message));
                    problemDetailError.Extensions.Add("errors", error);
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "خطای سفارش منقضی شده";
                    oel.Code = 109;
                    oel.RefId = oeException.EventId;
                    break;
                }
            case PlanMinOrderAmountException pmoaException:
                {
                    problemDetailError.Key = 110;
                    problemDetailError.Title = "payping.order_plan_minamount_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "خطای حداقل مبلغ سفارش در انتخاب طرح اقساطی");
                    var error = new List<PaypingParameterError>(1);
                    error.Add(new PaypingParameterError("payping.wrong_plan", pmoaException.Message));
                    problemDetailError.Extensions.Add("errors", error);
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "خطای حداقل مبلغ سفارش در انتخاب طرح اقساطی";
                    oel.Code = 110;
                    oel.RefId = pmoaException.EventId;
                    break;
                }
            case PostalCodeInquiryException pciException:
                {
                    problemDetailError.Key = 111;
                    problemDetailError.Title = "payping.postal_code_inquiry_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "خطای استعلام کد پستی");
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;
                    break;
                }
            case CreditScoreStepException cssException:
                {
                    problemDetailError.Key = 112;
                    problemDetailError.Title = "payping.credit_score_step_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "خطای استعلام رتبه اعتباری");
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "خطای استعلام رتبه اعتباری";
                    oel.Code = 112;
                    oel.RefId = cssException.EventId;
                    break;
                }
            case ConsumerOrderSameMerchantLimitException colException:
                {
                    problemDetailError.Key = 113;
                    problemDetailError.Title = "payping.order_same_merchant_limit_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "کاربر در این فروشگاه خرید اقساطی تسویه نشده دارد");
                    var error = new List<PaypingParameterError>(2);
                    error.Add(new PaypingParameterError("payping.order_same_merchant_limit_message", colException.Message));
                    error.Add(new PaypingParameterError("payping.order_same_merchant_limit_merchantid", colException.MerchantId));
                    problemDetailError.Extensions.Add("errors", error);
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "کاربر در این فروشگاه خرید اقساطی تسویه نشده دارد";
                    oel.Code = 113;
                    oel.RefId = colException.EventId;
                    break;
                }
            case ConsumerOrderParallelLimitException colException:
                {
                    problemDetailError.Key = 114;
                    problemDetailError.Title = "payping.order_parallel_limit_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "کاربر در سیستم تعداد غیر مجاز خرید اقساطی تسویه نشده دارد");
                    var error = new List<PaypingParameterError>(1);
                    error.Add(new PaypingParameterError("payping.order_parallel_limit", colException.Message));
                    problemDetailError.Extensions.Add("errors", error);
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "کاربر در سیستم تعداد غیر مجاز خرید اقساطی تسویه نشده دارد";
                    oel.Code = 114;
                    oel.RefId = colException.EventId;
                    break;
                }
            case ConsumerOrderDelayedInstallmentLimitException colException:
                {
                    problemDetailError.Key = 115;
                    problemDetailError.Title = "payping.order_delay_installment_limit_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "کاربر در سیستم قسط معوق دارد");
                    var error = new List<PaypingParameterError>(1);
                    error.Add(new PaypingParameterError("payping.order_delay_installment_limit", colException.Message));
                    problemDetailError.Extensions.Add("errors", error);
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "کاربر در سیستم قسط معوق دارد";
                    oel.Code = 115;
                    oel.RefId = colException.EventId;
                    break;
                }
            case ConsumerOrderInProgressLimitException colException:
                {
                    problemDetailError.Key = 116;
                    problemDetailError.Title = "payping.order_inprogress_limit_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "کاربر در سیستم خرید اقساطی جاری دارد");
                    var error = new List<PaypingParameterError>(1);
                    error.Add(new PaypingParameterError("payping.order_inprogress_limit", colException.Message));
                    problemDetailError.Extensions.Add("errors", error);
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "کاربر در سیستم خرید اقساطی جاری دارد";
                    oel.Code = 116;
                    oel.RefId = colException.EventId;
                    break;
                }
            case EmployeeUnknownException euException:
                {
                    problemDetailError.Key = 117;
                    problemDetailError.Title = "payping.employee_unknown_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "خطای عدم تطابق شماره موبایل کاربر در طرح با ضمانت کسر از حقوق");
                    var error = new List<PaypingParameterError>(1);
                    error.Add(new PaypingParameterError("payping.employee_unknown", euException.Message));
                    problemDetailError.Extensions.Add("errors", error);
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "خطای عدم تطابق شماره موبایل کاربر در طرح با ضمانت کسر از حقوق";
                    oel.Code = 117;
                    oel.RefId = euException.EventId;
                    break;
                }
            case RefundNotActiveException rnaException:
                {
                    problemDetailError.Key = 118;
                    problemDetailError.Title = "payping.refund_not_active_error";
                    (statusCode, errorMessage) = (StatusCodes.Status400BadRequest, "سرویس استرداد وجه غیرفعال است");
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;

                    oel.Name = "سرویس استرداد وجه غیرفعال است";
                    oel.Code = 118;
                    oel.RefId = rnaException.EventId;
                    break;
                }
            case WalletLockAcquisitionException wlaException:
                {
                    problemDetailError.Key = 118;
                    problemDetailError.Title = "payping.wallet_lock_error";
                    (statusCode, errorMessage) = (StatusCodes.Status409Conflict, "خطا در قفل کردن کیف پول");
                    var error = new List<PaypingParameterError>(1)
                    {
                        new("payping.wallet_lock_acquisition", wlaException.Message, "walletId")
                    };
                    problemDetailError.Extensions.Add("errors", error);
                    problemDetailError.Detail = errorMessage;
                    problemDetailError.Status = statusCode == 0 ? 500 : statusCode;

                    oel.Name = "خطا در قفل کردن کیف پول";
                    oel.Code = 118;
                    oel.RefId = wlaException.EventId;
                    break;
                }
            default:
                (statusCode, errorMessage) = default((int, string));
                problemDetailError.Detail = errorMessage;
                problemDetailError.Status = statusCode == default(int) ? 500 : statusCode;
                break;
        }

        if (statusCode == default(int))
        {
            return false;
        }

        if (oel.Code > default(int) && !string.IsNullOrEmpty(oel.RefId))
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                var userContext = scope.ServiceProvider.GetRequiredService<IUserContext>();

                oel.UserId = userContext.CurrentUserId ?? default;

                dbContext.EventLogs.Add(oel);
                await dbContext.SaveChangesAsync();
            }
        }


        _logger.LogError(exception, exception.Message);
        httpContext.Response.StatusCode = statusCode.Value;
        await httpContext.Response.WriteAsJsonAsync(problemDetailError);
        return true;
    }
}