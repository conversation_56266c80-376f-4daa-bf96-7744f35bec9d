﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedUserInfos : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "DueDate",
                table: "Orders",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GuaranteeRefId",
                table: "OrderGuarantees",
                type: "character varying(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "GuaranteeProvider",
                table: "Guarantors",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "ConsumerInfos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ConsumerUserId = table.Column<int>(type: "integer", nullable: false),
                    FirstNameEnglish = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    LastNameEnglish = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    IsMale = table.Column<bool>(type: "boolean", nullable: false),
                    NationalIdSeries = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    PostalCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    Address = table.Column<string>(type: "character varying(2048)", maxLength: 2048, nullable: true),
                    IBan = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: true),
                    Email = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    ProvinceEnglish = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    CityEnglish = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: true),
                    LastModifiedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConsumerInfos", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MerchantInfos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MerchantUserId = table.Column<int>(type: "integer", nullable: false),
                    PredefinedCode = table.Column<string>(type: "character varying(128)", maxLength: 128, nullable: true),
                    LastModifiedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MerchantInfos", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OrderGuarantee_GuaranteeRefId",
                table: "OrderGuarantees",
                column: "GuaranteeRefId");

            migrationBuilder.CreateIndex(
                name: "IX_ConsumerInfo_ConsumerUserId",
                table: "ConsumerInfos",
                column: "ConsumerUserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MerchantInfo_MerchantUserId",
                table: "MerchantInfos",
                column: "MerchantUserId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ConsumerInfos");

            migrationBuilder.DropTable(
                name: "MerchantInfos");

            migrationBuilder.DropIndex(
                name: "IX_OrderGuarantee_GuaranteeRefId",
                table: "OrderGuarantees");

            migrationBuilder.DropColumn(
                name: "DueDate",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "GuaranteeRefId",
                table: "OrderGuarantees");

            migrationBuilder.DropColumn(
                name: "GuaranteeProvider",
                table: "Guarantors");
        }
    }
}
