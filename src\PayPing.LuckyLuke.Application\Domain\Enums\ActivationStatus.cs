﻿using PayPing.LuckyLuke.Application.Infrastructure.Web;

namespace PayPing.LuckyLuke.Application.Domain.Enums
{
    public enum ActivationStatus
    {
        CreateContract = 10,
        SetCredit = 20,
        SetWalletCreditAmount = 30,
        SetMinPrePaymentRate = 40,
        SetMonthlyInterestRate = 50,
        CompleteMerchantProfileInfo = 60,
        AcceptRegulations = 70,
        AdminContractWageSet = 80,
        Final = 90
    }

    public static class ActivationStatusProvider
    {
        public static ActivationStatus[] ResetableCreditBalanceContractStatuses() =>
            [
                ActivationStatus.CreateContract,
                ActivationStatus.SetCredit,
                ActivationStatus.SetWalletCreditAmount,
                ActivationStatus.SetMinPrePaymentRate,
                ActivationStatus.SetMonthlyInterestRate,
                ActivationStatus.CompleteMerchantProfileInfo,
                ActivationStatus.AcceptRegulations
            ];

        public static ActivationSteps ToActivationStep(this ActivationStatus activationStatus)
        {
            switch (activationStatus)
            {
                case ActivationStatus.CreateContract:
                    return ActivationSteps.SetCredit;
                case ActivationStatus.SetCredit:
                    return ActivationSteps.SetWalletCreditAmount;
                case ActivationStatus.SetWalletCreditAmount:
                    return ActivationSteps.SetMinPrePaymentRate;
                case ActivationStatus.SetMinPrePaymentRate:
                    return ActivationSteps.SetMonthlyInterestRate;
                case ActivationStatus.SetMonthlyInterestRate:
                    return ActivationSteps.CompleteMerchantProfileInfo;
                case ActivationStatus.CompleteMerchantProfileInfo:
                    return ActivationSteps.UploadSignature;
                case ActivationStatus.AcceptRegulations:
                    return ActivationSteps.WaitingForAdmin;
                case ActivationStatus.AdminContractWageSet:
                    return ActivationSteps.SignContract;
                case ActivationStatus.Final:
                    return ActivationSteps.NoNext;
                default:
                    return ActivationSteps.CreateContract;
            }
        }
    }
}
