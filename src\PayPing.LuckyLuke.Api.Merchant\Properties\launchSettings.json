{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ELASTIC_APM_ENABLED": "false", "ELASTIC_APM_LOG_LEVEL": "info", "ELASTIC_APM_SERVER_URL": "http://apm-apm-server.elastic-system:8200", "ELASTIC_APM_SERVICE_NAME": "bnpl-merchant.dev", "ELASTIC_APM_TRANSACTION_SAMPLE_RATE": "1.0", "ConnectionStrings__ElasticsearchConnection": "http://paypingservice:<EMAIL>-system:9200", "ELASTIC_HOST": "http://paypingservice:<EMAIL>-system:9200", "ElasticSearchConnection": "http://paypingservice:<EMAIL>-system:9200", "PayPing_Coupons_Address": "http://localhost:5033", "PayPing_Customers_Address": "http://localhost:5038", "PayPing_FileManager_Address": "http://localhost:64803", "PayPing_FileManager_Grpc_Address": "http://localhost:64804", "PayPing_Ghesta_Address": "http://localhost:5022", "PayPing_GoToIpg_Address": "https://api.payping.dev/v1/pay/gotoipg", "PayPing_Identity_Address": "https://oauth.payping.dev", "PayPing_Inquiry_DecisionMaker_Address": "http://localhost:5050", "PayPing_Inquiry_ReportGrpc_Address": "http://localhost:5051", "PayPing_Integrations_Address": "http://localhost:5111", "PayPing_Invoices_Address": "http://localhost:5018", "PayPing_Ipg_Address": "https://api.payping.dev", "PayPing_ItemSale_Address": "http://localhost:5048", "PayPing_Products_Address": "http://localhost:5014", "PayPing_Report_Address": "http://localhost:5013", "PayPing_Settlement_Address": "http://localhost:5090", "PayPing_Shaparak_Address": "http://localhost:5085", "PayPing_SignalR_Address": "http://localhost:5080", "PayPing_TerminalManager_Address": "http://localhost:5085", "PayPing_TimeLine_Address": "http://localhost:5031", "PayPing_Token_Address": "http://localhost:5016", "PayPing_UserServices_Address": "http://localhost:5015", "Files_Base_Url": "https://files.payping.dev", "ConcurrencyRedis": "127.0.0.1", "EFRedisConectionString": "127.0.0.1", "REDIS_HOST": "127.0.0.1", "REDIS_PORT": "6379", "RedisClusterConnectionString": "127.0.0.1:6379", "RabbitLogConectionString": "amqp://payping:6dj3cupEDgdah2@localhost:5672", "RabbitMqConectionString": "amqp://payping:6dj3cupEDgdah2@localhost:5672", "RabbitMqPassword": "6dj3cupEDgdah2", "RabbitMqTimeLineConectionString": "amqp://payping:6dj3cupEDgdah2@localhost:5672", "RabbitMqUri": "amqp://payping:6dj3cupEDgdah2@localhost:5672", "RabbitMqUsername": "payping", "TimeLineRabbitMqPassword": "6dj3cupEDgdah2", "TimeLineRabbitMqUri": "amqp://payping:6dj3cupEDgdah2@localhost:5672", "TimeLineRabbitMqUsername": "payping", "IsMana": "true", "LogEventLevel": "Information", "NEXT_LOG_LEVEL": "info", "PayPing_Inquiry_ApiGateway_Grpc_Address": "http://localhost:59101", "Refund_Grpc_Address": "http://localhost:5351", "PayPing_Token_Grpc_Address": "http://localhost:5001", "PayPing_Wallet_Grpc_Address": "http://localhost:5002", "ASPNETCORE_ENVIRONMENT": "Development", "BNPL__ClientId": "payping-BNPLClient", "BNPL__ClientSecret": "secret", "BNPL__ApiName": "BNPLApi", "BNPL__ApiSecret": "secret", "BNPL__BaseUrl": "http://localhost:5227", "BNPL__BasePath": "", "BNPL__ConsumerUIBaseUrl": "https://customers.payping.dev", "BNPL__MerchantUIBaseUrl": "https://app.payping.dev", "BNPL__PlatformClientId": "payping-BNPLPlatformClient", "BNPL__PlatformClientSecret": "secret", "Postgres__ConnectionString": "UserID=payping;Password=********************************;Host=localhost;Port=5432;Database=bnpldb;Pooling=true;", "Postgres__UseInMemory": "false", "Postgres__MigrationAssembly": "PayPing.LuckyLuke.Application", "InquiryServiceAddress": "http://localhost:5050/inquiry/", "PaymentServiceAddress": "https://api3.payping.dev", "PaymentGrpcServiceAddress": "http://localhost:5081", "Kuknos__BaseUrl": "https://api.dev.kuknos.ir", "Kuknos__Platform": "payping_v1.0.0", "Kuknos__Email": "<EMAIL>", "Kuknos__Password": "payping123", "Proxy__Address": "*************:3128", "Proxy__Password": "SNZesKAzvQSzhZSJ", "Proxy__UserName": "arvan-proxy", "RedisInstanceName": "master", "LogLevel": "Information", "Logstash_Address": "tcp://logstash-headless.elastic-system", "Logstash_Port": "1514", "Logstash_Type": "TCP", "Kiahooshan__BaseUrl": "https://sandbox.vidaverify.ir:9091", "Kiahooshan__Username": "demo_payping", "Kiahooshan__Password": "Pt_Wertdf34"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5227"}, "https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7070;http://localhost:5227"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "environmentVariables": {"ASPNETCORE_HTTPS_PORTS": "8081", "ASPNETCORE_HTTP_PORTS": "8080"}, "publishAllPorts": true, "useSSL": true}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:42643", "sslPort": 44368}}}