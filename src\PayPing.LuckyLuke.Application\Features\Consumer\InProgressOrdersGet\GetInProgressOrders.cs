﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer.InProgressOrdersGet
{
    public record GetInProgressOrdersRequest(Guid trackingCode);

    public record GetInProgressOrderResponse(Guid trackingCode, bool isCurrent, DateTimeOffset expireDate, decimal creditedAmount);

    public class GetInProgressOrdersEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.GetInProgressOrders,
                async (
                    [AsParameters] GetInProgressOrdersRequest request,
                    IGetInProgressOrdersRequestHandler handler,
                    IValidator<GetInProgressOrdersRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetInProgressOrders")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<List<GetInProgressOrderResponse>>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get InProgress Orders")
            .WithDescription("Get InProgress Orders")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetInProgressOrdersRequestHandler : IGetInProgressOrdersRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;

        public GetInProgressOrdersRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext)
        {
            _dbContext = dbContext;
            _userContext = userContext;
        }

        public async ValueTask<List<GetInProgressOrderResponse>> HandleAsync(GetInProgressOrdersRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var result = new List<GetInProgressOrderResponse>();

            var nathash = _userContext.CurrentUserNationalCode.HashSHA256NationalCode();

            var expirableinprogressStatuses = OrderStatusProvider.GetExpirable();

            var userInProgressOrders = await _dbContext.Orders.AsNoTracking()
                .Where(o =>
                    o.TrackingCode == request.trackingCode || 
                    (
                        o.ConsumerNationalHashId == nathash &&
                        expirableinprogressStatuses.Contains(o.Status) &&
                        o.ExpireDate > DateTimeOffset.UtcNow
                     ))
                .ToListAsync();

            if (userInProgressOrders != null && userInProgressOrders.Count > 0)
            {
                foreach (var item in userInProgressOrders.OrderBy(x => x.ExpireDate))
                {
                    result.Add(new GetInProgressOrderResponse(
                        item.TrackingCode,
                        item.TrackingCode == request.trackingCode,
                        item.ExpireDate ?? DateTimeOffset.UtcNow.AddMinutes(ApplicationConstants.OrderExpireDurationMinutes),
                        item.OrderTotalAmount));
                }
            }

            return result;
        }
    }
    public interface IGetInProgressOrdersRequestHandler
    {
        ValueTask<List<GetInProgressOrderResponse>> HandleAsync(GetInProgressOrdersRequest request, CancellationToken cancellationToken);
    }

    public class GetInProgressOrdersValidator : AbstractValidator<GetInProgressOrdersRequest>
    {
        public GetInProgressOrdersValidator()
        {
            RuleFor(x => x.trackingCode).NotEqual(Guid.Empty).WithResourceError(() => ValidatorDictionary.tracking_code_is_required); 
        }
    }
}
