﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Clients.Http;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.<PERSON><PERSON><PERSON><PERSON>
{
    public class KiaFinalizeHandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IKiahooshanPromissoryService
    {
        private readonly IS3ServiceApiClient _s3ServiceApiClient;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly ILogger<KiaFinalizeHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public KiaFinalizeHandler(
            IOptions<BNPLOptions> bnplOptions,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IS3ServiceApiClient s3ServiceApiClient,
            IKiahooshanApiHttpClient kihooApi,
            IUploadGrpcClient uploadGrpcClient,
            IOptions<KiahooshanOptions> kihooOptions,
            ILogger<KiaFinalizeHandler> logger)
            : base(dbContext, userContext, kihooApi, kihooOptions)
        {
            _s3ServiceApiClient = s3ServiceApiClient;
            _uploadGrpcClient = uploadGrpcClient;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<object> HandleAsync(KiahooshanPromissoryContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KiahooshanPromissoryStatusV1.Finalize)
                throw new Exception("promissory not in Finalize status");

            ValidateContext(context);

            var signedpdfDlResult = await _uploadGrpcClient.GetPresignedUrlAsync(context.PromissorySignedDocumentFileId, context.ConsumerUserId, FileManager.Grpc.UploadTypeEnum.Misc, cancellationToken);
            if (!signedpdfDlResult.Succeeded)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "KiaFinalizeHandler; could not download signed promissory", false);
            }

            using Stream signedpdfStream = await _s3ServiceApiClient.DownloadAsync(signedpdfDlResult.SuccessResult, cancellationToken);

            var signedbytes = await signedpdfStream.ToByteArrayAsync();

            var dsc = await GetValidDigitalSignAsync(context.ConsumerUserId);

            var result = await _kihooApi.FinalizeAsync(
                new KiahooshanApiFinalizePromissoryRequest() 
                {
                    uniqueId = context.PromissoryUniqueId,
                    signedPdf = Convert.ToBase64String(signedbytes),
                    dsigUniqueId = dsc.SignatureUniqueId    
                }, context.OrderTrackingCode, cancellationToken);

            if (!result.Succeeded)
            {
                await ResetOrderPromissoryToRenewIssue(context);

                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), result.Message, result.Message != result.RawResponseContent);
            }


            var finalbytes = Convert.FromBase64String(result.SuccessResult.data.signedPdf);

            // upload file to storage
            string validName = Path.ChangeExtension(Path.GetRandomFileName(), ".pdf");

            var uresult = await _uploadGrpcClient.UploadUserDocumentAsync(finalbytes, Path.GetExtension(validName).TrimStart('.'), context.ConsumerUserId, cancellationToken);

            if (!uresult.Succeeded)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "KiaFinalizeHandler; could not upload final promissory to storage service", false);
            }

            context.Status = KiahooshanPromissoryStatusV1.Done;
            context.PromissoryTreasuryId = result.SuccessResult.data.treasury;
            context.PromissoryFinalDocumentFileId = uresult.SuccessResult;
            context.PromissoryFinalDocumentFileContentType = "application/pdf";

            //// save context
            await UpdateContextAsync(context);

            _logger.LogInformation(ApplicationConstants.PromissoryDoneLogMessageKey, context.OrderGuaranteeId, context.OrderId);

            return new DoneResult(JsonSerializer.Serialize(new { treasuryId = context.PromissoryTreasuryId, signedPdf = context.PromissoryFinalDocumentFileId }));
        }

        protected override void ValidateContext(KiahooshanPromissoryContextV1 context)
        {
            base.ValidateContext(context);

        }
    }
}
