﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class Order : BaseEntity<long>, IAuditableEntity
    {
        public Guid TrackingCode { get; set; }

        public int? PlanId { get; set; }

        public int MerchantUserId { get; set; }

        public string MerchantName { get; set; }

        // is unknown before kyc, borrower
        public int? ConsumerUserId { get; set; }

        public string ConsumerNationalHashId { get; set; }

        public string ConsumerName { get; set; }

        public string ConsumerUserName { get; set; }

        public string ConsumerIdentifier { get; set; }

        public string CreditLockId { get; set; }

        public decimal OrderTotalAmount { get; set; }
        public decimal CreditedAmount { get; set; }
        public decimal MerchantOperationCostAmount { get; set; }
        public decimal ConsumerOperationCostAmount { get; set; }
        public decimal PrePaymentAmount { get; set; }
        public decimal InstallmentsTotalRawAmount { get; set; }
        public decimal GuaranteeAmount { get; set; }

        public string ClientCallbackUrl { get; set; }
        public string ClientCancelUrl { get; set; }
        public string ClientRefId { get; set; }


        public bool PlanIsLocked { get; set; }

        /// <summary>
        /// only consider expiredate for orders that are not prepaid successfully
        /// set expiry with each credit locking (consider stamped orderplan on each resetting expire)
        /// </summary>
        public DateTimeOffset? ExpireDate { get; set; }

        public OrderPlan OrderPlan { get; set; }

        public OrderStatus Status { get; set; }

        public string Description { get; set; }

        public DateTimeOffset? DueDate { get; set; }

        public DateTimeOffset? LatestPrePaymentTryDate { get; set; }

        public bool GuaranteeRevoked { get; set; }

        public string SignedContractFileId { get; set; }

        public OrderCancellation OrderCancellation { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }

        public int? LastModifiedBy { get; set; }

        public Plan Plan { get; set; }
        public ICollection<Installment> Installments { get; } = new HashSet<Installment>();
        public ICollection<OrderCreditValidation> OrderCreditValidations { get; } = new HashSet<OrderCreditValidation>();
        public ICollection<OrderGuarantee> OrderGuarantees { get; } = new HashSet<OrderGuarantee>();
        public ICollection<OrderPayment> OrderPayments { get; } = new HashSet<OrderPayment>();
        public ICollection<OrderExternalEventOutbox> OrderExternalEventOutboxes { get; } = new HashSet<OrderExternalEventOutbox>();
        public ICollection<OrderTargetPlan> OrderTargetPlans { get; } = new HashSet<OrderTargetPlan>();


    }


}
