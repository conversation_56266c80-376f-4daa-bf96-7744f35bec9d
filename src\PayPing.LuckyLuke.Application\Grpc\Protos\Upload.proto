syntax = "proto3";

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "Grpc/Protos/common.proto";

option csharp_namespace = "PayPing.FileManager.Grpc";

package upload;

service Uploads {
  rpc AdminUpload (AdminUploadRequest) returns (AdminUploadResponse);
  rpc ReturnFilePath (ReturnFilePathRequest) returns (ReturnFilePathResponse);
}

message AdminUploadRequest {
  int32 UserId = 1;
  bytes File = 2;
  UploadTypeEnum UploadType = 3;
  google.protobuf.StringValue Extension = 4;
}

message AdminUploadResponse {
  google.protobuf.StringValue FileName = 1;
}

message ReturnFilePathRequest {
  int32 UserId = 1;
  UploadTypeEnum UploadType = 3;
  google.protobuf.StringValue FileName = 4;
}

message ReturnFilePathResponse {
  google.protobuf.StringValue FileName = 1;
}
