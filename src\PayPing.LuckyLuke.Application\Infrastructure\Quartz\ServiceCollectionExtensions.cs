﻿using Microsoft.Extensions.Configuration;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Quartz
{
    public static partial class ServiceCollectionExtensions
    {
        public static void AddJobAndUtcTrigger<T>(this IServiceCollectionQuartzConfigurator quartz, IConfiguration config, string groupName) where T : IJob
        {
            string jobName = typeof(T).Name;

            var configKey = $"{BackgroundJobOptions.SectionName}:{jobName}Cron";
            var cronSchedule = config[configKey];

            if (string.IsNullOrEmpty(cronSchedule))
            {
                throw new Exception($"No Quartz.NET Cron schedule found for job in configuration at {configKey}");
            }

            var jobKey = new JobKey(jobName, groupName);
            quartz.AddJob<T>(opts => opts.WithIdentity(jobKey));

            quartz.AddTrigger(opts => opts
                .ForJob(jobKey)
                .WithIdentity($"{jobName}-trigger")
                .WithCronSchedule(cronSchedule, x => x.InTimeZone(TimeZoneInfo.Utc))
                );
        }

        public static void AddJob<T>(this IServiceCollectionQuartzConfigurator quartz, IConfiguration config, string groupName) where T : IJob
        {
            string jobName = typeof(T).Name;

            var jobKey = new JobKey(jobName, groupName);
            quartz.AddJob<T>(opts => opts.WithIdentity(jobKey).StoreDurably(true));
        }

    }
}
