﻿using PayPing.LuckyLuke.Application.Infrastructure.Web;
using System.ComponentModel;

namespace PayPing.LuckyLuke.Application.Domain.Enums
{
    public enum OrderStatus
    {
        [Description("اولیه")]
        Init = 0,
        [Description("پلن انتخاب شده")]
        PlanLocked = 10,
        [Description("در حال استعلام رتبه اعتباری")]
        CreditValidationInProgress = 20,
        [Description("استعلام رتبه اعتباری ناموفق")]
        CreditValidationFailed = 21,
        [Description("استعلام رتبه اعتباری موفق")]
        CreditValidationSucceeded = 25,
        [Description("اطلاعات تکمیل شده")]
        ProfileFilled = 29,
        [Description("امضا دیجیتال ساخته شده")]
        DigitalSignatureCreated = 45,
        [Description("در حال صدور سفته")]
        GuaranteeInProgress = 30,
        [Description("صدور ناموفق سفته")]
        GuaranteeFailed = 31,
        [Description("صدور موفق سفته")]
        GuaranteeSucceeded = 35,
        [Description("قرارداد امضا شده")]
        ContractSigned = 75,
        [Description("لغو توسط خریدار")]
        CanceledByConsumer = 40,
        [Description("در حال پیش پرداخت")]
        PrePaymentInProgress = 50,
        [Description("پیش پرداخت ناموفق")]
        PrePaymentFailed = 51,
        [Description("تایید ناموفق پیش پرداخت")]
        PrePaymentVerifyFailed = 53,
        [Description("پیش پرداخت موفق")]
        PrePaymentSucceeded = 55,
        [Description("در انتظار پرداخت اقساط")]
        InstallmentsPaymentInProgress = 60,
        [Description("تعویق در پرداخت اقساط")]
        InstallmentsPaymentDelayed = 61,
        [Description("نکول در پرداخت اقساط")]
        InstallmentsPaymentDefaulted = 62,
        [Description("در انتظار کسر از حقوق")]
        InstallmentsWaitingForSalaryDeduction = 63,
        [Description("لغو توسط پذیرنده")]
        CanceledBeforePaymentByMerchant = 70,
        [Description("در انتظار لغو توسط پذیرنده")]
        SemiCanceledAfterPaymentByMerchant = 71,
        [Description("لغو توسط پذیرنده")]
        FullyCanceledAfterPaymentByMerchant = 72,
        [Description("تسویه شده")]
        PaidOff = 100
    }

    public static class OrderStatusProvider
    {
        public static OrderStatus[] GetExpirable() => [
            OrderStatus.Init,
            OrderStatus.PlanLocked,
            OrderStatus.CreditValidationInProgress,
            OrderStatus.CreditValidationFailed,
            OrderStatus.CreditValidationSucceeded,
            OrderStatus.ProfileFilled,
            OrderStatus.DigitalSignatureCreated,
            OrderStatus.GuaranteeInProgress,
            OrderStatus.GuaranteeFailed,
            OrderStatus.GuaranteeSucceeded,
            OrderStatus.ContractSigned,
            OrderStatus.PrePaymentInProgress,
            OrderStatus.PrePaymentFailed,
            OrderStatus.PrePaymentVerifyFailed
        ];

        public static OrderStatus[] GetExpirableWithPlan() => [
            OrderStatus.PlanLocked,
            OrderStatus.CreditValidationInProgress,
            OrderStatus.CreditValidationFailed,
            OrderStatus.CreditValidationSucceeded,
            OrderStatus.ProfileFilled,
            OrderStatus.DigitalSignatureCreated,
            OrderStatus.GuaranteeInProgress,
            OrderStatus.GuaranteeFailed,
            OrderStatus.GuaranteeSucceeded,
            OrderStatus.ContractSigned,
            OrderStatus.PrePaymentInProgress,
            OrderStatus.PrePaymentFailed,
            OrderStatus.PrePaymentVerifyFailed
        ];

        public static OrderStatus[] GetNonExpirableInProgress() => [
            OrderStatus.PrePaymentSucceeded,
            OrderStatus.InstallmentsPaymentInProgress,
            OrderStatus.InstallmentsPaymentDelayed,
            OrderStatus.InstallmentsPaymentDefaulted,
            OrderStatus.InstallmentsWaitingForSalaryDeduction,
        ];

        public static OrderStatus[] GetConsumerCancelable() => GetExpirable();
        public static OrderStatus[] GetMerchantBeforePaymentCancelable() => GetExpirable();
        public static OrderStatus[] GetMerchantAfterPaymentCancelable() => [
            OrderStatus.PrePaymentSucceeded
            ];
        public static OrderStatus[] GetMerchantAfterPaymentCancelableOrSemiCanceled() => [
            OrderStatus.PrePaymentSucceeded,
            OrderStatus.SemiCanceledAfterPaymentByMerchant,
            ];

        public static OrderStatus[] GetJobCancelable() => GetExpirable();

        public static OrderStatus[] GetNotInProgress() => [
            OrderStatus.CanceledByConsumer,
            OrderStatus.CanceledBeforePaymentByMerchant,
            OrderStatus.SemiCanceledAfterPaymentByMerchant,
            OrderStatus.FullyCanceledAfterPaymentByMerchant,
            OrderStatus.PaidOff
        ];

        public static OrderStatus[] GetCanceled() => [
            OrderStatus.CanceledByConsumer,
            OrderStatus.CanceledBeforePaymentByMerchant,
            OrderStatus.SemiCanceledAfterPaymentByMerchant,
            OrderStatus.FullyCanceledAfterPaymentByMerchant,
        ];

        public static OrderStatus[] GetAllMerchantCanceled() => [
            OrderStatus.CanceledBeforePaymentByMerchant,
            OrderStatus.SemiCanceledAfterPaymentByMerchant,
            OrderStatus.FullyCanceledAfterPaymentByMerchant,
        ];

        public static OrderStatus[] GetPrePaymentNonExpirableAcceptable() => [
            OrderStatus.GuaranteeSucceeded,
            OrderStatus.ContractSigned,
            OrderStatus.PrePaymentFailed,
            OrderStatus.PrePaymentVerifyFailed
            ];

        public static OrderStatus[] GetNoneGuaranteeContractLessPrePaymentNonExpirableAcceptable() => [
            OrderStatus.CreditValidationSucceeded,
            OrderStatus.PrePaymentFailed,
            OrderStatus.PrePaymentVerifyFailed
            ];

        public static OrderStatus[] GetPrePaymentExpirableAcceptable() => [
            OrderStatus.PrePaymentInProgress
            ];

        public static OrderStatus[] GetPrePaid() => [
            OrderStatus.PrePaymentSucceeded,
            OrderStatus.InstallmentsPaymentInProgress,
            OrderStatus.InstallmentsPaymentDelayed,
            OrderStatus.InstallmentsPaymentDefaulted,
            OrderStatus.InstallmentsWaitingForSalaryDeduction,
            OrderStatus.PaidOff
        ];

        public static OrderStatus[] GetPrePaidOrMerchantCanceled() => [
            OrderStatus.PrePaymentSucceeded,
            OrderStatus.InstallmentsPaymentInProgress,
            OrderStatus.InstallmentsPaymentDelayed,
            OrderStatus.InstallmentsPaymentDefaulted,
            OrderStatus.InstallmentsWaitingForSalaryDeduction,
            OrderStatus.PaidOff,
            OrderStatus.CanceledBeforePaymentByMerchant,
            OrderStatus.SemiCanceledAfterPaymentByMerchant,
            OrderStatus.FullyCanceledAfterPaymentByMerchant,
        ];

        // this should not let InstallmentsWaitingForSalaryDeduction get into payment
        public static OrderStatus[] GetInstallmentPaymentAcceptable() => [
            OrderStatus.PrePaymentSucceeded,
            OrderStatus.InstallmentsPaymentInProgress,
            OrderStatus.InstallmentsPaymentDelayed,
            OrderStatus.InstallmentsPaymentDefaulted,
       ];

        public static OrderStatus[] GetInstallmentDueJobAcceptable() => [
            OrderStatus.PrePaymentSucceeded,
            OrderStatus.InstallmentsPaymentInProgress,
            OrderStatus.InstallmentsPaymentDelayed,
            OrderStatus.InstallmentsPaymentDefaulted,
            OrderStatus.InstallmentsWaitingForSalaryDeduction,
       ];
        public static OrderStatus[] GetInstallmentDelayedJobAcceptable() => [
            OrderStatus.PrePaymentSucceeded,
            OrderStatus.InstallmentsPaymentInProgress,
            OrderStatus.InstallmentsPaymentDelayed,
            OrderStatus.InstallmentsPaymentDefaulted,
            // salary deduction should be in this list, because of there might be future delayed installments with this order status
            OrderStatus.InstallmentsWaitingForSalaryDeduction,
       ];
        public static OrderStatus[] GetInstallmentDefaultedJobAcceptable() => [
            OrderStatus.PrePaymentSucceeded,
            OrderStatus.InstallmentsPaymentInProgress,
            OrderStatus.InstallmentsPaymentDelayed,
            OrderStatus.InstallmentsPaymentDefaulted,
       ];

        public static OrderStatus[] GetPaidOff() => [
            OrderStatus.PaidOff
        ];
        
        public static OrderStatus[] GetNotPaidOff() => [
            OrderStatus.PrePaymentSucceeded,
            OrderStatus.InstallmentsPaymentInProgress,
            OrderStatus.InstallmentsPaymentDelayed,
            OrderStatus.InstallmentsPaymentDefaulted,
            OrderStatus.InstallmentsWaitingForSalaryDeduction,
        ];
        
        public static OrderStatus[] GetDelayed() => [
            OrderStatus.InstallmentsPaymentDelayed,
            OrderStatus.InstallmentsWaitingForSalaryDeduction,
        ];

        public static OrderStatus[] GetDefaulted() => [
            OrderStatus.InstallmentsPaymentDefaulted
        ];

        public static OrderSteps ToOrderStep(this OrderStatus status)
        {
            return status switch
            {
                OrderStatus.Init => OrderSteps.PlanList,

                OrderStatus.PlanLocked or
                OrderStatus.CreditValidationInProgress or
                OrderStatus.CreditValidationFailed => OrderSteps.CreditScore,

                OrderStatus.CreditValidationSucceeded => OrderSteps.FillProfile,

                OrderStatus.ProfileFilled or
                OrderStatus.DigitalSignatureCreated => OrderSteps.Unknown,

                // these will be resolved in provider
                OrderStatus.GuaranteeFailed or
                OrderStatus.GuaranteeInProgress => OrderSteps.Unknown,

                // this will be resolved in contract setting
                OrderStatus.GuaranteeSucceeded => OrderSteps.Unknown,

                OrderStatus.ContractSigned or
                OrderStatus.PrePaymentFailed or
                OrderStatus.PrePaymentInProgress or
                OrderStatus.PrePaymentVerifyFailed => OrderSteps.Quote,

                OrderStatus.PrePaymentSucceeded or
                OrderStatus.InstallmentsPaymentDefaulted or
                OrderStatus.InstallmentsWaitingForSalaryDeduction or
                OrderStatus.InstallmentsPaymentDelayed or
                OrderStatus.InstallmentsPaymentInProgress => OrderSteps.GetInstallments,

                OrderStatus.PaidOff => OrderSteps.PaidOff,

                OrderStatus.SemiCanceledAfterPaymentByMerchant => OrderSteps.SemiCanceled,

                OrderStatus.CanceledByConsumer or 
                OrderStatus.CanceledBeforePaymentByMerchant or 
                OrderStatus.FullyCanceledAfterPaymentByMerchant => OrderSteps.Canceled,

                _ => throw new Exception($"At ToOrderStep, wrong status detected. status: {status}")
            };
        }

        public static MerchantOrderSteps ToMerchantOrderStep(this OrderStatus status)
        {
            return status switch
            {
                OrderStatus.Init or
                OrderStatus.PlanLocked or
                OrderStatus.CreditValidationInProgress or
                OrderStatus.CreditValidationFailed or
                OrderStatus.CreditValidationSucceeded or
                OrderStatus.ProfileFilled or
                OrderStatus.DigitalSignatureCreated or
                OrderStatus.GuaranteeFailed or
                OrderStatus.GuaranteeInProgress or
                OrderStatus.GuaranteeSucceeded or
                OrderStatus.ContractSigned or
                OrderStatus.PrePaymentFailed or
                OrderStatus.PrePaymentInProgress or
                OrderStatus.PrePaymentVerifyFailed => MerchantOrderSteps.Init,

                OrderStatus.PrePaymentSucceeded or
                OrderStatus.InstallmentsPaymentDefaulted or
                OrderStatus.InstallmentsWaitingForSalaryDeduction or
                OrderStatus.InstallmentsPaymentDelayed or
                OrderStatus.InstallmentsPaymentInProgress => MerchantOrderSteps.Payment,

                OrderStatus.PaidOff => MerchantOrderSteps.PaidOff,

                OrderStatus.CanceledByConsumer or 
                OrderStatus.CanceledBeforePaymentByMerchant or 
                OrderStatus.FullyCanceledAfterPaymentByMerchant => MerchantOrderSteps.Canceled,

                OrderStatus.SemiCanceledAfterPaymentByMerchant => MerchantOrderSteps.Unknown,

                _ => throw new Exception($"At ToMerchantOrderStep, wrong status detected. status: {status}")
            };
        }

        public static ConsumerOrderBriefStatus ToConsumerOrderBriefStatus(this OrderStatus status)
        {
            return status switch
            {
                OrderStatus.Init or
                OrderStatus.PlanLocked or
                OrderStatus.CreditValidationInProgress or
                OrderStatus.CreditValidationFailed or
                OrderStatus.CreditValidationSucceeded or
                OrderStatus.ProfileFilled or
                OrderStatus.DigitalSignatureCreated or
                OrderStatus.GuaranteeFailed or
                OrderStatus.GuaranteeInProgress or
                OrderStatus.GuaranteeSucceeded or
                OrderStatus.ContractSigned or
                OrderStatus.PrePaymentFailed or
                OrderStatus.PrePaymentInProgress or
                OrderStatus.PrePaymentVerifyFailed => ConsumerOrderBriefStatus.Init,

                OrderStatus.PrePaymentSucceeded or
                OrderStatus.InstallmentsPaymentDefaulted or
                OrderStatus.InstallmentsWaitingForSalaryDeduction or
                OrderStatus.InstallmentsPaymentDelayed or
                OrderStatus.InstallmentsPaymentInProgress => ConsumerOrderBriefStatus.PrePaid,

                OrderStatus.PaidOff => ConsumerOrderBriefStatus.PaidOff,

                OrderStatus.CanceledByConsumer or
                OrderStatus.CanceledBeforePaymentByMerchant or
                OrderStatus.FullyCanceledAfterPaymentByMerchant => ConsumerOrderBriefStatus.Canceled,

                OrderStatus.SemiCanceledAfterPaymentByMerchant => ConsumerOrderBriefStatus.SemiCanceled,

                _ => throw new Exception($"At ConsumerOrderBriefStatus, wrong status detected. status: {status}")
            };
        }
    }
}
