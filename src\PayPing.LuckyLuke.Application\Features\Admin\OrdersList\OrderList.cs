﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Admin.OrdersList
{
    public record OrderListRequest(int MerchantId,
                                   DateTime? FromDate,
                                   DateTime? ToDate,
                                   string consumerName,
                                   string mobile,
                                   decimal? creditAmount,
                                   Guid? trackingCode,
                                   OrderStatusRequest? orderStatusRequest,
                                   int PageSize = 10,
                                   int PageNumber = 1);

    public enum OrderStatusRequest
    {
        [Description("تکمیل نشده")]
        NotPrepaid = 0,
        [Description("در حال پرداخت (جاری)")]
        Paying = 1,
        [Description("معوق")]
        Delayed = 2,
        [Description("نکول شده")]
        Defaulted = 3,
        [Description("لغو شده توسط پذیرنده")]
        CanceledByMerchant = 4,
        [Description("در انتظار کسر از حقوق")]
        WaitingForSalaryDeduction = 5,
        [Description("تسویه شده")]
        PaidOff = 6,
    }

    public class OrderListResponse
    {
        public OrderListResponse(List<OrderInfo> orders, long total)
        {
            Orders = orders;
            Total = total;
        }

        public long Total { get; set; }

        [Required]
        public List<OrderInfo> Orders { get; set; }
    }

    public class OrderSelectDto
    {

        public long OrderId { get; set; }
        public DateTimeOffset CreatedDate { get; set; }
        public decimal CreditAmount { get; set; }

        public int? ConsumerUserId { get; set; }
        public string ConsumerName { get; set; }
        public string ConsumerMobile { get; set; }

        public int PaidInstallmentCount { get; set; }
        public int RemainedInstallmentCount { get; set; }
        public decimal PaidInstallmentAmount { get; set; }
        public decimal RemainedInstallmentAmount { get; set; }
        public decimal TotalInterest { get; set; }
        public OrderStatus Status { get; set; }
        public string RefundRefId { get; set; }
        public bool RefundBalanceEnough { get; set; }

    }

    public class OrderInfo
    {

        public long OrderId { get; set; }
        public DateTimeOffset CreatedDate { get; set; }
        public decimal CreditAmount { get; set; }

        public int? ConsumerUserId { get; set; }
        public string ConsumerName { get; set; }
        public string ConsumerMobile { get; set; }

        public int PaidInstallmentCount { get; set; }
        public int RemainedInstallmentCount { get; set; }
        public decimal PaidInstallmentAmount { get; set; }
        public decimal RemainedInstallmentAmount { get; set; }
        public decimal TotalInterest { get; set; }
        public MerchantOrderSteps orderStep { get; set; }
        public string RefundRefId { get; set; }
        public bool RefundBalanceEnough { get; set; }

    }

    public class OrderListEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                AdminRoutes.OrderList,
                async (
                    [AsParameters] OrderListRequest request,
                    IOrderListRequestHandler handler,
                    IValidator<OrderListRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var result = await handler.HandleAsync(request, cancellationToken);
                    return Results.Ok(result);
                })
                .RequireAuthorization("admin")
                .WithName("OrderList")
                .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
                .Produces<OrderListResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Order List")
                .WithDescription("Order List")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OrderListRequestHandler : IOrderListRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserService _userService;

        public OrderListRequestHandler(ApplicationDbContext dbContext, IUserService userService)
        {
            _dbContext = dbContext;
            _userService = userService;
        }

        public async ValueTask<OrderListResponse> HandleAsync(OrderListRequest request, CancellationToken cancellationToken)
        {
            var query = BuildOrderQuery(request);
            var total = await GetTotalAsync(query);
            var orders = await GetPagedOrdersAsync(query, request, cancellationToken);

            return new OrderListResponse(orders, total);
        }

        private IQueryable<Order> BuildOrderQuery(OrderListRequest request)
        {


            var query = _dbContext.Orders.AsNoTracking().Where(o => o.MerchantUserId == request.MerchantId);

            switch (request.orderStatusRequest)
            {
                case OrderStatusRequest.NotPrepaid:
                    var expirableWithPlanSet = OrderStatusProvider.GetExpirableWithPlan();
                    query = query.Where(o => expirableWithPlanSet.Contains(o.Status));
                    break;
                case OrderStatusRequest.Paying:
                    query = query.Where(o => o.Status == OrderStatus.PrePaymentSucceeded || o.Status == OrderStatus.InstallmentsPaymentInProgress);
                    break;
                case OrderStatusRequest.Delayed:
                    query = query.Where(o => o.Status == OrderStatus.InstallmentsPaymentDelayed);
                    break;
                case OrderStatusRequest.Defaulted:
                    query = query.Where(o => o.Status == OrderStatus.InstallmentsPaymentDefaulted);
                    break;
                case OrderStatusRequest.CanceledByMerchant:
                    var mac = OrderStatusProvider.GetAllMerchantCanceled();
                    query = query.Where(o => mac.Contains(o.Status));
                    break;
                case OrderStatusRequest.WaitingForSalaryDeduction:
                    query = query.Where(o => o.Status == OrderStatus.InstallmentsWaitingForSalaryDeduction);
                    break;
                case OrderStatusRequest.PaidOff:
                    query = query.Where(o => o.Status == OrderStatus.PaidOff);
                    break;
                default:
                    query = query.Where(o => o.Status != OrderStatus.Init && o.Status != OrderStatus.CanceledByConsumer);
                    break;
            }

            if (request.FromDate.HasValue)
            {
                query = query.Where(o => o.CreatedAt >= request.FromDate.Value);
            }

            if (request.ToDate.HasValue)
            {
                query = query.Where(o => o.CreatedAt <= request.ToDate.Value);
            }

            if (!string.IsNullOrEmpty(request.consumerName))
            {
                query = query.Where(o => o.ConsumerUserId.HasValue && o.ConsumerName.Contains(request.consumerName));
            }

            if (!string.IsNullOrEmpty(request.mobile))
            {
                query = query.Where(o => o.ConsumerUserName == request.mobile);
            }

            if (request.creditAmount.HasValue)
            {
                query = query.Where(o => o.CreditedAmount == request.creditAmount);
            }

            if (request.trackingCode != null)
            {
                query = query.Where(o => o.TrackingCode == request.trackingCode);
            }

            return query;
        }

        private async ValueTask<long> GetTotalAsync(IQueryable<Order> query)
        {
            return await query.LongCountAsync();
        }

        private async ValueTask<List<OrderInfo>> GetPagedOrdersAsync(IQueryable<Order> query, OrderListRequest request, CancellationToken cancellationToken)
        {
            var results = new List<OrderInfo>();

            var orders = await query
                .OrderByDescending(o => o.CreatedAt)
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .Select(o => new OrderSelectDto
                {
                    OrderId = o.Id,
                    ConsumerUserId = o.ConsumerUserId,
                    CreatedDate = o.CreatedAt,
                    CreditAmount = o.CreditedAmount,
                    PaidInstallmentCount = o.Installments.Where(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff).Count(),
                    RemainedInstallmentCount = o.Installments.Where(i => i.Status != InstallmentStatus.PaidOff && i.Status != InstallmentStatus.SalaryDeductionPaidOff).Count(),
                    PaidInstallmentAmount = o.Installments.Where(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff).Sum(i => i.FinalAmount),
                    RemainedInstallmentAmount = o.Installments.Where(i => i.Status != InstallmentStatus.PaidOff && i.Status != InstallmentStatus.SalaryDeductionPaidOff).Sum(i => i.FinalAmount),
                    TotalInterest = o.Installments.Where(i => true).Sum(i => i.InterestAmount),
                    ConsumerName = o.ConsumerName,
                    ConsumerMobile = o.ConsumerUserName,
                    Status = o.Status,
                    RefundRefId = o.OrderCancellation.RefundRefId,
                    RefundBalanceEnough = o.OrderCancellation.IsRefundBalanceEnough,
                })
                .ToListAsync(cancellationToken);

            foreach (var order in orders)
            {
                MerchantOrderSteps orderStep = order.Status.ToMerchantOrderStep();
                if (orderStep == MerchantOrderSteps.Unknown)
                {
                    if (order.RefundRefId.HasValue())
                    {
                        if (order.RefundBalanceEnough)
                        {
                            orderStep = MerchantOrderSteps.SemiCanceledRefundInProgress;
                        }
                        else
                        {
                            orderStep = MerchantOrderSteps.SemiCanceledRefundBalanceDeficit;
                        }
                    }
                    else
                    {
                        orderStep = MerchantOrderSteps.SemiCanceledRefundInactive;
                    }
                }

                results.Add(new OrderInfo
                {
                    OrderId = order.OrderId,
                    ConsumerUserId = order.ConsumerUserId,
                    CreatedDate = order.CreatedDate,
                    CreditAmount = order.CreditAmount,
                    PaidInstallmentCount = order.PaidInstallmentCount,
                    RemainedInstallmentCount = order.RemainedInstallmentCount,
                    PaidInstallmentAmount = order.PaidInstallmentAmount,
                    RemainedInstallmentAmount = order.RemainedInstallmentAmount,
                    TotalInterest = order.TotalInterest,
                    ConsumerName = order.ConsumerName,
                    ConsumerMobile = order.ConsumerMobile,
                    orderStep = orderStep,
                    RefundRefId = order.RefundRefId,
                    RefundBalanceEnough = order.RefundBalanceEnough,
                });
            }

            return results;
        }
    }

    public interface IOrderListRequestHandler
    {
        ValueTask<OrderListResponse> HandleAsync(OrderListRequest request, CancellationToken cancellationToken);
    }

    public class OrderListValidator : AbstractValidator<OrderListRequest>
    {
        public OrderListValidator()
        {
            RuleFor(x => x.MerchantId).NotEmpty();
            RuleFor(x => x.consumerName).MinimumLength(3).When(x => !string.IsNullOrWhiteSpace(x.consumerName));
            RuleFor(x => x.PageSize).GreaterThan(0);
            RuleFor(x => x.PageNumber).GreaterThan(0);
        }
    }
}


