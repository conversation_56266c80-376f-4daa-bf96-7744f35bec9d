﻿using Google.Protobuf;
using Grpc.Net.Client;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.FileManager.Grpc;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.Tools.SdkBase.Types;


namespace PayPing.LuckyLuke.Application.Shared.Clients.Grpc
{
    public class UploadGrpcClient : IUploadGrpcClient
    {
        private readonly Uploads.UploadsClient _client;
        private readonly ILogger<UploadGrpcClient> _logger;

        public UploadGrpcClient(Uploads.UploadsClient client, ILogger<UploadGrpcClient> logger)
        {
            _client = client;
            _logger = logger;
        }

        public async ValueTask<ServiceResult<string>> UploadExcelAsync(Stream stream, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var byteString = await ByteString.FromStreamAsync(stream, cancellationToken);
                var uploadResponse = await _client.AdminUploadAsync(new AdminUploadRequest
                {
                    UserId = userId,
                    UploadType = UploadTypeEnum.BnplorderListExportExcel,
                    File = byteString,
                    Extension = "xlsx"
                }, cancellationToken: cancellationToken);

                var filePathResponse = await _client.ReturnFilePathAsync(new ReturnFilePathRequest
                {
                    UserId = userId,
                    UploadType = UploadTypeEnum.BnplorderListExportExcel,
                    FileName = uploadResponse.FileName
                }, cancellationToken: cancellationToken);

                return new ServiceResult<string>
                {
                    Succeeded = true,
                    SuccessResult = filePathResponse.FileName,
                    RawResponseContent = filePathResponse.FileName
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc Upload Excel failed for merchant user: {userId}");
                return new ServiceResult<string>
                {
                    Succeeded = false,
                    Message = ex.Message,
                };
            }
            
        }

        public async ValueTask<ServiceResult<string>> UploadUserDocumentAsync(byte[] file, string extension, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                using var stream = new MemoryStream(file);
                var byteString = await ByteString.FromStreamAsync(stream, cancellationToken);
                var uploadResponse = await _client.AdminUploadAsync(new AdminUploadRequest
                {
                    UserId = userId,
                    UploadType = UploadTypeEnum.Misc,
                    File = byteString,
                    Extension = extension.Replace(".", "")
                }, cancellationToken: cancellationToken);

                return new ServiceResult<string>
                {
                    Succeeded = true,
                    SuccessResult = uploadResponse.FileName,
                    RawResponseContent = uploadResponse.FileName
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc UploadSignatureImageAsync failed for merchant user: {userId}");
                return new ServiceResult<string>
                {
                    Succeeded = false,
                    Message = ex.Message,
                };
            }

        }

        public async ValueTask<ServiceResult<string>> GetPresignedUrlAsync(string fileName, int userId, UploadTypeEnum uploadType, CancellationToken cancellationToken = default)
        {
            try
            {
                var filePathResponse = await _client.ReturnFilePathAsync(new ReturnFilePathRequest
                {
                    UserId = userId,
                    UploadType = uploadType,
                    FileName = fileName
                }, cancellationToken: cancellationToken);

                return new ServiceResult<string>
                {
                    Succeeded = true,
                    SuccessResult = filePathResponse.FileName,
                    RawResponseContent = filePathResponse.FileName
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc GetPresignedUrlAsync failed for merchant user: {userId}");
                return new ServiceResult<string>
                {
                    Succeeded = false,
                    Message = ex.Message,
                };
            }

        }

    }

}
