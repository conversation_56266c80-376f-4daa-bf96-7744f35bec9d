﻿using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public class NationalCodeToOwnerResponseModel
    {
        // this needs padleft(0) if used
        public string NationalCode { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string FatherName { get; set; }

        public string BirthDate { get; set; }

        public bool Alive { get; set; }
    }

}
