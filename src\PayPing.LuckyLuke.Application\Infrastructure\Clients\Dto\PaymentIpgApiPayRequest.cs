﻿using Asp.Versioning;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto
{
    public record PaymentIpgApiPayRequest
    {
        public PaymentIpgApiPayRequest(int userId, string returnUrl, string payerName, string description, string clientRefId, string clientId, int amount, string payerIdentity)
        {
            UserId = userId;
            ReturnUrl = returnUrl;
            PayerName = payerName;
            Description = description;
            ClientRefId = clientRefId;
            ClientId = clientId;
            Amount = amount;
            PayerIdentity = payerIdentity;
            Version = 1; // PayPing.Ipg.ModelLayer.Helper.ApiVersion.v_2
        }

        public int UserId { get; set; }
        public string ReturnUrl { get; set; }
        public string PayerName { get; set; }
        public string Description { get; set; }
        public string ClientRefId { get; set; }
        public string ClientId { get; set; }
        public int Amount { get; set; }
        public string PayerIdentity { get; set; }
        public int Version { get; set; }
    }
    public record PaymentIpgApiVerifyRequest(string RefId, int Amount);

}
