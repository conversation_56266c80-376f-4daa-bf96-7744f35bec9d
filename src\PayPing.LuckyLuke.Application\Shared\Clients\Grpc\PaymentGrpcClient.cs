﻿using Grpc.Net.Client;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PaymentGrpc;
using Microsoft.Extensions.Options;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using Microsoft.Extensions.Logging;
using Grpc.Core;

namespace PayPing.LuckyLuke.Application.Shared.Clients.Grpc
{
    public class PaymentGrpcClient : IPaymentGrpcClient
    {
        private readonly PaymentService.PaymentServiceClient _client;
        private readonly ILogger<PaymentGrpcClient> _logger;

        public PaymentGrpcClient(PaymentService.PaymentServiceClient client, ILogger<PaymentGrpcClient> logger)
        {
            _client = client;
            _logger = logger;
        }

        public async ValueTask<PaymentGrpcResponse> CreatePaymentAsync(PaymentGrpcRequest request)
        {
            var payRequest = new CreateSimplePaymentRequest()
            {
                Amount = request.amount,
                ClientRefId = request.clientRefId,
                Description = request.description,
                MerchantWage = request.merchantWage,
                PayerName = request.payerName,
                PayerWage = request.payerWage,
                ReturnUrl = request.returnUrl,
                ServiceType = ServiceType.PaypingBnpl,
                UserId = request.userId,
                PayerIdentity = request.payerIdentity,
            };
            try
            {
                var payResponse = await _client.CreateSimplePaymentWithAdditionalWageAsync(payRequest, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return new PaymentGrpcResponse(payResponse.PaymentCode, payResponse.Url, payResponse.Amount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc create payment failed for merchant user: {request.userId}");
                return null;
            }
        }

        public async ValueTask<PaymentVerifyGrpcResponse> VerifyPaymentAsync(PaymentVerifyGrpcRequest request)
        {
            var vRequest = new VerifyPaymentRequest()
            {
                PaymentRefId = request.paymentRefId,
                UserId = request.userId,
                ServiceType = ServiceType.PaypingBnpl,
                PaymentCode = request.paymentCode,
            };
            try
            {
                var verifyResponse = await _client.VerifyPaymentAsync(vRequest, new Metadata() { { "x-client-usage", "Payping_BnplClient" } });
                return new PaymentVerifyGrpcResponse(verifyResponse.Amount, verifyResponse.CardNumber, verifyResponse.CardHashPan, verifyResponse.PayedDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"grpc verify payment failed for merchant user: {request.userId}");
                return null;
            }

        }
    }
}
