﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class EventLog : BaseEntity<long>
    {
        public string RefId { get; set; }

        public EventLogType LogType { get; set; }

        public OrderStatus? Status { get; set; }

        public int UserId { get; set; }

        public int Code { get; set; }

        public string Name { get; set; }

        public string Message { get; set; }

    }


}
