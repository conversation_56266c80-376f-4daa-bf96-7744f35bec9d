﻿using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Shared.Abstractions;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class NoGuaranteeContractMandatoryStepsProvider : IContractMandatoryStepsProvider
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IEnumerable<IDigitalSignProvider> _digitalSignProviders;

        public NoGuaranteeContractMandatoryStepsProvider(ApplicationDbContext dbContext, IGuaranteeService guaranteeService, IEnumerable<IDigitalSignProvider> digitalSignProviders)
        {
            _dbContext = dbContext;
            _guaranteeService = guaranteeService;
            _digitalSignProviders = digitalSignProviders;
        }

        public GuaranteeType GuaranteeType => GuaranteeType.None;

        public async ValueTask<OrderStepsDto> CreateContractMandatorySteps(Order order, int userId)
        {
            OrderSteps nextCode = order.Status.ToOrderStep();

            var result = new OrderStepsDto(nextCode, PreGuaranteeSteps);

            // ConsumerUserId, Which is set on Order, related dynamic steps 
            if (order.PlanIsLocked)
            {
                ConsumerInfo consumerInfo = await _dbContext.ConsumerInfos.Where(x => x.ConsumerUserId == userId).FirstOrDefaultAsync();

                var dt = await _guaranteeService.GetGuarantorCompliantSignatureTypeByGuaranteeType(GuaranteeType, order.MerchantUserId, CancellationToken.None);
                var provider = ChooseProvider(dt);

                bool hasSignature = await provider.UserHasValidDigitalSignatureAsync(userId);

                var dsStep = provider.GetDigitalSignStep();

                if (!hasSignature)
                {
                    result.Steps.Add(dsStep);
                }

                if (order.Status == OrderStatus.ProfileFilled)
                {
                    if (hasSignature)
                    {
                        result.Next = OrderSteps.Quote;
                    }
                    else
                    {
                        result.Next = dsStep.Code;
                    }
                }
                else if (order.Status == OrderStatus.DigitalSignatureCreated)
                {
                    result.Next = OrderSteps.Quote;
                }
                else if (order.Status == OrderStatus.GuaranteeFailed || order.Status == OrderStatus.GuaranteeInProgress)
                {
                    throw new InvalidOperationException("no guarantee contract mandatory type of order should not be in GuaranteeInProgress or GuaranteeFailed step");
                }
            }

            result.Steps.Add(new("امضا قرارداد", OrderSteps.SignContract, true));

            if (order.Status == OrderStatus.GuaranteeSucceeded)
            {
                result.Next = OrderSteps.SignContract;
            }


            result.Steps.AddRange(PostContractSteps);

            foreach (var step in result.Steps)
            {
                if ((int)result.Next > (int)step.Code)
                {
                    step.IsPassed = true;
                }
            }

            return result;
        }

        private List<OrderStepDto> PreGuaranteeSteps => new List<OrderStepDto>()
        {
            new ("انتخاب طرح اعتباری", OrderSteps.PlanList, false),
            new ("رتبه سنجی اعتباری", OrderSteps.CreditScore, true),
            new ("تکمیل اطلاعات", OrderSteps.FillProfile, true),
        };

        private List<OrderStepDto> PostContractSteps => new List<OrderStepDto>()
        {
            new ("پیش فاکتور", OrderSteps.Quote, true),
            new ("اقساط", OrderSteps.GetInstallments, false),
            new ("تسویه شده", OrderSteps.PaidOff, false),
            new ("لغو شده", OrderSteps.Canceled, false)
        };

        private IDigitalSignProvider ChooseProvider(DigitalSignType digitalSignType)
        {
            return _digitalSignProviders.First(f => f.DigitalSignType == digitalSignType);
        }
    }
}
