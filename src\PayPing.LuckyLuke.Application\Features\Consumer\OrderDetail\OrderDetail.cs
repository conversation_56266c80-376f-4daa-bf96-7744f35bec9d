﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Consumer.OrderDetail
{
    public record OrderDetailRequest(Guid trackingCode);

    public class OrderDetailResponse
    {
        public OrderDetailResponse(DateTimeOffset orderCreatedDate, decimal creditAmount, int paidInstallmentCount, int totalInstallmentCount, decimal paidInstallmentAmount, decimal remainedInstallmentAmount, decimal monthlyInstallmentTotalAmount, decimal interestRate, decimal interestSumAmount, decimal totalPaymentAmount, string merchantName, ConsumerOrderBriefStatus status)
        {
            OrderCreatedDate = orderCreatedDate;
            CreditAmount = creditAmount;
            PaidInstallmentCount = paidInstallmentCount;
            TotalInstallmentCount = totalInstallmentCount;
            PaidInstallmentAmount = paidInstallmentAmount;
            RemainedInstallmentAmount = remainedInstallmentAmount;
            MonthlyInstallmentTotalAmount = monthlyInstallmentTotalAmount;
            InterestRate = interestRate;
            InterestSumAmount = interestSumAmount;
            TotalPaymentAmount = totalPaymentAmount;
            MerchantName = merchantName;
            Status = status;
        }

        public DateTimeOffset OrderCreatedDate { get; set; }
        public decimal CreditAmount { get; set; }

        public int PaidInstallmentCount { get; set; }
        public int TotalInstallmentCount { get; set; }

        public decimal PaidInstallmentAmount { get; set; }
        public decimal RemainedInstallmentAmount { get; set; }

        public decimal MonthlyInstallmentTotalAmount { get; set; }
        public decimal InterestRate { get; set; }
        public decimal InterestSumAmount { get; set; }
        public decimal TotalPaymentAmount { get; set; }

        public decimal DiscountOnPayOff { get; set; }

        public string MerchantName { get; set; }

        public ConsumerOrderBriefStatus Status { get; set; }

        public DateTimeOffset? CanceledByMerchantDate { get; set; }
        public decimal? CanceledByMerchantAmount { get; set; }

    }

    public class OrderDetailEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                    ConsumerRoutes.OrderDetail,
                    async (
                        [AsParameters] OrderDetailRequest request,
                        IOrderDetailRequestHandler handler,
                        IValidator<OrderDetailRequest> validator,
                        CancellationToken cancellationToken) =>
                    {
                        var validationResult = await validator.ValidateAsync(request, cancellationToken);
                        if (!validationResult.IsValid)
                        {
                            throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                        }

                        var result = await handler.HandleAsync(request, cancellationToken);
                        return Results.Ok(result);
                    })
                .RequireAuthorization("read")
                .WithName("Dashboard OrderDetail")
                .WithApiVersionSet(builder.NewApiVersionSet("Dashboard").Build())
                .Produces<OrderDetailResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Dashboard Order Detail")
                .WithDescription("Dashboard Order Detail")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OrderDetailRequestHandler : IOrderDetailRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;

        public OrderDetailRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext)
        {
            _dbContext = dbContext;
            _userContext = userContext;
        }

        public async ValueTask<OrderDetailResponse> HandleAsync(
            OrderDetailRequest request,
            CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.AsNoTracking()
                .Where(o => o.TrackingCode == request.trackingCode && o.ConsumerUserId == userId)
                .Include(x => x.Installments)
                .FirstOrDefaultAsync(cancellationToken);

            if (order == null)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش یافت نشد", string.Empty);
            }

            return ToResponse(order);
        }

        public OrderDetailResponse ToResponse(Order order)
        {
            var result = new OrderDetailResponse(
                order.CreatedAt,
                order.CreditedAmount,
                order.Installments?.Where(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff).Count() ?? default,
                order.Installments?.Count() ?? default,
                order.Installments?.Where(i => i.Status == InstallmentStatus.PaidOff || i.Status == InstallmentStatus.SalaryDeductionPaidOff).Sum(i => i.FinalAmount) ?? default,
                order.Installments?.Where(i => i.Status != InstallmentStatus.PaidOff && i.Status != InstallmentStatus.SalaryDeductionPaidOff).Sum(i => i.FinalAmount) ?? default,
                order.Installments?.Select(i => i.TotalAmount).FirstOrDefault() ?? default,
                order.OrderPlan.InterestRate,
                order.Installments?.Sum(i => i.InterestAmount) ?? default,
                order.Installments?.Sum(i => i.FinalAmount) ?? default,
                order.MerchantName,
                order.Status.ToConsumerOrderBriefStatus());

            var merchcanceleds = OrderStatusProvider.GetAllMerchantCanceled();
            if (merchcanceleds.Contains(order.Status))
            {
                result.CanceledByMerchantDate = order.OrderCancellation.CanceledDate;
                if (order.Status == OrderStatus.FullyCanceledAfterPaymentByMerchant)
                {
                    result.CanceledByMerchantAmount = order.PrePaymentAmount;
                }
            }

            var payableInstallments = order.Installments?
                .Where(i => i.Status != InstallmentStatus.PaidOff && i.Status != InstallmentStatus.SalaryDeductionPaidOff && i.PaymentStatus != PaymentStatus.PaymentSucceeded)
                .OrderBy(o => o.DueDate)
                .ToList() ?? default;

            var now = DateTimeOffset.UtcNow;
            foreach (var item in payableInstallments.Where(r => r.DueDate >= now).OrderBy(x => x.DueDate))
            {
                result.DiscountOnPayOff = decimal.Add(result.DiscountOnPayOff, item.InterestAmount);
            }

            return result;
        }
    }

    public interface IOrderDetailRequestHandler
    {
        ValueTask<OrderDetailResponse> HandleAsync(OrderDetailRequest request, CancellationToken cancellationToken);
    }

    public class OrderDetailValidator : AbstractValidator<OrderDetailRequest>
    {
        public OrderDetailValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required); 
        }
    }
}

