﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.Promissory.Kiahooshan;

namespace PayPing.LuckyLuke.Application.Shared.Services.Promissory.Kiah<PERSON>han
{
    public class KiaSignRawDocumentHandler : <PERSON><PERSON><PERSON>han<PERSON><PERSON><PERSON>and<PERSON>, IKiahooshanPromissoryService
    {
        private readonly IKiahooshanPromissoryService _next;
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly IS3ServiceApiClient _s3ServiceApiClient;

        public KiaSignRawDocumentHandler(
            IKiahooshanPromissoryService next,
            IUploadGrpcClient uploadGrpcClient,
            IS3ServiceApiClient s3ServiceApiClient,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKiahooshanApiHttpClient kihooApi,
            IOptions<KiahooshanOptions> kihooOptions)
            : base(dbContext, userContext, kihooApi, kihooOptions)
        {
            _next = next;
            _uploadGrpcClient = uploadGrpcClient;
            _s3ServiceApiClient = s3ServiceApiClient;
        }

        public async ValueTask<object> HandleAsync(KiahooshanPromissoryContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KiahooshanPromissoryStatusV1.Sign)
                return await _next.HandleAsync(context, cancellationToken);

            ValidateContext(context);

            var consumerInfo = await _dbContext.ConsumerInfos.AsNoTracking().Where(x => x.ConsumerUserId == context.ConsumerUserId).FirstOrDefaultAsync();

            var dsc = await GetValidDigitalSignAsync(context.ConsumerUserId);

            var rawpdfDlResult = await _uploadGrpcClient.GetPresignedUrlAsync(context.PromissoryRawDocumentFileId, context.ConsumerUserId, FileManager.Grpc.UploadTypeEnum.Misc, cancellationToken);
            if (!rawpdfDlResult.Succeeded)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "KiaSignRawDocumentHandler; could not download raw promissory", false);
            }

            using Stream rawpdfStream = await _s3ServiceApiClient.DownloadAsync(rawpdfDlResult.SuccessResult, cancellationToken);

            var result = await _kihooApi.SignDoc(dsc.SignatureUniqueId, rawpdfStream, "rawcontract.pdf", context.OrderTrackingCode, cancellationToken);
            if (result == null)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "KiaSignRawDocumentHandler; could not sign raw document", false);
            }

            string validName = Path.ChangeExtension(Path.GetRandomFileName(), ".pdf");

            var uresult = await _uploadGrpcClient.UploadUserDocumentAsync(await result.ToByteArrayAsync(), Path.GetExtension(validName).TrimStart('.'), context.ConsumerUserId, cancellationToken);
            if (!uresult.Succeeded)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "KiaSignRawDocumentHandler; could not upload signed promissory to storage service", false);
            }


            context.Status = KiahooshanPromissoryStatusV1.Finalize;
            context.PromissorySignedDocumentFileId = uresult.SuccessResult;
            context.PromissorySignedDocumentFileContentType = "application/pdf";


            //// save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

        protected override void ValidateContext(KiahooshanPromissoryContextV1 context)
        {
            base.ValidateContext(context);

            Guard.Against.NullOrEmpty(context.PromissoryUniqueId);
            Guard.Against.NullOrEmpty(context.PromissoryRawDocumentFileId);

        }
    }
}
