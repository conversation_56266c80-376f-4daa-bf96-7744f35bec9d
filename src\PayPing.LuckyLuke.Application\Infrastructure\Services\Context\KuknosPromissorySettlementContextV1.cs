﻿namespace PayPing.LuckyLuke.Application.Infrastructure.Services.Context
{
    public struct KuknosPromissorySettlementContextV1
    {
        public DateTimeOffset IssueDate { get; set; }
        public long OrderId { get; set; }
        public Guid OrderTrackingCode { get; set; }
        public long OrderGuaranteeId { get; set; }
        public int ConsumerUserId { get; set; }
        public int RecipientUserId { get; set; }

        public string PromissoryId { get; set; }
        public string SettlementRawDocumentHash { get; set; }
        public string SettlementRawDocumentFileContentType { get; set; }

        public string SettlementSignedDocumentHash { get; set; }

        public string SettlementFinalDocumentHash { get; set; }
        public string SettlementFinalDocumentFileId { get; set; }
        public string SettlementFinalDocumentFileContentType { get; set; }

        public KuknosPromissorySettlementStatusV1 Status { get; set; }

    }

    public enum KuknosPromissorySettlementStatusV1
    {
        IssueSettlement = 0,
        Sign = 50,
        Finalize = 80,
        Done = 90,

    }
}
