﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedCodeToCredit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "Code",
                table: "Plans",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<decimal>(
                name: "GuaranteeAmount",
                table: "Orders",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<Guid>(
                name: "Code",
                table: "Guarantors",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<Guid>(
                name: "CreditCode",
                table: "CreditTransactions",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<Guid>(
                name: "Code",
                table: "Credits",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_Plan_Code",
                table: "Plans",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Guarantor_Code",
                table: "Guarantors",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CreditTransaction_CreditCode",
                table: "CreditTransactions",
                column: "CreditCode");

            migrationBuilder.CreateIndex(
                name: "IX_Credit_Code",
                table: "Credits",
                column: "Code",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Plan_Code",
                table: "Plans");

            migrationBuilder.DropIndex(
                name: "IX_Guarantor_Code",
                table: "Guarantors");

            migrationBuilder.DropIndex(
                name: "IX_CreditTransaction_CreditCode",
                table: "CreditTransactions");

            migrationBuilder.DropIndex(
                name: "IX_Credit_Code",
                table: "Credits");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "Plans");

            migrationBuilder.DropColumn(
                name: "GuaranteeAmount",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "Guarantors");

            migrationBuilder.DropColumn(
                name: "CreditCode",
                table: "CreditTransactions");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "Credits");
        }
    }
}
