﻿namespace PayPing.LuckyLuke.Application.Infrastructure.Web.Routes
{
    public class MerchantRoutes
    {
        private const string MerchantApplicationPrefixUri = "v{version:apiVersion}/bnpl/merchant";

        public static string CreateOrder => $"{MerchantApplicationPrefixUri}/order/create";
        public static string OrderList => $"{MerchantApplicationPrefixUri}/order/list";
        public static string OrderListExcel => $"{MerchantApplicationPrefixUri}/order/export/excel";
        public static string OrderDetail => $"{MerchantApplicationPrefixUri}/order/detail";
        public static string OrderCancel => $"{MerchantApplicationPrefixUri}/order/cancel";
        public static string GetOrderRefundInfo => $"{MerchantApplicationPrefixUri}/order/refund-info/get";

        public static string CreatePlan => $"{MerchantApplicationPrefixUri}/plan/create";
        public static string UpdatePlan => $"{MerchantApplicationPrefixUri}/plan/update";
        public static string TogglePlanActive => $"{MerchantApplicationPrefixUri}/plan/toggle-active";

        public static string PlansList => $"{MerchantApplicationPrefixUri}/plan/list";
        public static string PlanGet => $"{MerchantApplicationPrefixUri}/plan/get";

        public static string CreateContract => $"{MerchantApplicationPrefixUri}/contract/create";
        public static string SetCredit => $"{MerchantApplicationPrefixUri}/contract/set-credit";
        public static string SetWalletCreditAmount => $"{MerchantApplicationPrefixUri}/contract/wallet-credit-amount";
        public static string SetMinPrePaymentRate => $"{MerchantApplicationPrefixUri}/contract/min-prepayment-rate";
        public static string SetMonthlyInterestRate => $"{MerchantApplicationPrefixUri}/contract/monthly-interest-rate";
        public static string GetMerchantProfileInfo => $"{MerchantApplicationPrefixUri}/contract/get-merchant-profile-info";
        public static string CompleteMerchantProfileInfo => $"{MerchantApplicationPrefixUri}/contract/complete-profile-info";
        public static string UploadSignatureImage => $"{MerchantApplicationPrefixUri}/contract/upload-signature-image";
        public static string AcceptRegulations => $"{MerchantApplicationPrefixUri}/contract/accept-regulations";
        public static string MerchantSignContract => $"{MerchantApplicationPrefixUri}/contract/merchant-sign-contract";
        public static string GetContractStatus => $"{MerchantApplicationPrefixUri}/contract/get-Contract-status";
        public static string GetContract => $"{MerchantApplicationPrefixUri}/contract/get";
        public static string OwnedCreditIncrease => $"{MerchantApplicationPrefixUri}/contract/credit/increase";
        public static string CreditTransactionList => $"{MerchantApplicationPrefixUri}/credit-transaction/list";
        public static string GetOptions => $"{MerchantApplicationPrefixUri}/options/get";
        public static string ConfigurationsUpdate => $"{MerchantApplicationPrefixUri}/config/update";
        public static string GetDownloadPath => $"{MerchantApplicationPrefixUri}/file/get";
        public static string InstallmentsCalendar => $"{MerchantApplicationPrefixUri}/installment/calendar";
        public static string SetSalaryDeductionPayment => $"{MerchantApplicationPrefixUri}/installment/salary-deduction/pay";


        // front end routes
        public static string PanelInstallmentLandingUI => $"myShop/bnpl/manage";
    }
}
