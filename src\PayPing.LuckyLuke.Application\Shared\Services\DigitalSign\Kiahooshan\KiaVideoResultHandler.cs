﻿using Ardalis.GuardClauses;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
using PayPing.LuckyLuke.Application.Shared.Clients.Http;
using System.Security.Cryptography;
using System.Text.Json.Serialization;

namespace PayPing.LuckyLuke.Application.Shared.Services.DigitalSign.Kiahooshan
{
    public class KiaVideoResultHandler : KiahooshanBaseDigitalSignHandler, IKiahooshanDigitalSignService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IKiahooshanDigitalSignService _next;

        public KiaVideoResultHandler(
            IWebHostEnvironment environment,
            IKiahooshanDigitalSignService next,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKiahooshanApiHttpClient kihooApi,
            IS3ServiceApiClient s3ServiceApiClient,
            IUploadGrpcClient uploadGrpcClient,
            IMemoryCache memoryCache,
            IOptions<KiahooshanOptions> kihooOptions)
            : base(dbContext, userContext, kihooApi, kihooOptions, s3ServiceApiClient, uploadGrpcClient, memoryCache)
        {
            _environment = environment;
            _next = next;
        }

        public async ValueTask<object> HandleAsync(KiahooshanDigitalSignContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KiahooshanDigitalSignStatusV1.VideoResult)
                return await _next.HandleAsync(context, cancellationToken);


            ValidateContext(context);

            var result = await _kihooApi.GetVideoVerifyResult(context.SelfieVideoUniqueId, context.OrderTrackingCode, cancellationToken);

            if (result.status != ApplicationConstants.KiahooshanSelfieVideoSuccessStatus || !result.matching.HasValue || !result.liveness.HasValue || !result.spoofing.HasValue)
            {
                throw new CertificateNotReadyException(context.OrderTrackingCode?.ToString(), System.Text.Json.JsonSerializer.Serialize(result));
            }

            if (!result.matching.Value)
            {
                throw new DigitalSignatureProviderException(context.OrderTrackingCode?.ToString(), "خطا در تطابق ویدیو ارسالی با کارت ملی", true);
            }

            if (!result.liveness.Value)
            {
                throw new DigitalSignatureProviderException(context.OrderTrackingCode?.ToString(), "ویدیو ارسالی به صورت زنده ضبط نشده است", true);
            }

            if (!_environment.IsDevelopment() && result.spoofing.Value) 
            {
                throw new DigitalSignatureProviderException(context.OrderTrackingCode?.ToString(), "ویدیو ارسالی نا معتبر است", true);
            }


            context.Status = KiahooshanDigitalSignStatusV1.CreateAccount;

            // save context
            await UpdateContextAsync(context);

            return await _next.HandleAsync(context, cancellationToken);
        }

        protected override void ValidateContext(KiahooshanDigitalSignContextV1 context)
        {
            base.ValidateContext(context);
            Guard.Against.NullOrEmpty(context.SelfieVideoUniqueId);
        }
    }
}
