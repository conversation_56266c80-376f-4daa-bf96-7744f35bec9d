﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using Quartz;

namespace PayPing.LuckyLuke.Application.Features.Admin.ExpiredOrderProcessJob
{
    [DisallowConcurrentExecution]
    public class ExpiredOrderProcessJob : IJob
    {
        private readonly BackgroundJobOptions _jobOptions;
        private readonly ISchedulerFactory _schedulerfactory;
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<ExpiredOrderProcessJob> _logger;
        public ExpiredOrderProcessJob(ISchedulerFactory schedulerfactory, IOptions<BackgroundJobOptions> jobOptions, ApplicationDbContext dbContext, ILogger<ExpiredOrderProcessJob> logger)
        {
            _jobOptions = jobOptions.Value;
            _schedulerfactory = schedulerfactory;
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            if (context.RefireCount > 10)
            {
                _logger.LogInformation($"At ExpiredOrderProcessJob Refire Count exceeded 10 times, short-circuiting job");
                return;
            }

            try
            {
                _logger.LogInformation($"At ExpiredOrderProcessJob started at {DateTimeOffset.UtcNow}");

                int unfreezeCount = 0;

                // only consider expiredate for orders that are not prepaid successfully
                var cancelables = OrderStatusProvider.GetJobCancelable();
                DateTimeOffset margin = DateTimeOffset.UtcNow.AddMinutes(-20);

                var expiredOrders = await _dbContext.Orders
                    .Where(o => o.ExpireDate != null && o.ExpireDate <= margin && cancelables.Contains(o.Status))
                    .ToListAsync();

                var contextResult = new List<long>();

                if (expiredOrders != null && expiredOrders.Count > 0)
                {
                    _logger.LogInformation($"At ExpiredOrderProcessJob found {expiredOrders.Count} expired orders");

                    foreach (var order in expiredOrders)
                    {
                        _logger.LogInformation($"At ExpiredOrderProcessJob found expired order with id: {order.Id}, tracking code: {order.TrackingCode}");


                        if (!string.IsNullOrWhiteSpace(order.CreditLockId))
                        {
                            // unfreeze as much as credited amount
                            _dbContext.CreditTransactionOutboxes.Add(new CreditTransactionOutbox()
                            {
                                Amount = order.CreditedAmount,
                                CreditId = order.OrderPlan.CreditId,
                                TransactionType = CreditTransactionType.UnFreeze,
                                LockId = order.CreditLockId,
                                Message = "Expired Order Process Job Queued for Unfreez",
                                WalletId = order.OrderPlan.WalletId,
                                HasFailed = false
                            });
                            unfreezeCount++;
                        }

                        if (!string.IsNullOrWhiteSpace(order.ClientCancelUrl))
                        {
                            // schedule a webhook for client with clientrefid
                            _dbContext.OrderExternalEventOutboxes.Add(new OrderExternalEventOutbox()
                            {
                                OrderId = order.Id,
                                ClientRefId = order.ClientRefId,
                                ClientCancelUrl = order.ClientCancelUrl,
                                EventType = OrderExternalEventType.CancelHook
                            });
                        }

                        if (!order.GuaranteeRevoked)
                        {
                            // schedule a guarantee revoke
                            _dbContext.OrderExternalEventOutboxes.Add(new OrderExternalEventOutbox()
                            {
                                OrderId = order.Id,
                                EventType = OrderExternalEventType.RevokeGuarantee
                            });
                        }

                        order.Status = OrderStatus.CanceledByConsumer;

                        contextResult.Add(order.Id);
                    }

                    var result = await _dbContext.SaveChangesAsync();


                    _logger.LogInformation($"At ExpiredOrderProcessJob made {contextResult.Count} number of expired orders, expire.");
                }

                if (unfreezeCount > 0)
                {
                    var scheduler = await _schedulerfactory.GetScheduler();
                    await scheduler.TriggerJob(new JobKey(nameof(SuspeciousCreditTransactionJob), _jobOptions.ExpiredOrderJobGroup));
                }

                context.Result = contextResult;
            }
            catch (Exception ex)
            {
                throw new JobExecutionException(msg: "At ExpiredOrderProcessJob exception happened", refireImmediately: false, cause: ex);
            }

        }
    }
}
