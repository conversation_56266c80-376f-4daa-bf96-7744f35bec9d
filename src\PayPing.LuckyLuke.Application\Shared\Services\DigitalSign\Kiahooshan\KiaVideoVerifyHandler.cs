﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kiahooshan;
using PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
using PayPing.LuckyLuke.Application.Shared.Clients.Http;
using System.Security.Cryptography;

namespace PayPing.LuckyLuke.Application.Shared.Services.DigitalSign.Kiahooshan
{
    public class KiaVideoVerifyHandler : KiahooshanBaseDigitalSignHand<PERSON>, IKiahooshanDigitalSignService
    {
        private readonly I<PERSON>hanDigitalSignService _next;

        public KiaVideoVerifyHandler(
            IKiahooshanDigitalSignService next,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKiahooshanApiHttpClient kihooApi,
            IS3ServiceApiClient s3ServiceApiClient,
            IUploadGrpcClient uploadGrpcClient,
            IMemoryCache memoryCache,
            IOptions<KiahooshanOptions> kihooOptions)
            : base(dbContext, userContext, kihooApi, kihooOptions, s3ServiceApiClient, uploadGrpcClient, memoryCache)
        {
            _next = next;
        }

        public async ValueTask<object> HandleAsync(KiahooshanDigitalSignContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KiahooshanDigitalSignStatusV1.VideoVerify)
                return await _next.HandleAsync(context, cancellationToken);

            // here we should have signature video stored in storage

            ValidateContext(context);

            // download selfie video from storage

            var consumerInfo = await _dbContext.ConsumerInfos.AsNoTracking().Where(x => x.ConsumerUserId == context.UserId).FirstOrDefaultAsync();

            var sigVideo = await GetCachedSelfieVideoAsync(context, consumerInfo.SignatureVideoFileId, cancellationToken);


            var result = await _kihooApi.VerifyVideoWithNationalId(context.BirthDate, context.NationalCode, consumerInfo.SignatureVideoRandomSentence, sigVideo, consumerInfo.SignatureVideoFileName, context.OrderTrackingCode, cancellationToken);

            context.UserId = _userContext.CurrentUserId.Value;
            context.SelfieVideoUniqueId = result.uniqueId;
            context.Status = KiahooshanDigitalSignStatusV1.VideoResult;


            // save context
            await UpdateContextAsync(context);

            await Task.Delay(10000, cancellationToken);

            return await _next.HandleAsync(context, cancellationToken);
        }

        protected override void ValidateContext(KiahooshanDigitalSignContextV1 context)
        {
            base.ValidateContext(context);
        }
    }
}
