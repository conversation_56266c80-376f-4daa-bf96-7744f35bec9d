﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Factory;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Consumer._03_GetSteps
{
    public record GetOrderRequest(Guid trackingCode);

    public class GetOrderResponse
    {
        public GetOrderResponse(string merchantName, OrderSteps nextStep, DateTimeOffset expireDate, string redirectUrl, bool canCancel, List<GetOrderStepResponse> steps)
        {
            this.merchantName = merchantName;
            this.nextStep = nextStep;
            this.expireDate = expireDate;
            merchantRedirectUrl = redirectUrl;
            this.canCancel = canCancel;
            this.steps = steps;
        }

        public string merchantName { get; set; }
        public DateTimeOffset expireDate { get; set; }
        public OrderSteps nextStep { get; set; }
        public string merchantRedirectUrl { get; set; }
        public bool canCancel { get; set; }
        [Required]
        public List<GetOrderStepResponse> steps { get; set; }
    }

    public class GetOrderStepResponse
    {
        public GetOrderStepResponse(string name, OrderSteps code, bool isPublic, bool isPassed)
        {
            this.name = name;
            this.code = code;
            this.isPublic = isPublic;
            this.isPassed = isPassed;
        }

        public string name { get; set; }
        public OrderSteps code { get; set; }
        public bool isPassed { get; set; }
        public bool isPublic { get; set; }
    };

    public class GetOrderEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                ConsumerRoutes.GetOrder,
                async (
                    [AsParameters] GetOrderRequest request,
                    IGetOrderRequestHandler handler,
                    IValidator<GetOrderRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("GetOrder")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<GetOrderResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Get Order")
            .WithDescription("Get Order")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class GetOrderRequestHandler : IGetOrderRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly IUserService _userService;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IEnumerable<IDigitalSignProvider> _digitalSignProviders;
        private readonly IEnumerable<IOrderStepsFactory> _orderStepsFactories;
        private readonly BNPLOptions _bnplOptions;

        public GetOrderRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IUserService userService,
            IGuaranteeService guaranteeService,
            IEnumerable<IDigitalSignProvider> digitalSignProviders,
            IEnumerable<IOrderStepsFactory> orderStepsFactories,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _userService = userService;
            _guaranteeService = guaranteeService;
            _digitalSignProviders = digitalSignProviders;
            _orderStepsFactories = orderStepsFactories;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<GetOrderResponse> HandleAsync(GetOrderRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.AsNoTracking()
                .Where(o => o.TrackingCode == request.trackingCode)
                .Include(o => o.OrderGuarantees)
                .FirstOrDefaultAsync();

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            {
                throw new OrderExpiredException(request.trackingCode.ToString(), "سفارش منقضی شده است");
            }

            if (order.ConsumerUserId != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }

            var cancelableStatuses = OrderStatusProvider.GetConsumerCancelable();

            string cancelUrl = order.ClientCancelUrl.BuildCancelUrl(order.TrackingCode, order.ClientRefId, order.ClientCallbackUrl);

            var guaranteeTypes = await _guaranteeService.GetGuaranteeTypesByGuarantorId(order.OrderPlan.GuarantorId);

            var stepsFactory = _orderStepsFactories.Where(x => x.GuaranteeType == guaranteeTypes.guaranteeType).First();

            OrderStepsDto StepsResult;

            if (order.OrderPlan.ContractIsMandatory)
            {
                var cmfact = stepsFactory.CreateContractMandatory();
                StepsResult = await cmfact.CreateContractMandatorySteps(order, userId);
            }
            else
            {
                var clfact = stepsFactory.CreateContractLess();
                StepsResult = await clfact.CreateContractLessSteps(order, userId);
            }

            var result = new GetOrderResponse(order.MerchantName, StepsResult.Next, order.ExpireDate.Value, cancelUrl, cancelableStatuses.Contains(order.Status), ToResponseSteps(StepsResult.Steps));

            return result;
        }


        private List<GetOrderStepResponse> ToResponseSteps(List<OrderStepDto> request)
        {
            var response = new List<GetOrderStepResponse>();
            foreach (var item in request)
            {
                response.Add(new GetOrderStepResponse(item.Name, item.Code, item.IsPublic, item.IsPassed));
            }
            return response;
        }
    }
    public interface IGetOrderRequestHandler
    {
        ValueTask<GetOrderResponse> HandleAsync(GetOrderRequest request, CancellationToken cancellationToken);
    }

    public class GetOrderValidator : AbstractValidator<GetOrderRequest>
    {
        public GetOrderValidator()
        {
            RuleFor(x => x.trackingCode).NotEqual(Guid.Empty).WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}
