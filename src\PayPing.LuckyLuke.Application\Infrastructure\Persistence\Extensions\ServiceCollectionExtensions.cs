﻿using Microsoft.AspNetCore.Connections;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Extensions
{
    public static partial class ServiceCollectionExtensions
    {
        public static IServiceCollection AddPostgresDbContext(this IServiceCollection services)
        {
            services.AddDbContext<ApplicationDbContext>((serviceProvider, optionsBuilder) =>
            {
                var postgresOptions = serviceProvider.GetService<IOptions<PostgresOptions>>().Value;

                optionsBuilder.UseNpgsql(postgresOptions.ConnectionString);

            });

            return services;
        }
    }
}
