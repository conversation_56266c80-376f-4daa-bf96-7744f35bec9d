﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Configurations
{
    public sealed class BNPLOptions
    {
        public static string SectionName => "BNPL";
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string ApiName { get; set; }
        public string ApiSecret { get; set; }
        public string BaseUrl { get; set; }
        public string BasePath { get; set; }
        public string ConsumerUIBaseUrl { get; set; }
        public string MerchantUIBaseUrl { get; set; }
        public string PlatformClientId { get; set; }
        public string PlatformClientSecret { get; set; }
    }

    public sealed class ProxyOptions
    {
        public static string SectionName => "Proxy";

        public string Address { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
    }

    public sealed class IdentityServerOptions
    {
        [ConfigurationKeyName("PayPing_Identity_Address")]
        public string Identity_Address { get; set; }
    }

    public sealed class PostgresOptions
    {
        public static string SectionName => "Postgres";

        public string ConnectionString { get; set; }
        public bool UseInMemory { get; set; }
        public string MigrationAssembly { get; set; }
    }

    //public sealed class SerilogOptions
    //{
    //    public static string SectionName => "Serilog";

    //    public string SeqUrl { get; set; }
    //    public string UseConsole { get; set; } = "true";
    //    public string ExportLogsToOpenTelemetry { get; set; } = "false";
    //    public string GrafanaLokiUrl { get; set; }
    //    public bool UseElasticsearchJsonFormatter { get; set; }
    //    public string LogTemplate { get; set; } =
    //        "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level} - {Message:lj}{NewLine}{Exception}";
    //    public string LogPath { get; set; }
    //}

    public sealed class ServiceDiscoveryOptions
    {
        public string InquiryServiceAddress { get; set; }

        [ConfigurationKeyName("PayPing_UserServices_Address")]
        public string UserServiceAddress { get; set; }

        [ConfigurationKeyName("PayPing_FileManager_Address")]
        public string FileManagerServiceAddress { get; set; }

        [ConfigurationKeyName("PayPing_FileManager_Grpc_Address")]
        public string FileManagerGrpcServiceAddress { get; set; }

        [ConfigurationKeyName("Files_Base_Url")]
        public string S3ServiceAddress { get; set; }

        public string PaymentServiceAddress { get; set; }

        public string PaymentGrpcServiceAddress { get; set; }

        [ConfigurationKeyName("PayPing_Ipg_Address")]
        public string IpgServiceAddress { get; set; }

        [ConfigurationKeyName("PayPing_Integrations_Address")]
        public string IntegrationServiceAddress { get; set; }

        [ConfigurationKeyName("PayPing_Inquiry_ApiGateway_Grpc_Address")]
        public string InquiryGrpcServiceAddress { get; set; }

        [ConfigurationKeyName("Refund_Grpc_Address")]
        public string RefundGrpcServiceAddress { get; set; }

        [ConfigurationKeyName("PayPing_Token_Grpc_Address")]
        public string TokenGrpcServiceAddress { get; set; }

        [ConfigurationKeyName("PayPing_Wallet_Grpc_Address")]
        public string WalletGrpcAddress { get; set; }
    }

    public sealed class ElasticOptions
    {
        public string ELASTIC_APM_ENABLED { get; set; }

        public string ELASTIC_APM_LOG_LEVEL { get; set; }

        public string ELASTIC_APM_SERVER_URL { get; set; }

        public string ELASTIC_APM_SERVICE_NAME { get; set; }

        public string ELASTIC_APM_TRANSACTION_SAMPLE_RATE { get; set; }

        public string ELASTIC_HOST { get; set; }

        public string LogLevel { get; set; }
        public string Logstash_Address { get; set; }
        public string Logstash_Port { get; set; }
        public string Logstash_Type { get; set; }
    }

    public sealed class KuknosOptions
    {
        public static string SectionName => "Kuknos";

        public string BaseUrl { get; set; }
        public string Platform { get; set; }
        public string Email { get; set; }
        public string Password { get; set; }
    }

    public sealed class KiahooshanOptions
    {
        public static string SectionName => "Kiahooshan";

        public string BaseUrl { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
    }

    public sealed class RedisOptions
    {
        [ConfigurationKeyName("RedisClusterConnectionString")]
        public string ConnectionString { get; set; }
        public string RedisInstanceName { get; set; }
    }

    public sealed class BackgroundJobOptions
    {
        public static string SectionName => "BackgroundJob";

        public string ExpiredOrderJobGroup { get; set; }
        public string ExpiredOrderProcessJobCron { get; set; }
        public string OrderExternalEventJobCron { get; set; }

        public string InstallmentJobGroup { get; set; }
        public string InstallmentDelayedJobCron { get; set; }
        public string InstallmentDefaultedJobCron { get; set; }

    }

    public sealed class RabbitMqOptions
    {
        [ConfigurationKeyName("RabbitMqUri")]
        public string RabbitUri { get; set; }

        [ConfigurationKeyName("RabbitMqUsername")]
        public string RabbitUsername { get; set; }

        [ConfigurationKeyName("RabbitMqPassword")]
        public string RabbitPassword { get; set; }
    }
}
