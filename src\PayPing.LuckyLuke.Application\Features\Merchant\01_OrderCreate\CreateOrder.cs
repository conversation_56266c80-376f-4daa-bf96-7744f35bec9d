﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Services.User;

namespace PayPing.LuckyLuke.Application.Features.Merchant._01_OrderCreate
{
    public record CreateOrderRequest(int amount, string mobile, string description, string callbackUrl, string cancelUrl, string refId, List<Guid> targetPlans);

    public record CreateOrderResponse(string redirectUrl, string orderTrackingCode);

    public class CreateOrderEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.CreateOrder,
                async (
                    CreateOrderRequest request,
                    ICreateOrderRequestHandler handler,
                    IValidator<CreateOrderRequest> validator,
                    IHttpContextAccessor httpContextAccessor,
                    CancellationToken cancellationToken) =>
            {

                var validationResult = await validator.ValidateAsync(request, cancellationToken);
                if (!validationResult.IsValid)
                {
                    throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                }
                string Authorization = httpContextAccessor.HttpContext.Request.Headers.Authorization[0];

                string token = Authorization?.Trim().Replace("Bearer ", "").Replace("bearer ", "");
                if (string.IsNullOrWhiteSpace(token))
                {
                    throw new ArgumentException("authorization is not provided");
                }

                var result = await handler.HandleAsync(request, token, cancellationToken);

                return Results.Ok(result);
            })
            .RequireAuthorization("write")
            .WithName("CreateOrder")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<CreateOrderResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Create Order")
            .WithDescription("Create Order")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CreateOrderRequestHandler : ICreateOrderRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IWebHostEnvironment _environment;
        private readonly IUserContext _userContext;
        private readonly IUserService _userService;
        private readonly BNPLOptions _bnplOptions;

        public CreateOrderRequestHandler(ApplicationDbContext dbContext, IWebHostEnvironment environment, IUserContext userContext, IUserService userService, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _environment = environment;
            _userContext = userContext;
            _userService = userService;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<CreateOrderResponse> HandleAsync(CreateOrderRequest request, string token, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            if (!UrlHelpers.IsValidUrl(request.callbackUrl))
            {
                throw new CustomValidationException(null, "آدرس مقصد اطلاعات پرداخت نامعتبر است. آدرس باید با http(s) آغاز شود.", nameof(request.callbackUrl));
            }

            if (!string.IsNullOrWhiteSpace(request.cancelUrl) && !UrlHelpers.IsValidUrl(request.cancelUrl))
            {
                throw new CustomValidationException(null, "آدرس مقصد لغو فرایند پرداخت نامعتبر است. آدرس باید با http(s) آغاز شود.", nameof(request.cancelUrl));
            }

            if (_environment.IsDevelopment())
            {
                if (request.amount < 1000)
                {
                    throw new CustomValidationException(null, ValidatorDictionary.amount_greater_1000, nameof(request.amount));
                }
            }
            else
            {
                if (request.amount < 50_000)
                {
                    throw new CustomValidationException(null, ValidatorDictionary.amount_greaterorequal_50000, nameof(request.amount));
                }
            }

            // check if refid and lenderid is not duplicate
            var prepaids = OrderStatusProvider.GetPrePaid();

            var order = await _dbContext.Orders.AsNoTracking()
                .Where(o => o.ClientRefId == request.refId && o.MerchantUserId == userId && prepaids.Contains(o.Status))
                .FirstOrDefaultAsync(cancellationToken);

            if (order != null)
            {
                throw new CustomValidationException(null, "سفارش پرداخت شده ای با شناسه یکتا پذیرنده، موجود است", nameof(request.refId));
            }

            // check lender has active credit and contract and plans
            var activeContractId = await _dbContext.Contracts
                .Where(x =>
                    x.MerchantUserId == userId &&
                    x.ActivationStatus == ActivationStatus.Final &&
                    x.ExpireDate > DateTimeOffset.UtcNow)
                .OrderByDescending(x => x.CreatedAt)
                .Select(x => x.Id)
                .FirstOrDefaultAsync(cancellationToken);

            if (activeContractId == default)
            {
                throw new ArgumentException("قرارداد فعالی یافت نشد");
            }

            bool anyPlan = await _dbContext.Plans
                .Where(p =>
                    p.MerchantUserId == userId &&
                    p.IsActive &&
                    p.Credit.IsActive &&
                    p.Credit.ContractId == activeContractId)
                .AnyAsync(cancellationToken);

            if (!anyPlan)
            {
                throw new ArgumentException("طرح اعتباری فعالی یافت نشد");
            }

            var merchExtInfo = await _userService.GetMerchantExtraInfoAsync(userId, cancellationToken);

            Guid tcode = Guid.NewGuid();

            order = new Order
            {
                TrackingCode = tcode,
                MerchantUserId = userId,
                MerchantName = merchExtInfo.DisplayName,
                ClientCallbackUrl = request.callbackUrl,
                ClientCancelUrl = request.cancelUrl,
                ClientRefId = request.refId,
                ConsumerIdentifier = request.mobile,
                Description = request.description,
                OrderTotalAmount = request.amount,
                Status = Domain.Enums.OrderStatus.Init,
            };

            if (request.targetPlans != null && request.targetPlans.Count > 0)
            {
                var planIds = await _dbContext.Plans
                    .Where(p =>
                        request.targetPlans.Contains(p.Code) &&
                        p.MerchantUserId == userId &&
                        p.Credit.ContractId == activeContractId &&
                        p.IsActive)
                    .Select(p => p.Id)
                    .ToListAsync();

                foreach (var planId in planIds)
                {
                    order.OrderTargetPlans.Add(new OrderTargetPlan
                    {
                        PlanId = planId
                    });
                }
            }

            _dbContext.Orders.Add(order);

            await _dbContext.SaveChangesAsync(cancellationToken);

            return new CreateOrderResponse(UrlHelpers.BuildFullUiUrl(_bnplOptions.ConsumerUIBaseUrl, ConsumerRoutes.PlansListUI, [new("trackingCode", tcode.ToString())]), tcode.ToString());
        }
    }

    public interface ICreateOrderRequestHandler
    {
        ValueTask<CreateOrderResponse> HandleAsync(CreateOrderRequest request, string token, CancellationToken cancellationToken);
    }

    public class CreateOrderValidator : AbstractValidator<CreateOrderRequest>
    {
        public CreateOrderValidator()
        {
            RuleFor(x => x.callbackUrl).NotEmpty().WithResourceError(() => ValidatorDictionary.call_back_url_is_required);
            RuleFor(x => x.refId).NotEmpty().WithResourceError(() => ValidatorDictionary.ref_id_is_required);
            RuleFor(x => x.mobile).NotEmpty().WithResourceError(() => ValidatorDictionary.mobile_is_required)
                .Matches("^09[0-9]{9}$").WithResourceError(() => ValidatorDictionary.mobile_not_valid);
        }
    }

}
