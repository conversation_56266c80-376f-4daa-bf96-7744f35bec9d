﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using Quartz;

namespace PayPing.LuckyLuke.Application.Features.Admin.SuspeciousCreditTransactionJob
{
    [DisallowConcurrentExecution]
    public class SuspeciousCreditTransactionJob : IJob
    {
        private readonly BackgroundJobOptions _jobOptions;
        private readonly ApplicationDbContext _dbContext;
        private readonly ICreditService _creditService;
        private readonly IWalletService _walletService;
        private readonly ILogger<SuspeciousCreditTransactionJob> _logger;
        public SuspeciousCreditTransactionJob(IOptions<BackgroundJobOptions> jobOptions, ApplicationDbContext dbContext, ICreditService creditService, IWalletService walletService, ILogger<SuspeciousCreditTransactionJob> logger)
        {
            _jobOptions = jobOptions.Value;
            _dbContext = dbContext;
            _creditService = creditService;
            _walletService = walletService;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            if (context.RefireCount > 10)
            {
                _logger.LogInformation("At SuspeciousCreditTransactionJob Refire Count exceeded 10 times, short-circuiting job");
                return;
            }

            try
            {
                _logger.LogInformation("At SuspeciousCreditTransactionJob started at {DateTimeOffset}", DateTimeOffset.UtcNow);

                int successResult = 0;
                int failedResult = 0;

                // retry outbox and then trigger guarantee job
                var suspeciousTransactions = await _dbContext.CreditTransactionOutboxes
                    .Where(x => x.FailCount <= 10)
                    .OrderBy(t => t.CreatedAt).ToListAsync();

                foreach (var trx in suspeciousTransactions)
                {
                    if (trx.HasFailed && trx.FailCount > 10)
                    {
                        _logger.LogInformation("At SuspeciousCreditTransactionJob bypassing suspecious transaction. id: {TrxId} with fail count: {TrxFailCount}", trx.Id, trx.FailCount);
                        continue;
                    }

                    switch (trx.TransactionType)
                    {
                        case CreditTransactionType.UnFreeze:
                            {
                                var wresult = await _walletService.UnLockCreditAndSaveAsync(trx.WalletId, trx.LockId, trx.Amount, context.CancellationToken);
                                if (wresult.Success)
                                {
                                    successResult++;
                                    _dbContext.CreditTransactionOutboxes.Remove(trx);
                                }
                                else
                                {
                                    failedResult++;
                                    trx.HasFailed = true;
                                    trx.FailCount += 1;
                                    trx.Message = wresult.Error;
                                }
                                break;
                            }
                        case CreditTransactionType.Withdraw:
                            {
                                var wresult = await _walletService.WithdrawCreditAndSaveAsync(trx.WalletId, trx.LockId, trx.Amount, context.CancellationToken);
                                if (wresult.Success)
                                {
                                    successResult++;
                                    _dbContext.CreditTransactionOutboxes.Remove(trx);
                                }
                                else
                                {
                                    failedResult++;
                                    trx.HasFailed = true;
                                    trx.FailCount += 1;
                                    trx.Message = wresult.Error;
                                }
                                break;
                            }
                        case CreditTransactionType.PrepaymentRS:
                            {
                                var wresult = await _creditService.IncreasePrePaymentSumOnCreditAndSaveAsync(trx.CreditId, true, trx.Amount, decimal.Zero, decimal.Zero, context.CancellationToken);
                                if (wresult.Success)
                                {
                                    successResult++;
                                    _dbContext.CreditTransactionOutboxes.Remove(trx);
                                }
                                else
                                {
                                    failedResult++;
                                    trx.HasFailed = true;
                                    trx.FailCount += 1;
                                    trx.Message = wresult.Error;
                                }
                                break;
                            }
                        case CreditTransactionType.PrepaymentRSNegative:
                            {
                                var wresult = await _creditService.DecreaseRawPrePaymentSumOnCreditAndSaveAsync(trx.CreditId, trx.Amount, context.CancellationToken);
                                if (wresult.Success)
                                {
                                    successResult++;
                                    _dbContext.CreditTransactionOutboxes.Remove(trx);
                                }
                                else
                                {
                                    failedResult++;
                                    trx.HasFailed = true;
                                    trx.FailCount += 1;
                                    trx.Message = wresult.Error;
                                }
                                break;
                            }
                        case CreditTransactionType.MerchantWageRS:
                            {
                                var wresult = await _creditService.IncreasePrePaymentSumOnCreditAndSaveAsync(trx.CreditId, false, decimal.Zero, trx.Amount, decimal.Zero, context.CancellationToken);
                                if (wresult.Success)
                                {
                                    successResult++;
                                    _dbContext.CreditTransactionOutboxes.Remove(trx);
                                }
                                else
                                {
                                    failedResult++;
                                    trx.HasFailed = true;
                                    trx.FailCount += 1;
                                    trx.Message = wresult.Error;
                                }
                                break;
                            }
                        case CreditTransactionType.ConsumerWageRS:
                            {
                                var wresult = await _creditService.IncreasePrePaymentSumOnCreditAndSaveAsync(trx.CreditId, false, decimal.Zero, decimal.Zero, trx.Amount, context.CancellationToken);
                                if (wresult.Success)
                                {
                                    successResult++;
                                    _dbContext.CreditTransactionOutboxes.Remove(trx);
                                }
                                else
                                {
                                    failedResult++;
                                    trx.HasFailed = true;
                                    trx.FailCount += 1;
                                    trx.Message = wresult.Error;
                                }
                                break;
                            }
                        case CreditTransactionType.InstallmentPaymentRS:
                            {
                                var wresult = await _creditService.IncreaseInstallmentSumOnCreditAndSaveAsync(trx.CreditId, trx.Amount, context.CancellationToken);
                                if (wresult.Success)
                                {
                                    successResult++;
                                    _dbContext.CreditTransactionOutboxes.Remove(trx);
                                }
                                else
                                {
                                    failedResult++;
                                    trx.HasFailed = true;
                                    trx.FailCount += 1;
                                    trx.Message = wresult.Error;
                                }
                                break;
                            }
                        case CreditTransactionType.Deposit:
                            {
                                var result = await _walletService.IncreaseWalletCreditAndSaveAsync(trx.WalletId,
                                    trx.Amount,
                                    context.CancellationToken);

                                if (result.Success)
                                {
                                    successResult++;
                                    _dbContext.CreditTransactionOutboxes.Remove(trx);
                                }
                                else
                                {
                                    failedResult++;
                                    trx.HasFailed = true;
                                    trx.FailCount += 1;
                                    trx.Message = result.Error;
                                }
                                break;
                            }
                        case CreditTransactionType.Freeze:
                            {
                                var result = await _walletService.LockCreditAndSaveAsync(trx.WalletId,
                                    trx.Amount,
                                    context.CancellationToken);

                                if (result.Success)
                                {
                                    successResult++;
                                    _dbContext.CreditTransactionOutboxes.Remove(trx);
                                }
                                else
                                {
                                    failedResult++;
                                    trx.HasFailed = true;
                                    trx.FailCount += 1;
                                    trx.Message = result.Error;
                                }
                                break;
                            }
                        default:
                            _logger.LogWarning("At SuspeciousCreditTransactionJob unknown transaction type: {CreditTransactionType}", trx.TransactionType);
                            failedResult++;
                            trx.HasFailed = true;
                            trx.FailCount += 1;
                            trx.Message = $"Unknown transaction type: {trx.TransactionType}";
                            break;
                    }
                }

                if (successResult > 0 || failedResult > 0)
                {
                    await _dbContext.SaveChangesAsync();

                    if (successResult > 0)
                        _logger.LogInformation("At SuspeciousCreditTransactionJob successfully resolved {SuccessResult} number of suspecious credit transactions", successResult);

                    if (failedResult > 0)
                        _logger.LogWarning("At SuspeciousCreditTransactionJob failed {FailedResult} number of suspecious credit transactions", failedResult);

                }

                context.Result = successResult;
            }
            catch (Exception ex)
            {
                throw new JobExecutionException(msg: "At SuspeciousCreditTransactionJob exception happened", refireImmediately: false, cause: ex);
            }

        }
    }
}
