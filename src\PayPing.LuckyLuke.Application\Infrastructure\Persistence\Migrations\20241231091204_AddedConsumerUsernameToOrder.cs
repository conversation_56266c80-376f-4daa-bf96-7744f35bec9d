﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedConsumerUsernameToOrder : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ConsumerName",
                table: "Orders",
                type: "character varying(2046)",
                maxLength: 2046,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ConsumerUserName",
                table: "Orders",
                type: "character varying(64)",
                maxLength: 64,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Order_ConsumerName",
                table: "Orders",
                column: "ConsumerName");

            migrationBuilder.CreateIndex(
                name: "IX_Order_ConsumerUserName",
                table: "Orders",
                column: "ConsumerUserName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Order_ConsumerName",
                table: "Orders");

            migrationBuilder.DropIndex(
                name: "IX_Order_ConsumerUserName",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "ConsumerUserName",
                table: "Orders");

            migrationBuilder.AlterColumn<string>(
                name: "ConsumerName",
                table: "Orders",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(2046)",
                oldMaxLength: 2046,
                oldNullable: true);
        }
    }
}
