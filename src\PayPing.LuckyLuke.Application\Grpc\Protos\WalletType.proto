syntax = "proto3";

option csharp_namespace = "Wallet.Type";

import "Grpc/Protos/BaseExceptionResponse.proto";
import "google/protobuf/wrappers.proto";

service WalletTypeGrpc {
   rpc GetWalletTypes (GetWalletTypesRequest) returns (GetWalletTypesResponse);
}

/* GET WALLET TYPE */
message GetWalletTypesRequest {  
}

message GetWalletTypesResponse {
  repeated WalletTypeDetail walletTypes = 1;
  baseExceptionResponse.ProblemDetail problemDetail = 2;
}

message WalletTypeDetail {
  string id = 1;
  string title = 2;
  repeated WalletTypePolicyDetail walletTypePolicies = 3;
}

message WalletTypePolicyDetail {
  string id = 1;
  string title = 2;
}