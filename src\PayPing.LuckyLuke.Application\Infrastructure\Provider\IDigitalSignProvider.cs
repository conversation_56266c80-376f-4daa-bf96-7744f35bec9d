﻿using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;

namespace PayPing.LuckyLuke.Application.Infrastructure.Provider
{
    public interface IDigitalSignProvider
    {
        DigitalSignType DigitalSignType { get; }

        ValueTask<string> CreateDigitalSignAsync(Guid? orderTrackingCode, long id, int userId, bool userIsConsumer, CancellationToken cancellationToken);
        OrderStepDto GetDigitalSignStep();
        ValueTask<(string privateKey, string certificate)> GetPdfSignatureDataAsync(int userId);
        ValueTask<DigitalSign> GetUserValidDigitalSignatureAsync(int userId, bool updateable);
        ValueTask<DigitalSign> GetUserInProgressOrValidDigitalSignatureAsync(int userId, bool updateable);

        //(bool setNext, OrderSteps? next, List<(string name, OrderSteps code)> steps) GetSteps(string context);
        ValueTask<DigitalSignCheckPointResponse> PushToNextCheckPointAsync(string contextData, CancellationToken cancellationToken);
        ValueTask<bool> UserHasValidDigitalSignatureAsync(int userId);
    }
}