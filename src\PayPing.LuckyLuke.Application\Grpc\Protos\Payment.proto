﻿syntax = "proto3";

option csharp_namespace = "PaymentGrpc";

package PaymentGrpc;

import "google/protobuf/timestamp.proto";
import "Grpc/Protos/Enums/ServiceType.proto";
import "Grpc/Protos/Enums/IpgType.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/wrappers.proto";

service PaymentService {
	rpc CreateSimplePaymentWithAdditionalWage (CreateSimplePaymentRequest) returns (CreateSimplePaymentResponse);
	rpc CreateCashPayment (CreateCashPaymentRequest) returns (CreateCashPaymentResponse);
	rpc VerifyPayment (VerifyPaymentRequest) returns (VerifyPaymentResponse);
	rpc GetPaymentByCode (GetPaymentByCodeRequest) returns (PaymentInfo);
	rpc GetPaymentDetails (GetPaymentByCodeRequest) returns (GetPaymentDetailsResponse);
	rpc RemovePaymentByCode (RemovePaymentByCodeRequest) returns (google.protobuf.Empty);
	rpc GetPaymentsByCodes (GetPaymentsByCodesRequest) returns (GetPaymentsByCodesResponse);
}

/* CREATE SIMPLE PAYMENT */
message CreateSimplePaymentRequest {
	int32 userId = 1;
	int32 amount = 2;
	optional int32 payerWage = 3;
	optional int32 merchantWage = 4;
	//نام سرویس استفاده کننده
	ServiceType serviceType = 5;
	string returnUrl = 6;
	optional string payerName = 7;
	optional string description = 8;
	string clientRefId = 9;
	optional string PayerIdentity = 10;
}

message CreateSimplePaymentResponse {
	string paymentCode = 1;
	string url = 2;
	int32 amount = 3;
}

/******************************************/
/* CREATE CASH PAYMENT */
message CreateCashPaymentRequest {
	int32 userId = 1;
	int32 amount = 2;
	ServiceType serviceType = 3;
	string customerCode = 4;
	string returnUrl = 5;
	string payerName = 6;
	int32 pluginType = 7;
	string description = 8;
	string clientRefId = 9;
	string PayerIdentity = 10;
}

message CreateCashPaymentResponse {
	string paymentCode = 1;
	string url = 2;
	int32 amount = 3;
}

/******************************************/
/* VERIFY PAYMENT MODEL */
message VerifyPaymentRequest {
  optional int32 userId = 1;
  ServiceType serviceType = 2;
  int64 paymentRefId = 3;
  string paymentCode = 4;
}


message VerifyPaymentResponse {
	int32 amount = 1;
	string cardNumber = 2;
	string cardHashPan = 3;
	string payedDate = 4;
	string code = 5;
}

/******************************************/
/* FIND PAYMENT BY CODE */
message GetPaymentByCodeRequest {
	string code = 1;
}

message GetPaymentDetailsResponse {
	int32 payment_id = 1;
	bool is_paid = 2;
	google.protobuf.StringValue rrn = 3;
	int32 amount = 4;
	google.protobuf.StringValue hashed_pan = 5;
	IpgType ipg_type = 6;
	int32 user_id = 7;
	google.protobuf.StringValue terminal_id = 8;
	google.protobuf.StringValue acceptor_code = 9;
	google.protobuf.Timestamp pay_date = 10;
	google.protobuf.StringValue masked_pan = 11;
}

/******************************************/
/* REMOVE PAYMENT BY CODE */
message RemovePaymentByCodeRequest {
	int32 userId = 1;
	string code = 2;
	ServiceType serviceType = 3;
}

/******************************************/
/* GET PAYMENTs BY CODEs */
message GetPaymentsByCodesRequest {
	repeated string codes = 1;
}

message GetPaymentsByCodesResponse {		
	repeated PaymentInfo payments = 1;
}

message PaymentInfo {
    int32 id = 1;
   	int32 userId = 2;
   	int32 amount = 3;
   	google.protobuf.StringValue ipgToken = 4;
   	google.protobuf.StringValue ipgRefId = 5;
   	bool isPaid = 6;
   	google.protobuf.StringValue description = 7;
   	google.protobuf.StringValue payerName = 8;
   	google.protobuf.StringValue payerIdentity =9;
   	bool isRequest = 10;
   	string code = 11;
   	google.protobuf.StringValue refId = 12;
   	string clientId = 13;
   	string clientRefId = 14;
   	google.protobuf.Timestamp payDate = 16;
}