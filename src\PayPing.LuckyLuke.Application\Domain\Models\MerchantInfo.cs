﻿using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class MerchantInfo : BaseEntity<int>, IAuditableEntity
    {
        public int MerchantUserId { get; set; }

        public MerchantInfoAgent MerchantInfoAgent { get; set; }
        public string Email { get; set; }
        public string ProvinceEnglish { get; set; }
        public string CityEnglish { get; set; }
        public string PostalCode { get; set; }
        public string Address { get; set; }

        public string PredefinedCode { get; set; }

        public decimal MinPrePaymentRate { get; set; }

        public decimal InterestRate { get; set; }

        // signature image jpg under 50kb
        public string SignatureImageFileId { get; set; }
        public string SignatureImageFileName { get; set; }

        public DateTimeOffset? LastModifiedAt { get; set; }
        public int? LastModifiedBy { get; set; }

    }


}
