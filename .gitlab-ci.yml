image: docker:latest

variables:
  DOCKER_TLS_CERTDIR: "/certs"
  PROJECT_URL: gitlab.peypin.ir:5050/manatadbir/organization/technical/code/bnplservice
  BINARIES_DIRECTORY: 'src/*/bin'
  OBJECTS_DIRECTORY: 'src/*/obj'
  KUBECONFIG: /root/.kube/config
  DOTNETRUNV: $DOTNET9RUNHOST
  DOTNETSDKV: $DOTNET9SDKHOST

# stages:
#   - build
#   - containerize
#   - deploy
#   - publish

include:
  - project: manatadbir/organization/infrastructure/devops/ci-manager
    file: stages/dotnet.yml
    ref: master
    rules:
      - if: $Internal_Gitlab == "Yes"

  - project: manatadbir/organization/infrastructure/devops/ci-manager
    file: ci-engine-dotnet.yml
    ref: master
    inputs:
      job_suffix: 'merchant'
      project_path: 'src/PayPing.LuckyLuke.Api.Merchant/PayPing.LuckyLuke.Api.Merchant.csproj'
      dll_filename: 'PayPing.LuckyLuke.Api.Merchant.dll'
      deployment_name: 'bnpl-merchant'
      port: 5050
    rules:
      - if: $Internal_Gitlab == "Yes"
  - project: manatadbir/organization/infrastructure/devops/ci-manager
    file: ci-engine-dotnet.yml
    ref: feature/loop-autovalues
    inputs:
      job_suffix: 'consumer'
      project_path: 'src/PayPing.LuckyLuke.Api/PayPing.LuckyLuke.Api.csproj'
      dll_filename: 'PayPing.LuckyLuke.Api.dll'
      deployment_name: 'bnpl-consumer'
      port: 5050
    rules:
      - if: $Internal_Gitlab == "Yes"
  - project: manatadbir/organization/infrastructure/devops/ci-manager
    file: ci-engine-dotnet.yml
    ref: feature/loop-autovalues
    inputs:
      job_suffix: 'admin'
      project_path: 'src/PayPing.LuckyLuke.Api.Admin/PayPing.LuckyLuke.Api.Admin.csproj'
      dll_filename: 'PayPing.LuckyLuke.Api.Admin.dll'
      deployment_name: 'bnpl-admin'
      port: 5050
    rules:
      - if: $Internal_Gitlab == "Yes"

  - project: manatadbir/organization/infrastructure/devops/ci-manager
    file: ci-engine-common.yml
    ref: feature/loop-autovalues
    inputs:
      job_suffix: 'admin'    
    rules:
      - if: $Internal_Gitlab == "Yes"
