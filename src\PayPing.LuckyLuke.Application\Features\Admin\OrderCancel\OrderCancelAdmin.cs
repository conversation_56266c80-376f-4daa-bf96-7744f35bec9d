﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Services.User;

namespace PayPing.LuckyLuke.Application.Features.Admin.OrderCancel
{
    public record OrderCancelAdminRequest(int merchantUserId, Guid trackingCode);

    public record OrderCancelAdminResponse(Guid trackingCode);


    public class OrderCancelAdminEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                AdminRoutes.OrderCancel,
                async (
                    OrderCancelAdminRequest request,
                    IOrderCancelAdminRequestHandler handler,
                    IValidator<OrderCancelAdminRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("admin")
            .WithName("OrderCancelAdmin")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<OrderCancelAdminResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Cancel Order By Admin")
            .WithDescription("Cancel Order By Admin")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OrderCancelAdminRequestHandler : IOrderCancelAdminRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly INotifyService _notifyService;
        private readonly IWalletService _walletService;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IUserContext _userContext;
        private readonly ILogger<OrderCancelAdminRequestHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public OrderCancelAdminRequestHandler(ApplicationDbContext dbContext, INotifyService notifyService, IWalletService walletService, IGuaranteeService guaranteeService, IUserContext userContext, IOptions<BNPLOptions> bnplOptions, ILogger<OrderCancelAdminRequestHandler> logger)
        {
            _dbContext = dbContext;
            _notifyService = notifyService;
            _walletService = walletService;
            _guaranteeService = guaranteeService;
            _userContext = userContext;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<OrderCancelAdminResponse> HandleAsync(OrderCancelAdminRequest request, CancellationToken cancellationToken)
        {
            var order = await _dbContext.Orders.Where(o => o.TrackingCode == request.trackingCode)
                .Include(o => o.OrderGuarantees)
                .FirstOrDefaultAsync();
            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.MerchantUserId != request.merchantUserId)
            {
                throw new ArgumentException("سفارش متعلق به پذیرنده نیست");
            }

            var cancelableStatuses = OrderStatusProvider.GetMerchantBeforePaymentCancelable();
            if (!cancelableStatuses.Contains(order.Status))
            {
                throw new ArgumentException("امکان لغو سفارش وجود ندارد");
            }

            if (order.PlanIsLocked)
            {
                if (!string.IsNullOrWhiteSpace(order.CreditLockId))
                {
                    // unfreeze as much as credited amount
                    var unlockResult = await _walletService.UnLockCreditAndSaveAsync(
                        order.OrderPlan.WalletId,
                        order.CreditLockId,
                        order.CreditedAmount,
                        cancellationToken);

                    // add a suspecious credit on failure
                    if (!unlockResult.Success)
                    {
                        await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                            order.OrderPlan.CreditId,
                            order.OrderPlan.WalletId,
                            order.CreditedAmount,
                            CreditTransactionType.UnFreeze,
                            order.CreditLockId,
                            unlockResult.Error);
                    }
                }

                if (order.OrderGuarantees != null && order.OrderGuarantees.Any())
                {
                    var guaranteeProvider = await _guaranteeService.GetGuaranteeProviderByGuarantorId(order.OrderPlan.GuarantorId);

                    // revoke guarantee
                    var guarantee = order.OrderGuarantees.OrderByDescending(g => g.CreatedAt).First();
                    var revokeResult = await guaranteeProvider.DeleteGuaranteeAsync(guarantee.Data);
                    if (!revokeResult.success)
                    {
                        _logger.LogWarning($"could not revoke guarantee order id: {order.Id}, message: {revokeResult.error}");

                        _dbContext.OrderExternalEventOutboxes.Add(new OrderExternalEventOutbox()
                        {
                            OrderId = order.Id,
                            EventType = OrderExternalEventType.RevokeGuarantee,
                            HasFailed = true,
                            FailCount = 1,
                            Message = revokeResult.error
                        });
                    }

                    order.GuaranteeRevoked = revokeResult.success;
                }
            }

            order.Status = OrderStatus.CanceledBeforePaymentByMerchant;

            // schedule a webhook for client with clientrefid
            if (!string.IsNullOrWhiteSpace(order.ClientCancelUrl))
            {
                _dbContext.OrderExternalEventOutboxes.Add(new OrderExternalEventOutbox()
                {
                    OrderId = order.Id,
                    ClientRefId = order.ClientRefId,
                    ClientCancelUrl = order.ClientCancelUrl,
                    EventType = OrderExternalEventType.CancelHook
                });
            }

            await _dbContext.SaveChangesAsync();

            // send consumer canceled by merch notification
            await _notifyService.SendCanceledByMerchantMessage(order.ConsumerUserName, order.MerchantUserId, cancellationToken);

            return new OrderCancelAdminResponse(order.TrackingCode);
        }
    }

    public interface IOrderCancelAdminRequestHandler
    {
        ValueTask<OrderCancelAdminResponse> HandleAsync(OrderCancelAdminRequest request, CancellationToken cancellationToken);
    }

    public class OrderCancelAdminValidator : AbstractValidator<OrderCancelAdminRequest>
    {
        public OrderCancelAdminValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}
