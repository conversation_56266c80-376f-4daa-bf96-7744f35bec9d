﻿syntax = "proto3";

option csharp_namespace = "PayPing.Refund.WebApi.gRPC";

import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";
import "Grpc/Protos/Types/RefundEnumerations.proto";
import "Grpc/Protos/Types/RefundGrpcProblemDetails.proto";

package RefundGrpcServices;

service RefundOperationService {
   rpc CreateRefund (CreateRefundRequest) returns (CreateRefundResponse);
   rpc GetRefundDetails (GetRefundDetailsRequest) returns (GetRefundDetailsResponse);
}

message CreateRefundRequest {
	int32 userId = 1;
	int32 businessId = 2;
	string paymentCode = 3;
	google.protobuf.Int32Value amount = 4;
	WagePayer wagePayer = 5;
	google.protobuf.StringValue clientReference = 6;
}

message CreateRefundSuccessResult {
	string code = 1;
	bool isPendingRecharge = 2;
}

message CreateRefundResponse {
	oneof result {
		CreateRefundSuccessResult success = 1;
		GrpcProblemDetails failure = 2;
	}
}

message GetRefundDetailsRequest {
	int32 userId = 1;
	int32 businessId = 2;
	string code = 3;
}

message RefundDetails {
	string code = 1;
	RefundOperationState state = 2;
	string localizedState = 3;
	string paymentCode = 4;
	int32 requestedAmount = 5;
	int32 refundAmount = 6;
	int32 refundWage = 7;
	WagePayer wagePayer = 8;
	string localizedWagePayer = 9;
	string maskedPAN = 10;
	google.protobuf.Timestamp creationDateTime = 11;
	google.protobuf.Timestamp preparationDateTime = 12;
	google.protobuf.Timestamp processStartDateTime = 13;
	google.protobuf.Timestamp failureDateTime = 14;
	google.protobuf.Timestamp successDateTime = 15;
	google.protobuf.Timestamp cancellationDateTime = 16;
	google.protobuf.StringValue transferReference = 17;
	google.protobuf.StringValue bankTrackingCode = 18;
}

message GetRefundDetailsResponse {
	oneof result {
		RefundDetails success = 1;
		GrpcProblemDetails failure = 2;
	}
}