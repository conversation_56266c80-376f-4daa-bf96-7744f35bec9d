﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.Tools.SdkBase.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public interface IUserApiClient
    {
        ValueTask<ServiceResult<UserApiGetUserExtraResponse>> GetUserExtraAsync(int userId, CancellationToken cancellationToken = default);
    }
}
