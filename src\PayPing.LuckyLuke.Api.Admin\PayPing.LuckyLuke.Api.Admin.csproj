﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>98370022-CC4C-4F96-946A-6652ADF88D11</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PayPing.LuckyLuke.Application\PayPing.LuckyLuke.Application.csproj" />
  </ItemGroup>

</Project>
