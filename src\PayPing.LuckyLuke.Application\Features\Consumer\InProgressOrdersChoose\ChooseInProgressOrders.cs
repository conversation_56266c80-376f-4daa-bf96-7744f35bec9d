﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Features.Consumer._01_SetPlan;
using PayPing.LuckyLuke.Application.Features.Consumer._40_OrderCancel;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer.InProgressOrdersChoose
{
    public record ChooseInProgressOrdersRequest(Guid trackingCode, int planId);

    public class ChooseInProgressOrdersEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.ChooseInProgressOrders,
                async (
                    ChooseInProgressOrdersRequest request,
                    IChooseInProgressOrdersRequestHandler handler,
                    IValidator<ChooseInProgressOrdersRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok();
                })
            .RequireAuthorization("write")
            .WithName("ChooseInProgressOrders")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Choose InProgress Orders")
            .WithDescription("Choose InProgress Orders")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class ChooseInProgressOrdersRequestHandler : IChooseInProgressOrdersRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly INotifyService _notifyService;
        private readonly ISetPlanRequestHandler _setPlanRequestHandler;
        private readonly IOrderCancelConsumerRequestHandler _orderCancelConsumerRequestHandler;
        private readonly BNPLOptions _bnplOptions;

        public ChooseInProgressOrdersRequestHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            INotifyService notifyService,
            ISetPlanRequestHandler setPlanRequestHandler,
            IOrderCancelConsumerRequestHandler orderCancelConsumerRequestHandler,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _notifyService = notifyService;
            _setPlanRequestHandler = setPlanRequestHandler;
            _orderCancelConsumerRequestHandler = orderCancelConsumerRequestHandler;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask HandleAsync(ChooseInProgressOrdersRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var nathash = _userContext.CurrentUserNationalCode.HashSHA256NationalCode();

            var expirableinprogressStatuses = OrderStatusProvider.GetExpirable();

            var userInProgressOrders = await _dbContext.Orders.AsNoTracking()
                .Where(o =>
                    o.TrackingCode == request.trackingCode || 
                    (
                        o.ConsumerNationalHashId == nathash &&
                        expirableinprogressStatuses.Contains(o.Status) &&
                        o.ExpireDate > DateTimeOffset.UtcNow
                    ))
                .ToListAsync();

            if (userInProgressOrders != null && userInProgressOrders.Count > 0)
            {
                foreach (var item in userInProgressOrders.OrderBy(x => x.ExpireDate))
                {
                    if (item.TrackingCode == request.trackingCode)
                    {
                        if (item.Status == OrderStatus.Init)
                        {
                            await _setPlanRequestHandler.HandleAsync(new SetPlanRequest(request.trackingCode, request.planId), cancellationToken);
                        }
                    }
                    else
                    {
                        await _orderCancelConsumerRequestHandler.HandleAsync(new OrderCancelConsumerRequest(item.TrackingCode), cancellationToken);
                    }
                }
            }
        }
    }

    public interface IChooseInProgressOrdersRequestHandler
    {
        ValueTask HandleAsync(ChooseInProgressOrdersRequest request, CancellationToken cancellationToken);
    }

    public class ChooseInProgressOrdersValidator : AbstractValidator<ChooseInProgressOrdersRequest>
    {
        public ChooseInProgressOrdersValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
            RuleFor(x => x.planId).GreaterThan(0).WithResourceError(() => ValidatorDictionary.plan_id_is_required);

        }
    }
}
