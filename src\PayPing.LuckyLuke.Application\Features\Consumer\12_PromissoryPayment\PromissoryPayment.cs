﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Factory;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Services.Guarantee;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Features.Consumer._12_PromissoryPayment
{
    public record PromissoryPaymentRequest(Guid trackingCode);

    //public class PromissoryPaymentEndpoint : IMinimalEndpoint
    //{
    //    public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
    //    {
    //        builder.MapPost(
    //            ConsumerRoutes.PromissoryPayment,
    //            async (
    //                PromissoryPaymentRequest request,
    //                IPromissoryPaymentRequestHandler handler,
    //                IValidator<PromissoryPaymentRequest> validator,
    //                CancellationToken cancellationToken) =>
    //            {

    //                var validationResult = await validator.ValidateAsync(request, cancellationToken);
    //                if (!validationResult.IsValid)
    //                {
    //                    throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
    //                }

    //                var result = await handler.HandleAsync(request, cancellationToken);

    //                return Results.Ok(result);
    //            })
    //        .RequireAuthorization("write")
    //        .WithName("PromissoryPayment")
    //        .WithApiVersionSet(builder.NewApiVersionSet("Promissory").Build())
    //        .Produces<PromissoryCheckPointResponse>()
    //        .ProducesProblem(StatusCodes.Status400BadRequest)
    //        .WithSummary("Promissory Payment")
    //        .WithDescription("Promissory Payment")
    //        .WithOpenApi()
    //        .HasApiVersion(1.0);

    //        return builder;
    //    }
    //}

    public class PromissoryPaymentRequestHandler : IPromissoryPaymentRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IUserContext _userContext;
        private readonly BNPLOptions _bnplOptions;

        public PromissoryPaymentRequestHandler(
            ApplicationDbContext dbContext,
            IGuaranteeService guaranteeService,
            IUserContext userContext,
            IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _guaranteeService = guaranteeService;
            _userContext = userContext;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<PromissoryCheckPointResponse> HandleAsync(PromissoryPaymentRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders
                .Where(o => o.TrackingCode == request.trackingCode)
                .Include(o => o.OrderGuarantees)
                .ThenInclude(g => g.Guarantor)
                .FirstOrDefaultAsync(cancellationToken);

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            {
                throw new OrderExpiredException(request.trackingCode.ToString(), "سفارش منقضی شده است");
            }

            if (order.ConsumerUserId != userId)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);
            }

            if (order.Status != OrderStatus.GuaranteeInProgress && order.Status != OrderStatus.GuaranteeFailed)
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "سفته در جریان نیست", string.Empty);
            }

            if (!order.OrderGuarantees.Any())
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "نوع ضمانت سفته تعیین نشده است", string.Empty);
            }

            var orderGuarantee = CurrentGuarantee(order.OrderGuarantees);

            IPromissoryGuaranteeProvider provider = ChooseProvider(orderGuarantee);

            PromissoryCheckPointResponse checkPoint = await provider.PushToNextCheckPointAsync(orderGuarantee.Data, cancellationToken);

            if (checkPoint.isDone)
            {
                order.Status = OrderStatus.GuaranteeSucceeded;

                await _dbContext.SaveChangesAsync();

                return new PromissoryCheckPointResponse(true, true, string.Empty, Infrastructure.Web.OrderSteps.Quote, checkPoint.data);
            }

            return checkPoint;
        }

        private OrderGuarantee CurrentGuarantee(ICollection<OrderGuarantee> guarantees)
        {
            return guarantees.OrderByDescending(x => x.CreatedAt).FirstOrDefault();
        }

        private IPromissoryGuaranteeProvider ChooseProvider(OrderGuarantee guarantee)
        {
            var pt = guarantee.Guarantor.GuaranteeProvider;
            return _guaranteeService.GetPromissoryProviderByProvider(pt);
        }
    }

    public interface IPromissoryPaymentRequestHandler
    {
        ValueTask<PromissoryCheckPointResponse> HandleAsync(PromissoryPaymentRequest request, CancellationToken cancellationToken);
    }

    public class PromissoryPaymentValidator : AbstractValidator<PromissoryPaymentRequest>
    {
        public PromissoryPaymentValidator()
        {
            RuleFor(x => x.trackingCode).NotEqual(Guid.Empty).WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}
