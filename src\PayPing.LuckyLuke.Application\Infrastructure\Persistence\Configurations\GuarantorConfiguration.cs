﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PayPing.BNPL.Domain.Models;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Configurations;

public class GuarantorConfiguration : IEntityTypeConfiguration<Guarantor>
{
    public void Configure(EntityTypeBuilder<Guarantor> builder)
    {
        builder.Property(nameof(Guarantor.Name)).HasMaxLength(256);
        builder.Property(nameof(Guarantor.Domain)).HasMaxLength(256);

        builder.HasIndex(x => x.Code).IsUnique().HasDatabaseName("IX_Guarantor_Code");
        builder.HasIndex(x => x.Name).HasDatabaseName("IX_Guarantor_Name");

        builder
            .HasMany(e => e.OrderGuarantees)
            .WithOne(e => e.Guarantor)
            .HasForeignKey(e => e.GuarantorId)
            .IsRequired();
    }
}
