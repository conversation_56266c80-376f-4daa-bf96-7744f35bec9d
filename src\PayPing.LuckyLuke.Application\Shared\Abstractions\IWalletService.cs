﻿using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;

namespace PayPing.LuckyLuke.Application.Shared.Abstractions;

public interface IWalletService
{
    ValueTask<int> AddCreditTransactionOutboxAndSaveAsync(int creditId, Guid walletId, decimal amount, CreditTransactionType transactionType, string lockId, string message);
    ValueTask<int> AddFailedCreditTransactionOutboxAndSaveAsync(int creditId, Guid walletId, decimal amount, CreditTransactionType transactionType, string lockId, string message);

    Task<CreateWalletResponse> CreateWallet(int userId, Guid creditCode, CancellationToken cancellationToken = default);
    ValueTask<GetWalletCreditInfoResponse> GetWalletCreditInfoAsync(Guid walletId, CancellationToken cancellationToken);
    Task<IncreaseWalletCreditResponse> IncreaseWalletCreditAndSaveAsync(Guid walletId, decimal amount, CancellationToken cancellationToken);
    Task<LockWalletCreditResponse> LockCreditAndSaveAsync(Guid walletId, decimal amount, CancellationToken cancellationToken);
    Task<PartialWithdrawWalletCreditResponse> WithdrawCreditAndSaveAsync(Guid walletId, string lockId, decimal amount, CancellationToken cancellationToken);
    Task<UnBlockWalletCreditResponse> UnLockCreditAndSaveAsync(Guid walletId, string lockId, decimal amount, CancellationToken cancellationToken);
    Task<IncreaseWalletCreditResponse> ResetWalletCreditAmountAndSaveAsync(Guid walletId, decimal amount, int walletOwnerUserId, CancellationToken cancellationToken);
}