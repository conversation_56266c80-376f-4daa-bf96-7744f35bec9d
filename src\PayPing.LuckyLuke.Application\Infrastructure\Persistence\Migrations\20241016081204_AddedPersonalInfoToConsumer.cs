﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddedPersonalInfoToConsumer : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FirstName",
                table: "ConsumerInfos",
                type: "character varying(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FullName",
                table: "ConsumerInfos",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastName",
                table: "ConsumerInfos",
                type: "character varying(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NationalCode",
                table: "ConsumerInfos",
                type: "character varying(64)",
                maxLength: 64,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PersianBirthDate",
                table: "ConsumerInfos",
                type: "character varying(64)",
                maxLength: 64,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PhoneNumber",
                table: "ConsumerInfos",
                type: "character varying(64)",
                maxLength: 64,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UserName",
                table: "ConsumerInfos",
                type: "character varying(64)",
                maxLength: 64,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FirstName",
                table: "ConsumerInfos");

            migrationBuilder.DropColumn(
                name: "FullName",
                table: "ConsumerInfos");

            migrationBuilder.DropColumn(
                name: "LastName",
                table: "ConsumerInfos");

            migrationBuilder.DropColumn(
                name: "NationalCode",
                table: "ConsumerInfos");

            migrationBuilder.DropColumn(
                name: "PersianBirthDate",
                table: "ConsumerInfos");

            migrationBuilder.DropColumn(
                name: "PhoneNumber",
                table: "ConsumerInfos");

            migrationBuilder.DropColumn(
                name: "UserName",
                table: "ConsumerInfos");
        }
    }
}
