﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Clients.Grpc;
using PayPing.LuckyLuke.Application.Shared.Clients.Http;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading;

namespace PayPing.LuckyLuke.Application.Shared.Services.DigitalSign.Ki<PERSON><PERSON>han
{
    public abstract class KiahooshanBaseDigitalSignHandler
    {
        protected readonly ApplicationDbContext _dbContext;
        protected readonly IUserContext _userContext;
        protected readonly IKiahooshanApiHttpClient _kihooApi;
        protected readonly IS3ServiceApiClient _s3ServiceApiClient;
        protected readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly IMemoryCache _memoryCache;
        protected readonly KiahooshanOptions _kihooOptions;
        
        protected KiahooshanBaseDigitalSignHandler(
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKiahooshanApiHttpClient kihooApi,
            IOptions<KiahooshanOptions> kihooOptions,
            IS3ServiceApiClient s3ServiceApiClient,
            IUploadGrpcClient uploadGrpcClient,
            IMemoryCache memoryCache)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _kihooApi = kihooApi;
            _s3ServiceApiClient = s3ServiceApiClient;
            _uploadGrpcClient = uploadGrpcClient;
            _memoryCache = memoryCache;
            _kihooOptions = kihooOptions.Value;
        }

        protected async ValueTask<byte[]> GetCachedSelfieVideoAsync(KiahooshanDigitalSignContextV1 context, string signatureVideoFileId, CancellationToken cancellationToken)
        {
            string key = $"{ApplicationConstants.KiahooshanVideoCacheKey}_{signatureVideoFileId}";

            if (!_memoryCache.TryGetValue(key, out byte[] selfievideo))
            {
                var cacheEntryOptions = new MemoryCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(3) };

                var presignedUrl = await _uploadGrpcClient.GetPresignedUrlAsync(signatureVideoFileId, context.UserId, FileManager.Grpc.UploadTypeEnum.Misc, cancellationToken);

                using Stream sigVideo = await _s3ServiceApiClient.DownloadAsync(presignedUrl.SuccessResult, cancellationToken);

                var sv = await sigVideo.ToByteArrayAsync();

                _memoryCache.Set(key, sv, cacheEntryOptions);

                return sv;
            }

            return selfievideo;
        }

        protected async ValueTask<int> UpdateContextAsync(KiahooshanDigitalSignContextV1 context)
        {
            var og = await _dbContext.DigitalSigns.Where(x => x.Id == context.DigitalSignId).FirstOrDefaultAsync();

            Guard.Against.Null(og);

            og.Data = JsonSerializer.Serialize(context);

            return await _dbContext.SaveChangesAsync();
        }

        protected virtual void ValidateContext(KiahooshanDigitalSignContextV1 context)
        {
            Guard.Against.Null(context);

            Guard.Against.NullOrEmpty(context.NationalCode);
            Guard.Against.NullOrEmpty(context.Mobile);
            Guard.Against.NullOrEmpty(context.FullNameFa);
            Guard.Against.NullOrEmpty(context.Address);
            Guard.Against.NullOrEmpty(context.PostalCode);
            Guard.Against.NullOrEmpty(context.Iban);


        }

    }
}
