﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients;

public interface ICreditService
{
    Task<IncreasePrePaymentSumOnCreditResponse> IncreasePrePaymentSumOnCreditAndSaveAsync(int creditId, bool increaseCount, decimal rawPrepaymentAmount, decimal merchWageAmount, decimal consumerWageAmount, CancellationToken cancellationToken);
    Task<IncreaseInstallmentPaymentSumOnCreditResponse> IncreaseInstallmentSumOnCreditAndSaveAsync(int creditId, decimal installmentPaymentAmount, CancellationToken cancellationToken);
    Task<DecreaseRawPrePaymentSumOnCreditResponse> DecreaseRawPrePaymentSumOnCreditAndSaveAsync(int creditId, decimal rawPrepaymentAmount, CancellationToken cancellationToken);
}