﻿using DNTPersianUtils.Core;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Web;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Infrastructure.Provider
{
    public class SalaryGuaranteeProvider : ISalaryGuaranteeProvider
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IUserService _userService;
        private readonly ApplicationDbContext _dbContext;

        public GuaranteeType GuaranteeType => GuaranteeType.Salary;

        public SalaryGuaranteeProvider(IWebHostEnvironment environment, IUserService userService, ApplicationDbContext dbContext)
        {
            _environment = environment;
            _userService = userService;
            _dbContext = dbContext;
        }

        public (bool isValid, string error, long? guaranteeId) ValidateIssuance(Order order)
        {
            switch (order.Status)
            {

                case OrderStatus.ProfileFilled:
                case OrderStatus.GuaranteeFailed:
                    return (true, null, null);
                case OrderStatus.GuaranteeInProgress:
                    {
                        var guarantee = order.OrderGuarantees?.OrderByDescending(x => x.CreatedAt).FirstOrDefault();
                        if (guarantee != null)
                        {
                            return (true, null, guarantee.Id);
                        }
                        else
                        {
                            return (true, null, null);
                        }
                    }
                default:
                    return (false, "سفارش در این مرحله قرار ندارد", null);
            }
        }

        public async ValueTask<int> CurrentGuarantorIdAsync(CancellationToken cancellationToken)
        {
            return await _dbContext.Guarantors.Where(x => x.GuaranteeType == GuaranteeType.Salary && x.GuaranteeProvider == GuaranteeProvider.Salary).Select(x => x.Id).FirstOrDefaultAsync(cancellationToken);
        }

        public OrderGuaranteeDto CreateOrderGuarantee(
            long id,
            long orderId,
            int guarantorId,
            int consumerId,
            DateTimeOffset dueDate,
            CancellationToken cancellationToken)
        {
            var context = new SalaryGuaranteeContextV1
            {
                OrderId = orderId,
                OrderGuaranteeId = id,
                DueDate = dueDate.ToShortPersianDateString(),
                Description = "جهت خرید اقساطی با ضمانت کسر از حقوق",
                ConsumerUserId = consumerId,
            };

            return new OrderGuaranteeDto(orderId, guarantorId, JsonSerializer.Serialize(context));
        }

        public (bool setNext, OrderSteps? next, List<(string name, OrderSteps code)> steps) GetSteps(string context)
        {
            return new(false, null, new List<(string name, OrderSteps code)>());
        }

        public async ValueTask<(bool success, string error)> DeleteGuaranteeAsync(string contextData)
        {
            return new(true, string.Empty);
        }

        public string GetFinalGuaranteeDocumentId(string contextData)
        {
            return string.Empty;
        }

        public string GetFinalSettlementGuaranteeDocumentId(string contextData)
        {
            return string.Empty;
        }
    }
}
