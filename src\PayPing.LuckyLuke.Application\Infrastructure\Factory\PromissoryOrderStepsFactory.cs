﻿using PayPing.LuckyLuke.Application.Domain.Enums;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class PromissoryOrderStepsFactory : IOrderStepsFactory
    {
        private readonly IContractMandatoryStepsProvider _contractMandatoryStepsProvider;
        private readonly IContractLessStepsProvider _contractLessStepsProvider;

        public PromissoryOrderStepsFactory(IEnumerable<IContractMandatoryStepsProvider> contractMandatoryStepsProviders, IEnumerable<IContractLessStepsProvider> contractLessStepsProviders)
        {
            _contractMandatoryStepsProvider = contractMandatoryStepsProviders.Where(x => x.GuaranteeType == GuaranteeType.Promissory).First();
            _contractLessStepsProvider = contractLessStepsProviders.Where(x => x.GuaranteeType == GuaranteeType.Promissory).First();
        }

        public GuaranteeType GuaranteeType => GuaranteeType.Promissory;

        public IContractMandatoryStepsProvider CreateContractMandatory()
        {
            return _contractMandatoryStepsProvider;
        }

        public IContractLessStepsProvider CreateContractLess()
        {
            return _contractLessStepsProvider;
        }
    }
}
