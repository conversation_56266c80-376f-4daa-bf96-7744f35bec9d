﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System.Reflection.Metadata;
using PayPing.BNPL.Application.Infrastructure.Persistence.NpgSql;
using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Application.Infrastructure.Persistence.Extensions
{
    public static class ModelBuilderExtensions
    {
        public static ModelBuilder EntitiesOfType<T>(this ModelBuilder modelBuilder,
        Action<EntityTypeBuilder> buildAction) where T : class
        {
            return modelBuilder.EntitiesOfType(typeof(T), buildAction);
        }

        public static ModelBuilder EntitiesOfType(this ModelBuilder modelBuilder, Type type,
            Action<EntityTypeBuilder> buildAction)
        {
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
                if (type.IsAssignableFrom(entityType.ClrType))
                    buildAction(modelBuilder.Entity(entityType.ClrType));

            return modelBuilder;
        }

        public static ModelBuilder ConfigureBaseEntityTypes(this ModelBuilder modelBuilder)
        {
            modelBuilder.EntitiesOfType<IBaseEntity>(builder =>
            {
                builder.Property(nameof(IBaseEntity.CreatedAt)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);
            });

            return modelBuilder;
        }

        public static ModelBuilder ConfigureAuditableEntityTypes(this ModelBuilder modelBuilder)
        {
            modelBuilder.EntitiesOfType<ISoftDeletableEntity>(builder =>
            {
                builder.Property(nameof(IAuditableEntity.LastModifiedAt)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);
            });

            return modelBuilder;
        }

        public static ModelBuilder ConfigureSoftDeletableEntityTypes(this ModelBuilder modelBuilder)
        {
            modelBuilder.EntitiesOfType<ISoftDeletableEntity>(builder =>
            {
                builder.Property(nameof(ISoftDeletableEntity.DeletedAt)).HasColumnType(NpgSqlConstants.TimestampWithTimezoneColumnType);

                // query filter

                var param = Expression.Parameter(builder.Metadata.ClrType, "p");
                var body = Expression.Equal(Expression.Property(param, nameof(ISoftDeletableEntity.IsDeleted)), Expression.Constant(false));
                builder.HasQueryFilter(Expression.Lambda(body, param));
            });

            return modelBuilder;
        }
    }
}
