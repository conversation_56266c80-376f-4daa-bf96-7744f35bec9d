﻿syntax = "proto3";

option csharp_namespace = "UserGrpc";

package UserGrpc;

import "google/protobuf/timestamp.proto";
import "Grpc/Protos/Enums/UserVerificationType.proto";

service UserService {
	rpc GetUserProfile (UserProfileRequest) returns (UserProfileResponse);
    rpc GetUserIdentities (UserIdentitiesRequest) returns (UserIdentitiesResponse);
}

message UserProfileRequest {
	int32 userId = 1;
}

message UserProfileResponse {
	UserVerificationType verify_type = 1;
	google.protobuf.Timestamp trial_date = 2;
	int32 invited_by = 3;
}

message UserIdentitiesRequest {
    repeated string identities = 1;
}

message UserIdentityResult {
    int32 id = 1;
    bool is_pardakhtyar = 2;
    string sheba = 3;
    bool is_settlement = 4;
    string identity = 5;
	google.protobuf.Timestamp trial_date = 6;
}

message UserIdentitiesResponse {
    repeated UserIdentityResult results = 1;
}
