﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PayPing.LuckyLuke.Application.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class PromissoryContext : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FileId",
                table: "OrderGuarantees");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "OrderGuarantees");

            migrationBuilder.AddColumn<string>(
                name: "ClientCallbackUrl",
                table: "Orders",
                type: "character varying(512)",
                maxLength: 512,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Data",
                table: "OrderGuarantees",
                type: "jsonb",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Client<PERSON>allbackUrl",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "Data",
                table: "OrderGuarantees");

            migrationBuilder.AddColumn<string>(
                name: "FileId",
                table: "OrderGuarantees",
                type: "character varying(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "OrderGuarantees",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }
    }
}
