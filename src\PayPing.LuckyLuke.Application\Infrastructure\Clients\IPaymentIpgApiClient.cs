﻿using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;

namespace PayPing.LuckyLuke.Application.Infrastructure.Clients
{
    public interface IPaymentIpgApiClient
    {
        ValueTask<PaymentApiPayResponse> PayInstallmentAsync(PaymentIpgApiPayRequest model, CancellationToken cancellationToken = default);
        ValueTask<PaymentApiVerifyResponse> VerifyInstallmentAsync(PaymentIpgApiVerifyRequest model, CancellationToken cancellationToken = default);

    }
}
