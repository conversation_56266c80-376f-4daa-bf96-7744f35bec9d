﻿using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Web.Routes
{
    public static class ConsumerRoutes
    {
        private const string ConsumerApplicationPrefixUri = "v{version:apiVersion}/bnpl/consumer";

        public static string Dummy => $"{ConsumerApplicationPrefixUri}/dummy";


        public static string SetPlan => $"{ConsumerApplicationPrefixUri}/plan/set";
        public static string PlanList => $"{ConsumerApplicationPrefixUri}/plan/list";

        public static string GetOrder => $"{ConsumerApplicationPrefixUri}/order/get";
        public static string GetQuote => $"{ConsumerApplicationPrefixUri}/order/quote";
        public static string OrderCancelConsumer => $"{ConsumerApplicationPrefixUri}/order/cancel";
        
        public static string OrderList => $"{ConsumerApplicationPrefixUri}/dashboard/order/list";
        public static string OrderDetail => $"{ConsumerApplicationPrefixUri}/dashboard/order/detail";
        public static string PaymentList => $"{ConsumerApplicationPrefixUri}/dashboard/payment/list";

        public static string GetSignature => $"{ConsumerApplicationPrefixUri}/signature/get";
        public static string UploadSignature => $"{ConsumerApplicationPrefixUri}/signature/upload";
        public static string UploadVideoSignature => $"{ConsumerApplicationPrefixUri}/signature/selfie/upload";
        public static string CheckSignature => $"{ConsumerApplicationPrefixUri}/signature/check";

        public static string GetPromissoryPreIssueInfo => $"{ConsumerApplicationPrefixUri}/promissory/getinfo";
        public static string PromissoryIssue => $"{ConsumerApplicationPrefixUri}/promissory/issue";
        public static string PromissorySigning => $"{ConsumerApplicationPrefixUri}/promissory/sign";
        public static string PromissoryPayment => $"{ConsumerApplicationPrefixUri}/promissory/pay";
        public static string PromissoryPaymentVerify => $"{ConsumerApplicationPrefixUri}/promissory/verify";

        public static string GetProfile => $"{ConsumerApplicationPrefixUri}/profile/get";
        public static string FillProfile => $"{ConsumerApplicationPrefixUri}/profile/fill";

        public static string PayPrePayment => $"{ConsumerApplicationPrefixUri}/order/prepay";
        public static string VerifyPayment => $"{ConsumerApplicationPrefixUri}/order/prepay/verify";
        public static string CreditScoreValidationOTP => $"{ConsumerApplicationPrefixUri}/credit-score/create";
        public static string CreditScoreValidationVerify => $"{ConsumerApplicationPrefixUri}/credit-score/verify";
        public static string CreditScoreInquiry => $"{ConsumerApplicationPrefixUri}/credit-score/inquiry";

        public static string InstallmentsList => $"{ConsumerApplicationPrefixUri}/installment/list";
        public static string InstallmentDetail => $"{ConsumerApplicationPrefixUri}/installment/detail";
        public static string InstallmentPayment => $"{ConsumerApplicationPrefixUri}/installment/pay";
        public static string InstallmentPaymentVerify => $"{ConsumerApplicationPrefixUri}/installment/verify";

        public static string InstallmentGroupPayment => $"{ConsumerApplicationPrefixUri}/installment/group/pay";
        public static string InstallmentGroupPaymentVerify => $"{ConsumerApplicationPrefixUri}/installment/group/verify";


        public static string GetDownloadPath => $"{ConsumerApplicationPrefixUri}/file/get";

        public static string GetContract => $"{ConsumerApplicationPrefixUri}/contract/get";
        public static string GetContractInfo => $"{ConsumerApplicationPrefixUri}/contract/info";
        public static string SignContract => $"{ConsumerApplicationPrefixUri}/contract/sign";

        public static string SendContinueOnPhoneMessage => $"{ConsumerApplicationPrefixUri}/message/continue-on-phone";

        public static string GetInProgressOrders => $"{ConsumerApplicationPrefixUri}/order/inprogress/get";
        public static string ChooseInProgressOrders => $"{ConsumerApplicationPrefixUri}/order/inprogress/choose";


        // front end routes
        public static string PlansListUI => $"pay/choose-plan";
        public static string GetOrderUI => $"pay/get-order";
        public static string KuknosPromissoryPaymentCallBackUI => $"pay/kuknospayment-callback";
        public static string PrePayResultUI => "pay/prepay-callback";
        public static string InstallmentVerifyUI => "pay/installment-verify-callback";
        public static string InstallmentDetailUI(Guid code) => $"home/installment/{code}";

    }
}
