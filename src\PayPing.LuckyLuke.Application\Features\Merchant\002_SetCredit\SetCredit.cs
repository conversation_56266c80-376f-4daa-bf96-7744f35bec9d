﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Merchant._002_SetCredit
{
    public record SetCreditRequest(int contractId);

    public record SetCreditResponse(int contractId);

    public class SetCreditEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.SetCredit,
                async (SetCreditRequest request, ISetCreditRequestHandler handler, IValidator<SetCreditRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("SetCredit")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<SetCreditResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Set Credit")
            .WithDescription("Set  Credit")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class SetCreditRequestHandler : ISetCreditRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;
        private readonly IWalletService _walletService;

        public SetCreditRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IWalletService walletService, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
            _walletService = walletService;
        }

        public async ValueTask<SetCreditResponse> HandleAsync(SetCreditRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var contract = await _dbContext.Contracts
              .Where(c => c.Id == request.contractId && c.ActivationStatus == ActivationStatus.CreateContract)
              .Include(p => p.Credits)
              .FirstOrDefaultAsync(cancellationToken);

            if (contract == null)
            {
                throw new ArgumentException("قرارداد فروش اقساطی فعال یافت نشد");
            }

            var previousContract = await _dbContext.Contracts.AsNoTracking()
                .Where(p => p.MerchantUserId == userId && p.Id != contract.Id)
                .Include(p => p.Credits)
                .OrderByDescending(p => p.ExpireDate)
                .FirstOrDefaultAsync(cancellationToken);

            var creditCode = Guid.NewGuid();
            
            Guid? walletId = null;
            if (previousContract is { Credits: not null } && previousContract.Credits.Any(c => c.IsActive))
            {
                walletId = previousContract.Credits.First(c => c.IsActive).WalletId;
            }

            if (!walletId.HasValue)
            {
                var wallet = await _walletService.CreateWallet(userId, creditCode, cancellationToken);
                if (!wallet.Success)
                {
                    throw new ArgumentException("خطایی در فرآیند ایجاد کیف پول رخ داده است");
                }
                
                walletId = wallet.WalletId;
            }
            
            Credit credit = new Credit()
            {
                IsActive = true,
                WalletId = walletId.Value,
                Code = creditCode,
            };

            contract.Credits.Add(credit);

            contract.ActivationStatus = ActivationStatus.SetCredit;

            await _dbContext.SaveChangesAsync(cancellationToken);

            return new SetCreditResponse(contract.Id);
        }

    }
    public interface ISetCreditRequestHandler
    {
        ValueTask<SetCreditResponse> HandleAsync(SetCreditRequest request, CancellationToken cancellationToken);
    }

    public class SetCreditValidator : AbstractValidator<SetCreditRequest>
    {
        public SetCreditValidator()
        {
            RuleFor(c => c.contractId)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.contract_id_is_required);
        }
    }

}
