﻿using DNTPersianUtils.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using Quartz;

namespace PayPing.LuckyLuke.Application.Features.Admin.InstallmentDueJob
{
    [DisallowConcurrentExecution]
    public class InstallmentDueJob : IJob
    {
        private readonly BackgroundJobOptions _jobOptions;
        private readonly ApplicationDbContext _dbContext;
        private readonly INotifyService _notifyService;
        private readonly ILogger<InstallmentDueJob> _logger;
        public InstallmentDueJob(IOptions<BackgroundJobOptions> jobOptions,
                                             ApplicationDbContext dbContext,
                                             INotifyService notifyService,
                                             ILogger<InstallmentDueJob> logger)
        {
            _jobOptions = jobOptions.Value;
            _dbContext = dbContext;
            _notifyService = notifyService;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            if (context.RefireCount > 10)
            {
                _logger.LogInformation($"At InstallmentDueJob Refire Count exceeded 10 times, short-circuiting job");
                return;
            }

            try
            {
                _logger.LogInformation($"At InstallmentDueJob started at {DateTimeOffset.UtcNow}");

                // since installments duedates had safe-scaped irantimezone
                // no duedate is set between 23:30 and 3:29 irantime
                // comparing dates makes no problem
                DateTimeOffset margin = DateTimeOffset.UtcNow.AddDays(1);

                var contextResult = new List<long>();

                var stats = OrderStatusProvider.GetInstallmentDueJobAcceptable();

                var installments = await _dbContext.Installments
                    .Where(i => 
                        i.Status != InstallmentStatus.PaidOff &&
                        stats.Contains(i.Order.Status) &&
                        i.PaymentStatus != PaymentStatus.PaymentSucceeded &&
                        i.DueDate.Date == margin.Date)
                    .Select(i => new InstallmentDueMessage
                    {
                        Id = i.Id,
                        ConsumerUserId = i.Order.ConsumerUserId,
                        ConsumerUserName = i.Order.ConsumerUserName,
                        MerchantUserId = i.Order.MerchantUserId,
                        DueDate = i.DueDate,
                        Amount = i.FinalAmount,
                        InstallmentCode = i.Code
                    })
                    .ToListAsync();

                if (installments != null && installments.Count > 0)
                {
                    _logger.LogInformation($"At InstallmentDueJob found {installments.Count} installments near to due date");

                    foreach (var installment in installments)
                    {
                        _logger.LogInformation($"At InstallmentDueJob found installment near to due date with id: {installment.Id}");

                        if (installment.ConsumerUserId.HasValue && !string.IsNullOrWhiteSpace(installment.ConsumerUserName)) 
                        {
                            await _notifyService.SendInstallmentDueMessage(
                                installment.ConsumerUserName,
                                installment.MerchantUserId,
                                installment.DueDate.ToShortPersianDateString(),
                                $"{installment.Amount:N0} تومان",
                                installment.InstallmentCode,
                                context.CancellationToken);

                            _logger.LogInformation($"At InstallmentDueJob, InstallmentDueMessage sent for consumer: {installment.ConsumerUserId.Value}, merchant: {installment.MerchantUserId}, installment code: {installment.InstallmentCode}");

                            contextResult.Add(installment.Id);
                        }
                    }

                    _logger.LogInformation($"At InstallmentDueJob sent {contextResult.Count} number of installments notifications.");
                }
                else
                {
                    _logger.LogInformation($"At InstallmentDueJob no due installments found");
                }

                context.Result = contextResult;
            }
            catch (Exception ex)
            {
                throw new JobExecutionException(msg: "At InstallmentDueJob exception happened", refireImmediately: false, cause: ex);
            }

        }

        public class InstallmentDueMessage
        {
            public long Id { get; set; }
            public int? ConsumerUserId { get; set; }
            public string ConsumerUserName { get; set; }
            public int MerchantUserId { get; set; }
            public DateTimeOffset DueDate { get; set; }
            public decimal Amount { get; set; }
            public Guid InstallmentCode { get; set; }
        }
    }
}
