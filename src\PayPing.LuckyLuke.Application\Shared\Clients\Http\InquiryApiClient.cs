﻿using Ardalis.GuardClauses;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using System.Net.Http.Json;
using System.Text.Json;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.Tools.SdkBase.Types;

namespace PayPing.LuckyLuke.Application.Shared.Clients.Http
{
    public class InquiryApiClient : IInquiryApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonSerializerOptions;


        public InquiryApiClient(HttpClient httpClient)
        {
            _httpClient = Guard.Against.Null(httpClient, nameof(httpClient));
            _jsonSerializerOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

        }


        public async Task<ServiceResult<NationalCodeToOwnerResponseModel>> GetPersonalInfoByNationalCode(string nationalCode, string birthDate, CancellationToken cancellationToken = default)
        {
            birthDate = birthDate.Replace("/", "");
            var url = $"v1/NationalCode/inquiry-with-personal-info?nationalCode={nationalCode}&birthDate={birthDate}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                return new ServiceResult<NationalCodeToOwnerResponseModel>
                {
                    Succeeded = false,
                    Message = message
                };
            }
            
            var res = await httpResponse.Content.ReadFromJsonAsync<NationalCodeToOwnerResponseModel>(_jsonSerializerOptions, cancellationToken: cancellationToken);
            
            return new ServiceResult<NationalCodeToOwnerResponseModel> 
            {
                Succeeded = true,
                SuccessResult = res
            };
        }

        public async Task<ServiceResult<MatchingResponseModel>> IsMatchingMobileWithNationalCode(string mobileNumber, string nationalCode, CancellationToken cancellationToken = default)
        {
            var url = $"v1/Matching/nationalcode-with-mobile?mobileNumber={mobileNumber}&nationalCode={nationalCode}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                return new ServiceResult<MatchingResponseModel>
                {
                    Succeeded = false,
                    Message = message
                };
            }
            
            var res = await httpResponse.Content.ReadFromJsonAsync<MatchingResponseModel>(_jsonSerializerOptions, cancellationToken: cancellationToken);

            return new ServiceResult<MatchingResponseModel>
            {
                Succeeded = true,
                SuccessResult = res
            };
        }

        public async Task<MatchingResponseModel> IsMatchingShebaWithNationalCode(string sheba, string nationalCode, string birthDate, CancellationToken cancellationToken = default)
        {
            birthDate = birthDate.Replace("/", "");
            var url = $"v1/Matching/nationalCode-with-sheba?sheba={sheba}&nationalCode={nationalCode}&birthDate={birthDate}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                throw new Exception($"At InquiryApiClient; {nameof(IsMatchingShebaWithNationalCode)} failed for sheba: {sheba}, natinalcode: {nationalCode} and birthdate: {birthDate} with message:\n {message}");
            }
            return await httpResponse.Content.ReadFromJsonAsync<MatchingResponseModel>(_jsonSerializerOptions, cancellationToken: cancellationToken);
        }

        public async Task<ServiceResult<MatchingResponseModel>> IsMatchingCardWithNationalCode(string cardNumber, string nationalCode, string birthDate, CancellationToken cancellationToken = default)
        {
            birthDate = birthDate.Replace("/", "");
            var url = $"v1/Matching/nationalCode-with-card?cardNumber={cardNumber}&nationalCode={nationalCode}&birthDate={birthDate}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                return new ServiceResult<MatchingResponseModel>()
                {
                    Message = message,
                    Succeeded = false
                };
            }
            var result =  await httpResponse.Content.ReadFromJsonAsync<MatchingResponseModel>(_jsonSerializerOptions, cancellationToken: cancellationToken);

            return new ServiceResult<MatchingResponseModel>() 
            {
                Succeeded = true,
                SuccessResult = result
            };
        }

        public async Task<ShebaToOwnerResponse> GetPersonalInfoByIban(string sheba, CancellationToken cancellationToken = default)
        {
            var url = $"v1/sheba/inquiry-with-personal-info?sheba={sheba}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                throw new Exception($"At InquiryApiClient; {nameof(GetPersonalInfoByIban)} failed for sheba: {sheba} with message:\n {message}");
            }
            return await httpResponse.Content.ReadFromJsonAsync<ShebaToOwnerResponse>(_jsonSerializerOptions, cancellationToken: cancellationToken);
        }

        public async Task<StartCreditScoreResponseModel> StartCreditScore(string nationalCode, string mobileNumber, CancellationToken cancellationToken = default)
        {
            var url = $"v1/CreditScoreProvider/start-credit-score?nationalCode={nationalCode}&mobileNumber={mobileNumber}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                return new StartCreditScoreResponseModel() { Success = false, Message = $"At InquiryApiClient; {nameof(StartCreditScore)} failed for nationalcode: {nationalCode} and mobile : {mobileNumber} with message:\n {message}" };
            }
            return await httpResponse.Content.ReadFromJsonAsync<StartCreditScoreResponseModel>(cancellationToken: cancellationToken);
        }

        public async Task<ValidateCreditScoreResponseModel> ValidateCreditScore(string code, string token, CancellationToken cancellationToken = default)
        {
            var url = $"v1/CreditScoreProvider/validate-credit-score?code={code}&token={token}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                return new ValidateCreditScoreResponseModel() { Success = false, Message = $"At InquiryApiClient; {nameof(ValidateCreditScore)} failed for code: {code} with message:\n {message}" };
            }
            return await httpResponse.Content.ReadFromJsonAsync<ValidateCreditScoreResponseModel>(cancellationToken: cancellationToken);
        }

        public async Task<ReportLinkCreditScoreResultModel> GenerateReportLinkCreditScore(string code, string token, CancellationToken cancellationToken = default)
        {
            var url = $"v1/CreditScoreProvider/report-link-credit-score?code={code}&token={token}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                return new ReportLinkCreditScoreResultModel() { Success = false, Message = $"At InquiryApiClient; {nameof(ValidateCreditScore)} failed for code: {code} with message:\n {message}" };
            }
            return await httpResponse.Content.ReadFromJsonAsync<ReportLinkCreditScoreResultModel>(cancellationToken: cancellationToken);
        }

        public async Task<CreditScoreReportResponseModel> GetCreditScoreReport(string uniqueCode, CancellationToken cancellationToken = default)
        {
            var url = $"v1/CreditScoreProvider/credit-score-report?uniqueCode={uniqueCode}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);

            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                return new CreditScoreReportResponseModel() { Success = false, Description = message };
            }

            return await httpResponse.Content.ReadFromJsonAsync<CreditScoreReportResponseModel>(cancellationToken: cancellationToken);
        }

        public async Task<ServiceResult<PostalCodeInfoResponseModel>> GetPostalCodeInfo(string postalCode, CancellationToken cancellationToken = default)
        {
            var url = $"v1/postalCode/inquiry-postal-code?postalCode={postalCode}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                return new ServiceResult<PostalCodeInfoResponseModel>
                {
                    Succeeded = false,
                    Message = message
                };
            }

            var resp = await httpResponse.Content.ReadFromJsonAsync<PostalCodeInfoResponseModel>(_jsonSerializerOptions, cancellationToken: cancellationToken);
            return new ServiceResult<PostalCodeInfoResponseModel>
            {
                SuccessResult = resp,
                Succeeded = true
            };
        }

        public async Task<ServiceResult<ConvertCardToShebaResponseModel>> ConvertCardToSheba(string cardNumber, CancellationToken cancellationToken = default)
        {
            var url = $"v1/Cards/card-to-sheba-inquiry?cardNumber={cardNumber}";
            var httpResponse = await _httpClient.GetAsync(url, cancellationToken);
            if (!httpResponse.IsSuccessStatusCode)
            {
                var message = await httpResponse.Content.ReadAsStringAsync();

                return new ServiceResult<ConvertCardToShebaResponseModel>
                {
                    Succeeded = false,
                    Message = message
                };
            }

            var resp = await httpResponse.Content.ReadFromJsonAsync<ConvertCardToShebaResponseModel>(_jsonSerializerOptions, cancellationToken: cancellationToken);
            return new ServiceResult<ConvertCardToShebaResponseModel>
            {
                SuccessResult = resp,
                Succeeded = true
            };
        }

    }
}
