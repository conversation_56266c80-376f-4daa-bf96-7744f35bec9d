﻿using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.BNPL.Domain.Models
{
    public class Credit : BaseEntity<int>, IAuditableEntity
    {
        public int ContractId { get; set; }

        // must be a credit-type wallet
        public Guid WalletId { get; set; }

        public Guid Code { get; set; }

        public bool IsActive { get; set; } = true;

        public long TotalRawPrePaymentCount { get; set; }
        public decimal TotalRawPrePaymentAmount { get; set; }
        public decimal TotalMerchantWageAmount { get; set; }
        public decimal TotalConsumerWageAmount { get; set; }

        public long TotalInstallmentPaymentCount { get; set; }
        public decimal TotalInstallmentPaymentAmount { get; set; }


        public DateTimeOffset? LastModifiedAt { get; set; }
        public int? LastModifiedBy { get; set; }

        public Contract Contract { get; }

        public ICollection<Plan> Plans { get; } = new HashSet<Plan>();
        public ICollection<CreditTransaction> CreditTransactions { get; } = new HashSet<CreditTransaction>();
        public ICollection<CreditTransactionOutbox> CreditFailedTransactions { get; } = new HashSet<CreditTransactionOutbox>();


    }
}
