﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Admin.CreditTransactionList
{
    public record CreditTransactionListRequest(int creditId, int merchantId, int pageNumber = 1, int pageSize = 10);
    public class CreditTransactionListResponse
    {
        public CreditTransactionListResponse(long total, List<CreditTransaction> creditTransactions)
        {
            this.total = total;
            this.creditTransactions = creditTransactions;
        }

        public long total { get; set; }

        [Required]
        public List<CreditTransaction> creditTransactions { get; set; }
    }
    public class CreditTransaction
    {
        public DateTimeOffset CreatedDate { get; set; }
        public decimal Amount { get; set; }
        public CreditTransactionType CreditTransactionType { get; set; }
        public string Description { get; set; }
    }


    public class CreditTransactionListEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                AdminRoutes.CreditTransactionList,
                async (
                    [AsParameters] CreditTransactionListRequest request,
                    ICreditTransactionListRequestHandler handler,
                    IValidator<CreditTransactionListRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("admin")
            .WithName("CreditTransactionList")
            .WithApiVersionSet(builder.NewApiVersionSet("Credit").Build())
            .Produces<CreditTransactionListResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Credit Transaction List")
            .WithDescription("Credit Transaction List")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CreditTransactionListRequestHandler : ICreditTransactionListRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserService _userService;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public CreditTransactionListRequestHandler(ApplicationDbContext dbContext, IUserService userService, IOptions<BNPLOptions> bnplOptions, IUserContext userContext)
        {
            _dbContext = dbContext;
            _userService = userService;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<CreditTransactionListResponse> HandleAsync(CreditTransactionListRequest request, CancellationToken cancellationToken)
        {
            var q = _dbContext.CreditTransactions.AsNoTracking()
                .Where(c => 
                    c.Credit.Contract.MerchantUserId == request.merchantId &&
                    c.CreditId == request.creditId && 
                    (c.TransactionType == CreditTransactionType.Deposit || c.TransactionType == CreditTransactionType.Withdraw) &&
                    c.Credit.IsActive);

            var total = await q.LongCountAsync();

            var creditTransactions = await q
                .Select(c => new CreditTransaction
                {
                    Amount = c.Amount,
                    CreatedDate = c.CreatedAt,
                    CreditTransactionType = c.TransactionType,
                    Description = c.Description
                })
                .OrderByDescending(o => o.CreatedDate)
                .Skip((request.pageNumber - 1) * request.pageSize)
                .Take(request.pageSize)
                .ToListAsync(cancellationToken);

            return new CreditTransactionListResponse(total, creditTransactions);
        }

    }
    public interface ICreditTransactionListRequestHandler
    {
        ValueTask<CreditTransactionListResponse> HandleAsync(CreditTransactionListRequest request, CancellationToken cancellationToken);
    }

    public class CreditTransactionListValidator : AbstractValidator<CreditTransactionListRequest>
    {
        public CreditTransactionListValidator()
        {
            RuleFor(x => x.merchantId).NotEmpty().WithResourceError(() => ValidatorDictionary.merchant_id_is_required);
            RuleFor(x => x.creditId).NotEmpty().WithResourceError(() => ValidatorDictionary.credit_id_is_required);
            RuleFor(x => x.pageSize).GreaterThan(0).WithResourceError(() => ValidatorDictionary.page_size_greater_zero);
            RuleFor(x => x.pageNumber).GreaterThan(0).WithResourceError(() => ValidatorDictionary.page_number_greater_zero);
        }
    }
}
