﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.Contracts;

namespace PayPing.LuckyLuke.Application.Features.Admin.ContractList
{
    public record ContractListRequest(int merchantUserId);

    public class ContractListResponse
    {
        public ContractListResponse(List<ContractResponse> contracts)
        {
            Contracts = contracts;
        }

        [Required]
        public List<ContractResponse> Contracts { get; set; }
    }

    public class ContractResponse
    {
        public int Id { get; set; }

        public int MerchantUserId { get; set; }

        public DateTimeOffset StartDate { get; set; }

        public DateTimeOffset ExpireDate { get; set; }

        public decimal OperationCost { get; set; }
        public MerchantOperationCostStrategy OperationCostStrategy { get; set; }

        public bool IsActive { get; set; }

        public ActivationStatus ActivationStatus { get; set; }

        [Required]
        public List<ContractCreditResponse> Credits { get; set; }
    }

    public class ContractCreditResponse
    {
        public int Id { get; set; }
        public Guid WalletId { get; set; }

        public bool IsActive { get; set; }
    }

    public class ContractListEndpoint : IAdminMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                AdminRoutes.ContractList,
                async (
                    [AsParameters] ContractListRequest request,
                    IContractListRequestHandler handler,
                    IValidator<ContractListRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
                .RequireAuthorization("admin")
                .WithName("ContractList")
                .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
                .Produces<ContractListResponse>()
                .ProducesProblem(StatusCodes.Status400BadRequest)
                .WithSummary("Contract List")
                .WithDescription("Contract List")
                .WithOpenApi()
                .HasApiVersion(1.0);

            return builder;
        }
    }

    public class ContractListRequestHandler : IContractListRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;

        public ContractListRequestHandler(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async ValueTask<ContractListResponse> HandleAsync(ContractListRequest request, CancellationToken cancellationToken)
        {
            var now = DateTimeOffset.UtcNow;

            var merchant = await _dbContext.MerchantInfos.AsNoTracking()
               .Where(m => m.MerchantUserId == request.merchantUserId)
               .FirstOrDefaultAsync();

            if (merchant == null)
            {
                throw new ArgumentException("پذیرنده یافت نشد");
            }

            var q = _dbContext.Contracts.AsNoTracking().Where(c => c.MerchantUserId == request.merchantUserId);

            var contracts = await q
                .Select(c => new ContractResponse
                {
                    Id = c.Id,
                    ExpireDate = c.ExpireDate,
                    StartDate = c.StartDate,
                    MerchantUserId = c.MerchantUserId,
                    OperationCost = c.OperationCost,
                    OperationCostStrategy = c.OperationCostStrategy,
                    Credits = c.Credits.Select(r => new ContractCreditResponse { Id = r.Id, IsActive = r.IsActive, WalletId = r.WalletId }).ToList(),
                    ActivationStatus = c.ActivationStatus
                })
                .OrderByDescending(c => c.ExpireDate)
                .ToListAsync();

            if (contracts.Count > 0)
            {
                contracts.ForEach(c => c.IsActive = c.ExpireDate >= now && c.ActivationStatus == ActivationStatus.Final);
            }

            return new ContractListResponse(contracts);
        }

    }

    public interface IContractListRequestHandler
    {
        ValueTask<ContractListResponse> HandleAsync(ContractListRequest request, CancellationToken cancellationToken);
    }

    public class ContractListValidator : AbstractValidator<ContractListRequest>
    {
        public ContractListValidator()
        {
            RuleFor(x => x.merchantUserId).NotEmpty().WithResourceError(() => ValidatorDictionary.merchant_user_id_is_required); 
        }
    }
}
