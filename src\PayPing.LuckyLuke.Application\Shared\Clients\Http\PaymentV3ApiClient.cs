﻿using Ardalis.GuardClauses;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using System.Net.Http.Json;
using System.Text.Json;
using Newtonsoft.Json.Linq;

namespace PayPing.LuckyLuke.Application.Shared.Clients.Http
{
    public class PaymentV3ApiClient : IPaymentV3ApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonSerializerOptions;


        public PaymentV3ApiClient(HttpClient httpClient)
        {
            _httpClient = Guard.Against.Null(httpClient, nameof(httpClient));
            _jsonSerializerOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
        }


        public async ValueTask<PaymentApiPayResponse> PayAsync(PaymentApiPayRequest model, string merchantToken, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}v3/pay");

            request.Headers.Add("Accept", "application/json");

            request.Headers.Add("Authorization", $"Bearer {merchantToken}");

            request.Content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");

            var response = await _httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var rc = await response.Content.ReadFromJsonAsync<PaymentApiClientPayResponse>(_jsonSerializerOptions, cancellationToken);
                return new PaymentApiPayResponse(true, default, null, null, default, null, rc.paymentCode, rc.url);
            }
            else
            {
                var ec = await response.Content.ReadFromJsonAsync<PaymentApiErrorResponse>(_jsonSerializerOptions, cancellationToken);
                return new PaymentApiPayResponse(false, ec?.Key ?? default, ec?.Message, ec?.Title, ec?.Status ?? default, ec?.Type, null, null);
            }
        }

        public async ValueTask<PaymentApiVerifyResponse> VerifyAsync(PaymentApiVerifyRequest model, string merchantToken, CancellationToken cancellationToken = default)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_httpClient.BaseAddress}v3/pay/verify");

            request.Headers.Add("Accept", "application/json");

            request.Headers.Add("Authorization", $"Bearer {merchantToken}");

            request.Content = new StringContent(JsonSerializer.Serialize(model), null, "application/json");

            var response = await _httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var rc = await response.Content.ReadFromJsonAsync<PaymentApiClientVerifyResponse>(_jsonSerializerOptions, cancellationToken);
                return new PaymentApiVerifyResponse(true, default, null, null, default, null, rc.amount, rc.cardNumber, rc.clientRefId, rc.paymentRefId);
            }
            else
            {
                var ec = await response.Content.ReadAsStringAsync(cancellationToken);
                return new PaymentApiVerifyResponse(false, default, ec, default, default, default, default, null, null, default);
            }
        }
    }
}
