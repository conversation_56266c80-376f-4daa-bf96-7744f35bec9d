﻿using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Shared.Abstractions.DigitalSign.Kiahooshan
{
    public interface IKiahooshanDigitalSignService
    {
        ValueTask<object> HandleAsync(KiahooshanDigitalSignContextV1 context, CancellationToken cancellationToken = default);
    }
}
