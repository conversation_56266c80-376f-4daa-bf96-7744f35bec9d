﻿using PayPing.LuckyLuke.Application.Infrastructure.Persistence.Abstractions;

namespace PayPing.BNPL.Domain.Models
{
    public class EmployeeInfo : BaseEntity<int>
    {
        public int EmployerUserId { get; set; }
        public string MobileNumber { get; set; }
        public string NationalCode { get; set; }

        public ICollection<PlanEmployee> PlanEmployees { get; } = new HashSet<PlanEmployee>();

    }

}
