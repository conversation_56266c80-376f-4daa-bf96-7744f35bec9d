﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using System.ComponentModel.DataAnnotations;

namespace PayPing.LuckyLuke.Application.Features.Merchant.PlanList
{
    public class PlanListResponse
    {
        public PlanListResponse(List<PlanInfo> plansInfo)
        {
            this.plansInfo = plansInfo;
        }

        [Required]
        public List<PlanInfo> plansInfo { get; set; }
    }
    public class PlanInfo
    {
        public Guid code { get; set; }
        public DateTimeOffset planCreatedDate { get; set; }
        public bool isActive { get; set; }
        public GuaranteeType guaranteeType { get; set; }
        public decimal totalMaxAmount { get; set; }
        public int installmentsCount { get; set; }
    }

    public class PlanListEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapGet(
                MerchantRoutes.PlansList,
                async (
                    IPlanListRequestHandler handler,
                    CancellationToken cancellationToken) =>
                {

                    var result = await handler.HandleAsync(cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("read")
            .WithName("PlanList")
            .WithApiVersionSet(builder.NewApiVersionSet("Plan").Build())
            .Produces<PlanListResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Plan List")
            .WithDescription("Plan List")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class PlanListRequestHandler : IPlanListRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly BNPLOptions _bnplOptions;

        public PlanListRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<PlanListResponse> HandleAsync(CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var plans = await _dbContext.Plans.AsNoTracking()
                .Where(p =>
                    p.MerchantUserId == userId &&
                    p.Credit.IsActive &&
                    p.Credit.Contract.StartDate <= DateTimeOffset.UtcNow &&
                    p.Credit.Contract.ExpireDate > DateTimeOffset.UtcNow)
                .Select(p => new PlanInfo()
                {
                    totalMaxAmount = p.MaxCreditAmount,
                    installmentsCount = p.InstallmentCount,
                    guaranteeType = p.GuaranteeType,
                    planCreatedDate = p.CreatedAt,
                    isActive = p.IsActive,
                    code = p.Code,
                })
                .ToListAsync();

            return new PlanListResponse(plans);
        }

    }
    public interface IPlanListRequestHandler
    {
        ValueTask<PlanListResponse> HandleAsync(CancellationToken cancellationToken);
    }
}
