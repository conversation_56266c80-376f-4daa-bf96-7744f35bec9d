﻿using Ardalis.GuardClauses;
using DNTPersianUtils.Core;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Resources.ResourceExtension;
using PayPing.Tools.SdkBase.Types;

namespace PayPing.LuckyLuke.Application.Features.Consumer._04_FillProfile
{
    public record FillProfileRequest(Guid? trackingCode,
                                     string postalCode,
                                     string address,
                                     string firstNameEnglish,
                                     string lastNameEnglish,
                                     bool isMale,
                                     string nationalIdSeries,
                                     string cardNumber,
                                     string email,
                                     string provinceEnglish,
                                     string cityEnglish);

    public class FillProfileEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.FillProfile,
                async (
                    FillProfileRequest request,
                    IFillProfileRequestHandler handler,
                    IValidator<FillProfileRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok();
                })
            .RequireAuthorization("write")
            .WithName("FillProfile")
            .WithApiVersionSet(builder.NewApiVersionSet("Profile").Build())
            .Produces(200)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Fill Profile")
            .WithDescription("Fill Profile")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class FillProfileRequestHandler : IFillProfileRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<FillProfileRequestHandler> _logger;
        private readonly IEnumerable<IDigitalSignProvider> _digitalSignProviders;
        private readonly IInquiryApiClient _inquiryApiClient;
        private readonly IUserService _userService;
        private readonly IUserContext _userContext;
        private readonly IGuaranteeService _guaranteeService;
        private readonly INoneGuaranteeProvider _noneGuaranteeProvider;

        public FillProfileRequestHandler(ApplicationDbContext dbContext, ILogger<FillProfileRequestHandler> logger, IEnumerable<IDigitalSignProvider> digitalSignProviders, IInquiryApiClient inquiryApiClient, IUserService userService, IUserContext userContext, IGuaranteeService guaranteeService, INoneGuaranteeProvider noneGuaranteeProvider)
        {
            _dbContext = dbContext;
            _logger = logger;
            _digitalSignProviders = digitalSignProviders;
            _inquiryApiClient = inquiryApiClient;
            _userService = userService;
            _userContext = userContext;
            _guaranteeService = guaranteeService;
            _noneGuaranteeProvider = noneGuaranteeProvider;
        }

        public async ValueTask HandleAsync(FillProfileRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;
            var currentUserInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);

            var ibanResult = await ValidateIBan(request, currentUserInfo, cancellationToken);

            var consumerInfo = await GetOrCreateConsumerInfo(userId);

            bool hasChangeInSignatureData = HasChangeInDigitalSignatureRelatedProps(consumerInfo, request);

            await UpdateConsumerInfo(request, consumerInfo, hasChangeInSignatureData, ibanResult.shouldUpdateIban, ibanResult.newcardNumber, ibanResult.newiban);

            if (request.trackingCode.HasValue)
            {
                await UpdateOrder(request.trackingCode.Value, cancellationToken);
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            await _userService.SetNewConsumerInfoAsync(userId, consumerInfo, cancellationToken);
        }


        private async ValueTask<(bool shouldUpdateIban, string newiban, string newcardNumber)> ValidateIBan(FillProfileRequest request, UserInfoDto currentUserInfo, CancellationToken cancellationToken)
        {
            if (currentUserInfo == null)
            {
                var shebaResult = await ConvertCardToIban(request.trackingCode, request.cardNumber, _userContext.CurrentUserNationalCode, _userContext.CurrentUserPersianBirthDate, cancellationToken);

                return new(true, shebaResult.SuccessResult.ShebaNumber, request.cardNumber);
            }
            else if (currentUserInfo.CardNumber != request.cardNumber)
            {
                var shebaResult = await ConvertCardToIban(request.trackingCode, request.cardNumber, currentUserInfo.NationalCode, currentUserInfo.BirthDate.ToShortPersianDateString(), cancellationToken);
                
                return new(true, shebaResult.SuccessResult.ShebaNumber, request.cardNumber);
            }
            else
            {
                return new(false, default, default);
            }
        }

        private async ValueTask<ServiceResult<ConvertCardToShebaResponseModel>> ConvertCardToIban(Guid? trackingCode, string card, string nationalCode, string persianBirthDate, CancellationToken cancellationToken)
        {
            var cardResult = await _inquiryApiClient.IsMatchingCardWithNationalCode(
                    card,
                    nationalCode,
                    persianBirthDate,
                    cancellationToken);

            if (!cardResult.Succeeded)
            {
                _logger.LogWarning("At FillProfile ConvertCardToIban IsMatchingCardWithNationalCode request failed with message {InquiryMessage}", cardResult.Message);

                throw new CustomValidationException(trackingCode?.ToString(), "تطابق شماره کارت با کدملی با خطا مواجه شد", string.Empty);
            }

            if (!cardResult.SuccessResult.Matched)
            {
                throw new CustomValidationException(trackingCode?.ToString(), "شماره کارت متعلق به کاربر نیست", string.Empty);
            }

            ServiceResult<ConvertCardToShebaResponseModel> shebaResult = await _inquiryApiClient.ConvertCardToSheba(card, cancellationToken);

            if (!shebaResult.Succeeded)
            {
                _logger.LogWarning("At FillProfile ConvertCardToIban ConvertCardToSheba request failed with message {InquiryMessage}", cardResult.Message);

                throw new CustomValidationException(trackingCode?.ToString(), "تبدیل شماره کارت به شماره شبا با خطا مواجه شد", string.Empty);
            }

            return shebaResult;
        }

        private async ValueTask<ConsumerInfo> GetOrCreateConsumerInfo(int userId)
        {
            var consumerInfo = await _dbContext.ConsumerInfos
                .FirstOrDefaultAsync(x => x.ConsumerUserId == userId);

            if (consumerInfo != null) return consumerInfo;

            consumerInfo = new ConsumerInfo
            {
                ConsumerUserId = userId,
                UserName = _userContext.CurrentUserName.Trim(),
                PhoneNumber = _userContext.CurrentUserName.Trim(),
                FirstName = _userContext.CurrentUserFirstname.Trim(),
                LastName = _userContext.CurrentUserLastname.Trim(),
                NationalCode = _userContext.CurrentUserNationalCode.Trim(),
                PersianBirthDate = _userContext.CurrentUserPersianBirthDate.Trim(),
                FullName = $"{_userContext.CurrentUserFirstname} {_userContext.CurrentUserLastname}".Trim(),
                FatherName = _userContext.CurrentUserFatherName.Trim()
            };

            _dbContext.ConsumerInfos.Add(consumerInfo);
            return consumerInfo;
        }

        private async ValueTask UpdateConsumerInfo(FillProfileRequest request, ConsumerInfo consumerInfo, bool hasChangeInSignatureData, bool updateIBan, string newCardNumber, string newIban)
        {
            // force steps generator to add signature image step
            if (hasChangeInSignatureData)
            {
                consumerInfo.SignatureImageFileId = null;
                consumerInfo.SignatureImageFileName = null;
                consumerInfo.SignatureVideoFileId = null;
                consumerInfo.SignatureVideoFileName = null;
                consumerInfo.SignatureVideoRandomSentence = null;

                var digitalSigns = await _dbContext.DigitalSigns
                    .Where(x => x.UserId == consumerInfo.ConsumerUserId && !string.IsNullOrEmpty(x.SignatureRefId) && x.ExpiredAt == null)
                    .ToListAsync();

                if (digitalSigns.Count > 0)
                {
                    foreach (var item in digitalSigns)
                    {
                        item.ExpiredAt = DateTimeOffset.UtcNow;
                    }
                }
            }

            consumerInfo.FirstNameEnglish = request.firstNameEnglish;
            consumerInfo.LastNameEnglish = request.lastNameEnglish;
            consumerInfo.NationalIdSeries = request.nationalIdSeries;

            if (updateIBan)
            {
                consumerInfo.CardNumber = newCardNumber;
                consumerInfo.IBan = newIban;
            }

            consumerInfo.Email = request.email;
            consumerInfo.IsMale = request.isMale;

            if (!string.IsNullOrEmpty(request.address) &&
                !string.IsNullOrEmpty(request.provinceEnglish) &&
                !string.IsNullOrEmpty(request.cityEnglish))
            {
                consumerInfo.Address = request.address;
                consumerInfo.ProvinceEnglish = request.provinceEnglish;
                consumerInfo.CityEnglish = request.cityEnglish;
                consumerInfo.PostalCode = request.postalCode;
            }
            else if (request.postalCode != consumerInfo.PostalCode) // if postal code has changed, we should get new address
            {
                await UpdateAddressFromPostalCode(request.postalCode, consumerInfo);
                consumerInfo.PostalCode = request.postalCode;
            }
        }

        private async ValueTask UpdateAddressFromPostalCode(string postalCode, ConsumerInfo consumerInfo)
        {
            var postalCodeInfo = await _inquiryApiClient.GetPostalCodeInfo(postalCode);
            if (!postalCodeInfo.Succeeded)
            {
                throw new PostalCodeInquiryException("خطا در استعلام کد پستی");
            }

            consumerInfo.Address = FormatAddress(postalCodeInfo.SuccessResult);
            consumerInfo.ProvinceEnglish = ResourceExtensionManager.GetProvinceEnCode(postalCodeInfo.SuccessResult.Province) ?? "TEH";
            consumerInfo.CityEnglish = ResourceExtensionManager.GetCityEnCode(postalCodeInfo.SuccessResult.City) ?? "TEH";
        }

        private string FormatAddress(PostalCodeInfoResponseModel info)
        {
            return string.Join(", ",
                info.Province,
                info.City,
                info.Township,
                $"{ValidatorDictionary.locality} {info.Locality}",
                info.Avenue,
                info.StopStreet,
                $"{ValidatorDictionary.No} {info.No}",
                $"{ValidatorDictionary.Floor} {info.Floor}");
        }

        private async ValueTask UpdateOrder(Guid trackingCode, CancellationToken cancellationToken)
        {
            var order = await _dbContext.Orders.FirstOrDefaultAsync(o => o.TrackingCode == trackingCode, cancellationToken);

            if (order == null)
            {
                throw new NotFoundException(trackingCode.ToString(), "order");
            }

            order.Status = OrderStatus.ProfileFilled;
        }

        private bool HasChangeInDigitalSignatureRelatedProps(ConsumerInfo currentUserInfo, FillProfileRequest request)
        {
            if(currentUserInfo == null)
                return true;

            // PostalCode
            // Address
            // Province
            // City
            if (currentUserInfo.PostalCode != request.postalCode) return true;

            // Email
            if (currentUserInfo.Email != request.email) return true;

            // NationalCardSeries
            if (currentUserInfo.NationalIdSeries != request.nationalIdSeries) return true;

            // FirstNameEn
            if (currentUserInfo.FirstNameEnglish != request.firstNameEnglish) return true;

            // LastNameEn
            if (currentUserInfo.LastNameEnglish != request.lastNameEnglish) return true;

            return false;
        }
    }

    public interface IFillProfileRequestHandler
    {
        ValueTask HandleAsync(FillProfileRequest request, CancellationToken cancellationToken);
    }

    public class FillProfileValidator : AbstractValidator<FillProfileRequest>
    {
        public FillProfileValidator()
        {
            RuleFor(c => c.postalCode)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.postal_code_is_required)
                .Matches("[0-9]{10}").WithResourceError(() => ValidatorDictionary.postal_code_not_valid);

            //RuleFor(c => c.address)
            //    .NotEmpty().WithResourceError(() => ValidatorDictionary.address_is_required);

            RuleFor(c => c.firstNameEnglish)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.first_name_english_is_required)
                .Matches("^[A-Za-z ]+$").WithResourceError(() => ValidatorDictionary.first_name_english_not_valid);

            RuleFor(c => c.lastNameEnglish)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.last_name_english_is_required)
                .Matches("^[A-Za-z ]+$").WithResourceError(() => ValidatorDictionary.last_name_english_not_valid);

            RuleFor(c => c.nationalIdSeries)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.national_id_series_is_required)
                .Matches("^[A-Za-z0-9]+$").WithResourceError(() => ValidatorDictionary.national_id_series_not_valid);

            RuleFor(c => c.cardNumber)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.card_number_is_required)
                .Matches("^[0-9]{16}$")
                .WithResourceError(() => ValidatorDictionary.card_number_not_valid);

            RuleFor(c => c.email)
                .NotEmpty().WithResourceError(() => ValidatorDictionary.email_is_required)
                .EmailAddress().WithResourceError(() => ValidatorDictionary.email_not_valid);

            //RuleFor(c => c.provinceEnglish)
            //    .NotEmpty().WithResourceError(() => ValidatorDictionary.province_english_is_required)
            //    .Matches("^[A-Za-z]+$").WithResourceError(() => ValidatorDictionary.province_english_not_valid);

            //RuleFor(c => c.cityEnglish)
            //    .NotEmpty().WithResourceError(() => ValidatorDictionary.city_english_is_required)
            //    .Matches("^[A-Za-z]+$").WithResourceError(() => ValidatorDictionary.city_english_not_valid);

        }
    }
}
