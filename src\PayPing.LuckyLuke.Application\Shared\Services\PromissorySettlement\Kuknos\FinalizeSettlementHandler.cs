﻿using Ardalis.GuardClauses;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Dto.Kuknos;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Services.Context;
using PayPing.LuckyLuke.Application.Infrastructure.Utilities;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Abstractions.PromissorySettlement.Kuknos;
using System.Text.Json;

namespace PayPing.LuckyLuke.Application.Shared.Services.PromissorySettlement.Kuknos
{
    public class FinalizeSettlementHandler : KuknosSettlementBaseHandler, IKuknosPromissorySettlementService
    {
        private readonly IUploadGrpcClient _uploadGrpcClient;
        private readonly BNPLOptions _bnplOptions;

        public FinalizeSettlementHandler(
            IOptions<BNPLOptions> bnplOptions,
            ApplicationDbContext dbContext,
            IUserContext userContext,
            IKuknosApiHttpClient kuknosApi,
            IUploadGrpcClient uploadGrpcClient,
            IOptions<KuknosOptions> kuknosOptions)
            : base(dbContext, userContext, kuknosApi, kuknosOptions)
        {
            _uploadGrpcClient = uploadGrpcClient;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<object> HandleAsync(KuknosPromissorySettlementContextV1 context, CancellationToken cancellationToken = default)
        {
            if (context.Status != KuknosPromissorySettlementStatusV1.Finalize)
                throw new Exception("promissory settlement not in finalize status");

            ValidateContext(context);

            var result = await _kuknosApi.FinalizeSettlementAsync(context.PromissoryId, context.OrderTrackingCode, cancellationToken);

            if (result.data == null || string.IsNullOrWhiteSpace(result.data.final_pdf_document))
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "promissory settlement finalize result is empty", false);
            }

            var downloaded = await _kuknosApi.DownloadAsync(result.data.final_pdf_document, context.OrderTrackingCode, cancellationToken);
            if (downloaded == null || downloaded.Length == 0)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not download final promissory settlement from kuknos ipfs download", false);
            }

            var file = await downloaded.ToByteArrayAsync();

            // upload file to storage
            string validName = Path.ChangeExtension(Path.GetRandomFileName(), ".pdf");

            var uresult = await _uploadGrpcClient.UploadUserDocumentAsync(file, Path.GetExtension(validName).TrimStart('.'), context.ConsumerUserId, cancellationToken);

            if (!uresult.Succeeded)
            {
                throw new PromissoryProviderException(context.OrderTrackingCode.ToString(), "could not upload final promissory settlement to storage service", false);
            }


            context.Status = KuknosPromissorySettlementStatusV1.Done;
            context.SettlementFinalDocumentHash = result.data.final_pdf_document;
            context.SettlementFinalDocumentFileId = uresult.SuccessResult;
            context.SettlementFinalDocumentFileContentType = "application/pdf";

            //// save context
            await UpdateContextAsync(context);

            return new DoneResult(JsonSerializer.Serialize(new { signedPdf = context.SettlementFinalDocumentFileId }));
        }

        protected override void ValidateContext(KuknosPromissorySettlementContextV1 context)
        {
            base.ValidateContext(context);
            Guard.Against.NullOrEmpty(context.SettlementSignedDocumentHash);
        }
    }
}
