﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Infrastructure.Caching;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Clients.Dto;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;

namespace PayPing.LuckyLuke.Application.Features.Consumer.CreditScoreValidationVerify
{
    public record CreditScoreValidationVerifyRequest(Guid trackingCode, string Code);

    public class CreditScoreValidationVerifyEndpoint : IMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                ConsumerRoutes.CreditScoreValidationVerify,
                async (
                    CreditScoreValidationVerifyRequest request,
                    ICreditScoreValidationVerifyRequestHandler handler,
                    IValidator<CreditScoreValidationVerifyRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok();
                })
            .RequireAuthorization("write")
            .WithName("CreditScoreVerify")
            .WithApiVersionSet(builder.NewApiVersionSet("CreditScore").Build())
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Credit Score Verify")
            .WithDescription("Credit Score Verify")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class CreditScoreValidationVerifyRequestHandler : ICreditScoreValidationVerifyRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IUserContext _userContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserService _userService;
        private readonly IInquiryApiClient _inquiryApiClient;
        private readonly IDistributedCache _cache;
        private readonly ILogger<CreditScoreValidationVerifyRequestHandler> _logger;

        public CreditScoreValidationVerifyRequestHandler(ApplicationDbContext dbContext,
                                                      IUserContext userContext,
                                                      IOptions<BNPLOptions> bnplOptions,
                                                      IUserService userService,
                                                      IInquiryApiClient inquiryApiClient,
                                                      IDistributedCache cache,
                                                      ILogger<CreditScoreValidationVerifyRequestHandler> logger)
        {
            _dbContext = dbContext;
            _userContext = userContext;
            _bnplOptions = bnplOptions.Value;
            _userService=userService;
            _inquiryApiClient = inquiryApiClient;
            _cache = cache;
            _logger = logger;
        }

        public async ValueTask HandleAsync(CreditScoreValidationVerifyRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            var consumerInfo = await _userService.GetConsumerInfoAsync(userId, cancellationToken);

            if (!_cache.TryGetValue($"CreditScoreToken_{consumerInfo.NationalCode}_{consumerInfo.UserName}", out string cachedToken))
            {
                throw new CustomValidationException(request.trackingCode.ToString(), "کد اعتبارسنجی منقضی شده است", string.Empty);
            }

            var inProcessResult = await _inquiryApiClient.ValidateCreditScore(request.Code, cachedToken, cancellationToken);

            if (inProcessResult == null || !inProcessResult.Success)
            {
                _logger.LogWarning($"provider error at creditscore verify with message: {inProcessResult?.Message}");
                throw new CustomValidationException(request.trackingCode.ToString(), "کد اعتبارسنجی نامعتبر است", string.Empty);
            }

            await _cache.SetAsync(
                   $"CreditScoreOtpCode_{consumerInfo.NationalCode}_{consumerInfo.UserName}",
                   request.Code,
                   new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5) });

            await _cache.SetAsync(
                   $"CreditScoreReportTryCount_{consumerInfo.NationalCode}_{request.Code}",
                   new CreditScoreReportTryCountModel(0),
                   new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5) });
        }
    }

    public interface ICreditScoreValidationVerifyRequestHandler
    {
        ValueTask HandleAsync(CreditScoreValidationVerifyRequest request, CancellationToken cancellationToken);
    }

    public class CreditScoreValidationVerifyValidator : AbstractValidator<CreditScoreValidationVerifyRequest>
    {
        public CreditScoreValidationVerifyValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
            RuleFor(x => x.Code).NotEmpty().WithResourceError(() => ValidatorDictionary.otp_code_is_required);
        }
    }

}