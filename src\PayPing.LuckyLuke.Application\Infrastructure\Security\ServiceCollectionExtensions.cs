﻿using IdentityModel.Client;
using Microsoft.Extensions.DependencyInjection;
using PayPing.BNPL.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Security
{
    public static partial class ServiceCollectionExtensions
    {
        public static IServiceCollection AddAutomaticAccessTokenManagementForHttpClients(this IServiceCollection services, IdentityServerOptions identityServerOptions, BNPLOptions bNPLOptions)
        {
            services.AddClientAccessTokenManagement(options =>
            {
                options.Clients.Add(ApplicationConstants.AutomaticAccessTokenManagementName, new ClientCredentialsTokenRequest
                {
                    Address = $"{identityServerOptions.Identity_Address}/connect/token",
                    ClientId = bNPLOptions.ClientId,
                    ClientSecret = bNPLOptions.ClientSecret,
                });
                options.CacheKeyPrefix = "Payping_BNPL_CC_Token_";
            });

            return services;
        }

    }
}
