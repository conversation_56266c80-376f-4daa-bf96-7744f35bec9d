﻿using PayPing.LuckyLuke.Application.Infrastructure.Web.Attributes;
using System.ComponentModel;

namespace PayPing.LuckyLuke.Application.Infrastructure.Web
{
    public enum ConsumerOrderBriefStatus
    {
        [Description("تکمیل نشده")]
        Init = 0,
        [Description("جاری")]
        PrePaid,
        [Description("در انتظار لغو")]
        SemiCanceled,
        [Description("لغو شده")]
        Canceled,
        [Description("تسویه شده")]
        PaidOff,
        [OpenApiIgnore]
        Unknown = 100
    }
}
