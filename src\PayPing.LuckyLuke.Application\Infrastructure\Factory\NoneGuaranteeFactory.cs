﻿using Microsoft.Extensions.DependencyInjection;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Provider;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class NoneGuaranteeFactory : IGuaranteeFactory
    {
        private readonly IServiceProvider _serviceProvider;

        public NoneGuaranteeFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public GuaranteeProvider GuaranteeProvider => GuaranteeProvider.None;

        public IPromissoryGuaranteeProvider CreatePromissory()
        {
            throw new NotImplementedException();
        }

        public IChequeGuaranteeProvider CreateCheque()
        {
            throw new NotImplementedException();
        }

        public INoneGuaranteeProvider CreateNoneGuarantor()
        {
            return _serviceProvider.GetServices<INoneGuaranteeProvider>().First();

        }

        public ISalaryGuaranteeProvider CreateSalaryGuarantor()
        {
            throw new NotImplementedException();
        }
    }
}
