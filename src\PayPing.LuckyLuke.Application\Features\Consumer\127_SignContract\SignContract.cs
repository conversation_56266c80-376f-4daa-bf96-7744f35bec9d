﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Services.PdfSigner;

namespace PayPing.LuckyLuke.Application.Features.Consumer._127_SignContract;

public record SignContractRequest(Guid TrackingCode);

public record SignContractResponse(string FileName);

public class SignContractEndpoint : IMinimalEndpoint
{
    public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
    {
        builder.MapPost(
                ConsumerRoutes.SignContract,
                async (
                    SignContractRequest request,
                    ISignContractRequestHandler handler,
                    IValidator<SignContractRequest> validator,
                    CancellationToken cancellationToken) =>
                {
                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                        throw new CustomValidationException(request.TrackingCode.ToString(),
                            new ValidationResultModel(validationResult));

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("SignContract")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<SignContractResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Sign Contract")
            .WithDescription("Sign Contract")
            .WithOpenApi()
            .HasApiVersion(1.0);

        return builder;
    }
}

public class SignContractRequestHandler(
    ApplicationDbContext dbContext,
    IGuaranteeService guaranteeService,
    IEnumerable<IPdfSignerService> pdfSignerServices,
    IUserContext userContext,
    IRawContractPdfGenerator rawContractPdfGenerator)
    : ISignContractRequestHandler
{
    public async ValueTask<SignContractResponse> HandleAsync(SignContractRequest request,
        CancellationToken cancellationToken)
    {
        var userId = userContext.CurrentUserId.GetValueOrDefault();

        var order = await dbContext.Orders.AsNoTracking()
                        .Where(o => o.TrackingCode == request.TrackingCode)
                        .Include(o => o.Installments)
                        .FirstOrDefaultAsync(cancellationToken)
                    ?? throw new NotFoundException(request.TrackingCode.ToString(), "order");

        if (order.ExpireDate.HasValue && order.ExpireDate.Value <= DateTimeOffset.UtcNow)
            throw new OrderExpiredException(request.TrackingCode.ToString(), "سفارش منقضی شده است");

        if (order.ConsumerUserId != userId)
            throw new CustomValidationException(request.TrackingCode.ToString(), "سفارش متعلق به کاربر نیست", string.Empty);

        var gtypes = await guaranteeService.GetGuaranteeTypesByGuarantorId(order.OrderPlan.GuarantorId);
        switch (gtypes.guaranteeType)
        {
            case GuaranteeType.None:
            case GuaranteeType.Salary:
                if (order.Status != OrderStatus.CreditValidationSucceeded)
                    throw new CustomValidationException(request.TrackingCode.ToString(), "سفارش در مرحله امضای قرارداد نیست", string.Empty);
                break;
            case GuaranteeType.Promissory:
            case GuaranteeType.Cheque:
            default:
                if (order.Status != OrderStatus.GuaranteeSucceeded)
                    throw new CustomValidationException(request.TrackingCode.ToString(), "سفارش در مرحله امضای قرارداد نیست", string.Empty);
                break;
        }
        

        // Generate raw contract
        var rawContract = await rawContractPdfGenerator.GenerateAsync(order, cancellationToken);

        var digitalSignType = await guaranteeService.GetGuarantorCompliantSignatureTypeByGuarantorId(order.OrderPlan.GuarantorId);

        var pdfSigner = pdfSignerServices.FirstOrDefault(s => s.DigitalSignType == digitalSignType)
                        ?? throw new NotFoundException(request.TrackingCode.ToString(), "پروایدر امضا نامعتبر است");

        var signedContractFileId = await pdfSigner.SignPdfDocumentAsync(userId, rawContract, order.TrackingCode, cancellationToken);

        order.SignedContractFileId = signedContractFileId.SuccessResult;
        order.Status = OrderStatus.ContractSigned;
        dbContext.Orders.Update(order);
        await dbContext.SaveChangesAsync(cancellationToken);

        return new SignContractResponse(signedContractFileId.SuccessResult);
    }
}

public interface ISignContractRequestHandler
{
    ValueTask<SignContractResponse> HandleAsync(SignContractRequest request, CancellationToken cancellationToken);
}

public class SignContractValidator : AbstractValidator<SignContractRequest>
{
    public SignContractValidator()
    {
        RuleFor(x => x.TrackingCode).NotEqual(Guid.Empty)
            .WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
    }
}