﻿using Microsoft.Extensions.Logging;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Shared.Abstractions;

namespace PayPing.LuckyLuke.Application.Infrastructure.Factory
{
    public class MerchantBeforePaymentCanceler : IMerchantCanceler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly INotifyService _notifyService;
        private readonly IWalletService _walletService;
        private readonly IGuaranteeService _guaranteeService;
        private readonly ILogger<MerchantBeforePaymentCanceler> _logger;

        public MerchantBeforePaymentCanceler(
            ApplicationDbContext dbContext,
            INotifyService notifyService,
            IWalletService walletService,
            IGuaranteeService guaranteeService,
            ILogger<MerchantBeforePaymentCanceler> logger)
        {
            _dbContext = dbContext;
            _notifyService = notifyService;
            _walletService = walletService;
            _guaranteeService = guaranteeService;
            _logger = logger;
        }

        public async ValueTask CancelAsync(Order order, string reason, string description, CancellationToken cancellationToken)
        {
            var beforeStatuses = OrderStatusProvider.GetMerchantBeforePaymentCancelable();
            var afterStatuses = OrderStatusProvider.GetMerchantAfterPaymentCancelable();

            if (!beforeStatuses.Contains(order.Status) && !afterStatuses.Contains(order.Status))
            {
                throw new ArgumentException("امکان لغو سفارش وجود ندارد");
            }

            if (order.PlanIsLocked)
            {
                if (!string.IsNullOrWhiteSpace(order.CreditLockId))
                {
                    // unfreeze as much as credited amount
                    var unlockResult = await _walletService.UnLockCreditAndSaveAsync(
                        order.OrderPlan.WalletId,
                        order.CreditLockId,
                        order.CreditedAmount,
                        cancellationToken);

                    // add a suspecious credit on failure
                    if (!unlockResult.Success)
                    {
                        await _walletService.AddFailedCreditTransactionOutboxAndSaveAsync(
                            order.OrderPlan.CreditId,
                            order.OrderPlan.WalletId,
                            order.CreditedAmount,
                            CreditTransactionType.UnFreeze,
                            order.CreditLockId,
                            unlockResult.Error);
                    }
                }

                if (order.OrderGuarantees != null && order.OrderGuarantees.Any())
                {
                    var guaranteeProvider = await _guaranteeService.GetGuaranteeProviderByGuarantorId(order.OrderPlan.GuarantorId);

                    // revoke guarantee
                    var guarantee = order.OrderGuarantees.OrderByDescending(g => g.CreatedAt).First();
                    var revokeResult = await guaranteeProvider.DeleteGuaranteeAsync(guarantee.Data);
                    if (!revokeResult.success)
                    {
                        _logger.LogWarning($"could not revoke guarantee order id: {order.Id}, message: {revokeResult.error}");

                        _dbContext.OrderExternalEventOutboxes.Add(new OrderExternalEventOutbox()
                        {
                            OrderId = order.Id,
                            EventType = OrderExternalEventType.RevokeGuarantee,
                            HasFailed = true,
                            FailCount = 1,
                            Message = revokeResult.error
                        });
                    }

                    order.GuaranteeRevoked = revokeResult.success;
                }
            }

            order.Status = OrderStatus.CanceledBeforePaymentByMerchant;

            // schedule a webhook for client with clientrefid
            if (!string.IsNullOrWhiteSpace(order.ClientCancelUrl))
            {
                _dbContext.OrderExternalEventOutboxes.Add(new OrderExternalEventOutbox()
                {
                    OrderId = order.Id,
                    ClientRefId = order.ClientRefId,
                    ClientCancelUrl = order.ClientCancelUrl,
                    EventType = OrderExternalEventType.CancelHook
                });
            }

            await _dbContext.SaveChangesAsync();

            // send consumer canceled by merch notification
            await _notifyService.SendCanceledByMerchantMessage(order.ConsumerUserName, order.MerchantUserId, cancellationToken);
        }

        public ValueTask PostCancelAsync(Order order, CancellationToken cancellationToken)
        {
            return ValueTask.CompletedTask;
        }
    }
}
