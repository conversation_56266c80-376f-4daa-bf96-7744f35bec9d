﻿
syntax = "proto3";

option csharp_namespace = "RobinHood.Services.ServiceManager.Protos";

package inquiry;

import "google/protobuf/wrappers.proto";

service InquiryService {
  rpc ShebaNumberInquiryForPersonalInfo (ShebaNumberInquiryForPersonalInfoRequest) returns (ShebaNumberInquiryForPersonalInfoResponse);
  rpc CreditScoreReportInfo (CreditScoreReportInfoRequest) returns (CreditScoreReportInfoResponse);
  rpc GetCardToDeposit (GetCardToDepositRequest) returns (GetCardToDepositResponse);
  rpc GetCardToOwnerInfo (GetCardToOwnerInfoRequest) returns (GetCardToOwnerInfoResponse);
  rpc GetCardToSheba (GetCardToShebaRequest) returns (GetCardToShebaResponse);
  rpc NationalCodeInquiryWithPersonalInfo (NationalCodeInquiryWithPersonalInfoRequest) returns (NationalCodeInquiryWithPersonalInfoResponse);
  rpc PostalCodeInquiry (PostalCodeInquiryRequest) returns (PostalCodeInquiryResponse);
  rpc ReportLinkCreditScoreInfo (ReportLinkCreditScoreInfoRequest) returns (ReportLinkCreditScoreInfoResponse);
  rpc StartCreditScoreInfo (StartCreditScoreInfoRequest) returns (StartCreditScoreInfoResponse);
  rpc ValidateCreditScoreInfo (ValidateCreditScoreInfoRequest) returns (ValidateCreditScoreInfoResponse);
  rpc MachingCardWithNationalCode (MachingCardWithNationalCodeRequest) returns (MatchingResponse);
  rpc MachingMobileWithNationalCode (MachingMobileWithNationalCodeRequest) returns (MatchingResponse);
  rpc MachingShebaWithNationalCode (MachingShebaWithNationalCodeRequest) returns (MatchingResponse);
}


message MachingShebaWithNationalCodeRequest {
    google.protobuf.StringValue sheba_number = 1;
    google.protobuf.StringValue national_code = 2;
    google.protobuf.StringValue birth_date = 3;
}

message MachingMobileWithNationalCodeRequest {
    google.protobuf.StringValue mobile_number = 1;
    google.protobuf.StringValue national_code = 2;
}

message MachingCardWithNationalCodeRequest {
    google.protobuf.StringValue card_number = 1;
    google.protobuf.StringValue national_code = 2;
    google.protobuf.StringValue birth_date = 3;
}

message MatchingResponse {
    bool matched = 1;
}

message ValidateCreditScoreInfoRequest {
    google.protobuf.StringValue code = 1;
    google.protobuf.StringValue token = 2;
}

message ValidateCreditScoreInfoResponse {
    bool success = 1;
    google.protobuf.StringValue validation_status = 2;
    google.protobuf.StringValue message = 3;
}

message StartCreditScoreInfoRequest {
    google.protobuf.StringValue national_code = 1;
    google.protobuf.StringValue mobile_number = 2;
}

message StartCreditScoreInfoResponse {
    bool success = 1;
    google.protobuf.StringValue token = 2;
    google.protobuf.StringValue message = 3;
}

message ReportLinkCreditScoreInfoRequest {
    google.protobuf.StringValue code = 1;
    google.protobuf.StringValue token = 2;
}

message ReportLinkCreditScoreInfoResponse {
    bool success = 1;
    google.protobuf.StringValue unique_code = 2;
    google.protobuf.StringValue message = 3;
}

message PostalCodeInquiryRequest {
    google.protobuf.StringValue postal_code = 1;
}

message PostalCodeInquiryResponse {
    google.protobuf.StringValue city = 1;
    google.protobuf.StringValue province = 2;
    google.protobuf.StringValue township = 3;
    google.protobuf.StringValue locality = 4;
    google.protobuf.StringValue avenue = 5;
    google.protobuf.StringValue stop_street = 6;
    int32 no = 7;
    google.protobuf.StringValue floor = 8;
}

message NationalCodeInquiryWithPersonalInfoRequest {
    google.protobuf.StringValue national_code = 1;
    google.protobuf.StringValue birth_date = 2;
}

message NationalCodeInquiryWithPersonalInfoResponse {
    google.protobuf.StringValue national_code = 1;
    google.protobuf.StringValue first_name = 2;
    google.protobuf.StringValue last_name = 3;
    google.protobuf.StringValue father_name = 4;
    google.protobuf.StringValue birth_date = 5;
    bool alive = 6;
}

message GetCardToShebaRequest {
    google.protobuf.StringValue card = 1;
}

message GetCardToShebaResponse {
    google.protobuf.StringValue sheba_number = 1;
    BankEnum bank_name = 2;
}

message GetCardToOwnerInfoRequest {
    google.protobuf.StringValue card = 1;
}

message GetCardToOwnerInfoResponse {
    BankEnum bank_name = 1;
    BankAccountStatusEnum account_status = 2;
    repeated AccountOwnerInfo account_owner_infos = 3;
}

message GetCardToDepositRequest {
    google.protobuf.StringValue card = 1;
}

message GetCardToDepositResponse {
    google.protobuf.StringValue deposit_number = 1;
    BankEnum bank_name = 2;
}

message CreditScoreReportInfoRequest {
  google.protobuf.StringValue uniqueCode = 1;
}

message CreditScoreReportInfoResponse 
{
	bool success = 1;
    bool has_loans = 2;
    int32 score = 3;
    google.protobuf.StringValue description = 4;
    google.protobuf.StringValue risk = 5;
}

message ShebaNumberInquiryForPersonalInfoRequest {
  google.protobuf.StringValue shebaNumber = 1;
}

message ShebaNumberInquiryForPersonalInfoResponse {
  google.protobuf.StringValue shebaNumber = 1;
  BankEnum bankName = 2;
  BankAccountStatusEnum accountStatus = 3;
  google.protobuf.StringValue depositNumber = 4;
  repeated AccountOwnerInfo accountOwnerInfos = 5;
}


message AccountOwnerInfo{
  google.protobuf.StringValue firstName = 1;
  google.protobuf.StringValue lastName = 2;
}

enum BankEnum {
  UNKNOWN = 0;
  CENTRAL_BANK = 1;
  MELLI = 2;
  MELLAT = 3;
  SAMAN = 4;
  AYANDEH = 5;
  EGHTESAD_NOVIN = 6;
  SEPAH = 7;
  TEJARAT = 8;
  PASARGAD = 9;
  POSTBANK = 10;
  PARSIAN = 11;
  IRAN_ZAMIN = 12;
  KESHVARZI = 13;
  TOSEE_TAAVON = 14;
  TOSEE_SADERAT = 15;
  DEY = 16;
  KHAVAR_MIANEH = 17;
  RESALAT = 18;
  SARMAYEH = 19;
  SINA = 20;
  SHAHR = 21;
  SADERAT = 22;
  SANAT_VA_MADAN = 23;
  MEHR_IRAN = 24;
  REFAH = 25;
  GARDESHGARI = 26;
  MASKAN = 27;
  KOSAR = 28;
  MELLAL = 29;
  ETEBARI_TOSEE = 30;
  KAR_AFARIN = 31;
  NOOR = 32;
  IRAN_VENEZUELA = 33;
}

enum BankAccountStatusEnum {
  UNKNOWN_STATUS = 0;
  ACTIVE = 1;
  BLOCK_WITH_DEPOSIT = 2;
  BLOCK_WITHOUT_DEPOSIT = 3;
  IDLE = 4;
}