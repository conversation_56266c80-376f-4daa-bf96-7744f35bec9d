﻿using Ardalis.GuardClauses;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.BNPL.Domain.Models;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Clients;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Factory;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;
using PayPing.LuckyLuke.Application.Shared.Resources;
using PayPing.LuckyLuke.Application.Shared.Resources.ExtensionValidation;
using PayPing.LuckyLuke.Application.Shared.Services.User;

namespace PayPing.LuckyLuke.Application.Features.Merchant._40_OrderCancel
{
    public record OrderCancelMerchantRequest(Guid trackingCode, string reason, string description);

    public record OrderCancelMerchantResponse(Guid trackingCode);


    public class OrderCancelMerchantEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.OrderCancel,
                async (
                    OrderCancelMerchantRequest request,
                    IOrderCancelMerchantRequestHandler handler,
                    IValidator<OrderCancelMerchantRequest> validator,
                    CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request, cancellationToken);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(request.trackingCode.ToString(), new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("OrderCancelMerchant")
            .WithApiVersionSet(builder.NewApiVersionSet("Order").Build())
            .Produces<OrderCancelMerchantResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Cancel Order By Merchant")
            .WithDescription("Cancel Order By Merchant")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class OrderCancelMerchantRequestHandler : IOrderCancelMerchantRequestHandler
    {
        private readonly IMerchantCancelFactory _merchantCancelFactory;
        private readonly ApplicationDbContext _dbContext;
        private readonly INotifyService _notifyService;
        private readonly IWalletService _walletService;
        private readonly IGuaranteeService _guaranteeService;
        private readonly IUserContext _userContext;
        private readonly ILogger<OrderCancelMerchantRequestHandler> _logger;
        private readonly BNPLOptions _bnplOptions;

        public OrderCancelMerchantRequestHandler(IMerchantCancelFactory merchantCancelFactory, ApplicationDbContext dbContext, INotifyService notifyService, IWalletService walletService, IGuaranteeService guaranteeService, IUserContext userContext, IOptions<BNPLOptions> bnplOptions, ILogger<OrderCancelMerchantRequestHandler> logger)
        {
            _merchantCancelFactory = merchantCancelFactory;
            _dbContext = dbContext;
            _notifyService = notifyService;
            _walletService = walletService;
            _guaranteeService = guaranteeService;
            _userContext = userContext;
            _logger = logger;
            _bnplOptions = bnplOptions.Value;
        }

        public async ValueTask<OrderCancelMerchantResponse> HandleAsync(OrderCancelMerchantRequest request, CancellationToken cancellationToken)
        {
            int merchUserId = _userContext.CurrentUserId.Value;

            var order = await _dbContext.Orders.Where(o => o.TrackingCode == request.trackingCode)
                .Include(o => o.OrderGuarantees)
                .Include(o => o.OrderPayments)
                .Include(o => o.Installments)
                .FirstOrDefaultAsync();

            if (order == null)
            {
                throw new NotFoundException(request.trackingCode.ToString(), "order");
            }

            if (order.MerchantUserId != merchUserId)
            {
                throw new ArgumentException("سفارش متعلق به پذیرنده نیست");
            }

            var canceler = _merchantCancelFactory.Create(order.Status);

            await canceler.CancelAsync(order, request.reason, request.description, cancellationToken);

            return new OrderCancelMerchantResponse(order.TrackingCode);
        }
    }

    public interface IOrderCancelMerchantRequestHandler
    {
        ValueTask<OrderCancelMerchantResponse> HandleAsync(OrderCancelMerchantRequest request, CancellationToken cancellationToken);
    }

    public class OrderCancelMerchantValidator : AbstractValidator<OrderCancelMerchantRequest>
    {
        public OrderCancelMerchantValidator()
        {
            RuleFor(x => x.trackingCode).NotEmpty().WithResourceError(() => ValidatorDictionary.tracking_code_is_required);
        }
    }
}
