﻿using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PayPing.BNPL.Application.Infrastructure.Persistence;
using PayPing.LuckyLuke.Application.Domain.Enums;
using PayPing.LuckyLuke.Application.Infrastructure.Configurations;
using PayPing.LuckyLuke.Application.Infrastructure.Exceptions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Abstractions;
using PayPing.LuckyLuke.Application.Infrastructure.Web.Routes;
using PayPing.LuckyLuke.Application.Shared.Abstractions;

namespace PayPing.LuckyLuke.Application.Features.Merchant._007_AcceptRegulations
{
    public record AcceptRegulationsRequest(bool acceptance = false);

    public record AcceptRegulationsResponse(int contractId);

    public class AcceptRegulationsEndpoint : IMerchantMinimalEndpoint
    {
        public IEndpointRouteBuilder MapEndpoint(IEndpointRouteBuilder builder)
        {
            builder.MapPost(
                MerchantRoutes.AcceptRegulations,
                async (AcceptRegulationsRequest request, IAcceptRegulationsRequestHandler handler, IValidator<AcceptRegulationsRequest> validator, CancellationToken cancellationToken) =>
                {

                    var validationResult = await validator.ValidateAsync(request);
                    if (!validationResult.IsValid)
                    {
                        throw new CustomValidationException(null, new ValidationResultModel(validationResult));
                    }

                    var result = await handler.HandleAsync(request, cancellationToken);

                    return Results.Ok(result);
                })
            .RequireAuthorization("write")
            .WithName("AcceptRegulations")
            .WithApiVersionSet(builder.NewApiVersionSet("Contract").Build())
            .Produces<AcceptRegulationsResponse>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .WithSummary("Accept Regulations")
            .WithDescription("Accept Regulations")
            .WithOpenApi()
            .HasApiVersion(1.0);

            return builder;
        }
    }

    public class AcceptRegulationsRequestHandler : IAcceptRegulationsRequestHandler
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly BNPLOptions _bnplOptions;
        private readonly IUserContext _userContext;

        public AcceptRegulationsRequestHandler(ApplicationDbContext dbContext, IUserContext userContext, IOptions<BNPLOptions> bnplOptions)
        {
            _dbContext = dbContext;
            _bnplOptions = bnplOptions.Value;
            _userContext = userContext;
        }

        public async ValueTask<AcceptRegulationsResponse> HandleAsync(AcceptRegulationsRequest request, CancellationToken cancellationToken)
        {
            int userId = _userContext.CurrentUserId.Value;

            if(!request.acceptance)
            {
                throw new InvalidOperationException("با قوانین و مقررات موافقت کنید");
            }

            var contract = await _dbContext.Contracts
               .Where(c => c.MerchantUserId == userId && c.ActivationStatus == ActivationStatus.CompleteMerchantProfileInfo)
               .FirstOrDefaultAsync();

            if (contract == null)
            {
                throw new ArgumentException("قرارداد فروش اقساطی فعال یافت نشد");
            }

            contract.ActivationStatus = ActivationStatus.AcceptRegulations;
            await _dbContext.SaveChangesAsync();

            return new AcceptRegulationsResponse(contract.Id);
        }

    }
    public interface IAcceptRegulationsRequestHandler
    {
        ValueTask<AcceptRegulationsResponse> HandleAsync(AcceptRegulationsRequest request, CancellationToken cancellationToken);
    }

    public class AcceptRegulationsValidator : AbstractValidator<AcceptRegulationsRequest>
    {
        public AcceptRegulationsValidator()
        {
            
        }
    }

}
